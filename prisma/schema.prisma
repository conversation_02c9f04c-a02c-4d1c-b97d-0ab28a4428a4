// Soimagine Marketing Platform Database Schema
// Comprehensive backend system for credit management, gamification, and marketing automation

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // Add vector extension
  extensions = [pgvector(map: "vector")]
}

/*
  Note: Vector type is not directly supported by Prisma
  We use raw SQL queries for vector operations 

  - to store embeddings
    embedding Unsupported("vector(1536)")

  - to query vectors
    SELECT * FROM my_table WHERE embedding <-> '[...]';
    
  - to find similar vectors
    SELECT * FROM my_table 
    ORDER BY embedding <-> '[...]' 
    LIMIT 5;

  - to calculate cosine similarity
    SELECT 1 - (embedding <=> '[...]') as cosine_similarity 
    FROM my_table;

  Note: This requires the pgvector extension installed on your PostgreSQL database.
  You can install it using:
  CREATE EXTENSION IF NOT EXISTS vector;

  Common vector operations:
  <->  : Euclidean distance
  <=>  : Cosine distance
  <#>  : Inner product
*/

// ============================================================================
// AUTHENTICATION & USER MANAGEMENT
// ============================================================================

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)

  // Profile information
  firstName     String?
  lastName      String?
  company       String?
  phone         String?
  timezone      String?

  // Account status
  isActive      Boolean   @default(true)
  isVerified    Boolean   @default(false)
  lastLoginAt   DateTime?

  // Security fields
  lockedUntil           DateTime?
  failedLoginAttempts   Int       @default(0)

  // Timestamps
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts         Account[]
  sessions         Session[]
  authEvents       AuthEvent[]
  creditBalance    CreditBalance?
  creditTransactions CreditTransaction[]
  userProgress     UserProgress[]
  challengeCompletions ChallengeCompletion[]
  achievements     UserAchievement[]
  socialShares     SocialShare[]
  referrals        Referral[]
  apiUsage         ApiUsage[]

  // Social Media Management Relations
  socialAccounts   SocialAccount[]
  posts            Post[]
  campaigns        Campaign[]
  contentTemplates ContentTemplate[]
  brandVoices      BrandVoice[]
  scheduledPosts   ScheduledPost[]
  analytics        Analytics[]
  teamMembers      TeamMember[]
  clients          Client[]
  notifications    Notification[]
  abTests          AbTest[]

  @@map("users")
}

model AuthEvent {
  id        String   @id @default(cuid())
  type      String   // e.g., "SIGN_IN", "SIGN_OUT", "FAILED_LOGIN"
  userId    String
  success   Boolean
  metadata  Json?    // Additional event data
  createdAt DateTime @default(now())

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("auth_events")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum UserRole {
  USER
  ADMIN
  MODERATOR
}

// ============================================================================
// CREDIT MANAGEMENT SYSTEM
// ============================================================================

model CreditBalance {
  id              String   @id @default(cuid())
  userId          String   @unique

  // Current balances
  totalCredits    Int      @default(0)
  dailyCredits    Int      @default(0)
  monthlyCredits  Int      @default(0)

  // Limits
  dailyLimit      Int      @default(100)
  monthlyLimit    Int      @default(1000)

  // Reset tracking
  lastDailyReset  DateTime @default(now())
  lastMonthlyReset DateTime @default(now())

  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("credit_balances")
}

model CreditTransaction {
  id              String            @id @default(cuid())
  userId          String

  // Transaction details
  type            CreditTransactionType
  amount          Int
  description     String

  // Context
  source          String?           // e.g., "challenge_completion", "daily_bonus", "ai_usage"
  sourceId        String?           // Reference to related entity
  metadata        Json?             // Additional context data

  // Balance after transaction
  balanceAfter    Int

  // Timestamps
  createdAt       DateTime          @default(now())

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("credit_transactions")
}

enum CreditTransactionType {
  EARNED
  SPENT
  BONUS
  PENALTY
  REFUND
  ADMIN_ADJUSTMENT
}

// ============================================================================
// GAMIFICATION SYSTEM
// ============================================================================

model Challenge {
  id              String            @id @default(cuid())

  // Basic info
  title           String
  description     String
  instructions    String?

  // Challenge configuration
  type            ChallengeType
  category        ChallengeCategory
  difficulty      ChallengeDifficulty @default(BEGINNER)

  // Rewards
  creditReward    Int               @default(0)
  xpReward        Int               @default(0)

  // Requirements
  requirements    Json?             // Flexible requirements structure
  timeLimit       Int?              // Time limit in minutes

  // Status and availability
  isActive        Boolean           @default(true)
  isPublic        Boolean           @default(true)
  startDate       DateTime?
  endDate         DateTime?

  // AI Generation context
  generatedBy     String?           // AI agent that created this challenge
  learningData    Json?             // Data for AI learning

  // Performance metrics
  completionRate  Float             @default(0.0)
  averageRating   Float             @default(0.0)
  totalAttempts   Int               @default(0)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  completions     ChallengeCompletion[]

  @@map("challenges")
}

model ChallengeCompletion {
  id              String            @id @default(cuid())
  userId          String
  challengeId     String

  // Completion details
  status          CompletionStatus
  score           Int?              // Score achieved (0-100)
  timeSpent       Int?              // Time spent in minutes

  // Submission data
  submissionData  Json?             // User's submission/proof
  feedback        String?           // AI or admin feedback

  // Verification
  isVerified      Boolean           @default(false)
  verifiedBy      String?           // Admin or AI agent
  verifiedAt      DateTime?

  // Rewards given
  creditsAwarded  Int               @default(0)
  xpAwarded       Int               @default(0)

  // Timestamps
  startedAt       DateTime          @default(now())
  completedAt     DateTime?

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  challenge       Challenge         @relation(fields: [challengeId], references: [id], onDelete: Cascade)

  @@unique([userId, challengeId])
  @@map("challenge_completions")
}

model UserProgress {
  id              String            @id @default(cuid())
  userId          String

  // Experience and levels
  totalXp         Int               @default(0)
  currentLevel    Int               @default(1)

  // Challenge statistics
  challengesCompleted Int           @default(0)
  challengesAttempted Int           @default(0)

  // Category-specific progress
  marketingXp     Int               @default(0)
  socialXp        Int               @default(0)
  contentXp       Int               @default(0)
  referralXp      Int               @default(0)

  // Streaks
  currentStreak   Int               @default(0)
  longestStreak   Int               @default(0)
  lastActivityDate DateTime?

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId])
  @@map("user_progress")
}

model Achievement {
  id              String            @id @default(cuid())

  // Basic info
  name            String            @unique
  description     String
  icon            String?

  // Achievement configuration
  type            AchievementType
  category        AchievementCategory
  rarity          AchievementRarity @default(COMMON)

  // Requirements
  requirements    Json              // Flexible requirements structure

  // Rewards
  creditReward    Int               @default(0)
  xpReward        Int               @default(0)

  // Status
  isActive        Boolean           @default(true)
  isSecret        Boolean           @default(false)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  userAchievements UserAchievement[]

  @@map("achievements")
}

model UserAchievement {
  id              String            @id @default(cuid())
  userId          String
  achievementId   String

  // Achievement details
  progress        Float             @default(0.0) // Progress towards achievement (0.0 - 1.0)
  isCompleted     Boolean           @default(false)

  // Rewards
  creditsAwarded  Int               @default(0)
  xpAwarded       Int               @default(0)

  // Timestamps
  unlockedAt      DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement     Achievement       @relation(fields: [achievementId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

// ============================================================================
// CONTENT MARKETING AUTOMATION
// ============================================================================

model SocialShare {
  id              String            @id @default(cuid())
  userId          String

  // Share details
  platform        SocialPlatform
  contentType     ContentType
  contentUrl      String?
  referralLink    String

  // Content metadata
  title           String?
  description     String?
  imageUrl        String?
  hashtags        String?           // Comma-separated hashtags

  // Tracking
  clicks          Int               @default(0)
  conversions     Int               @default(0)
  engagement      Json?             // Platform-specific engagement metrics

  // Verification
  isVerified      Boolean           @default(false)
  verificationData Json?            // Proof of sharing

  // Rewards
  creditsEarned   Int               @default(0)
  xpEarned        Int               @default(0)

  // Timestamps
  sharedAt        DateTime          @default(now())
  lastTrackedAt   DateTime?

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("social_shares")
}

model Referral {
  id              String            @id @default(cuid())
  referrerId      String            // User who made the referral

  // Referral details
  referralCode    String            @unique
  email           String?
  name            String?

  // Status
  status          ReferralStatus    @default(PENDING)
  convertedUserId String?           // ID of user who signed up

  // Rewards
  creditsAwarded  Int               @default(0)
  xpAwarded       Int               @default(0)

  // Tracking
  clickCount      Int               @default(0)
  lastClickAt     DateTime?
  convertedAt     DateTime?

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  referrer        User              @relation(fields: [referrerId], references: [id], onDelete: Cascade)

  @@map("referrals")
}

model ApiUsage {
  id              String            @id @default(cuid())
  userId          String

  // Usage details
  endpoint        String
  method          String
  assistantType   String?           // consultant, engineer, executive

  // Request details
  requestSize     Int?              // Size in bytes
  responseSize    Int?              // Size in bytes
  processingTime  Int?              // Time in milliseconds

  // Credits
  creditsUsed     Int               @default(1)

  // Status
  status          ApiUsageStatus
  errorMessage    String?

  // Metadata
  userAgent       String?
  ipAddress       String?
  metadata        Json?

  // Timestamps
  createdAt       DateTime          @default(now())

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_usage")
}

// ============================================================================
// SOCIAL MEDIA MANAGEMENT SYSTEM
// ============================================================================

model SocialAccount {
  id              String            @id @default(cuid())
  userId          String

  // Platform details
  platform        SocialPlatform
  platformUserId  String            // Platform-specific user ID
  username        String
  displayName     String?
  profileUrl      String?
  profileImage    String?

  // Authentication
  accessToken     String?           @db.Text
  refreshToken    String?           @db.Text
  tokenExpiry     DateTime?

  // Account status
  isActive        Boolean           @default(true)
  isVerified      Boolean           @default(false)
  lastSyncAt      DateTime?

  // Account metrics
  followersCount  Int               @default(0)
  followingCount  Int               @default(0)
  postsCount      Int               @default(0)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  posts           Post[]
  scheduledPosts  ScheduledPost[]
  analytics       Analytics[]

  @@unique([userId, platform, platformUserId])
  @@map("social_accounts")
}

model Post {
  id              String            @id @default(cuid())
  userId          String
  socialAccountId String?
  campaignId      String?

  // Content
  content         String            @db.Text
  title           String?
  description     String?

  // Media
  mediaUrls       String[]          // Array of media URLs
  mediaTypes      MediaType[]       // Corresponding media types
  thumbnailUrl    String?

  // Platform-specific data
  platform        SocialPlatform
  platformPostId  String?           // ID from the social platform
  postUrl         String?

  // Scheduling
  status          PostStatus        @default(DRAFT)
  publishedAt     DateTime?
  scheduledFor    DateTime?

  // Engagement metrics
  likes           Int               @default(0)
  comments        Int               @default(0)
  shares          Int               @default(0)
  views           Int               @default(0)
  clicks          Int               @default(0)

  // AI Generation
  isAiGenerated   Boolean           @default(false)
  aiPrompt        String?           @db.Text
  brandVoiceId    String?

  // Hashtags and mentions
  hashtags        String[]
  mentions        String[]

  // A/B Testing
  isAbTest        Boolean           @default(false)
  abTestGroup     String?
  abTestId        String?

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  socialAccount   SocialAccount?    @relation(fields: [socialAccountId], references: [id], onDelete: SetNull)
  campaign        Campaign?         @relation(fields: [campaignId], references: [id], onDelete: SetNull)
  brandVoice      BrandVoice?       @relation(fields: [brandVoiceId], references: [id], onDelete: SetNull)
  abTest          AbTest?           @relation(fields: [abTestId], references: [id], onDelete: SetNull)
  analytics       Analytics[]

  @@map("posts")
}

model Campaign {
  id              String            @id @default(cuid())
  userId          String

  // Basic info
  name            String
  description     String?
  objective       CampaignObjective

  // Targeting
  targetAudience  Json?             // Audience targeting criteria
  platforms       SocialPlatform[]

  // Budget and scheduling
  budget          Float?
  startDate       DateTime
  endDate         DateTime?

  // Status
  status          CampaignStatus    @default(DRAFT)
  isActive        Boolean           @default(true)

  // Performance tracking
  totalPosts      Int               @default(0)
  totalReach      Int               @default(0)
  totalEngagement Int               @default(0)
  totalClicks     Int               @default(0)
  totalConversions Int              @default(0)

  // AI optimization
  isAiOptimized   Boolean           @default(false)
  optimizationGoals Json?

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  posts           Post[]
  abTests         AbTest[]
  analytics       Analytics[]

  @@map("campaigns")
}

model ContentTemplate {
  id              String            @id @default(cuid())
  userId          String

  // Template details
  name            String
  description     String?
  category        TemplateCategory

  // Content structure
  template        String            @db.Text
  variables       Json?             // Template variables and their types

  // Platform optimization
  platforms       SocialPlatform[]

  // Usage tracking
  usageCount      Int               @default(0)

  // Status
  isActive        Boolean           @default(true)
  isPublic        Boolean           @default(false)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("content_templates")
}

model BrandVoice {
  id              String            @id @default(cuid())
  userId          String

  // Brand voice details
  name            String
  description     String?

  // Voice characteristics
  tone            String            // e.g., "professional", "casual", "friendly"
  style           String            // e.g., "formal", "conversational", "humorous"
  personality     Json              // Detailed personality traits

  // Content guidelines
  dosList         String[]          // Things to do
  dontsList       String[]          // Things to avoid
  keywords        String[]          // Preferred keywords
  hashtags        String[]          // Brand hashtags

  // AI training data
  sampleContent   String[]          // Sample content for AI training

  // Usage
  isDefault       Boolean           @default(false)
  isActive        Boolean           @default(true)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  posts           Post[]

  @@map("brand_voices")
}

model ScheduledPost {
  id              String            @id @default(cuid())
  userId          String
  socialAccountId String

  // Content
  content         String            @db.Text
  mediaUrls       String[]
  mediaTypes      MediaType[]

  // Scheduling
  scheduledFor    DateTime
  timezone        String            @default("UTC")

  // Status
  status          ScheduleStatus    @default(PENDING)
  publishedAt     DateTime?
  failureReason   String?

  // Platform-specific
  platform        SocialPlatform
  platformPostId  String?

  // Retry logic
  retryCount      Int               @default(0)
  maxRetries      Int               @default(3)
  nextRetryAt     DateTime?

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  socialAccount   SocialAccount     @relation(fields: [socialAccountId], references: [id], onDelete: Cascade)

  @@map("scheduled_posts")
}

model AbTest {
  id              String            @id @default(cuid())
  userId          String
  campaignId      String?

  // Test details
  name            String
  description     String?
  hypothesis      String?

  // Test configuration
  testType        AbTestType
  trafficSplit    Float             @default(0.5) // 0.0 to 1.0

  // Status
  status          AbTestStatus      @default(DRAFT)
  startDate       DateTime?
  endDate         DateTime?

  // Results
  winnerVariant   String?           // "A" or "B"
  confidenceLevel Float?            // Statistical confidence

  // Metrics
  totalImpressions Int              @default(0)
  totalClicks     Int               @default(0)
  totalConversions Int              @default(0)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  campaign        Campaign?         @relation(fields: [campaignId], references: [id], onDelete: SetNull)
  posts           Post[]

  @@map("ab_tests")
}

model Analytics {
  id              String            @id @default(cuid())
  userId          String
  socialAccountId String?
  postId          String?
  campaignId      String?

  // Time period
  date            DateTime          @db.Date
  period          AnalyticsPeriod   @default(DAILY)

  // Metrics
  impressions     Int               @default(0)
  reach           Int               @default(0)
  engagement      Int               @default(0)
  clicks          Int               @default(0)
  shares          Int               @default(0)
  comments        Int               @default(0)
  likes           Int               @default(0)
  saves           Int               @default(0)

  // Calculated metrics
  engagementRate  Float             @default(0.0)
  clickThroughRate Float            @default(0.0)

  // Platform-specific metrics
  platformMetrics Json?

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  socialAccount   SocialAccount?    @relation(fields: [socialAccountId], references: [id], onDelete: SetNull)
  post            Post?             @relation(fields: [postId], references: [id], onDelete: SetNull)
  campaign        Campaign?         @relation(fields: [campaignId], references: [id], onDelete: SetNull)

  @@unique([userId, socialAccountId, postId, campaignId, date, period])
  @@map("analytics")
}

model TeamMember {
  id              String            @id @default(cuid())
  userId          String            // Agency owner
  memberUserId    String            // Team member

  // Role and permissions
  role            TeamRole          @default(MEMBER)
  permissions     Json              // Detailed permissions

  // Access control
  isActive        Boolean           @default(true)
  canManagePosts  Boolean           @default(true)
  canManageCampaigns Boolean        @default(false)
  canViewAnalytics Boolean          @default(true)
  canManageTeam   Boolean           @default(false)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, memberUserId])
  @@map("team_members")
}

model Client {
  id              String            @id @default(cuid())
  userId          String            // Agency owner

  // Client details
  name            String
  email           String
  company         String?
  phone           String?

  // Account status
  isActive        Boolean           @default(true)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("clients")
}

model Notification {
  id              String            @id @default(cuid())
  userId          String

  // Notification details
  type            NotificationType
  title           String
  message         String            @db.Text

  // Status
  isRead          Boolean           @default(false)
  isArchived      Boolean           @default(false)

  // Action data
  actionUrl       String?
  actionData      Json?

  // Timestamps
  createdAt       DateTime          @default(now())
  readAt          DateTime?

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

enum ChallengeType {
  SOCIAL_SHARE
  CONTENT_CREATION
  REFERRAL
  ENGAGEMENT
  LEARNING
  COMMUNITY
  CUSTOM
}

enum ChallengeCategory {
  MARKETING
  SOCIAL_MEDIA
  CONTENT
  REFERRALS
  COMMUNITY
  LEARNING
  SPECIAL_EVENT
}

enum ChallengeDifficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum CompletionStatus {
  STARTED
  IN_PROGRESS
  COMPLETED
  FAILED
  ABANDONED
}

enum AchievementType {
  MILESTONE
  STREAK
  SOCIAL
  REFERRAL
  ENGAGEMENT
  LEARNING
  SPECIAL
}

enum AchievementCategory {
  PROGRESS
  SOCIAL
  CONTENT
  REFERRALS
  COMMUNITY
  LEARNING
  SPECIAL
}

enum AchievementRarity {
  COMMON
  UNCOMMON
  RARE
  EPIC
  LEGENDARY
}

enum SocialPlatform {
  TWITTER
  FACEBOOK
  INSTAGRAM
  LINKEDIN
  TIKTOK
  YOUTUBE
  REDDIT
  DISCORD
  OTHER
}

enum ContentType {
  POST
  STORY
  VIDEO
  IMAGE
  ARTICLE
  LINK
  POLL
  LIVE_STREAM
  OTHER
}

enum ReferralStatus {
  PENDING
  CLICKED
  CONVERTED
  EXPIRED
  CANCELLED
}

enum ApiUsageStatus {
  SUCCESS
  ERROR
  TIMEOUT
  RATE_LIMITED
  UNAUTHORIZED
}

// ============================================================================
// SOCIAL MEDIA MANAGEMENT ENUMS
// ============================================================================

enum MediaType {
  IMAGE
  VIDEO
  GIF
  AUDIO
  DOCUMENT
  CAROUSEL
  STORY
  REEL
  SHORT
}

enum PostStatus {
  DRAFT
  SCHEDULED
  PUBLISHED
  FAILED
  CANCELLED
  ARCHIVED
}

enum ScheduleStatus {
  PENDING
  PROCESSING
  PUBLISHED
  FAILED
  CANCELLED
  RETRYING
}

enum CampaignObjective {
  BRAND_AWARENESS
  REACH
  TRAFFIC
  ENGAGEMENT
  APP_INSTALLS
  VIDEO_VIEWS
  LEAD_GENERATION
  CONVERSIONS
  CATALOG_SALES
  STORE_TRAFFIC
}

enum CampaignStatus {
  DRAFT
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
  ARCHIVED
}

enum TemplateCategory {
  PROMOTIONAL
  EDUCATIONAL
  ENTERTAINMENT
  NEWS
  SEASONAL
  PRODUCT
  SERVICE
  EVENT
  TESTIMONIAL
  BEHIND_SCENES
  USER_GENERATED
  ANNOUNCEMENT
}

enum AbTestType {
  CONTENT
  TIMING
  AUDIENCE
  CREATIVE
  HASHTAGS
  CALL_TO_ACTION
}

enum AbTestStatus {
  DRAFT
  RUNNING
  COMPLETED
  CANCELLED
  ANALYZING
}

enum AnalyticsPeriod {
  HOURLY
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

enum TeamRole {
  OWNER
  ADMIN
  MANAGER
  MEMBER
  VIEWER
}

enum NotificationType {
  POST_PUBLISHED
  POST_FAILED
  CAMPAIGN_STARTED
  CAMPAIGN_ENDED
  ANALYTICS_REPORT
  TEAM_INVITATION
  ACCOUNT_CONNECTED
  ACCOUNT_DISCONNECTED
  SYSTEM_UPDATE
  BILLING_ALERT
  ENGAGEMENT_MILESTONE
  PERFORMANCE_ALERT
}

"use client"

import { useCallback } from "react"
import { useRouter } from "next/navigation"
import { useSession, signIn, signOut } from "next-auth/react"
import { useToast } from "@/hooks/use-toast"

export function useAuthHelpers() {
  const router = useRouter()
  const { data: session } = useSession()
  const { toast } = useToast()

  const login = useCallback(
    async (email: string, password: string) => {
      try {
        const result = await signIn("credentials", {
          redirect: false,
          email,
          password,
        })

        if (result?.error) {
          throw new Error(result.error)
        }

        toast({
          title: "Welcome back!",
          description: "You have successfully logged in.",
        })

        router.push("/dashboard")
        router.refresh()
      } catch (error) {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Invalid credentials",
          variant: "destructive",
        })
        throw error
      }
    },
    [router, toast]
  )

  const logout = useCallback(async () => {
    try {
      await signOut({ redirect: false })
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      })
      router.push("/")
      router.refresh()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to log out. Please try again.",
        variant: "destructive",
      })
    }
  }, [router, toast])

  const requireAuth = useCallback(
    (options?: { requireVerified?: boolean; requiredRole?: "USER" | "ADMIN" }) => {
      if (!session) {
        toast({
          title: "Access denied",
          description: "Please log in to access this page",
          variant: "destructive",
        })
        router.push("/login")
        return false
      }

      if (options?.requireVerified && !session.user.emailVerified) {
        toast({
          title: "Email verification required",
          description: "Please verify your email address to access this page",
          variant: "destructive",
        })
        router.push("/verify-email")
        return false
      }

      if (options?.requiredRole && session.user.role !== options.requiredRole) {
        toast({
          title: "Access denied",
          description: "You do not have permission to access this page",
          variant: "destructive",
        })
        router.push("/dashboard")
        return false
      }

      return true
    },
    [session, router, toast]
  )

  return {
    session,
    isAuthenticated: !!session,
    isVerified: !!session?.user?.emailVerified,
    role: session?.user?.role,
    login,
    logout,
    requireAuth,
  }
}

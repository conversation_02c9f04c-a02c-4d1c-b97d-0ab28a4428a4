'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { UserProgress } from '@/lib/types/marketing-platform'

interface UseUserProgressReturn {
  progress: UserProgress | null
  loading: boolean
  error: string | null
  refreshProgress: () => Promise<void>
  calculateLevel: (xp: number) => number
  getXpForNextLevel: (currentLevel: number) => number
  getLevelProgress: (xp: number, level: number) => number
}

export function useUserProgress(): UseUserProgressReturn {
  const { data: session } = useSession()
  const [progress, setProgress] = useState<UserProgress | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // XP calculation functions
  const calculateLevel = useCallback((xp: number): number => {
    // Level formula: level = floor(sqrt(xp / 100)) + 1
    // This means: Level 1: 0-99 XP, Level 2: 100-399 XP, Level 3: 400-899 XP, etc.
    return Math.floor(Math.sqrt(xp / 100)) + 1
  }, [])

  const getXpForNextLevel = useCallback((currentLevel: number): number => {
    // XP required for next level = (level^2) * 100
    return (currentLevel * currentLevel) * 100
  }, [])

  const getLevelProgress = useCallback((xp: number, level: number): number => {
    const currentLevelXp = ((level - 1) * (level - 1)) * 100
    const nextLevelXp = getXpForNextLevel(level)
    const progressXp = xp - currentLevelXp
    const requiredXp = nextLevelXp - currentLevelXp
    
    return Math.min((progressXp / requiredXp) * 100, 100)
  }, [getXpForNextLevel])

  const refreshProgress = useCallback(async () => {
    if (!session?.user?.id) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/user/progress')
      
      if (!response.ok) {
        throw new Error(`Failed to fetch user progress: ${response.statusText}`)
      }

      const data = await response.json()
      setProgress(data.progress)
    } catch (err) {
      console.error('Error fetching user progress:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch user progress')
    } finally {
      setLoading(false)
    }
  }, [session?.user?.id])

  // Fetch progress on mount and when session changes
  useEffect(() => {
    refreshProgress()
  }, [refreshProgress])

  // Listen for progress updates from challenge completions
  useEffect(() => {
    const handleProgressUpdate = (event: CustomEvent) => {
      const { xpAwarded, userProgress } = event.detail
      
      if (userProgress) {
        setProgress(userProgress)
      } else if (progress && xpAwarded) {
        // Update local progress if we have the data
        const newTotalXp = progress.totalXp + xpAwarded
        const newLevel = calculateLevel(newTotalXp)
        
        setProgress(prev => prev ? {
          ...prev,
          totalXp: newTotalXp,
          currentLevel: newLevel,
          challengesCompleted: prev.challengesCompleted + 1
        } : null)
      }
    }

    window.addEventListener('challengeCompleted' as any, handleProgressUpdate)
    
    return () => {
      window.removeEventListener('challengeCompleted' as any, handleProgressUpdate)
    }
  }, [progress, calculateLevel])

  return {
    progress,
    loading,
    error,
    refreshProgress,
    calculateLevel,
    getXpForNextLevel,
    getLevelProgress
  }
}

// Helper hook for level calculations
export function useLevelCalculations() {
  const calculateLevel = useCallback((xp: number): number => {
    return Math.floor(Math.sqrt(xp / 100)) + 1
  }, [])

  const getXpForLevel = useCallback((level: number): number => {
    return ((level - 1) * (level - 1)) * 100
  }, [])

  const getXpForNextLevel = useCallback((currentLevel: number): number => {
    return (currentLevel * currentLevel) * 100
  }, [])

  const getLevelProgress = useCallback((xp: number, level: number): number => {
    const currentLevelXp = getXpForLevel(level)
    const nextLevelXp = getXpForNextLevel(level)
    const progressXp = xp - currentLevelXp
    const requiredXp = nextLevelXp - currentLevelXp
    
    return Math.min((progressXp / requiredXp) * 100, 100)
  }, [getXpForLevel, getXpForNextLevel])

  const getXpToNextLevel = useCallback((xp: number, level: number): number => {
    const nextLevelXp = getXpForNextLevel(level)
    return Math.max(0, nextLevelXp - xp)
  }, [getXpForNextLevel])

  return {
    calculateLevel,
    getXpForLevel,
    getXpForNextLevel,
    getLevelProgress,
    getXpToNextLevel
  }
}

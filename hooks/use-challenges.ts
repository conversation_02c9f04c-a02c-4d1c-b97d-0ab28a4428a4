'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Challenge, ChallengeCompletion } from '@/lib/types/marketing-platform'

interface UseChallengesReturn {
  challenges: Challenge[]
  loading: boolean
  error: string | null
  refreshChallenges: () => Promise<void>
  completeChallenge: (challengeId: string, submissionData?: any, score?: number, timeSpent?: number) => Promise<boolean>
  generateChallenge: (params?: any) => Promise<Challenge | null>
}

export function useChallenges(filters?: {
  category?: string
  difficulty?: string
  type?: string
  status?: string
  page?: number
  limit?: number
}): UseChallengesReturn {
  const { data: session } = useSession()
  const [challenges, setChallenges] = useState<Challenge[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refreshChallenges = useCallback(async () => {
    if (!session?.user?.id) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (filters?.category) params.append('category', filters.category)
      if (filters?.difficulty) params.append('difficulty', filters.difficulty)
      if (filters?.type) params.append('type', filters.type)
      if (filters?.status) params.append('status', filters.status)
      if (filters?.page) params.append('page', filters.page.toString())
      if (filters?.limit) params.append('limit', filters.limit.toString())

      const response = await fetch(`/api/challenges/list?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch challenges: ${response.statusText}`)
      }

      const data = await response.json()
      setChallenges(data.challenges || [])
    } catch (err) {
      console.error('Error fetching challenges:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch challenges')
    } finally {
      setLoading(false)
    }
  }, [session?.user?.id, filters])

  const completeChallenge = useCallback(async (
    challengeId: string,
    submissionData?: any,
    score?: number,
    timeSpent?: number
  ): Promise<boolean> => {
    if (!session?.user?.id) {
      return false
    }

    try {
      const response = await fetch('/api/challenges/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          challengeId,
          submissionData,
          score,
          timeSpent
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to complete challenge')
      }

      const result = await response.json()
      
      // Update the challenge in the local state
      setChallenges(prev => prev.map(challenge => 
        challenge.id === challengeId 
          ? { 
              ...challenge, 
              userCompletion: result.completion 
            }
          : challenge
      ))

      // Dispatch event for other components to listen to
      const event = new CustomEvent('challengeCompleted', {
        detail: {
          challengeId,
          completion: result.completion,
          rewards: result.rewards
        }
      })
      window.dispatchEvent(event)

      return true
    } catch (err) {
      console.error('Error completing challenge:', err)
      setError(err instanceof Error ? err.message : 'Failed to complete challenge')
      return false
    }
  }, [session?.user?.id])

  const generateChallenge = useCallback(async (params?: any): Promise<Challenge | null> => {
    if (!session?.user?.id) {
      return null
    }

    try {
      const response = await fetch('/api/challenges/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params || {})
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate challenge')
      }

      const result = await response.json()
      
      // Add the new challenge to the local state
      if (result.challenge) {
        setChallenges(prev => [result.challenge, ...prev])
      }

      return result.challenge
    } catch (err) {
      console.error('Error generating challenge:', err)
      setError(err instanceof Error ? err.message : 'Failed to generate challenge')
      return null
    }
  }, [session?.user?.id])

  // Fetch challenges on mount and when filters change
  useEffect(() => {
    refreshChallenges()
  }, [refreshChallenges])

  return {
    challenges,
    loading,
    error,
    refreshChallenges,
    completeChallenge,
    generateChallenge
  }
}

// Helper hook for a single challenge
export function useChallenge(challengeId: string) {
  const [challenge, setChallenge] = useState<Challenge | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refreshChallenge = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/challenges/${challengeId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch challenge: ${response.statusText}`)
      }

      const data = await response.json()
      setChallenge(data.challenge)
    } catch (err) {
      console.error('Error fetching challenge:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch challenge')
    } finally {
      setLoading(false)
    }
  }, [challengeId])

  useEffect(() => {
    if (challengeId) {
      refreshChallenge()
    }
  }, [refreshChallenge, challengeId])

  return {
    challenge,
    loading,
    error,
    refreshChallenge
  }
}

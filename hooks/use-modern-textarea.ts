"use client"

import { useState, useRef, useCallback, useEffect } from 'react'
import { ModernTextAreaRef } from '@/components/ui/modern-textarea'

// Hook for managing textarea state and operations
export function useModernTextArea(initialValue = '') {
  const [value, setValue] = useState(initialValue)
  const [history, setHistory] = useState<string[]>([initialValue])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [isTyping, setIsTyping] = useState(false)
  const textareaRef = useRef<ModernTextAreaRef>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  // Handle value changes with history tracking
  const handleChange = useCallback((newValue: string) => {
    setValue(newValue)
    
    // Add to history if significantly different
    if (newValue !== history[historyIndex]) {
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(newValue)
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)
    }

    // Handle typing indicator
    setIsTyping(true)
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
    }, 1000)
  }, [history, historyIndex])

  // Undo operation
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1
      setValue(history[newIndex])
      setHistoryIndex(newIndex)
    }
  }, [history, historyIndex])

  // Redo operation
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      setValue(history[newIndex])
      setHistoryIndex(newIndex)
    }
  }, [history, historyIndex])

  // Insert text at cursor position
  const insertText = useCallback((text: string) => {
    textareaRef.current?.insertText(text)
  }, [])

  // Format text with markdown
  const formatText = useCallback((type: string) => {
    if (!textareaRef.current) return

    const selection = textareaRef.current.getSelection()
    const selectedText = value.slice(selection.start, selection.end)
    
    let formattedText = ''
    let cursorOffset = 0

    switch (type) {
      case 'bold':
        formattedText = `**${selectedText}**`
        cursorOffset = selectedText ? 0 : 2
        break
      case 'italic':
        formattedText = `*${selectedText}*`
        cursorOffset = selectedText ? 0 : 1
        break
      case 'code':
        formattedText = `\`${selectedText}\``
        cursorOffset = selectedText ? 0 : 1
        break
      case 'link':
        formattedText = `[${selectedText || 'link text'}](url)`
        cursorOffset = selectedText ? formattedText.length - 4 : 1
        break
      case 'image':
        formattedText = `![${selectedText || 'alt text'}](url)`
        cursorOffset = selectedText ? formattedText.length - 4 : 1
        break
      case 'heading':
        formattedText = `# ${selectedText}`
        cursorOffset = selectedText ? 0 : 0
        break
      case 'list':
        formattedText = `- ${selectedText}`
        cursorOffset = selectedText ? 0 : 0
        break
      case 'quote':
        formattedText = `> ${selectedText}`
        cursorOffset = selectedText ? 0 : 0
        break
      default:
        return
    }

    const newValue = value.slice(0, selection.start) + formattedText + value.slice(selection.end)
    handleChange(newValue)

    // Set cursor position
    setTimeout(() => {
      if (textareaRef.current) {
        const newPosition = selection.start + formattedText.length - cursorOffset
        textareaRef.current.setSelection(newPosition, newPosition)
      }
    }, 0)
  }, [value, handleChange])

  // Clear all content
  const clear = useCallback(() => {
    handleChange('')
  }, [handleChange])

  // Get text statistics
  const getStats = useCallback(() => {
    const charCount = value.length
    const wordCount = value.trim() ? value.trim().split(/\s+/).length : 0
    const lineCount = value.split('\n').length
    const paragraphCount = value.split(/\n\s*\n/).filter(p => p.trim()).length

    return {
      characters: charCount,
      words: wordCount,
      lines: lineCount,
      paragraphs: paragraphCount
    }
  }, [value])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [])

  return {
    value,
    setValue: handleChange,
    textareaRef,
    isTyping,
    history: {
      canUndo: historyIndex > 0,
      canRedo: historyIndex < history.length - 1,
      undo,
      redo
    },
    operations: {
      insertText,
      formatText,
      clear
    },
    stats: getStats()
  }
}

// Hook for auto-complete functionality
export function useAutoComplete(suggestions: string[] = []) {
  const [isVisible, setIsVisible] = useState(false)
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [trigger, setTrigger] = useState('')
  const [position, setPosition] = useState({ top: 0, left: 0 })

  const handleTextChange = useCallback((text: string, cursorPosition: number) => {
    // Look for trigger characters (@, #, /)
    const triggers = ['@', '#', '/']
    let foundTrigger = ''
    let triggerIndex = -1

    for (let i = cursorPosition - 1; i >= 0; i--) {
      const char = text[i]
      if (triggers.includes(char)) {
        foundTrigger = char
        triggerIndex = i
        break
      }
      if (char === ' ' || char === '\n') {
        break
      }
    }

    if (foundTrigger && triggerIndex !== -1) {
      const query = text.slice(triggerIndex + 1, cursorPosition).toLowerCase()
      const filtered = suggestions.filter(s => 
        s.toLowerCase().includes(query)
      ).slice(0, 10)

      if (filtered.length > 0) {
        setTrigger(foundTrigger)
        setFilteredSuggestions(filtered)
        setSelectedIndex(0)
        setIsVisible(true)
        // Position calculation would need textarea ref for accurate positioning
        setPosition({ top: 100, left: 100 })
      } else {
        setIsVisible(false)
      }
    } else {
      setIsVisible(false)
    }
  }, [suggestions])

  const selectSuggestion = useCallback((suggestion: string) => {
    setIsVisible(false)
    return `${trigger}${suggestion}`
  }, [trigger])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!isVisible) return false

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : 0
        )
        return true
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : filteredSuggestions.length - 1
        )
        return true
      case 'Enter':
      case 'Tab':
        e.preventDefault()
        if (filteredSuggestions[selectedIndex]) {
          selectSuggestion(filteredSuggestions[selectedIndex])
        }
        return true
      case 'Escape':
        setIsVisible(false)
        return true
      default:
        return false
    }
  }, [isVisible, filteredSuggestions, selectedIndex, selectSuggestion])

  return {
    isVisible,
    suggestions: filteredSuggestions,
    selectedIndex,
    trigger,
    position,
    handleTextChange,
    selectSuggestion,
    handleKeyDown,
    hide: () => setIsVisible(false)
  }
}

// Hook for file handling
export function useFileHandler() {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const handleFileDrop = useCallback(async (files: FileList) => {
    setIsUploading(true)
    const fileArray = Array.from(files)
    
    // Filter and validate files
    const validFiles = fileArray.filter(file => {
      // Add your file validation logic here
      const maxSize = 10 * 1024 * 1024 // 10MB
      const allowedTypes = ['image/', 'text/', 'application/json']
      
      return file.size <= maxSize && 
             allowedTypes.some(type => file.type.startsWith(type))
    })

    setUploadedFiles(prev => [...prev, ...validFiles])
    setIsUploading(false)

    // Return file contents for text files
    const textFiles = validFiles.filter(file => 
      file.type.startsWith('text/') || file.type === 'application/json'
    )
    
    const fileContents = await Promise.all(
      textFiles.map(async file => {
        const text = await file.text()
        return { name: file.name, content: text }
      })
    )

    return fileContents
  }, [])

  const removeFile = useCallback((index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index))
  }, [])

  const clearFiles = useCallback(() => {
    setUploadedFiles([])
  }, [])

  return {
    uploadedFiles,
    isUploading,
    handleFileDrop,
    removeFile,
    clearFiles
  }
}

// Hook for collaboration features
export function useCollaboration() {
  const [collaborators, setCollaborators] = useState<Array<{
    id: string
    name: string
    color: string
    cursor: number
    isTyping: boolean
  }>>([])

  const addCollaborator = useCallback((collaborator: {
    id: string
    name: string
    color: string
  }) => {
    setCollaborators(prev => [
      ...prev.filter(c => c.id !== collaborator.id),
      { ...collaborator, cursor: 0, isTyping: false }
    ])
  }, [])

  const removeCollaborator = useCallback((id: string) => {
    setCollaborators(prev => prev.filter(c => c.id !== id))
  }, [])

  const updateCollaboratorCursor = useCallback((id: string, cursor: number) => {
    setCollaborators(prev => prev.map(c => 
      c.id === id ? { ...c, cursor } : c
    ))
  }, [])

  const updateCollaboratorTyping = useCallback((id: string, isTyping: boolean) => {
    setCollaborators(prev => prev.map(c => 
      c.id === id ? { ...c, isTyping } : c
    ))
  }, [])

  return {
    collaborators,
    addCollaborator,
    removeCollaborator,
    updateCollaboratorCursor,
    updateCollaboratorTyping
  }
}

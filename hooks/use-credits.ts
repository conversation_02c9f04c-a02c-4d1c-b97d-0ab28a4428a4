'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { CreditBalance, CreditTransaction } from '@/lib/types/marketing-platform'

interface UseCreditReturn {
  balance: CreditBalance | null
  loading: boolean
  error: string | null
  refreshBalance: () => Promise<void>
  getHistory: (page?: number, limit?: number) => Promise<{
    transactions: CreditTransaction[]
    pagination: any
  } | null>
}

export function useCredits(): UseCreditReturn {
  const { data: session } = useSession()
  const [balance, setBalance] = useState<CreditBalance | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refreshBalance = useCallback(async () => {
    if (!session?.user?.id) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/credits/balance')
      
      if (!response.ok) {
        throw new Error(`Failed to fetch credit balance: ${response.statusText}`)
      }

      const data = await response.json()
      setBalance(data)
    } catch (err) {
      console.error('Error fetching credit balance:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch credit balance')
    } finally {
      setLoading(false)
    }
  }, [session?.user?.id])

  const getHistory = useCallback(async (page = 1, limit = 20) => {
    if (!session?.user?.id) {
      return null
    }

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      })

      const response = await fetch(`/api/credits/history?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch credit history: ${response.statusText}`)
      }

      return await response.json()
    } catch (err) {
      console.error('Error fetching credit history:', err)
      return null
    }
  }, [session?.user?.id])

  // Fetch balance on mount and when session changes
  useEffect(() => {
    refreshBalance()
  }, [refreshBalance])

  // Listen for credit updates from API responses
  useEffect(() => {
    const handleCreditUpdate = (event: CustomEvent) => {
      const { creditsUsed, creditsRemaining } = event.detail
      
      if (balance && typeof creditsRemaining === 'number') {
        setBalance(prev => prev ? {
          ...prev,
          totalCredits: creditsRemaining
        } : null)
      }
    }

    window.addEventListener('creditUpdate' as any, handleCreditUpdate)
    
    return () => {
      window.removeEventListener('creditUpdate' as any, handleCreditUpdate)
    }
  }, [balance])

  return {
    balance,
    loading,
    error,
    refreshBalance,
    getHistory
  }
}

// Helper hook for checking if user has enough credits
export function useCreditCheck() {
  const { balance } = useCredits()

  const hasCredits = useCallback((required: number) => {
    if (!balance) return false
    return balance.totalCredits >= required
  }, [balance])

  const hasDailyCredits = useCallback((required: number) => {
    if (!balance) return false
    return balance.dailyCredits >= required
  }, [balance])

  const hasMonthlyCredits = useCallback((required: number) => {
    if (!balance) return false
    return balance.monthlyCredits >= required
  }, [balance])

  const canAfford = useCallback((required: number) => {
    return hasCredits(required) && hasDailyCredits(required) && hasMonthlyCredits(required)
  }, [hasCredits, hasDailyCredits, hasMonthlyCredits])

  return {
    hasCredits,
    hasDailyCredits,
    hasMonthlyCredits,
    canAfford,
    balance
  }
}

// Helper function to dispatch credit update events
export function dispatchCreditUpdate(creditsUsed: number, creditsRemaining: number) {
  const event = new CustomEvent('creditUpdate', {
    detail: { creditsUsed, creditsRemaining }
  })
  window.dispatchEvent(event)
}

// Helper function to extract credit info from response headers
export function extractCreditInfo(response: Response) {
  const creditsUsed = response.headers.get('X-Credits-Used')
  const creditsRemaining = response.headers.get('X-Credits-Remaining')
  const dailyRemaining = response.headers.get('X-Daily-Credits-Remaining')
  const monthlyRemaining = response.headers.get('X-Monthly-Credits-Remaining')

  if (creditsUsed && creditsRemaining) {
    dispatchCreditUpdate(parseInt(creditsUsed), parseInt(creditsRemaining))
  }

  return {
    creditsUsed: creditsUsed ? parseInt(creditsUsed) : null,
    creditsRemaining: creditsRemaining ? parseInt(creditsRemaining) : null,
    dailyRemaining: dailyRemaining ? parseInt(dailyRemaining) : null,
    monthlyRemaining: monthlyRemaining ? parseInt(monthlyRemaining) : null
  }
}

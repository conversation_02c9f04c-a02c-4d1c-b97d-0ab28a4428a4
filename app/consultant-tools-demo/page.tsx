"use client"

import { AgentInterface } from "@/components/v0-interface"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { Briefcase, MessageSquare, FileText, Building, Lightbulb } from 'lucide-react'

export default function ConsultantToolsDemo() {
  const samplePrompts = [
    {
      category: "Service Information",
      icon: MessageSquare,
      color: "text-blue-500",
      prompts: [
        "What web development services do you offer?",
        "Tell me about your mobile app development packages",
        "What digital marketing services are available?",
        "Show me your UI/UX design offerings",
        "What branding services do you provide?"
      ]
    },
    {
      category: "Company Information", 
      icon: Building,
      color: "text-green-500",
      prompts: [
        "Tell me about your company and team",
        "What are your core values?",
        "Explain your development process",
        "How can I contact you?",
        "What's your company background?"
      ]
    },
    {
      category: "Project Proposals",
      icon: FileText,
      color: "text-purple-500",
      prompts: [
        "Generate a proposal for an e-commerce website with a $10k budget",
        "Create a proposal for a mobile app with standard timeline",
        "I need a proposal for UI/UX design services",
        "Generate a digital marketing proposal for a small business",
        "Create a branding proposal for a startup"
      ]
    }
  ]

  return (
    <main className="min-h-screen bg-gradient-to-b from-background to-background/90">
      <div className="p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center space-y-4"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <Briefcase className="h-8 w-8 text-primary" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
                Consultant Tools Demo
              </h1>
            </div>
            <p className="text-muted-foreground max-w-3xl mx-auto">
              Experience the Consultant AI assistant with integrated tool previews. Try the sample prompts below 
              to see how service information, company details, and project proposals are displayed in the preview panel.
            </p>
            <div className="flex items-center justify-center gap-2">
              <Badge variant="secondary" className="bg-primary/10 text-primary">
                <Lightbulb className="h-3 w-3 mr-1" />
                Tools Preview Enabled
              </Badge>
              <Badge variant="outline">
                Consultant Assistant Active
              </Badge>
            </div>
          </motion.div>

          {/* Sample Prompts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8"
          >
            {samplePrompts.map((category, categoryIndex) => (
              <motion.div
                key={category.category}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + categoryIndex * 0.1 }}
              >
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <category.icon className={`h-5 w-5 ${category.color}`} />
                      {category.category}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-sm text-muted-foreground mb-4">
                      Try these prompts to see {category.category.toLowerCase()} tools in action:
                    </p>
                    {category.prompts.map((prompt, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 + categoryIndex * 0.1 + index * 0.05 }}
                      >
                        <Button
                          variant="ghost"
                          className="w-full text-left justify-start h-auto p-3 text-sm hover:bg-muted/50"
                          onClick={() => {
                            // Copy to clipboard for easy pasting
                            navigator.clipboard.writeText(prompt)
                            // You could also auto-fill the input here if you had access to it
                          }}
                        >
                          <div className="flex items-start gap-2">
                            <div className={`w-2 h-2 ${category.color.replace('text-', 'bg-')} rounded-full mt-2 flex-shrink-0`} />
                            <span className="text-left">{prompt}</span>
                          </div>
                        </Button>
                      </motion.div>
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Instructions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mb-8"
          >
            <Card className="bg-gradient-to-r from-primary/5 via-blue-500/5 to-purple-500/5 border-primary/20">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Lightbulb className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-2">How to Use</h3>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p>1. <strong>Select Consultant</strong> from the assistant type dropdown (should be selected by default)</p>
                      <p>2. <strong>Click any sample prompt</strong> above to copy it to your clipboard</p>
                      <p>3. <strong>Paste and send</strong> the prompt in the chat interface below</p>
                      <p>4. <strong>Watch the preview panel</strong> automatically open and display the tool results</p>
                      <p>5. <strong>Explore different tools</strong> by trying prompts from each category</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* V0 Interface */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="border rounded-lg overflow-hidden shadow-lg"
          >
            <V0Interface />
          </motion.div>

          {/* Tool Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-blue-500" />
                  Service Info Tool
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Displays comprehensive service information including features, technologies, and pricing packages.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>Service descriptions</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>Technology stacks</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>Pricing packages</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Building className="h-5 w-5 text-green-500" />
                  Company Info Tool
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Shows company background, team information, values, and development process.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span>Company background</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span>Team expertise</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span>Development process</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5 text-purple-500" />
                  Proposal Generator
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Creates customized project proposals with deliverables, timelines, and next steps.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-purple-500 rounded-full" />
                    <span>Custom deliverables</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-purple-500 rounded-full" />
                    <span>Timeline estimates</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-2 h-2 bg-purple-500 rounded-full" />
                    <span>Clear next steps</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </main>
  )
}

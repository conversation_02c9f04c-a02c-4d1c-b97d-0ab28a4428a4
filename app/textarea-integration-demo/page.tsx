"use client"

import { useState } from 'react'
import { Navbar } from "@/components/navbar"
import { ModernTextArea } from "@/components/ui/modern-textarea"
import { useModernTextArea } from "@/hooks/use-modern-textarea"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"

export default function TextAreaIntegrationDemo() {
  const [assistantType, setAssistantType] = useState('consultant')
  const [isStreaming, setIsStreaming] = useState(false)
  const [messages, setMessages] = useState<Array<{ role: string; content: string; assistantType: string }>>([])
  
  const textArea = useModernTextArea('')

  const handleSend = () => {
    if (textArea.value.trim()) {
      // Add user message
      setMessages(prev => [...prev, { 
        role: 'user', 
        content: textArea.value, 
        assistantType 
      }])
      
      // Simulate AI response
      setIsStreaming(true)
      setTimeout(() => {
        setMessages(prev => [...prev, { 
          role: 'assistant', 
          content: `As a ${assistantType}, I understand you want to: "${textArea.value}". Let me help you with that!`, 
          assistantType 
        }])
        setIsStreaming(false)
      }, 2000)
      
      // Clear input
      textArea.operations.clear()
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-b from-background to-background/90">
      <Navbar />
      <div className="pt-16 p-8">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center space-y-4"
          >
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
              ModernTextArea Integration Demo
            </h1>
            <p className="text-muted-foreground">
              Experience the integrated send button and AI assistant selector in action
            </p>
          </motion.div>

          {/* Chat Messages */}
          <Card>
            <CardHeader>
              <CardTitle>Chat History</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 max-h-96 overflow-y-auto">
              {messages.length === 0 ? (
                <p className="text-muted-foreground text-center py-8">
                  Start a conversation by typing a message below
                </p>
              ) : (
                messages.map((message, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-[80%] rounded-lg p-3 ${
                      message.role === 'user' 
                        ? 'bg-primary text-primary-foreground' 
                        : 'bg-muted'
                    }`}>
                      {message.role === 'assistant' && (
                        <Badge variant="secondary" className="mb-2 text-xs">
                          {message.assistantType} Assistant
                        </Badge>
                      )}
                      <p className="text-sm">{message.content}</p>
                    </div>
                  </motion.div>
                ))
              )}
              {isStreaming && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-start"
                >
                  <div className="bg-muted rounded-lg p-3">
                    <Badge variant="secondary" className="mb-2 text-xs">
                      {assistantType} Assistant
                    </Badge>
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        <motion.div
                          className="w-2 h-2 bg-primary rounded-full"
                          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 0.8, repeat: Infinity, ease: "easeInOut" }}
                        />
                        <motion.div
                          className="w-2 h-2 bg-blue-500 rounded-full"
                          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 0.8, repeat: Infinity, ease: "easeInOut", delay: 0.2 }}
                        />
                        <motion.div
                          className="w-2 h-2 bg-purple-500 rounded-full"
                          animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 0.8, repeat: Infinity, ease: "easeInOut", delay: 0.4 }}
                        />
                      </div>
                      <span className="text-xs text-muted-foreground">AI is thinking...</span>
                    </div>
                  </div>
                </motion.div>
              )}
            </CardContent>
          </Card>

          {/* Enhanced TextArea with Integration */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Enhanced Input with Send Button & Assistant Selector</CardTitle>
              </CardHeader>
              <CardContent>
                <ModernTextArea
                  ref={textArea.textareaRef}
                  value={textArea.value}
                  onChange={textArea.setValue}
                  placeholder="Type your message here... Select an assistant type and click send!"
                  variant="message"
                  size="lg"
                  autoResize
                  minRows={3}
                  maxRows={8}
                  showCharCount
                  showWordCount
                  enableMarkdown
                  enableAutoComplete
                  showSendButton
                  onSend={handleSend}
                  sendButtonDisabled={!textArea.value.trim() || isStreaming}
                  isStreaming={isStreaming}
                  showAssistantSelector
                  assistantType={assistantType}
                  onAssistantTypeChange={setAssistantType}
                />
              </CardContent>
            </Card>
          </motion.div>

          {/* Feature Highlights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="font-semibold mb-2">Integrated Send Button</h3>
                <p className="text-sm text-muted-foreground">
                  Send button positioned perfectly within the textarea with gradient animations and loading states
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🤖</span>
                </div>
                <h3 className="font-semibold mb-2">Assistant Selector</h3>
                <p className="text-sm text-muted-foreground">
                  Choose between Consultant, Engineer, or Executive assistants with descriptive tooltips
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-purple-500/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">✨</span>
                </div>
                <h3 className="font-semibold mb-2">Seamless Integration</h3>
                <p className="text-sm text-muted-foreground">
                  All features work together without interfering with auto-resize or existing functionality
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Current State Display */}
          <Card>
            <CardHeader>
              <CardTitle>Current State</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Assistant Type:</span>
                  <p className="font-medium capitalize">{assistantType}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Characters:</span>
                  <p className="font-medium">{textArea.stats.characters}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Words:</span>
                  <p className="font-medium">{textArea.stats.words}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Status:</span>
                  <p className="font-medium">{isStreaming ? 'Streaming' : 'Ready'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  )
}

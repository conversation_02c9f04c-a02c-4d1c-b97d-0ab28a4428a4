"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Interactive3DLogo,
  Logo3DHero,
  LogoParticles,
  Logo3DLoader,
  LogoEnvironmentShowcase
} from "@/components/3d-logo"
import { ArrowLeft, Download, Settings, Play, Pause } from "lucide-react"
import Link from "next/link"

export default function Logo3DDemo() {
  const [showLoader, setShowLoader] = useState(false)
  const [loaderProgress, setLoaderProgress] = useState(0)

  const startLoader = () => {
    setShowLoader(true)
    setLoaderProgress(0)
    
    const interval = setInterval(() => {
      setLoaderProgress(prev => {
        if (prev >= 1) {
          clearInterval(interval)
          setTimeout(() => setShowLoader(false), 2000)
          return 1
        }
        return prev + 0.02
      })
    }, 100)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button asChild variant="ghost" size="sm">
                <Link href="/">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold">3D Logo Showcase</h1>
                <p className="text-sm text-muted-foreground">
                  Creative implementations using Three.js and React Three Fiber
                </p>
              </div>
            </div>
            <Badge variant="secondary">Interactive Demo</Badge>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="interactive" className="space-y-8">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="interactive">Interactive</TabsTrigger>
            <TabsTrigger value="hero">Hero Section</TabsTrigger>
            <TabsTrigger value="particles">Particles</TabsTrigger>
            <TabsTrigger value="environment">Environment</TabsTrigger>
            <TabsTrigger value="loader">Loader</TabsTrigger>
          </TabsList>

          {/* Interactive Logo Tab */}
          <TabsContent value="interactive" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Interactive 3D Logo</CardTitle>
                  <CardDescription>
                    Hover, click, and drag to interact with the 3D logo model. 
                    Features orbit controls, auto-rotation, and dynamic lighting.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <Interactive3DLogo
                        height="400px"
                        showControls={true}
                        autoRotate={true}
                        interactive={true}
                        environment="studio"
                        logoScale={1.2}
                        className="border rounded-lg"
                      />
                      <div className="text-xs text-muted-foreground space-y-1">
                        <p>• Mouse: Orbit around the logo</p>
                        <p>• Scroll: Zoom in/out</p>
                        <p>• Hover: Scale and lighting effects</p>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <Interactive3DLogo
                        height="400px"
                        showControls={false}
                        autoRotate={false}
                        interactive={true}
                        environment="night"
                        logoScale={1}
                        floatIntensity={2}
                        className="border rounded-lg"
                      />
                      <div className="text-xs text-muted-foreground space-y-1">
                        <p>• Presentation mode with constraints</p>
                        <p>• Enhanced floating animation</p>
                        <p>• Night environment preset</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Hero Section Tab */}
          <TabsContent value="hero" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>3D Hero Section</CardTitle>
                  <CardDescription>
                    Full-screen hero section with animated 3D logo, stars, and interactive elements.
                    Perfect for landing pages and main sections.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg overflow-hidden">
                    <Logo3DHero
                      showChatButton={true}
                      onStartChat={() => alert("Chat started!")}
                      className="h-[600px]"
                    />
                  </div>
                  <div className="mt-4 text-xs text-muted-foreground space-y-1">
                    <p>• Animated 3D logo with sparkles and dynamic lighting</p>
                    <p>• Starfield background with environment mapping</p>
                    <p>• Responsive design with call-to-action buttons</p>
                    <p>• Smooth animations and transitions</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Particles Tab */}
          <TabsContent value="particles" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Particle Logo System</CardTitle>
                  <CardDescription>
                    Logo represented as animated particles that morph between different formations.
                    Multiple color schemes and animation modes available.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Primary Colors</h4>
                      <LogoParticles
                        height="300px"
                        particleCount={1500}
                        colorScheme="primary"
                        morphing={true}
                        animationSpeed={1}
                        className="border rounded-lg"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Rainbow Spectrum</h4>
                      <LogoParticles
                        height="300px"
                        particleCount={2000}
                        colorScheme="rainbow"
                        morphing={true}
                        animationSpeed={1.5}
                        className="border rounded-lg"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">Monochrome</h4>
                      <LogoParticles
                        height="300px"
                        particleCount={1000}
                        colorScheme="monochrome"
                        morphing={false}
                        animationSpeed={0.8}
                        className="border rounded-lg"
                      />
                    </div>
                  </div>
                  
                  <div className="mt-4 text-xs text-muted-foreground space-y-1">
                    <p>• Configurable particle count and animation speed</p>
                    <p>• Multiple color schemes: primary, rainbow, monochrome</p>
                    <p>• Morphing between sphere and logo formations</p>
                    <p>• Additive blending for glowing effects</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Environment Tab */}
          <TabsContent value="environment" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Environment Showcase</CardTitle>
                  <CardDescription>
                    3D logo displayed in various environmental presets with realistic lighting,
                    shadows, and atmospheric effects. Switch between different environments.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <LogoEnvironmentShowcase
                    height="500px"
                    defaultEnvironment="studio"
                    showControls={true}
                    autoRotate={true}
                    className="border rounded-lg"
                  />
                  
                  <div className="mt-4 text-xs text-muted-foreground space-y-1">
                    <p>• Multiple environment presets: studio, sunset, night, dawn, warehouse, city</p>
                    <p>• Realistic contact shadows and platform</p>
                    <p>• Environment-specific lighting and effects</p>
                    <p>• Interactive controls for camera movement</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          {/* Loader Tab */}
          <TabsContent value="loader" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>3D Loading Animation</CardTitle>
                  <CardDescription>
                    Animated loading screen with 3D logo assembly, progress tracking,
                    and smooth transitions between loading states.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex gap-4">
                      <Button onClick={startLoader} className="gap-2">
                        <Play className="h-4 w-4" />
                        Start Loading Demo
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => setShowLoader(false)}
                        className="gap-2"
                      >
                        <Pause className="h-4 w-4" />
                        Stop Demo
                      </Button>
                    </div>
                    
                    <div className="p-4 border rounded-lg bg-muted/20">
                      <p className="text-sm text-muted-foreground mb-2">
                        Click "Start Loading Demo" to see the 3D loading animation in action.
                        The loader features three stages:
                      </p>
                      <ul className="text-xs text-muted-foreground space-y-1 ml-4">
                        <li>• <strong>Loading:</strong> Spinning logo with pulsing effects</li>
                        <li>• <strong>Assembling:</strong> Logo pieces flying in from different directions</li>
                        <li>• <strong>Complete:</strong> Final reveal with gentle rotation</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>

      {/* 3D Loader Overlay */}
      <Logo3DLoader
        isLoading={showLoader}
        progress={loaderProgress}
        onComplete={() => setShowLoader(false)}
      />
    </div>
  )
}

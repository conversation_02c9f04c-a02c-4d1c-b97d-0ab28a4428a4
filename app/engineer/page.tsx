import { <PERSON>ada<PERSON> } from 'next';
import { EngineerWorkspace } from '@/components/engineer-workspace';

export const metadata: Metadata = {
  title: 'Engineer AI - Code Generation & Development Assistant',
  description: 'Generate, modify, explain, and debug code with AI assistance. Get working prototypes that run in your browser with live preview.',
  keywords: ['AI', 'code generation', 'development', 'programming', 'sandpack', 'live preview'],
  openGraph: {
    title: 'Engineer AI - Code Generation & Development Assistant',
    description: 'Generate, modify, explain, and debug code with AI assistance. Get working prototypes that run in your browser with live preview.',
    type: 'website',
  },
};

export default function EngineerPage() {
  return (
    <div className="h-screen w-full overflow-hidden bg-background">
      <EngineerWorkspace />
    </div>
  );
}

"use client"

import { useState } from 'react'
import { Navbar } from "@/components/navbar"
import { ModernTextArea } from "@/components/ui/modern-textarea"
import { useModernTextArea } from "@/hooks/use-modern-textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { motion } from "framer-motion"
import { useChat } from '@ai-sdk/react'
import { Briefcase, MessageSquare, FileText, TrendingUp, Users, Star } from 'lucide-react'

export default function ConsultantDemo() {
  const [assistantType] = useState('consultant')
  const textArea = useModernTextArea('')

  const { messages, input, handleInputChange, handleSubmit, status } = useChat({
    maxSteps: 5,
    body: { assistantType }
  })

  const handleSend = () => {
    if (input.trim()) {
      const syntheticEvent = {
        preventDefault: () => {},
        currentTarget: {
          elements: { message: { value: input } }
        }
      } as any
      handleSubmit(syntheticEvent)
    }
  }

  const sampleQuestions = [
    "What web development services do you offer?",
    "Can you provide a quote for a mobile app?",
    "Tell me about your company and team",
    "What's your development process?",
    "Generate a proposal for an e-commerce website",
    "What digital marketing packages do you have?"
  ]

  return (
    <main className="min-h-screen bg-gradient-to-b from-background to-background/90">
      <Navbar />
      <div className="pt-16 p-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center space-y-4"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <Briefcase className="h-8 w-8 text-primary" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
                Consultant AI Assistant
              </h1>
            </div>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Your dedicated business consultant for Soimagine Digital Agency. Get information about our services, 
              company details, and customized project proposals.
            </p>
            <Badge variant="secondary" className="bg-primary/10 text-primary">
              Specialized in Business Consultation & Service Information
            </Badge>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            {[
              {
                icon: MessageSquare,
                title: "Service Information",
                description: "Get detailed information about our web development, mobile apps, UI/UX design, and digital marketing services.",
                color: "text-blue-500"
              },
              {
                icon: FileText,
                title: "Custom Proposals",
                description: "Generate tailored project proposals based on your specific requirements and budget.",
                color: "text-green-500"
              },
              {
                icon: TrendingUp,
                title: "Business Strategy",
                description: "Receive strategic advice for digital transformation and technology decisions.",
                color: "text-purple-500"
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardContent className="p-6 text-center">
                    <feature.icon className={`h-12 w-12 mx-auto mb-4 ${feature.color}`} />
                    <h3 className="font-semibold mb-2">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Chat Interface */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Chat Messages */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Consultant Chat
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 max-h-96 overflow-y-auto">
                  {messages.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Briefcase className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Ask me anything about Soimagine Digital Agency!</p>
                      <p className="text-sm mt-2">Try one of the sample questions below to get started.</p>
                    </div>
                  ) : (
                    messages.map((message, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`max-w-[80%] rounded-lg p-3 ${
                          message.role === 'user' 
                            ? 'bg-primary text-primary-foreground' 
                            : 'bg-muted'
                        }`}>
                          {message.role === 'assistant' && (
                            <Badge variant="secondary" className="mb-2 text-xs">
                              <Briefcase className="h-3 w-3 mr-1" />
                              Consultant
                            </Badge>
                          )}
                          <div className="text-sm whitespace-pre-wrap">
                            {message.content}
                          </div>
                        </div>
                      </motion.div>
                    ))
                  )}
                  {status === 'streaming' && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex justify-start"
                    >
                      <div className="bg-muted rounded-lg p-3">
                        <Badge variant="secondary" className="mb-2 text-xs">
                          <Briefcase className="h-3 w-3 mr-1" />
                          Consultant
                        </Badge>
                        <div className="flex items-center gap-2">
                          <div className="flex gap-1">
                            {[0, 1, 2].map((i) => (
                              <motion.div
                                key={i}
                                className="w-2 h-2 bg-primary rounded-full"
                                animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                                transition={{ duration: 0.8, repeat: Infinity, delay: i * 0.2 }}
                              />
                            ))}
                          </div>
                          <span className="text-xs text-muted-foreground">Consultant is analyzing...</span>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </CardContent>
              </Card>

              {/* Input Area */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mt-6"
              >
                <Card>
                  <CardContent className="p-4">
                    <ModernTextArea
                      ref={textArea.textareaRef}
                      value={input}
                      onChange={handleInputChange}
                      placeholder="Ask about our services, request a proposal, or get company information..."
                      variant="message"
                      size="md"
                      autoResize
                      minRows={2}
                      maxRows={6}
                      showCharCount
                      showSendButton
                      onSend={handleSend}
                      sendButtonDisabled={!input.trim() || status === 'streaming'}
                      isStreaming={status === 'streaming'}
                      showAssistantSelector={false}
                    />
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Sample Questions & Info */}
            <div className="space-y-6">
              {/* Sample Questions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Sample Questions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {sampleQuestions.map((question, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                    >
                      <Button
                        variant="ghost"
                        className="w-full text-left justify-start h-auto p-3 text-sm"
                        onClick={() => handleInputChange({ target: { value: question } } as any)}
                      >
                        {question}
                      </Button>
                    </motion.div>
                  ))}
                </CardContent>
              </Card>

              {/* Consultant Capabilities */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    Consultant Capabilities
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {[
                    "Service information & pricing",
                    "Custom project proposals",
                    "Company background & team",
                    "Development process details",
                    "Technology recommendations",
                    "Business strategy advice"
                  ].map((capability, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <div className="w-2 h-2 bg-primary rounded-full" />
                      {capability}
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Contact Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-500" />
                    Ready to Start?
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Get a free consultation and detailed proposal for your project.
                  </p>
                  <Button className="w-full">
                    Schedule Free Consultation
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 0 73% 41%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --primary: 0 73% 41%;
    --primary-foreground: 210 40% 98%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-primary;
  }
}

/* Font Utility Classes */
@layer utilities {
  .font-circular {
    font-family: var(--font-circular-std), 'Helvetica Neue', 'Arial', sans-serif;
  }

  .font-century-gothic {
    font-family: var(--font-century-gothic), 'Arial', sans-serif;
  }

  .font-primary {
    font-family: var(--font-circular-std), 'Helvetica Neue', 'Arial', sans-serif;
  }

  .font-secondary {
    font-family: var(--font-century-gothic), 'Arial', sans-serif;
  }
}

.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70;
}

.animated-gradient {
  background: linear-gradient(
    -45deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 0.8) 25%,
    hsl(var(--primary) / 0.6) 50%,
    hsl(var(--primary) / 0.8) 75%,
    hsl(var(--primary)) 100%
  );
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.glow {
  box-shadow: 0 0 15px hsl(var(--primary) / 0.5);
}

.glass {
  background: rgba(15, 23, 42, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* V0 Interface Enhancements */
.v0-particle-glow {
  filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.4));
}

.v0-container {
  position: relative;
  overflow: hidden;
  contain: layout style paint;
}

.v0-particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  contain: strict;
}

.v0-message-enter {
  animation: v0MessageSlideIn 0.5s ease-out;
}

@keyframes v0MessageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.v0-tool-call-highlight {
  position: relative;
  overflow: hidden;
}

.v0-tool-call-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.2),
    transparent
  );
  animation: v0Shimmer 2s infinite;
}

@keyframes v0Shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.v0-input-focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
}

.v0-floating-element {
  animation: v0Float 6s ease-in-out infinite;
}

@keyframes v0Float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

.v0-pulse-ring {
  animation: v0PulseRing 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

@keyframes v0PulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

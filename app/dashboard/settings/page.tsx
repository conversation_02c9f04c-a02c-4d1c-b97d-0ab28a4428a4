import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON>boardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { SettingsForm } from "@/components/dashboard/settings-form"

export const metadata: Metadata = {
  title: "Settings - Soimagine Digital Agency",
  description: "Manage your Soimagine account settings",
}

export default function SettingsPage() {
  return (
    <DashboardShell>
      <DashboardHeader heading="Settings" text="Manage your account settings and preferences." />
      <SettingsForm />
    </DashboardShell>
  )
}

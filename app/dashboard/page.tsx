import type { <PERSON>ada<PERSON> } from "next"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { DashboardOverview } from "@/components/dashboard/dashboard-overview"

export const metadata: Metadata = {
  title: "Dashboard - Soimagine Digital Agency",
  description: "Manage your Soimagine projects and services",
}

export default function DashboardPage() {
  return (
    <DashboardShell>
      <DashboardHeader heading="Dashboard" text="Manage your projects and access Soimagine services." />
      <DashboardOverview />
    </DashboardShell>
  )
}

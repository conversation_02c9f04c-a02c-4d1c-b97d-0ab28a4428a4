import type { <PERSON>ada<PERSON> } from "next"
import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { ProjectList } from "@/components/dashboard/project-list"

export const metadata: Metadata = {
  title: "Projects - Soimagine Digital Agency",
  description: "Manage your Soimagine projects",
}

export default function ProjectsPage() {
  return (
    <DashboardShell>
      <DashboardHeader heading="Projects" text="View and manage your ongoing and completed projects." />
      <ProjectList />
    </DashboardShell>
  )
}

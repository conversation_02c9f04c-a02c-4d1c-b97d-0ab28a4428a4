"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Navbar } from "@/components/navbar"
import { Logo3DHero, Interactive3DLogo, LogoParticles } from "@/components/3d-logo"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Sparkles, MessageSquare, ExternalLink } from "lucide-react"
import Link from "next/link"

export default function ThreeDShowcase() {
  const [showChat, setShowChat] = useState(false)

  return (
    <main className="min-h-screen bg-background">
      <Navbar />
      
      {/* Hero Section with 3D Logo */}
      <section className="relative">
        <Logo3DHero
          showChatButton={true}
          onStartChat={() => setShowChat(true)}
        />
      </section>

      {/* Features Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <Badge variant="secondary" className="mb-4">
              3D Technology Showcase
            </Badge>
            <h2 className="text-4xl font-bold mb-4">
              Creative 3D Logo Implementations
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Explore how we've creatively utilized the Soimagine 3D logo model using 
              Three.js and React Three Fiber to create immersive brand experiences.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* Interactive Logo Card */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    Interactive 3D Logo
                  </CardTitle>
                  <CardDescription>
                    Fully interactive 3D logo with orbit controls, hover effects, and dynamic lighting.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Interactive3DLogo
                    height="300px"
                    showControls={true}
                    autoRotate={true}
                    interactive={true}
                    environment="studio"
                    logoScale={1.2}
                    className="mb-4 border rounded-lg"
                  />
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <p>• Mouse controls for 360° viewing</p>
                    <p>• Hover effects with dynamic scaling</p>
                    <p>• Professional studio lighting</p>
                    <p>• Smooth animations and transitions</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Particle System Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5 text-primary" />
                    Particle Logo System
                  </CardTitle>
                  <CardDescription>
                    Logo represented as animated particles with morphing capabilities and color schemes.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <LogoParticles
                    height="300px"
                    particleCount={1500}
                    colorScheme="primary"
                    morphing={true}
                    animationSpeed={1.2}
                    environment="night"
                    className="mb-4 border rounded-lg"
                  />
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <p>• 1,500+ animated particles</p>
                    <p>• Morphing between formations</p>
                    <p>• Multiple color schemes available</p>
                    <p>• Additive blending effects</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Technical Details */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16"
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Three.js Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Leveraging the power of Three.js for high-performance 3D rendering 
                  with WebGL acceleration and optimized geometry processing.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">React Three Fiber</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  React declarative approach to 3D scenes with component-based 
                  architecture and seamless integration with React ecosystem.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Drei Helpers</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Utilizing drei's comprehensive collection of helpers for controls, 
                  environments, effects, and optimized 3D components.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <Card className="max-w-2xl mx-auto">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold mb-4">
                  Explore More 3D Implementations
                </h3>
                <p className="text-muted-foreground mb-6">
                  Visit our comprehensive demo page to see all the creative ways 
                  we've implemented the 3D logo, including loading animations, 
                  environment showcases, and interactive demos.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="gap-2">
                    <Link href="/3d-logo-demo">
                      <ExternalLink className="h-4 w-4" />
                      View Full Demo
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="lg" className="gap-2">
                    <Link href="/">
                      <Sparkles className="h-4 w-4" />
                      Back to Home
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Chat Interface (if activated) */}
      {showChat && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center p-4"
          onClick={() => setShowChat(false)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-background border rounded-lg p-6 max-w-md w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg font-semibold mb-2">AI Chat Interface</h3>
            <p className="text-muted-foreground mb-4">
              This would integrate with your existing AI chat system. 
              The 3D logo can serve as an animated avatar or loading indicator.
            </p>
            <Button onClick={() => setShowChat(false)} className="w-full">
              Close Demo
            </Button>
          </motion.div>
        </motion.div>
      )}
    </main>
  )
}

import type { <PERSON>ada<PERSON> } from "next"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { PortfolioGrid } from "@/components/portfolio-grid"
import { PortfolioHero } from "@/components/portfolio-hero"
import { AssistantButton } from "@/components/assistant-button"

export const metadata: Metadata = {
  title: "Portfolio - Soimagine Digital Agency",
  description: "Explore our portfolio of successful projects",
}

export default function PortfolioPage() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-background to-background/90 overflow-hidden">
      <Navbar />
      <PortfolioHero />
      <PortfolioGrid />
      <Footer />
      <AssistantButton />
    </main>
  )
}

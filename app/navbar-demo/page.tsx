"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, <PERSON>rk<PERSON>, <PERSON>Pointer, Eye, Zap } from "lucide-react"
import Link from "next/link"
import { Navbar } from "@/components/navbar"

export default function NavbarDemo() {
  const features = [
    {
      icon: <Sparkles className="h-5 w-5" />,
      title: "3D Logo Integration",
      description: "Interactive 3D logo that rotates and responds to hover effects"
    },
    {
      icon: <MousePointer className="h-5 w-5" />,
      title: "Hover Interactions",
      description: "Smooth scaling and color transitions on hover for enhanced UX"
    },
    {
      icon: <Eye className="h-5 w-5" />,
      title: "Fallback Support",
      description: "Graceful fallback to 2D logo if 3D fails to load"
    },
    {
      icon: <Zap className="h-5 w-5" />,
      title: "Performance Optimized",
      description: "Lightweight 3D rendering optimized for navbar usage"
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Demo Navbar */}
      <Navbar />
      
      <div className="pt-20"> {/* Add padding to account for fixed navbar */}
        {/* Header */}
        <header className="border-b border-border/40 bg-background/95 backdrop-blur">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button asChild variant="ghost" size="sm">
                  <Link href="/">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Home
                  </Link>
                </Button>
                <div>
                  <h1 className="text-2xl font-bold font-secondary">3D Navbar Demo</h1>
                  <p className="text-sm text-muted-foreground font-primary">
                    Interactive 3D logo integration in navigation
                  </p>
                </div>
              </div>
              <Badge variant="secondary" className="font-primary">
                <Sparkles className="h-3 w-3 mr-1" />
                Interactive
              </Badge>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-8">
          {/* Introduction */}
          <section className="mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center mb-8"
            >
              <h2 className="text-3xl font-bold mb-4 font-secondary">3D Logo in Navigation</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-primary">
                Experience the enhanced navbar with an interactive 3D logo that brings the brand to life.
                Hover over the logo in the navigation above to see it in action!
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gradient-to-r from-primary/10 via-purple-500/10 to-blue-500/10 rounded-lg p-8 text-center"
            >
              <h3 className="text-xl font-semibold mb-2 font-secondary">👆 Try It Now</h3>
              <p className="text-muted-foreground font-primary">
                Hover over the "Soimagine" logo in the navbar above to see the 3D interaction effects
              </p>
            </motion.div>
          </section>

          {/* Features */}
          <section className="mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="text-center mb-8"
            >
              <h2 className="text-3xl font-bold mb-4 font-secondary">Features</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-primary">
                The 3D navbar logo includes several enhancements for better user experience
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 font-secondary">
                        <div className="text-primary">{feature.icon}</div>
                        {feature.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="font-primary">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </section>

          {/* Technical Details */}
          <section className="mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="font-secondary">Technical Implementation</CardTitle>
                  <CardDescription className="font-primary">
                    How the 3D logo is integrated into the navbar component
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <h4 className="font-medium font-secondary">Component Structure</h4>
                      <div className="text-sm space-y-2 font-primary">
                        <p>• <code className="bg-muted px-2 py-1 rounded">NavbarLogo</code> - Optimized 3D logo component</p>
                        <p>• <code className="bg-muted px-2 py-1 rounded">Suspense</code> - Loading fallback handling</p>
                        <p>• <code className="bg-muted px-2 py-1 rounded">Canvas</code> - Three.js rendering context</p>
                        <p>• <code className="bg-muted px-2 py-1 rounded">Float</code> - Subtle animation effects</p>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <h4 className="font-medium font-secondary">Performance Optimizations</h4>
                      <div className="text-sm space-y-2 font-primary">
                        <p>• Low-power GPU preference for mobile</p>
                        <p>• Disabled antialiasing for better performance</p>
                        <p>• Minimal lighting setup</p>
                        <p>• Preloaded GLTF model</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-muted/20 rounded-lg">
                    <h4 className="font-medium mb-2 font-secondary">Usage Example</h4>
                    <pre className="text-sm overflow-x-auto font-mono">
{`<NavbarLogo
  width="40px"
  height="40px"
  className="rounded-lg overflow-hidden shadow-sm"
/>`}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </section>

          {/* Call to Action */}
          <section>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              className="text-center"
            >
              <Card className="max-w-2xl mx-auto">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold mb-4 font-secondary">
                    Explore More 3D Implementations
                  </h3>
                  <p className="text-muted-foreground mb-6 font-primary">
                    See how the 3D logo is used across different components and contexts
                    throughout the application.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button asChild size="lg" className="gap-2">
                      <Link href="/3d-showcase">
                        <Sparkles className="h-4 w-4" />
                        3D Showcase
                      </Link>
                    </Button>
                    <Button asChild variant="outline" size="lg" className="gap-2">
                      <Link href="/3d-logo-demo">
                        <Eye className="h-4 w-4" />
                        Full Demo
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </section>
        </div>
      </div>
    </div>
  )
}

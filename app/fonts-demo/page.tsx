"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Type, Download, Eye } from "lucide-react"
import Link from "next/link"
import { centuryGothic, circularStd } from "@/lib/fonts"

export default function FontsDemo() {
  const fontSamples = [
    {
      name: "Circular Std",
      description: "Modern, clean, and highly readable. Perfect for body text and UI elements.",
      className: "font-circular",
      fontObject: circularStd,
      weights: [
        { weight: "300", name: "Light", text: "The quick brown fox jumps over the lazy dog" },
        { weight: "400", name: "Regular", text: "The quick brown fox jumps over the lazy dog" },
        { weight: "700", name: "Bold", text: "The quick brown fox jumps over the lazy dog" },
      ]
    },
    {
      name: "Century Gothic",
      description: "Geometric sans-serif with a distinctive character. Great for headings and branding.",
      className: "font-century-gothic",
      fontObject: centuryGothic,
      weights: [
        { weight: "400", name: "Regular", text: "The quick brown fox jumps over the lazy dog" },
        { weight: "400", name: "Italic", text: "The quick brown fox jumps over the lazy dog", italic: true },
        { weight: "700", name: "Bold", text: "The quick brown fox jumps over the lazy dog" },
        { weight: "700", name: "Bold Italic", text: "The quick brown fox jumps over the lazy dog", italic: true },
      ]
    }
  ]

  const usageExamples = [
    {
      title: "Headings with Century Gothic",
      code: `<h1 className="font-century-gothic text-4xl font-bold">
  Main Heading
</h1>`,
      preview: (
        <h1 className="font-century-gothic text-4xl font-bold text-foreground">
          Main Heading
        </h1>
      )
    },
    {
      title: "Body Text with Circular Std",
      code: `<p className="font-circular text-base">
  This is body text using Circular Std font.
</p>`,
      preview: (
        <p className="font-circular text-base text-muted-foreground">
          This is body text using Circular Std font. It's clean, modern, and highly readable for longer content.
        </p>
      )
    },
    {
      title: "Mixed Typography",
      code: `<div>
  <h2 className="font-secondary text-2xl font-bold mb-2">
    Section Title
  </h2>
  <p className="font-primary text-base">
    Supporting content text.
  </p>
</div>`,
      preview: (
        <div>
          <h2 className="font-secondary text-2xl font-bold mb-2 text-foreground">
            Section Title
          </h2>
          <p className="font-primary text-base text-muted-foreground">
            Supporting content text using the primary font family.
          </p>
        </div>
      )
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button asChild variant="ghost" size="sm">
                <Link href="/">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Home
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold font-secondary">Custom Fonts Demo</h1>
                <p className="text-sm text-muted-foreground font-primary">
                  Circular Std & Century Gothic implementation with next/font
                </p>
              </div>
            </div>
            <Badge variant="secondary" className="font-primary">
              <Type className="h-3 w-3 mr-1" />
              Typography
            </Badge>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Font Families Section */}
        <section className="mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8"
          >
            <h2 className="text-3xl font-bold mb-4 font-secondary">Font Families</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-primary">
              Explore our custom font implementations using Next.js font optimization
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {fontSamples.map((font, index) => (
              <motion.div
                key={font.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className={`${font.className} text-2xl`}>
                      {font.name}
                    </CardTitle>
                    <CardDescription className="font-primary">
                      {font.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {font.weights.map((weight, weightIndex) => (
                      <div key={weightIndex} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-muted-foreground font-primary">
                            {weight.name} ({weight.weight})
                          </span>
                        </div>
                        <p 
                          className={`${font.className} text-lg`}
                          style={{ 
                            fontWeight: weight.weight,
                            fontStyle: weight.italic ? 'italic' : 'normal'
                          }}
                        >
                          {weight.text}
                        </p>
                        <Separator />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </section>

        {/* Usage Examples Section */}
        <section className="mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-center mb-8"
          >
            <h2 className="text-3xl font-bold mb-4 font-secondary">Usage Examples</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-primary">
              See how to use these fonts in your components with Tailwind CSS classes
            </p>
          </motion.div>

          <div className="space-y-6">
            {usageExamples.map((example, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="font-secondary text-xl">{example.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Preview */}
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-muted-foreground font-primary flex items-center gap-2">
                          <Eye className="h-4 w-4" />
                          Preview
                        </h4>
                        <div className="p-4 border rounded-lg bg-muted/20">
                          {example.preview}
                        </div>
                      </div>
                      
                      {/* Code */}
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-muted-foreground font-primary">
                          Code
                        </h4>
                        <pre className="p-4 bg-muted rounded-lg text-sm overflow-x-auto font-mono">
                          <code>{example.code}</code>
                        </pre>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </section>

        {/* Tailwind Classes Reference */}
        <section>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="font-secondary">Available Tailwind Classes</CardTitle>
                <CardDescription className="font-primary">
                  Use these classes in your components to apply the custom fonts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-medium font-secondary">Font Family Classes</h4>
                    <div className="space-y-2 text-sm font-mono">
                      <div><code className="bg-muted px-2 py-1 rounded">font-circular</code> - Circular Std</div>
                      <div><code className="bg-muted px-2 py-1 rounded">font-century-gothic</code> - Century Gothic</div>
                      <div><code className="bg-muted px-2 py-1 rounded">font-primary</code> - Primary font (Circular Std)</div>
                      <div><code className="bg-muted px-2 py-1 rounded">font-secondary</code> - Secondary font (Century Gothic)</div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-medium font-secondary">CSS Variables</h4>
                    <div className="space-y-2 text-sm font-mono">
                      <div><code className="bg-muted px-2 py-1 rounded">var(--font-circular-std)</code></div>
                      <div><code className="bg-muted px-2 py-1 rounded">var(--font-century-gothic)</code></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </section>
      </div>
    </div>
  )
}

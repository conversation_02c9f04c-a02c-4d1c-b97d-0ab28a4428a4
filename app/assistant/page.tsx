import { EnhancedAIAssistant } from "@/components/enhanced-ai-assistant"
import { RealTimeCollaboration } from "@/components/real-time-collaboration"
import { Navbar } from "@/components/navbar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AssistantPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/90">
      <Navbar />
      <div className="container mx-auto px-4 py-8 pt-24">
        <h1 className="text-4xl font-bold mb-8 text-center">
          Soimagine <span className="gradient-text">AI Assistant</span>
        </h1>

        <Tabs defaultValue="assistant" className="w-full h-[800px]">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="assistant">AI Assistant</TabsTrigger>
            <TabsTrigger value="collaboration">Real-Time Collaboration</TabsTrigger>
          </TabsList>

          <TabsContent value="assistant" className="h-full">
            <div className="h-full border border-border/50 rounded-lg overflow-hidden bg-card/50 backdrop-blur-sm">
              <EnhancedAIAssistant />
            </div>
          </TabsContent>

          <TabsContent value="collaboration" className="h-full">
            <div className="h-full border border-border/50 rounded-lg overflow-hidden bg-card/50 backdrop-blur-sm p-4">
              <RealTimeCollaboration />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

"use client"

import { useState } from 'react'
import { useChat } from '@ai-sdk/react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"

export default function TestChat() {
  const [testMode, setTestMode] = useState<'direct' | 'routed'>('direct')
  
  // Direct test (bypass routing)
  const directChat = useChat({
    api: '/api/test-consultant',
    onError: (error) => {
      console.error('Direct chat error:', error)
    }
  })

  // Routed test (through main routing)
  const routedChat = useChat({
    api: '/api/chat',
    body: { assistantType: 'consultant' },
    onError: (error) => {
      console.error('Routed chat error:', error)
    }
  })

  const currentChat = testMode === 'direct' ? directChat : routedChat

  return (
    <main className="min-h-screen p-8 bg-background">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Chat API Test</CardTitle>
            <div className="flex gap-2">
              <Button 
                variant={testMode === 'direct' ? 'default' : 'outline'}
                onClick={() => setTestMode('direct')}
              >
                Direct Test
              </Button>
              <Button 
                variant={testMode === 'routed' ? 'default' : 'outline'}
                onClick={() => setTestMode('routed')}
              >
                Routed Test
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {testMode === 'direct' 
                ? 'Testing direct API call to /api/test-consultant' 
                : 'Testing routed API call through /api/chat with consultant assistant'
              }
            </p>
            
            {/* Messages */}
            <div className="space-y-4 mb-4 max-h-96 overflow-y-auto">
              {currentChat.messages.map((message) => (
                <div
                  key={message.id}
                  className={`p-3 rounded-lg ${
                    message.role === 'user' 
                      ? 'bg-primary text-primary-foreground ml-12' 
                      : 'bg-muted mr-12'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">
                    {message.role === 'user' ? 'You' : 'Assistant'}
                  </div>
                  <div className="whitespace-pre-wrap">{message.content}</div>
                </div>
              ))}
              
              {currentChat.status === 'streaming' && (
                <div className="bg-muted p-3 rounded-lg mr-12">
                  <div className="text-sm font-medium mb-1">Assistant</div>
                  <div className="flex items-center gap-2">
                    <div className="flex gap-1">
                      {[0, 1, 2].map((i) => (
                        <div
                          key={i}
                          className="w-2 h-2 bg-primary rounded-full animate-pulse"
                          style={{ animationDelay: `${i * 0.2}s` }}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-muted-foreground">Thinking...</span>
                  </div>
                </div>
              )}
            </div>

            {/* Input */}
            <form onSubmit={currentChat.handleSubmit} className="space-y-4">
              <Textarea
                value={currentChat.input}
                onChange={currentChat.handleInputChange}
                placeholder="Type your message..."
                className="min-h-[100px]"
              />
              <div className="flex gap-2">
                <Button 
                  type="submit" 
                  disabled={!currentChat.input.trim() || currentChat.status === 'streaming'}
                >
                  Send
                </Button>
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => currentChat.setMessages([])}
                >
                  Clear
                </Button>
              </div>
            </form>

            {/* Status */}
            <div className="mt-4 text-xs text-muted-foreground">
              Status: {currentChat.status || 'ready'} | 
              Messages: {currentChat.messages.length} |
              Mode: {testMode}
            </div>
          </CardContent>
        </Card>

        {/* Quick Test Buttons */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  currentChat.setInput("Hello, can you help me?")
                }}
              >
                Simple Greeting
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  currentChat.setInput("What web development services do you offer?")
                }}
              >
                Service Question
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  currentChat.setInput("Tell me about your company")
                }}
              >
                Company Question
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  currentChat.setInput("Generate a proposal for a website")
                }}
              >
                Proposal Request
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  )
}

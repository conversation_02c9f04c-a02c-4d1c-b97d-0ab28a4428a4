import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { AIProvider } from "@/components/ai-provider"
import { circularStd, fontVariables } from "@/lib/fonts"
import { Toaster } from "@/components/ui/toaster"
import NextTopLoader from 'nextjs-toploader'

export const metadata: Metadata = {
  title: "Soimagine Digital Agency",
  description: "AI-powered digital agency solutions",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={fontVariables}>
      <body className={circularStd.className}>
        <NextTopLoader color="#2dd4bf" showSpinner={false} />
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem disableTransitionOnChange>
          <AIProvider>{children}</AIProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}

"use client"

import { useState } from 'react'
import { useChat } from '@ai-sdk/react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Briefcase, MessageSquare, CheckCircle, AlertCircle } from 'lucide-react'

export default function TestOpenAIConsultant() {
  const [testResults, setTestResults] = useState<{
    direct: 'pending' | 'success' | 'error',
    routed: 'pending' | 'success' | 'error'
  }>({
    direct: 'pending',
    routed: 'pending'
  })

  // Direct test (bypass routing)
  const directChat = useChat({
    api: '/api/test-consultant',
    onFinish: () => setTestResults(prev => ({ ...prev, direct: 'success' })),
    onError: () => setTestResults(prev => ({ ...prev, direct: 'error' }))
  })

  // Routed test (through main routing)
  const routedChat = useChat({
    api: '/api/chat',
    body: { assistantType: 'consultant' },
    onFinish: () => setTestResults(prev => ({ ...prev, routed: 'success' })),
    onError: () => setTestResults(prev => ({ ...prev, routed: 'error' }))
  })

  const runQuickTest = async (chat: any, testType: 'direct' | 'routed') => {
    setTestResults(prev => ({ ...prev, [testType]: 'pending' }))
    chat.setMessages([])
    chat.setInput("Hello, can you help me with web development services?")
    
    // Simulate form submission
    setTimeout(() => {
      const event = new Event('submit', { bubbles: true, cancelable: true })
      chat.handleSubmit(event)
    }, 100)
  }

  const samplePrompts = [
    "What web development services do you offer?",
    "Tell me about your company and team",
    "Generate a proposal for an e-commerce website",
    "What are your core values?",
    "How much does a mobile app cost?"
  ]

  return (
    <main className="min-h-screen p-8 bg-background">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-6 w-6 text-primary" />
              OpenAI Consultant API Test
            </CardTitle>
            <p className="text-muted-foreground">
              Testing the consultant API with OpenAI GPT-4o-mini model. This page tests both direct API calls 
              and routed calls through the main chat endpoint.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Badge variant={testResults.direct === 'success' ? 'default' : testResults.direct === 'error' ? 'destructive' : 'secondary'}>
                  {testResults.direct === 'success' && <CheckCircle className="h-3 w-3 mr-1" />}
                  {testResults.direct === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
                  Direct API: {testResults.direct}
                </Badge>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => runQuickTest(directChat, 'direct')}
                  disabled={directChat.status === 'streaming'}
                >
                  Test Direct
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={testResults.routed === 'success' ? 'default' : testResults.routed === 'error' ? 'destructive' : 'secondary'}>
                  {testResults.routed === 'success' && <CheckCircle className="h-3 w-3 mr-1" />}
                  {testResults.routed === 'error' && <AlertCircle className="h-3 w-3 mr-1" />}
                  Routed API: {testResults.routed}
                </Badge>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => runQuickTest(routedChat, 'routed')}
                  disabled={routedChat.status === 'streaming'}
                >
                  Test Routed
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Chat Interfaces */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Direct Chat */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-blue-500" />
                Direct API Test
                <Badge variant="outline" className="text-xs">
                  /api/test-consultant
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Messages */}
              <div className="space-y-3 max-h-64 overflow-y-auto border rounded-lg p-3">
                {directChat.messages.length === 0 ? (
                  <p className="text-muted-foreground text-sm text-center py-4">
                    No messages yet. Send a message to test the direct API.
                  </p>
                ) : (
                  directChat.messages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-2 rounded text-sm ${
                        message.role === 'user' 
                          ? 'bg-primary text-primary-foreground ml-8' 
                          : 'bg-muted mr-8'
                      }`}
                    >
                      <div className="font-medium text-xs mb-1">
                        {message.role === 'user' ? 'You' : 'Assistant'}
                      </div>
                      <div className="whitespace-pre-wrap">{message.content}</div>
                    </div>
                  ))
                )}
                
                {directChat.status === 'streaming' && (
                  <div className="bg-muted p-2 rounded mr-8">
                    <div className="font-medium text-xs mb-1">Assistant</div>
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        {[0, 1, 2].map((i) => (
                          <div
                            key={i}
                            className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse"
                            style={{ animationDelay: `${i * 0.2}s` }}
                          />
                        ))}
                      </div>
                      <span className="text-xs text-muted-foreground">Thinking...</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Input */}
              <form onSubmit={directChat.handleSubmit} className="space-y-2">
                <Textarea
                  value={directChat.input}
                  onChange={directChat.handleInputChange}
                  placeholder="Ask about services, company, or proposals..."
                  className="min-h-[80px] text-sm"
                />
                <div className="flex gap-2">
                  <Button 
                    type="submit" 
                    size="sm"
                    disabled={!directChat.input.trim() || directChat.status === 'streaming'}
                  >
                    Send
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline"
                    size="sm"
                    onClick={() => directChat.setMessages([])}
                  >
                    Clear
                  </Button>
                </div>
              </form>

              <div className="text-xs text-muted-foreground">
                Status: {directChat.status || 'ready'} | Messages: {directChat.messages.length}
              </div>
            </CardContent>
          </Card>

          {/* Routed Chat */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-green-500" />
                Routed API Test
                <Badge variant="outline" className="text-xs">
                  /api/chat → /api/chat/consultant
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Messages */}
              <div className="space-y-3 max-h-64 overflow-y-auto border rounded-lg p-3">
                {routedChat.messages.length === 0 ? (
                  <p className="text-muted-foreground text-sm text-center py-4">
                    No messages yet. Send a message to test the routed API.
                  </p>
                ) : (
                  routedChat.messages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-2 rounded text-sm ${
                        message.role === 'user' 
                          ? 'bg-primary text-primary-foreground ml-8' 
                          : 'bg-muted mr-8'
                      }`}
                    >
                      <div className="font-medium text-xs mb-1">
                        {message.role === 'user' ? 'You' : 'Consultant'}
                      </div>
                      <div className="whitespace-pre-wrap">{message.content}</div>
                    </div>
                  ))
                )}
                
                {routedChat.status === 'streaming' && (
                  <div className="bg-muted p-2 rounded mr-8">
                    <div className="font-medium text-xs mb-1">Consultant</div>
                    <div className="flex items-center gap-2">
                      <div className="flex gap-1">
                        {[0, 1, 2].map((i) => (
                          <div
                            key={i}
                            className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse"
                            style={{ animationDelay: `${i * 0.2}s` }}
                          />
                        ))}
                      </div>
                      <span className="text-xs text-muted-foreground">Thinking...</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Input */}
              <form onSubmit={routedChat.handleSubmit} className="space-y-2">
                <Textarea
                  value={routedChat.input}
                  onChange={routedChat.handleInputChange}
                  placeholder="Ask about services, company, or proposals..."
                  className="min-h-[80px] text-sm"
                />
                <div className="flex gap-2">
                  <Button 
                    type="submit" 
                    size="sm"
                    disabled={!routedChat.input.trim() || routedChat.status === 'streaming'}
                  >
                    Send
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline"
                    size="sm"
                    onClick={() => routedChat.setMessages([])}
                  >
                    Clear
                  </Button>
                </div>
              </form>

              <div className="text-xs text-muted-foreground">
                Status: {routedChat.status || 'ready'} | Messages: {routedChat.messages.length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sample Prompts */}
        <Card>
          <CardHeader>
            <CardTitle>Sample Prompts</CardTitle>
            <p className="text-sm text-muted-foreground">
              Click any prompt to copy it to your clipboard, then paste it in either chat above.
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {samplePrompts.map((prompt, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-3 text-left justify-start text-sm"
                  onClick={() => {
                    navigator.clipboard.writeText(prompt)
                  }}
                >
                  {prompt}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  )
}

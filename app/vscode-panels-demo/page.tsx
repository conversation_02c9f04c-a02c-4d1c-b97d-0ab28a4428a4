"use client"

import React, { useState } from 'react'
import { FileText, Search, Terminal, Settings, Code, Folder, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Workspace,
  Panel,
  Tab,
  PanelGroup
} from '@/lib/vscode-panels'

// Demo content components
function FileExplorer() {
  return (
    <div className="p-4 space-y-2">
      <div className="font-medium text-sm mb-3">Explorer</div>
      <div className="space-y-1">
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm cursor-pointer">
          <Folder className="w-4 h-4" />
          <span>src</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm ml-4 cursor-pointer">
          <FileText className="w-4 h-4" />
          <span>App.tsx</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm ml-4 cursor-pointer">
          <FileText className="w-4 h-4" />
          <span>index.tsx</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm cursor-pointer">
          <Folder className="w-4 h-4" />
          <span>components</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm ml-4 cursor-pointer">
          <FileText className="w-4 h-4" />
          <span>Panel.tsx</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm ml-4 cursor-pointer">
          <FileText className="w-4 h-4" />
          <span>Tab.tsx</span>
        </div>
      </div>
    </div>
  )
}

function CodeEditor({ fileName = "App.tsx" }: { fileName?: string }) {
  return (
    <div className="p-4 font-mono text-sm h-full overflow-auto">
      <div className="text-muted-foreground mb-2">{fileName}</div>
      <div className="space-y-1">
        <div><span className="text-blue-500">import</span> React <span className="text-blue-500">from</span> <span className="text-green-500">'react'</span></div>
        <div><span className="text-blue-500">import</span> &#123; Workspace, Panel, Tab &#125; <span className="text-blue-500">from</span> <span className="text-green-500">'@/lib/vscode-panels'</span></div>
        <div></div>
        <div><span className="text-blue-500">function</span> <span className="text-yellow-500">App</span>() &#123;</div>
        <div className="ml-4"><span className="text-blue-500">return</span> (</div>
        <div className="ml-8">&lt;<span className="text-red-500">Workspace</span>&gt;</div>
        <div className="ml-12">&lt;<span className="text-red-500">PanelGroup</span> <span className="text-blue-500">direction</span>=<span className="text-green-500">"horizontal"</span>&gt;</div>
        <div className="ml-16">&lt;<span className="text-red-500">Panel</span> <span className="text-blue-500">id</span>=<span className="text-green-500">"sidebar"</span> <span className="text-blue-500">title</span>=<span className="text-green-500">"Explorer"</span>&gt;</div>
        <div className="ml-20">&lt;<span className="text-red-500">Tab</span> <span className="text-blue-500">id</span>=<span className="text-green-500">"files"</span> <span className="text-blue-500">title</span>=<span className="text-green-500">"Files"</span>&gt;</div>
        <div className="ml-24">&lt;<span className="text-red-500">FileExplorer</span> /&gt;</div>
        <div className="ml-20">&lt;/<span className="text-red-500">Tab</span>&gt;</div>
        <div className="ml-16">&lt;/<span className="text-red-500">Panel</span>&gt;</div>
        <div className="ml-16">&lt;<span className="text-red-500">Panel</span> <span className="text-blue-500">id</span>=<span className="text-green-500">"editor"</span> <span className="text-blue-500">title</span>=<span className="text-green-500">"Editor"</span>&gt;</div>
        <div className="ml-20">&lt;<span className="text-red-500">Tab</span> <span className="text-blue-500">id</span>=<span className="text-green-500">"app"</span> <span className="text-blue-500">title</span>=<span className="text-green-500">"App.tsx"</span>&gt;</div>
        <div className="ml-24">&lt;<span className="text-red-500">CodeEditor</span> /&gt;</div>
        <div className="ml-20">&lt;/<span className="text-red-500">Tab</span>&gt;</div>
        <div className="ml-16">&lt;/<span className="text-red-500">Panel</span>&gt;</div>
        <div className="ml-12">&lt;/<span className="text-red-500">PanelGroup</span>&gt;</div>
        <div className="ml-8">&lt;/<span className="text-red-500">Workspace</span>&gt;</div>
        <div className="ml-4">)</div>
        <div>&#125;</div>
        <div></div>
        <div><span className="text-blue-500">export default</span> App</div>
      </div>
    </div>
  )
}

function TerminalPanel() {
  return (
    <div className="p-4 bg-black text-green-400 font-mono text-sm h-full">
      <div className="mb-2">Terminal</div>
      <div className="space-y-1">
        <div>$ npm install @soimagine/vscode-panels</div>
        <div className="text-green-300">✓ Package installed successfully</div>
        <div>$ npm run dev</div>
        <div className="text-blue-300">Starting development server...</div>
        <div className="text-blue-300">Local: http://localhost:3000</div>
        <div className="flex items-center">
          <span>$ </span>
          <span className="animate-pulse">_</span>
        </div>
      </div>
    </div>
  )
}

function SearchPanel() {
  return (
    <div className="p-4">
      <div className="font-medium text-sm mb-3">Search</div>
      <div className="space-y-3">
        <input 
          type="text" 
          placeholder="Search files..." 
          className="w-full px-3 py-2 border rounded-md text-sm"
        />
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">Results:</div>
          <div className="text-xs p-2 bg-muted rounded">
            <div className="font-medium">App.tsx</div>
            <div className="text-muted-foreground">Line 5: function App()</div>
          </div>
          <div className="text-xs p-2 bg-muted rounded">
            <div className="font-medium">Panel.tsx</div>
            <div className="text-muted-foreground">Line 12: export function Panel</div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Main demo page
export default function VSCodePanelsDemoPage() {
  const [activeDemo, setActiveDemo] = useState<'basic' | 'advanced'>('basic')
  
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">VS Code Panels & Tabs System</h1>
              <p className="text-muted-foreground mt-1">
                A comprehensive library inspired by VS Code's panels and tabs system
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant={activeDemo === 'basic' ? 'default' : 'outline'}
                onClick={() => setActiveDemo('basic')}
              >
                Basic Demo
              </Button>
              <Button
                variant={activeDemo === 'advanced' ? 'default' : 'outline'}
                onClick={() => setActiveDemo('advanced')}
              >
                Advanced Demo
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Features */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Resizable Panels</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-muted-foreground">
                Drag handles for intuitive panel resizing
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Draggable Tabs</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-muted-foreground">
                Reorder tabs with drag and drop
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Tab Pinning</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-muted-foreground">
                Pin important tabs to prevent closure
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Layout Persistence</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-muted-foreground">
                Save and restore layout configurations
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Demo */}
      <div className="container mx-auto px-4 pb-8">
        <div className="border rounded-lg overflow-hidden" style={{ height: '600px' }}>
          {activeDemo === 'basic' ? <BasicDemo /> : <AdvancedDemo />}
        </div>
      </div>

      {/* Footer */}
      <div className="border-t bg-muted/50 py-8">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm text-muted-foreground">
            Built with React, TypeScript, Zustand, and Framer Motion
          </p>
          <div className="flex justify-center gap-2 mt-2">
            <Badge variant="secondary">React</Badge>
            <Badge variant="secondary">TypeScript</Badge>
            <Badge variant="secondary">Zustand</Badge>
            <Badge variant="secondary">Framer Motion</Badge>
            <Badge variant="secondary">Tailwind CSS</Badge>
          </div>
        </div>
      </div>
    </div>
  )
}

// Basic demo
function BasicDemo() {
  return (
    <Workspace
      className="w-full h-full"
      showLayoutControls={true}
      theme="auto"
    >
      <PanelGroup direction="horizontal">
        {/* Sidebar */}
        <Panel
          id="sidebar"
          title="Explorer"
          icon={Folder}
          minWidth={200}
          defaultWidth={250}
        >
          <Tab id="files" title="Files" icon={FileText} pinned>
            <FileExplorer />
          </Tab>
          <Tab id="search" title="Search" icon={Search}>
            <SearchPanel />
          </Tab>
        </Panel>

        {/* Main content */}
        <PanelGroup direction="vertical">
          {/* Editor */}
          <Panel
            id="editor"
            title="Editor"
            icon={Code}
            defaultHeight={400}
          >
            <Tab id="app-tsx" title="App.tsx" icon={FileText} pinned isDirty>
              <CodeEditor fileName="App.tsx" />
            </Tab>
            <Tab id="panel-tsx" title="Panel.tsx" icon={FileText}>
              <CodeEditor fileName="Panel.tsx" />
            </Tab>
          </Panel>

          {/* Terminal */}
          <Panel
            id="terminal"
            title="Terminal"
            icon={Terminal}
            minHeight={150}
            defaultHeight={200}
          >
            <Tab id="bash" title="bash">
              <TerminalPanel />
            </Tab>
          </Panel>
        </PanelGroup>
      </PanelGroup>
    </Workspace>
  )
}

// Advanced demo
function AdvancedDemo() {
  return (
    <Workspace
      className="w-full h-full"
      showLayoutControls={true}
      theme="dark"
      persistLayout={true}
    >
      <PanelGroup direction="horizontal">
        {/* Left sidebar */}
        <Panel
          id="left-sidebar"
          title="Explorer"
          icon={Folder}
          minWidth={200}
          defaultWidth={280}
        >
          <Tab id="files" title="Files" icon={FileText} pinned>
            <FileExplorer />
          </Tab>
          <Tab id="search" title="Search" icon={Search}>
            <SearchPanel />
          </Tab>
          <Tab id="settings" title="Settings" icon={Settings}>
            <div className="p-4">
              <div className="font-medium text-sm mb-3">Settings</div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Theme</span>
                  <select className="px-2 py-1 border rounded text-sm">
                    <option>Auto</option>
                    <option>Light</option>
                    <option>Dark</option>
                  </select>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Auto Save</span>
                  <input type="checkbox" defaultChecked />
                </div>
              </div>
            </div>
          </Tab>
        </Panel>

        {/* Main area */}
        <PanelGroup direction="vertical">
          {/* Editor group */}
          <PanelGroup direction="horizontal">
            <Panel
              id="main-editor"
              title="Main Editor"
              icon={Code}
            >
              <Tab id="app" title="App.tsx" icon={FileText} pinned isDirty>
                <CodeEditor fileName="App.tsx" />
              </Tab>
              <Tab id="components" title="Components.tsx" icon={FileText}>
                <CodeEditor fileName="Components.tsx" />
              </Tab>
              <Tab id="utils" title="utils.ts" icon={FileText}>
                <CodeEditor fileName="utils.ts" />
              </Tab>
            </Panel>

            <Panel
              id="side-editor"
              title="Side Editor"
              icon={Code}
              defaultWidth={400}
            >
              <Tab id="readme" title="README.md" icon={FileText}>
                <div className="p-4">
                  <h2 className="text-lg font-bold mb-4">VS Code Panels Demo</h2>
                  <p className="text-sm text-muted-foreground mb-4">
                    This is a comprehensive demo of the VS Code-inspired panels and tabs system.
                  </p>
                  <ul className="text-sm space-y-1">
                    <li>• Resizable panels with drag handles</li>
                    <li>• Draggable tabs with reordering</li>
                    <li>• Tab pinning and dirty state</li>
                    <li>• Layout persistence</li>
                    <li>• Extensible architecture</li>
                  </ul>
                </div>
              </Tab>
            </Panel>
          </PanelGroup>

          {/* Bottom panel */}
          <Panel
            id="bottom-panel"
            title="Terminal"
            icon={Terminal}
            minHeight={150}
            defaultHeight={200}
          >
            <Tab id="terminal-1" title="Terminal 1">
              <TerminalPanel />
            </Tab>
            <Tab id="terminal-2" title="Terminal 2">
              <div className="p-4 bg-black text-green-400 font-mono text-sm h-full">
                <div className="mb-2">Terminal 2</div>
                <div className="space-y-1">
                  <div>$ git status</div>
                  <div className="text-red-300">On branch main</div>
                  <div className="text-red-300">Changes not staged for commit:</div>
                  <div className="text-red-300 ml-4">modified: src/App.tsx</div>
                  <div>$ </div>
                </div>
              </div>
            </Tab>
          </Panel>
        </PanelGroup>
      </PanelGroup>
    </Workspace>
  )
}

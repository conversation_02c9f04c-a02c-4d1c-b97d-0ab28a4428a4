import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const type = searchParams.get('type') || 'xp' // xp, challenges, credits
    const period = searchParams.get('period') || 'all' // all, monthly, weekly
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100)

    let orderBy: any = { totalXp: 'desc' }
    let selectFields: any = {
      user: {
        select: {
          id: true,
          name: true,
          image: true,
          company: true
        }
      },
      totalXp: true,
      currentLevel: true,
      challengesCompleted: true,
      currentStreak: true,
      longestStreak: true
    }

    // Adjust query based on leaderboard type
    switch (type) {
      case 'challenges':
        orderBy = { challengesCompleted: 'desc' }
        break
      case 'streak':
        orderBy = { currentStreak: 'desc' }
        break
      case 'level':
        orderBy = { currentLevel: 'desc' }
        break
      default: // xp
        orderBy = { totalXp: 'desc' }
    }

    // Add time period filter if needed
    let whereClause: any = {}
    if (period !== 'all') {
      const now = new Date()
      let startDate: Date

      if (period === 'weekly') {
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      } else if (period === 'monthly') {
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      } else {
        startDate = new Date(0) // All time
      }

      whereClause.lastActivityDate = {
        gte: startDate
      }
    }

    // Get leaderboard data
    const leaderboard = await prisma.userProgress.findMany({
      where: whereClause,
      orderBy,
      take: limit,
      select: selectFields
    })

    // Get current user's position
    const userProgress = await prisma.userProgress.findUnique({
      where: { userId: session.user.id },
      select: selectFields
    })

    let userRank = null
    if (userProgress) {
      // Calculate user's rank
      const userValue = type === 'challenges' ? userProgress.challengesCompleted :
                       type === 'streak' ? userProgress.currentStreak :
                       type === 'level' ? userProgress.currentLevel :
                       userProgress.totalXp

      const betterCount = await prisma.userProgress.count({
        where: {
          ...whereClause,
          [type === 'challenges' ? 'challengesCompleted' :
           type === 'streak' ? 'currentStreak' :
           type === 'level' ? 'currentLevel' :
           'totalXp']: {
            gt: userValue
          }
        }
      })

      userRank = betterCount + 1
    }

    // Transform leaderboard data
    const transformedLeaderboard = leaderboard.map((entry, index) => ({
      rank: index + 1,
      user: {
        id: entry.user.id,
        name: entry.user.name || 'Anonymous',
        image: entry.user.image,
        company: entry.user.company
      },
      stats: {
        totalXp: entry.totalXp,
        currentLevel: entry.currentLevel,
        challengesCompleted: entry.challengesCompleted,
        currentStreak: entry.currentStreak,
        longestStreak: entry.longestStreak
      },
      primaryValue: type === 'challenges' ? entry.challengesCompleted :
                   type === 'streak' ? entry.currentStreak :
                   type === 'level' ? entry.currentLevel :
                   entry.totalXp
    }))

    return NextResponse.json({
      leaderboard: transformedLeaderboard,
      userRank,
      userStats: userProgress ? {
        totalXp: userProgress.totalXp,
        currentLevel: userProgress.currentLevel,
        challengesCompleted: userProgress.challengesCompleted,
        currentStreak: userProgress.currentStreak,
        longestStreak: userProgress.longestStreak
      } : null,
      metadata: {
        type,
        period,
        total: leaderboard.length
      }
    })

  } catch (error) {
    console.error('Error fetching leaderboard:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { xai } from "@ai-sdk/xai"
import { generateText } from "ai"

export async function POST(req: NextRequest) {
  try {
    const { prompt, language } = await req.json()

    if (!prompt) {
      return NextResponse.json({ error: "Prompt is required" }, { status: 400 })
    }

    // Use AI to generate code based on the prompt and language
    const systemPrompt = `You are a code generation assistant for Soimagine Digital Agency.
Generate clean, well-documented ${language || "JavaScript"} code based on the user's request.
Only respond with code and brief comments explaining the code.`

    const { text } = await generateText({
      model: xai("grok-beta"),
      prompt,
      system: systemPrompt,
    })

    return NextResponse.json({
      code: text,
      language: language || "javascript",
    })
  } catch (error) {
    console.error("Error in code API:", error)

    // Provide a helpful fallback code example
    const fallbackCode = `// Soimagine Digital Agency - Code Template
// Generated for: ${prompt || "General purpose"}

function soimagineTemplate() {
  console.log("Welcome to Soimagine Digital Agency!");
  
  // Your custom code implementation goes here
  const projectData = {
    name: "New Project",
    type: "${language || "javascript"}",
    status: "in-progress"
  };
  
  return projectData;
}

// Initialize the template
soimagineTemplate();`

    return NextResponse.json({
      code: fallbackCode,
      language: language || "javascript",
    })
  }
}

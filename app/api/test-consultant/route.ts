import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { messages } = await req.json()

    console.log('Test consultant API called with messages:', messages)

    const result = streamText({
      model: openai('gpt-4o-mini'),
      messages,
      system: `You are a helpful consultant AI for Soimagine Digital Agency.

      Respond professionally and helpfully to questions about:
      - Web development services
      - Company information
      - Project proposals

      Keep responses concise but informative.`,
    })

    console.log('Streaming response created')
    return result.toDataStreamResponse()
  } catch (error) {
    console.error('Test consultant API error:', error)
    return new Response(`Error: ${error.message}`, { status: 500 })
  }
}

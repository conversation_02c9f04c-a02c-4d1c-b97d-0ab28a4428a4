import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(req.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
    const category = searchParams.get('category')
    const difficulty = searchParams.get('difficulty')
    const type = searchParams.get('type')
    const status = searchParams.get('status') // 'available', 'completed', 'in_progress'

    const skip = (page - 1) * limit

    // Build where clause for challenges
    const challengeWhere: any = {
      isActive: true,
      isPublic: true
    }

    if (category) {
      challengeWhere.category = category
    }

    if (difficulty) {
      challengeWhere.difficulty = difficulty
    }

    if (type) {
      challengeWhere.type = type
    }

    // Check date availability
    const now = new Date()
    challengeWhere.OR = [
      { startDate: null },
      { startDate: { lte: now } }
    ]
    challengeWhere.AND = [
      {
        OR: [
          { endDate: null },
          { endDate: { gte: now } }
        ]
      }
    ]

    // Get challenges with user completion status
    const challenges = await prisma.challenge.findMany({
      where: challengeWhere,
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
      include: {
        completions: {
          where: { userId: session.user.id },
          select: {
            id: true,
            status: true,
            score: true,
            completedAt: true,
            creditsAwarded: true,
            xpAwarded: true
          }
        }
      }
    })

    // Filter by completion status if requested
    let filteredChallenges = challenges
    if (status) {
      filteredChallenges = challenges.filter(challenge => {
        const completion = challenge.completions[0]
        
        switch (status) {
          case 'available':
            return !completion
          case 'completed':
            return completion && completion.status === 'COMPLETED'
          case 'in_progress':
            return completion && ['STARTED', 'IN_PROGRESS'].includes(completion.status)
          default:
            return true
        }
      })
    }

    // Transform the data for response
    const transformedChallenges = filteredChallenges.map(challenge => ({
      id: challenge.id,
      title: challenge.title,
      description: challenge.description,
      instructions: challenge.instructions,
      type: challenge.type,
      category: challenge.category,
      difficulty: challenge.difficulty,
      creditReward: challenge.creditReward,
      xpReward: challenge.xpReward,
      timeLimit: challenge.timeLimit,
      completionRate: challenge.completionRate,
      averageRating: challenge.averageRating,
      totalAttempts: challenge.totalAttempts,
      startDate: challenge.startDate,
      endDate: challenge.endDate,
      createdAt: challenge.createdAt,
      userCompletion: challenge.completions[0] || null
    }))

    const total = await prisma.challenge.count({ where: challengeWhere })
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      challenges: transformedChallenges,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

  } catch (error) {
    console.error('Error fetching challenges:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const completionSchema = z.object({
  challengeId: z.string(),
  submissionData: z.any().optional(),
  score: z.number().int().min(0).max(100).optional(),
  timeSpent: z.number().int().positive().optional()
})

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const validatedData = completionSchema.parse(body)

    // Get the challenge
    const challenge = await prisma.challenge.findUnique({
      where: { id: validatedData.challengeId },
      include: {
        completions: {
          where: { userId: session.user.id }
        }
      }
    })

    if (!challenge) {
      return NextResponse.json(
        { error: 'Challenge not found' },
        { status: 404 }
      )
    }

    if (!challenge.isActive) {
      return NextResponse.json(
        { error: 'Challenge is not active' },
        { status: 400 }
      )
    }

    // Check if challenge is still available (date range)
    const now = new Date()
    if (challenge.startDate && challenge.startDate > now) {
      return NextResponse.json(
        { error: 'Challenge has not started yet' },
        { status: 400 }
      )
    }

    if (challenge.endDate && challenge.endDate < now) {
      return NextResponse.json(
        { error: 'Challenge has ended' },
        { status: 400 }
      )
    }

    // Check if user already completed this challenge
    const existingCompletion = challenge.completions[0]
    if (existingCompletion && existingCompletion.status === 'COMPLETED') {
      return NextResponse.json(
        { error: 'Challenge already completed' },
        { status: 400 }
      )
    }

    // Calculate rewards
    const creditsAwarded = challenge.creditReward
    const xpAwarded = challenge.xpReward

    // Use transaction to ensure consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create or update challenge completion
      const completion = await tx.challengeCompletion.upsert({
        where: {
          userId_challengeId: {
            userId: session.user.id,
            challengeId: validatedData.challengeId
          }
        },
        update: {
          status: 'COMPLETED',
          score: validatedData.score,
          timeSpent: validatedData.timeSpent,
          submissionData: validatedData.submissionData,
          completedAt: new Date(),
          creditsAwarded,
          xpAwarded
        },
        create: {
          userId: session.user.id,
          challengeId: validatedData.challengeId,
          status: 'COMPLETED',
          score: validatedData.score,
          timeSpent: validatedData.timeSpent,
          submissionData: validatedData.submissionData,
          completedAt: new Date(),
          creditsAwarded,
          xpAwarded
        }
      })

      // Award credits if any
      if (creditsAwarded > 0) {
        // Get current credit balance
        const creditBalance = await tx.creditBalance.findUnique({
          where: { userId: session.user.id }
        })

        if (creditBalance) {
          // Update credit balance
          await tx.creditBalance.update({
            where: { userId: session.user.id },
            data: {
              totalCredits: creditBalance.totalCredits + creditsAwarded
            }
          })

          // Record credit transaction
          await tx.creditTransaction.create({
            data: {
              userId: session.user.id,
              type: 'EARNED',
              amount: creditsAwarded,
              description: `Challenge completion: ${challenge.title}`,
              source: 'challenge_completion',
              sourceId: challenge.id,
              balanceAfter: creditBalance.totalCredits + creditsAwarded
            }
          })
        }
      }

      // Update user progress
      const userProgress = await tx.userProgress.upsert({
        where: { userId: session.user.id },
        update: {
          totalXp: { increment: xpAwarded },
          challengesCompleted: { increment: 1 },
          // Update category-specific XP based on challenge category
          ...(challenge.category === 'MARKETING' && { marketingXp: { increment: xpAwarded } }),
          ...(challenge.category === 'SOCIAL_MEDIA' && { socialXp: { increment: xpAwarded } }),
          ...(challenge.category === 'CONTENT' && { contentXp: { increment: xpAwarded } }),
          ...(challenge.category === 'REFERRALS' && { referralXp: { increment: xpAwarded } }),
          lastActivityDate: new Date()
        },
        create: {
          userId: session.user.id,
          totalXp: xpAwarded,
          challengesCompleted: 1,
          challengesAttempted: 1,
          ...(challenge.category === 'MARKETING' && { marketingXp: xpAwarded }),
          ...(challenge.category === 'SOCIAL_MEDIA' && { socialXp: xpAwarded }),
          ...(challenge.category === 'CONTENT' && { contentXp: xpAwarded }),
          ...(challenge.category === 'REFERRALS' && { referralXp: xpAwarded }),
          lastActivityDate: new Date()
        }
      })

      // Update challenge statistics
      await tx.challenge.update({
        where: { id: validatedData.challengeId },
        data: {
          totalAttempts: { increment: 1 },
          // Update completion rate (simplified calculation)
          completionRate: {
            increment: 1 / (challenge.totalAttempts + 1)
          }
        }
      })

      return { completion, userProgress }
    })

    return NextResponse.json({
      success: true,
      completion: result.completion,
      rewards: {
        creditsAwarded,
        xpAwarded
      },
      userProgress: result.userProgress
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error completing challenge:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

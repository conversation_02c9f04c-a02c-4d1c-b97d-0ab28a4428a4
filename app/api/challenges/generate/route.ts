import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { openai } from '@ai-sdk/openai'
import { generateObject } from 'ai'
import { z } from 'zod'

const challengeGenerationSchema = z.object({
  title: z.string().min(5).max(100),
  description: z.string().min(20).max(500),
  instructions: z.string().min(10).max(1000),
  type: z.enum(['SOCIAL_SHARE', 'CONTENT_CREATION', 'REFERRAL', 'ENGAGEMENT', 'LEARNING', 'COMMUNITY', 'CUSTOM']),
  category: z.enum(['MARKETING', 'SOCIAL_MEDIA', 'CONTENT', 'REFERRALS', 'COMMUNITY', 'LEARNING', 'SPECIAL_EVENT']),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']),
  creditReward: z.number().int().min(1).max(500),
  xpReward: z.number().int().min(1).max(1000),
  timeLimit: z.number().int().min(5).max(10080).optional(), // 5 minutes to 1 week
  requirements: z.object({
    minFollowers: z.number().int().min(0).optional(),
    platforms: z.array(z.string()).optional(),
    hashtags: z.array(z.string()).optional(),
    contentType: z.string().optional(),
    targetAudience: z.string().optional(),
    metrics: z.object({
      minLikes: z.number().int().min(0).optional(),
      minShares: z.number().int().min(0).optional(),
      minComments: z.number().int().min(0).optional(),
      minViews: z.number().int().min(0).optional()
    }).optional()
  }).optional()
})

const requestSchema = z.object({
  category: z.enum(['MARKETING', 'SOCIAL_MEDIA', 'CONTENT', 'REFERRALS', 'COMMUNITY', 'LEARNING', 'SPECIAL_EVENT']).optional(),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  type: z.enum(['SOCIAL_SHARE', 'CONTENT_CREATION', 'REFERRAL', 'ENGAGEMENT', 'LEARNING', 'COMMUNITY', 'CUSTOM']).optional(),
  theme: z.string().optional(),
  targetAudience: z.string().optional(),
  learningData: z.any().optional() // Previous challenge performance data
})

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin (only admins can generate challenges for now)
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await req.json()
    const validatedRequest = requestSchema.parse(body)

    // Get recent challenge performance data for learning
    const recentChallenges = await prisma.challenge.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      select: {
        type: true,
        category: true,
        difficulty: true,
        completionRate: true,
        averageRating: true,
        totalAttempts: true,
        creditReward: true,
        xpReward: true
      },
      orderBy: { completionRate: 'desc' },
      take: 10
    })

    // Build AI prompt based on request and learning data
    const systemPrompt = `You are an AI Challenge Generation Agent for Soimagine Digital Agency's marketing platform. Your role is to create engaging, achievable marketing challenges that help users learn and grow their marketing skills while promoting the agency.

CHALLENGE DESIGN PRINCIPLES:
1. Challenges should be specific, measurable, and achievable
2. Rewards should be proportional to difficulty and time investment
3. Instructions should be clear and actionable
4. Consider the target audience's skill level
5. Incorporate current marketing trends and best practices

LEARNING DATA FROM RECENT CHALLENGES:
${recentChallenges.map(c => 
  `- ${c.category} ${c.type} (${c.difficulty}): ${(c.completionRate * 100).toFixed(1)}% completion, ${c.totalAttempts} attempts, ${c.creditReward} credits, ${c.xpReward} XP`
).join('\n')}

REWARD GUIDELINES:
- Beginner: 10-50 credits, 10-100 XP
- Intermediate: 25-100 credits, 50-250 XP  
- Advanced: 50-200 credits, 100-500 XP
- Expert: 100-500 credits, 200-1000 XP

TIME LIMITS:
- Quick tasks: 15-60 minutes
- Daily challenges: 1-24 hours
- Weekly challenges: 1-7 days
- No time limit for learning/research challenges

Generate a marketing challenge that is engaging, educational, and promotes Soimagine Digital Agency's services.`

    const userPrompt = `Generate a marketing challenge with these preferences:
${validatedRequest.category ? `Category: ${validatedRequest.category}` : ''}
${validatedRequest.difficulty ? `Difficulty: ${validatedRequest.difficulty}` : ''}
${validatedRequest.type ? `Type: ${validatedRequest.type}` : ''}
${validatedRequest.theme ? `Theme: ${validatedRequest.theme}` : ''}
${validatedRequest.targetAudience ? `Target Audience: ${validatedRequest.targetAudience}` : ''}

Create a challenge that will help users develop their marketing skills while subtly promoting Soimagine Digital Agency's expertise in web development, mobile apps, UI/UX design, digital marketing, and branding.`

    // Generate challenge using AI
    const { object: generatedChallenge } = await generateObject({
      model: openai('gpt-4o-mini'),
      system: systemPrompt,
      prompt: userPrompt,
      schema: challengeGenerationSchema
    })

    // Create the challenge in the database
    const challenge = await prisma.challenge.create({
      data: {
        title: generatedChallenge.title,
        description: generatedChallenge.description,
        instructions: generatedChallenge.instructions,
        type: generatedChallenge.type,
        category: generatedChallenge.category,
        difficulty: generatedChallenge.difficulty,
        creditReward: generatedChallenge.creditReward,
        xpReward: generatedChallenge.xpReward,
        timeLimit: generatedChallenge.timeLimit,
        requirements: generatedChallenge.requirements,
        generatedBy: 'ai_challenge_agent',
        learningData: {
          requestData: validatedRequest,
          recentPerformance: recentChallenges,
          generatedAt: new Date().toISOString()
        }
      }
    })

    return NextResponse.json({
      success: true,
      challenge: {
        id: challenge.id,
        title: challenge.title,
        description: challenge.description,
        instructions: challenge.instructions,
        type: challenge.type,
        category: challenge.category,
        difficulty: challenge.difficulty,
        creditReward: challenge.creditReward,
        xpReward: challenge.xpReward,
        timeLimit: challenge.timeLimit,
        requirements: challenge.requirements,
        createdAt: challenge.createdAt
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error generating challenge:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

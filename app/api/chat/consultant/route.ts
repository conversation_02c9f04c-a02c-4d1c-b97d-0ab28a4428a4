import { openai } from "@ai-sdk/openai";
import { streamText, tool } from "ai";
import { z } from "zod";
import { NextResponse } from "next/server";
import { withCreditCheck, calculateCreditCost } from "@/lib/credit-middleware";

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Consultant-specific tools
const consultantTools = {
  getServiceInfo: tool({
    description:
      "Get detailed information about Soimagine Digital Agency services",
    parameters: z.object({
      serviceType: z
        .enum([
          "web-development",
          "mobile-apps",
          "ui-ux-design",
          "digital-marketing",
          "branding",
          "consulting",
          "all",
        ])
        .describe("Type of service to get information about"),
      includePackages: z
        .boolean()
        .default(true)
        .describe("Whether to include pricing packages"),
      includeCaseStudies: z
        .boolean()
        .default(false)
        .describe("Whether to include case studies"),
    }),
    execute: async ({ serviceType, includePackages, includeCaseStudies }) => {
      const services = {
        "web-development": {
          title: "Web Development",
          description:
            "Custom web applications built with modern technologies like Next.js, React, and TypeScript",
          features: [
            "Responsive design for all devices",
            "SEO optimization",
            "Performance optimization",
            "Modern UI/UX design",
            "Content Management Systems",
            "E-commerce solutions",
          ],
          technologies: [
            "Next.js",
            "React",
            "TypeScript",
            "Tailwind CSS",
            "Node.js",
          ],
          packages: includePackages
            ? {
                starter: {
                  price: "$2,500",
                  timeline: "2-3 weeks",
                  pages: "5-8 pages",
                },
                professional: {
                  price: "$5,000",
                  timeline: "4-6 weeks",
                  pages: "10-15 pages",
                },
                enterprise: {
                  price: "$10,000+",
                  timeline: "8-12 weeks",
                  pages: "Custom",
                },
              }
            : undefined,
        },
        "mobile-apps": {
          title: "Mobile App Development",
          description:
            "Native and cross-platform mobile applications for iOS and Android",
          features: [
            "Cross-platform development",
            "Native performance",
            "App Store optimization",
            "Push notifications",
            "Offline functionality",
            "API integrations",
          ],
          technologies: ["React Native", "Flutter", "Swift", "Kotlin"],
          packages: includePackages
            ? {
                mvp: {
                  price: "$8,000",
                  timeline: "6-8 weeks",
                  features: "Core features",
                },
                standard: {
                  price: "$15,000",
                  timeline: "10-12 weeks",
                  features: "Full featured app",
                },
                premium: {
                  price: "$25,000+",
                  timeline: "16-20 weeks",
                  features: "Advanced features",
                },
              }
            : undefined,
        },
        "ui-ux-design": {
          title: "UI/UX Design",
          description:
            "User-centered design solutions that enhance user experience and drive conversions",
          features: [
            "User research and personas",
            "Wireframing and prototyping",
            "Visual design systems",
            "Usability testing",
            "Accessibility compliance",
            "Design system creation",
          ],
          technologies: ["Figma", "Adobe Creative Suite", "Sketch", "InVision"],
          packages: includePackages
            ? {
                consultation: {
                  price: "$1,500",
                  timeline: "1-2 weeks",
                  deliverables: "UX audit & recommendations",
                },
                design: {
                  price: "$4,000",
                  timeline: "3-4 weeks",
                  deliverables: "Complete UI/UX design",
                },
                system: {
                  price: "$8,000",
                  timeline: "6-8 weeks",
                  deliverables: "Design system & guidelines",
                },
              }
            : undefined,
        },
        "digital-marketing": {
          title: "Digital Marketing",
          description:
            "Comprehensive digital marketing strategies to grow your online presence",
          features: [
            "SEO optimization",
            "Social media marketing",
            "Content marketing",
            "PPC advertising",
            "Email marketing",
            "Analytics and reporting",
          ],
          technologies: [
            "Google Analytics",
            "Google Ads",
            "Facebook Ads",
            "Mailchimp",
          ],
          packages: includePackages
            ? {
                basic: {
                  price: "$1,000/month",
                  duration: "3 months minimum",
                  services: "SEO + Social media",
                },
                growth: {
                  price: "$2,500/month",
                  duration: "6 months minimum",
                  services: "Full digital marketing",
                },
                enterprise: {
                  price: "$5,000+/month",
                  duration: "12 months minimum",
                  services: "Custom strategy",
                },
              }
            : undefined,
        },
        branding: {
          title: "Brand Identity & Design",
          description:
            "Complete brand identity solutions from logo design to brand guidelines",
          features: [
            "Logo design and variations",
            "Brand color palette",
            "Typography selection",
            "Brand guidelines",
            "Business card design",
            "Marketing materials",
          ],
          technologies: ["Adobe Illustrator", "Adobe Photoshop", "Figma"],
          packages: includePackages
            ? {
                essential: {
                  price: "$2,000",
                  timeline: "2-3 weeks",
                  deliverables: "Logo + basic brand kit",
                },
                complete: {
                  price: "$4,500",
                  timeline: "4-5 weeks",
                  deliverables: "Full brand identity",
                },
                premium: {
                  price: "$8,000",
                  timeline: "6-8 weeks",
                  deliverables: "Brand system + applications",
                },
              }
            : undefined,
        },
        consulting: {
          title: "Digital Strategy Consulting",
          description:
            "Strategic guidance for digital transformation and technology decisions",
          features: [
            "Technology stack recommendations",
            "Digital transformation roadmap",
            "Performance optimization",
            "Security assessments",
            "Team training and workshops",
            "Ongoing support and maintenance",
          ],
          technologies: ["Various based on needs"],
          packages: includePackages
            ? {
                assessment: {
                  price: "$1,500",
                  timeline: "1 week",
                  deliverables: "Digital audit report",
                },
                strategy: {
                  price: "$5,000",
                  timeline: "2-3 weeks",
                  deliverables: "Complete strategy plan",
                },
                implementation: {
                  price: "$10,000+",
                  timeline: "3-6 months",
                  deliverables: "Guided implementation",
                },
              }
            : undefined,
        },
      };

      if (serviceType === "all") {
        return {
          services: Object.values(services),
          summary:
            "Soimagine Digital Agency offers comprehensive digital solutions including web development, mobile apps, UI/UX design, digital marketing, branding, and consulting services.",
          contact:
            "Contact us for a free consultation to discuss your specific needs.",
        };
      }

      const service = services[serviceType];
      if (!service) {
        return { error: "Service not found" };
      }

      return {
        service,
        caseStudies: includeCaseStudies
          ? [
              {
                title: "E-commerce Platform Redesign",
                client: "Fashion Retailer",
                results: "150% increase in conversions, 40% faster load times",
              },
              {
                title: "Mobile App Development",
                client: "Food Delivery Service",
                results: "50K+ downloads in first month, 4.8 star rating",
              },
            ]
          : undefined,
      };
    },
  }),

  getCompanyInfo: tool({
    description:
      "Get information about Soimagine Digital Agency - company background, team, values, and approach",
    parameters: z.object({
      infoType: z
        .enum(["about", "team", "values", "process", "contact", "all"])
        .describe("Type of company information to retrieve"),
    }),
    execute: async ({ infoType }) => {
      const companyInfo = {
        about: {
          title: "About Soimagine Digital Agency",
          description:
            "We are a full-service digital agency specializing in creating innovative web and mobile solutions that drive business growth. Founded with a passion for technology and design, we help businesses transform their digital presence.",
          founded: "2020",
          location: "Global (Remote-first)",
          specialties: [
            "Web Development",
            "Mobile Apps",
            "UI/UX Design",
            "Digital Marketing",
          ],
          mission:
            "To empower businesses with cutting-edge digital solutions that drive growth and success.",
        },
        team: {
          title: "Our Expert Team",
          description:
            "Our diverse team of experts brings together years of experience in technology, design, and digital marketing.",
          members: [
            {
              role: "Lead Developer",
              expertise: "Full-stack development, React, Node.js",
            },
            {
              role: "UI/UX Designer",
              expertise: "User experience design, Figma, Design systems",
            },
            {
              role: "Digital Marketing Specialist",
              expertise: "SEO, PPC, Social media marketing",
            },
            {
              role: "Project Manager",
              expertise: "Agile methodology, Client communication",
            },
          ],
          size: "10-15 professionals",
          approach: "Collaborative, client-focused, results-driven",
        },
        values: {
          title: "Our Core Values",
          values: [
            {
              name: "Innovation",
              description:
                "We stay ahead of technology trends to deliver cutting-edge solutions",
            },
            {
              name: "Quality",
              description:
                "We never compromise on quality and attention to detail",
            },
            {
              name: "Transparency",
              description:
                "Clear communication and honest feedback throughout the project",
            },
            {
              name: "Partnership",
              description:
                "We work as an extension of your team, not just a vendor",
            },
            {
              name: "Results",
              description:
                "We focus on measurable outcomes that drive business growth",
            },
          ],
        },
        process: {
          title: "Our Development Process",
          phases: [
            {
              phase: "Discovery",
              description: "Understanding your business goals and requirements",
              duration: "1-2 weeks",
            },
            {
              phase: "Strategy",
              description: "Creating a comprehensive project plan and timeline",
              duration: "1 week",
            },
            {
              phase: "Design",
              description: "UI/UX design and prototyping",
              duration: "2-4 weeks",
            },
            {
              phase: "Development",
              description: "Building your solution with regular updates",
              duration: "4-12 weeks",
            },
            {
              phase: "Testing",
              description: "Quality assurance and user testing",
              duration: "1-2 weeks",
            },
            {
              phase: "Launch",
              description: "Deployment and go-live support",
              duration: "1 week",
            },
            {
              phase: "Support",
              description: "Ongoing maintenance and optimization",
              duration: "Ongoing",
            },
          ],
          methodology:
            "Agile development with weekly sprints and regular client check-ins",
        },
        contact: {
          title: "Get In Touch",
          email: "<EMAIL>",
          phone: "+****************",
          website: "https://soimagine.com",
          consultation: "Free 30-minute consultation available",
          response_time: "We respond within 24 hours",
          availability: "Monday-Friday, 9 AM - 6 PM EST",
        },
      };

      if (infoType === "all") {
        return companyInfo;
      }

      return { [infoType]: companyInfo[infoType] };
    },
  }),

  generateProposal: tool({
    description:
      "Generate a customized project proposal based on client requirements",
    parameters: z.object({
      projectType: z
        .enum([
          "web-development",
          "mobile-app",
          "ui-ux-design",
          "digital-marketing",
          "branding",
          "consulting",
        ])
        .describe("Type of project"),
      budget: z
        .enum(["under-5k", "5k-15k", "15k-50k", "over-50k"])
        .describe("Client budget range"),
      timeline: z
        .enum(["urgent", "standard", "flexible"])
        .describe("Project timeline requirements"),
      requirements: z
        .string()
        .describe("Specific project requirements and goals"),
      companySize: z
        .enum(["startup", "small-business", "medium-business", "enterprise"])
        .optional()
        .describe("Client company size"),
    }),
    execute: async ({
      projectType,
      budget,
      timeline,
      requirements,
      companySize,
    }) => {
      const proposals = {
        "web-development": {
          title: "Custom Web Development Proposal",
          overview:
            "A modern, responsive website built with the latest technologies to drive your business growth.",
          deliverables: [
            "Responsive web design",
            "Content management system",
            "SEO optimization",
            "Performance optimization",
            "Security implementation",
            "Analytics integration",
          ],
          technologies: ["Next.js", "React", "TypeScript", "Tailwind CSS"],
          timeline_estimate:
            timeline === "urgent"
              ? "2-4 weeks"
              : timeline === "standard"
              ? "4-8 weeks"
              : "6-12 weeks",
        },
        "mobile-app": {
          title: "Mobile App Development Proposal",
          overview:
            "Cross-platform mobile application with native performance and user experience.",
          deliverables: [
            "iOS and Android apps",
            "Backend API development",
            "User authentication",
            "Push notifications",
            "App store submission",
            "Analytics integration",
          ],
          technologies: ["React Native", "Node.js", "Firebase"],
          timeline_estimate:
            timeline === "urgent"
              ? "6-10 weeks"
              : timeline === "standard"
              ? "10-16 weeks"
              : "16-24 weeks",
        },
        "ui-ux-design": {
          title: "UI/UX Design Proposal",
          overview:
            "User-centered design solutions that enhance user experience and drive conversions.",
          deliverables: [
            "User research and personas",
            "Wireframing and prototyping",
            "Visual design systems",
            "Usability testing",
            "Accessibility compliance",
            "Design system creation",
          ],
          technologies: ["Figma", "Adobe Creative Suite", "Sketch", "InVision"],
          timeline_estimate:
            timeline === "urgent"
              ? "1-3 weeks"
              : timeline === "standard"
              ? "3-6 weeks"
              : "6-10 weeks",
        },
        "digital-marketing": {
          title: "Digital Marketing Strategy Proposal",
          overview:
            "Comprehensive digital marketing strategies to grow your online presence and drive conversions.",
          deliverables: [
            "SEO optimization strategy",
            "Social media marketing plan",
            "Content marketing calendar",
            "PPC advertising campaigns",
            "Email marketing automation",
            "Analytics and reporting dashboard",
          ],
          technologies: [
            "Google Analytics",
            "Google Ads",
            "Facebook Ads",
            "Mailchimp",
          ],
          timeline_estimate:
            timeline === "urgent"
              ? "2-4 weeks"
              : timeline === "standard"
              ? "4-8 weeks"
              : "8-12 weeks",
        },
        branding: {
          title: "Brand Identity & Design Proposal",
          overview:
            "Complete brand identity solutions from logo design to comprehensive brand guidelines.",
          deliverables: [
            "Logo design and variations",
            "Brand color palette",
            "Typography selection",
            "Brand guidelines document",
            "Business card design",
            "Marketing materials templates",
          ],
          technologies: ["Adobe Illustrator", "Adobe Photoshop", "Figma"],
          timeline_estimate:
            timeline === "urgent"
              ? "1-3 weeks"
              : timeline === "standard"
              ? "3-6 weeks"
              : "6-10 weeks",
        },
        consulting: {
          title: "Digital Strategy Consulting Proposal",
          overview:
            "Strategic guidance for digital transformation and technology decisions to optimize your business operations.",
          deliverables: [
            "Technology stack recommendations",
            "Digital transformation roadmap",
            "Performance optimization plan",
            "Security assessment report",
            "Team training workshops",
            "Ongoing support strategy",
          ],
          technologies: ["Various based on needs"],
          timeline_estimate:
            timeline === "urgent"
              ? "1-2 weeks"
              : timeline === "standard"
              ? "2-4 weeks"
              : "4-8 weeks",
        },
      };

      const budgetRecommendations = {
        "under-5k": "Recommended for MVP or basic implementations",
        "5k-15k": "Ideal for standard business solutions",
        "15k-50k": "Perfect for comprehensive, feature-rich applications",
        "over-50k": "Suitable for enterprise-level, complex solutions",
      };

      const proposal = proposals[projectType] || proposals["web-development"];

      return {
        proposal: {
          ...proposal,
          requirements_analysis: requirements,
          budget_range: budget,
          budget_recommendation: budgetRecommendations[budget],
          company_fit: companySize
            ? `Tailored for ${companySize} needs`
            : "Customized solution",
          next_steps: [
            "Schedule a detailed consultation call",
            "Finalize project scope and requirements",
            "Sign project agreement",
            "Begin discovery phase",
            "Start development process",
          ],
          included_services: [
            "Project management",
            "Regular progress updates",
            "Quality assurance testing",
            "Documentation",
            "30 days post-launch support",
          ],
        },
        call_to_action:
          "Ready to get started? Schedule your free consultation today!",
      };
    },
  }),
};

export async function POST(req: Request) {
  // Calculate credit cost based on request
  const { messages } = await req.json();
  const messageCount = messages?.length || 0;
  const hasToolCalls =
    messages?.some((msg: any) => msg.toolInvocations?.length > 0) || false;
  const creditsRequired = calculateCreditCost(
    messageCount,
    "consultant",
    hasToolCalls
  );

  return withCreditCheck(
    req as any,
    {
      creditsRequired,
      description: `Consultant AI chat (${messageCount} messages)`,
      source: "ai_chat",
      assistantType: "consultant",
    },
    async (req) => {
      try {
        console.log(
          "Consultant API called with messages:",
          messageCount,
          "messages, credits required:",
          creditsRequired
        );

        // Check if OpenAI API key is available
        if (!process.env.OPENAI_API_KEY) {
          console.error("OPENAI_API_KEY not found in environment variables");
          return NextResponse.json(
            { error: "OpenAI API key not configured" },
            { status: 500 }
          );
        }

        const result = streamText({
          model: openai("gpt-4o-mini"),
          messages,
          system: `You are a Consultant AI for Soimagine Digital Agency. Your role is to:

1. **Provide Service Information**: Help potential clients understand our services, packages, and capabilities
2. **Company Representation**: Share information about our company, team, values, and approach
3. **Generate Proposals**: Create customized project proposals based on client needs
4. **Business Consultation**: Offer strategic advice for digital transformation and technology decisions

**Communication Style**:
- Professional yet approachable
- Focus on business value and ROI
- Ask clarifying questions to understand client needs
- Provide specific, actionable recommendations
- Always include next steps and clear calls to action

**Key Services to Highlight**:
- Web Development (Next.js, React, TypeScript)
- Mobile App Development (React Native, Flutter)
- UI/UX Design (Figma, Design Systems)
- Digital Marketing (SEO, PPC, Social Media)
- Brand Identity & Design
- Digital Strategy Consulting

**Always**:
- Use the available tools to provide accurate, up-to-date information
- Tailor responses to the client's specific industry and needs
- Mention relevant case studies and success stories when appropriate
- Offer free consultations and next steps
- Be transparent about timelines and pricing

Remember: You're representing a premium digital agency that delivers exceptional results. Focus on value, quality, and long-term partnerships.`,
          tools: consultantTools,
          maxSteps: 5,
        });

        console.log("Streaming response created successfully");
        const response = result.toDataStreamResponse();

        // Convert Response to NextResponse to satisfy the middleware type requirement
        return new NextResponse(response.body, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        });
      } catch (error: any) {
        console.error("Consultant API error:", error);
        return NextResponse.json(
          { error: `Error processing consultant request: ${error.message}` },
          { status: 500 }
        );
      }
    }
  );
}

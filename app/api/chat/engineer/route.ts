import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { engineerTools } from '@/lib/ai-engines/engineer-ai';

export const runtime = 'nodejs';
export const maxDuration = 60;

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    // Validate messages
    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 });
    }

    console.log('Engineer AI API called with messages:', messages.length);

    // Production-grade configuration for Engineer AI
    const result = streamText({
      model: openai('gpt-4o-mini'),
      messages,
      tools: engineerTools,
      maxSteps: 5,
      temperature: 0.1, // Lower temperature for more consistent code generation
      maxTokens: 4000,
      system: `You are an expert software engineer AI assistant. You help users:

1. **Generate Code Prototypes**: Create working, runnable code in various frameworks
2. **Modify Code**: Make intelligent changes to existing code with proper explanations
3. **Explain Code**: Provide detailed, educational explanations of code functionality
4. **Debug Code**: Identify issues and provide fixes with prevention tips

**Guidelines:**
- Always prioritize security and best practices
- Generate production-ready, well-structured code
- Provide comprehensive explanations and documentation
- Include error handling and validation
- Use modern syntax and patterns
- Consider performance and accessibility
- Suggest improvements and optimizations

**Code Generation Rules:**
- Use TypeScript when possible for better type safety
- Include proper imports and dependencies
- Add meaningful comments and documentation
- Follow framework-specific best practices
- Implement responsive design principles
- Include basic error boundaries and validation

**Security:**
- Never generate code with security vulnerabilities
- Sanitize all user inputs
- Avoid eval() and dangerous patterns
- Use secure coding practices
- Validate all data and parameters

When using tools, always explain what you're doing and why. Provide context about the generated code, its structure, and how to use it effectively.`,
      onFinish: (result) => {
        // Log for monitoring and analytics
        console.log('Engineer AI completed:', {
          usage: result.usage,
          finishReason: result.finishReason,
          toolCalls: result.toolCalls?.length || 0,
          timestamp: new Date().toISOString()
        });
      }
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Engineer AI API error:', error);
    
    // Return appropriate error response
    if (error instanceof Error) {
      return new Response(
        JSON.stringify({ 
          error: 'Internal Server Error', 
          message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong' 
        }), 
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    return new Response('Internal Server Error', { status: 500 });
  }
}

import { NextRequest } from 'next/server'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

// Route requests to specific assistant types
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { messages, assistantType = 'consultant' } = body

    console.log('Main chat API called:', {
      assistantType,
      messageCount: messages?.length || 0,
      url: req.url
    })

    // Route to specific assistant API based on type
    const assistantRoutes = {
      consultant: '/api/chat/consultant',
      engineer: '/api/chat/engineer',
      executive: '/api/chat/executive'
    }

    const targetRoute = assistantRoutes[assistantType as keyof typeof assistantRoutes] || assistantRoutes.consultant

    // Get the base URL from the request
    const baseUrl = new URL(req.url).origin
    const fullUrl = `${baseUrl}${targetRoute}`

    console.log('Forwarding to:', fullUrl)

    // Forward the request to the specific assistant API
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ messages })
    })

    console.log('Assistant API response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Assistant API error response:', errorText)
      throw new Error(`Assistant API responded with status: ${response.status} - ${errorText}`)
    }

    // Return the streaming response with proper headers
    return new Response(response.body, {
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })
  } catch (error) {
    console.error('Chat routing error:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return new Response(`Error routing to assistant: ${errorMessage}`, { status: 500 })
  }
}
import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const registerSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  password: z.string().min(6).max(100),
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  company: z.string().max(100).optional(),
  phone: z.string().max(20).optional()
})

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const validatedData = registerSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Import required functions
    const { generateVerificationToken } = await import('@/lib/tokens')
    const { sendVerificationEmail } = await import('@/lib/email')

    // Create user with initial credit balance in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          name: validatedData.name,
          email: validatedData.email,
          password: hashedPassword,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          company: validatedData.company,
          phone: validatedData.phone,
          isVerified: false
        }
      })

      // Generate verification token
      const verificationToken = await generateVerificationToken(user.email)

      // Send verification email
      await sendVerificationEmail(
        user.email,
        user.name || "User",
        verificationToken
      )

      // Create initial credit balance
      const creditBalance = await tx.creditBalance.create({
        data: {
          userId: user.id,
          totalCredits: 100, // Welcome bonus
          dailyCredits: 100,
          monthlyCredits: 100,
          dailyLimit: 100,
          monthlyLimit: 1000
        }
      })

      // Record welcome bonus transaction
      await tx.creditTransaction.create({
        data: {
          userId: user.id,
          type: 'BONUS',
          amount: 100,
          description: 'Welcome bonus for new user',
          source: 'welcome_bonus',
          balanceAfter: 100
        }
      })

      // Create initial user progress
      await tx.userProgress.create({
        data: {
          userId: user.id,
          totalXp: 0,
          currentLevel: 1
        }
      })

      return { user, creditBalance }
    })

    // Return user data (without password)
    return NextResponse.json({
      success: true,
      message: "Registration successful! Please check your email to verify your account.",
      user: {
        id: result.user.id,
        name: result.user.name,
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        company: result.user.company,
        phone: result.user.phone,
        createdAt: result.user.createdAt
      },
      credits: {
        totalCredits: result.creditBalance.totalCredits,
        dailyCredits: result.creditBalance.dailyCredits,
        monthlyCredits: result.creditBalance.monthlyCredits
      }
    }, { status: 201 })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { sendVerificationEmail } from "@/lib/email"
import { generateVerificationToken } from "@/lib/tokens"

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    if (user.emailVerified) {
      return NextResponse.json(
        { error: "Email already verified" },
        { status: 400 }
      )
    }

    // Check if there's a recent verification token to prevent spam
    const recentToken = await prisma.verificationToken.findFirst({
      where: {
        identifier: user.email,
        expires: {
          gt: new Date(Date.now() - 5 * 60 * 1000) // Token created in last 5 minutes
        }
      }
    })

    if (recentToken) {
      return NextResponse.json(
        { error: "Please wait 5 minutes before requesting another verification email" },
        { status: 429 }
      )
    }

    // Delete any existing verification tokens for this user
    await prisma.verificationToken.deleteMany({
      where: { identifier: user.email },
    })

    // Generate new verification token
    const token = await generateVerificationToken(user.email)
    
    // Send verification email
    await sendVerificationEmail(
      user.email,
      user.name || "User",
      token
    )

    return NextResponse.json({
      success: true,
      message: "Verification email sent",
    })
  } catch (error) {
    console.error("Resend verification error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

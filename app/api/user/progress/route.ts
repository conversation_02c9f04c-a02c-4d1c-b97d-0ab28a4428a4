import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get or create user progress
    let userProgress = await prisma.userProgress.findUnique({
      where: { userId: session.user.id }
    })

    if (!userProgress) {
      // Create initial user progress
      userProgress = await prisma.userProgress.create({
        data: {
          userId: session.user.id,
          totalXp: 0,
          currentLevel: 1,
          challengesCompleted: 0,
          challengesAttempted: 0,
          marketingXp: 0,
          socialXp: 0,
          contentXp: 0,
          referralXp: 0,
          currentStreak: 0,
          longestStreak: 0
        }
      })
    }

    // Calculate current level based on XP
    const calculatedLevel = Math.floor(Math.sqrt(userProgress.totalXp / 100)) + 1
    
    // Update level if it has changed
    if (calculatedLevel !== userProgress.currentLevel) {
      userProgress = await prisma.userProgress.update({
        where: { userId: session.user.id },
        data: { currentLevel: calculatedLevel }
      })
    }

    // Get additional stats
    const [recentCompletions, totalAchievements, unlockedAchievements] = await Promise.all([
      // Recent challenge completions (last 7 days)
      prisma.challengeCompletion.count({
        where: {
          userId: session.user.id,
          completedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          },
          status: 'COMPLETED'
        }
      }),
      
      // Total available achievements
      prisma.achievement.count({
        where: { isActive: true }
      }),
      
      // User's unlocked achievements
      prisma.userAchievement.count({
        where: {
          userId: session.user.id,
          isCompleted: true
        }
      })
    ])

    // Calculate streak (simplified - based on daily activity)
    const today = new Date()
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    
    const hasActivityToday = await prisma.challengeCompletion.findFirst({
      where: {
        userId: session.user.id,
        completedAt: {
          gte: new Date(today.getFullYear(), today.getMonth(), today.getDate())
        }
      }
    })

    const hasActivityYesterday = await prisma.challengeCompletion.findFirst({
      where: {
        userId: session.user.id,
        completedAt: {
          gte: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate()),
          lt: new Date(today.getFullYear(), today.getMonth(), today.getDate())
        }
      }
    })

    // Update streak logic (simplified)
    let currentStreak = userProgress.currentStreak
    if (hasActivityToday) {
      if (!userProgress.lastActivityDate || 
          userProgress.lastActivityDate < new Date(today.getFullYear(), today.getMonth(), today.getDate())) {
        // First activity today
        if (hasActivityYesterday || userProgress.currentStreak === 0) {
          currentStreak = userProgress.currentStreak + 1
        } else {
          currentStreak = 1 // Reset streak if no activity yesterday
        }
        
        // Update progress with new streak
        await prisma.userProgress.update({
          where: { userId: session.user.id },
          data: {
            currentStreak,
            longestStreak: Math.max(currentStreak, userProgress.longestStreak),
            lastActivityDate: new Date()
          }
        })
      }
    }

    // Calculate level progress
    const currentLevelXp = ((calculatedLevel - 1) * (calculatedLevel - 1)) * 100
    const nextLevelXp = (calculatedLevel * calculatedLevel) * 100
    const progressToNextLevel = Math.min(
      ((userProgress.totalXp - currentLevelXp) / (nextLevelXp - currentLevelXp)) * 100,
      100
    )

    return NextResponse.json({
      progress: {
        ...userProgress,
        currentLevel: calculatedLevel,
        currentStreak
      },
      stats: {
        recentCompletions,
        totalAchievements,
        unlockedAchievements,
        achievementProgress: totalAchievements > 0 ? (unlockedAchievements / totalAchievements) * 100 : 0
      },
      levelInfo: {
        currentLevel: calculatedLevel,
        currentLevelXp,
        nextLevelXp,
        progressToNextLevel,
        xpToNextLevel: nextLevelXp - userProgress.totalXp
      }
    })

  } catch (error) {
    console.error('Error fetching user progress:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { xai } from '@ai-sdk/xai'
import { streamText } from 'ai'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { messages } = await req.json()

    const result = streamText({
      model: xai('grok-beta'),
      messages,
      system: 'You are a helpful AI assistant for Soimagine Digital Agency. Be helpful and professional.',
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('Chat API error:', error)
    return new Response('Error processing request', { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get or create credit balance
    let creditBalance = await prisma.creditBalance.findUnique({
      where: { userId: session.user.id }
    })

    if (!creditBalance) {
      // Create initial credit balance for new user
      creditBalance = await prisma.creditBalance.create({
        data: {
          userId: session.user.id,
          totalCredits: 100, // Welcome bonus
          dailyCredits: 100,
          monthlyCredits: 100,
          dailyLimit: 100,
          monthlyLimit: 1000
        }
      })

      // Record the welcome bonus transaction
      await prisma.creditTransaction.create({
        data: {
          userId: session.user.id,
          type: 'BONUS',
          amount: 100,
          description: 'Welcome bonus',
          source: 'welcome_bonus',
          balanceAfter: 100
        }
      })
    }

    // Check if daily/monthly reset is needed
    const now = new Date()
    const lastDailyReset = new Date(creditBalance.lastDailyReset)
    const lastMonthlyReset = new Date(creditBalance.lastMonthlyReset)

    let needsUpdate = false
    const updates: any = {}

    // Check daily reset (if it's a new day)
    if (now.getDate() !== lastDailyReset.getDate() || 
        now.getMonth() !== lastDailyReset.getMonth() || 
        now.getFullYear() !== lastDailyReset.getFullYear()) {
      updates.dailyCredits = creditBalance.dailyLimit
      updates.lastDailyReset = now
      needsUpdate = true
    }

    // Check monthly reset (if it's a new month)
    if (now.getMonth() !== lastMonthlyReset.getMonth() || 
        now.getFullYear() !== lastMonthlyReset.getFullYear()) {
      updates.monthlyCredits = creditBalance.monthlyLimit
      updates.lastMonthlyReset = now
      needsUpdate = true
    }

    if (needsUpdate) {
      creditBalance = await prisma.creditBalance.update({
        where: { userId: session.user.id },
        data: updates
      })
    }

    return NextResponse.json({
      totalCredits: creditBalance.totalCredits,
      dailyCredits: creditBalance.dailyCredits,
      monthlyCredits: creditBalance.monthlyCredits,
      dailyLimit: creditBalance.dailyLimit,
      monthlyLimit: creditBalance.monthlyLimit,
      lastDailyReset: creditBalance.lastDailyReset,
      lastMonthlyReset: creditBalance.lastMonthlyReset
    })

  } catch (error) {
    console.error('Error fetching credit balance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

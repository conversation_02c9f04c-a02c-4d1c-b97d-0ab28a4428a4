import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const transactionSchema = z.object({
  type: z.enum(['EARNED', 'SPENT', 'BONUS', 'PENALTY', 'REFUND', 'ADMIN_ADJUSTMENT']),
  amount: z.number().int().positive(),
  description: z.string().min(1),
  source: z.string().optional(),
  sourceId: z.string().optional(),
  metadata: z.any().optional()
})

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const validatedData = transactionSchema.parse(body)

    // Get current credit balance
    const creditBalance = await prisma.creditBalance.findUnique({
      where: { userId: session.user.id }
    })

    if (!creditBalance) {
      return NextResponse.json(
        { error: 'Credit balance not found' },
        { status: 404 }
      )
    }

    // Calculate new balance based on transaction type
    let newTotalCredits = creditBalance.totalCredits
    let newDailyCredits = creditBalance.dailyCredits
    let newMonthlyCredits = creditBalance.monthlyCredits

    if (validatedData.type === 'SPENT') {
      // Check if user has enough credits
      if (creditBalance.totalCredits < validatedData.amount) {
        return NextResponse.json(
          { error: 'Insufficient credits' },
          { status: 400 }
        )
      }
      
      // Check daily limit
      if (creditBalance.dailyCredits < validatedData.amount) {
        return NextResponse.json(
          { error: 'Daily credit limit exceeded' },
          { status: 400 }
        )
      }

      // Check monthly limit
      if (creditBalance.monthlyCredits < validatedData.amount) {
        return NextResponse.json(
          { error: 'Monthly credit limit exceeded' },
          { status: 400 }
        )
      }

      newTotalCredits -= validatedData.amount
      newDailyCredits -= validatedData.amount
      newMonthlyCredits -= validatedData.amount
    } else if (['EARNED', 'BONUS', 'REFUND', 'ADMIN_ADJUSTMENT'].includes(validatedData.type)) {
      newTotalCredits += validatedData.amount
      // Don't add to daily/monthly limits for earned credits
    } else if (validatedData.type === 'PENALTY') {
      newTotalCredits = Math.max(0, newTotalCredits - validatedData.amount)
    }

    // Use transaction to ensure consistency
    const result = await prisma.$transaction(async (tx) => {
      // Update credit balance
      const updatedBalance = await tx.creditBalance.update({
        where: { userId: session.user.id },
        data: {
          totalCredits: newTotalCredits,
          dailyCredits: newDailyCredits,
          monthlyCredits: newMonthlyCredits
        }
      })

      // Create transaction record
      const transaction = await tx.creditTransaction.create({
        data: {
          userId: session.user.id,
          type: validatedData.type,
          amount: validatedData.amount,
          description: validatedData.description,
          source: validatedData.source,
          sourceId: validatedData.sourceId,
          metadata: validatedData.metadata,
          balanceAfter: newTotalCredits
        }
      })

      return { balance: updatedBalance, transaction }
    })

    return NextResponse.json({
      success: true,
      transaction: result.transaction,
      balance: {
        totalCredits: result.balance.totalCredits,
        dailyCredits: result.balance.dailyCredits,
        monthlyCredits: result.balance.monthlyCredits
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error processing credit transaction:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-here"

# OpenAI API
OPENAI_API_KEY="your-openai-api-key-here"

# Optional: XAI API (if using Grok)
XAI_API_KEY="your-xai-api-key-here"

# Optional: Email service (for verification emails)
EMAIL_SERVER_HOST="smtp.example.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-email-password"
EMAIL_FROM="<EMAIL>"

# Optional: Social media API keys (for content tracking)
TWITTER_API_KEY="your-twitter-api-key"
TWITTER_API_SECRET="your-twitter-api-secret"
FACEBOOK_APP_ID="your-facebook-app-id"
FACEBOOK_APP_SECRET="your-facebook-app-secret"
INSTAGRAM_ACCESS_TOKEN="your-instagram-access-token"
LINKEDIN_CLIENT_ID="your-linkedin-client-id"
LINKEDIN_CLIENT_SECRET="your-linkedin-client-secret"

# Optional: Analytics
GOOGLE_ANALYTICS_ID="your-ga-id"

# Optional: File storage (for media uploads)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-s3-bucket"

# Optional: Redis (for caching and rate limiting)
REDIS_URL="redis://localhost:6379"

# Application settings
NODE_ENV="development"
APP_URL="http://localhost:3000"
APP_NAME="Soimagine Marketing Platform"

import { PrismaClient } from '@prisma/client'
import { challengeTemplates } from '../lib/challenge-templates'

const prisma = new PrismaClient()

async function seedChallenges() {
  console.log('🌱 Seeding challenges...')
  
  for (const template of challengeTemplates) {
    try {
      await prisma.challenge.upsert({
        where: { id: template.id },
        update: {
          title: template.title,
          description: template.description,
          instructions: template.instructions,
          type: template.type as any,
          category: template.category as any,
          difficulty: template.difficulty as any,
          creditReward: template.creditReward,
          xpReward: template.xpReward,
          timeLimit: template.timeLimit,
          requirements: template.requirements,
          isActive: true,
          isPublic: true
        },
        create: {
          id: template.id,
          title: template.title,
          description: template.description,
          instructions: template.instructions,
          type: template.type as any,
          category: template.category as any,
          difficulty: template.difficulty as any,
          creditReward: template.creditReward,
          xpReward: template.xpReward,
          timeLimit: template.timeLimit,
          requirements: template.requirements,
          isActive: true,
          isPublic: true,
          generatedBy: 'seed_script'
        }
      })
      console.log(`✅ Created/updated challenge: ${template.title}`)
    } catch (error) {
      console.error(`❌ Failed to create challenge ${template.title}:`, error)
    }
  }
}

async function seedAchievements() {
  console.log('🏆 Seeding achievements...')
  
  const achievements = [
    {
      id: 'first-steps',
      name: 'First Steps',
      description: 'Complete your first challenge',
      icon: '🎯',
      type: 'MILESTONE',
      category: 'PROGRESS',
      rarity: 'COMMON',
      requirements: { challengesCompleted: 1 },
      creditReward: 25,
      xpReward: 50
    },
    {
      id: 'social-butterfly',
      name: 'Social Butterfly',
      description: 'Share content on 5 different social platforms',
      icon: '🦋',
      type: 'SOCIAL',
      category: 'SOCIAL',
      rarity: 'UNCOMMON',
      requirements: { socialShares: 5, platforms: 5 },
      creditReward: 50,
      xpReward: 100
    },
    {
      id: 'streak-master',
      name: 'Streak Master',
      description: 'Maintain a 10-day activity streak',
      icon: '🔥',
      type: 'STREAK',
      category: 'PROGRESS',
      rarity: 'RARE',
      requirements: { streak: 10 },
      creditReward: 100,
      xpReward: 250
    },
    {
      id: 'content-creator',
      name: 'Content Creator',
      description: 'Create 10 pieces of marketing content',
      icon: '✍️',
      type: 'MILESTONE',
      category: 'CONTENT',
      rarity: 'UNCOMMON',
      requirements: { contentCreated: 10 },
      creditReward: 75,
      xpReward: 150
    },
    {
      id: 'referral-champion',
      name: 'Referral Champion',
      description: 'Successfully refer 5 new members',
      icon: '👥',
      type: 'REFERRAL',
      category: 'REFERRALS',
      rarity: 'RARE',
      requirements: { referrals: 5 },
      creditReward: 200,
      xpReward: 400
    },
    {
      id: 'level-up-5',
      name: 'Rising Star',
      description: 'Reach Level 5',
      icon: '⭐',
      type: 'MILESTONE',
      category: 'PROGRESS',
      rarity: 'UNCOMMON',
      requirements: { level: 5 },
      creditReward: 100,
      xpReward: 200
    },
    {
      id: 'level-up-10',
      name: 'Marketing Pro',
      description: 'Reach Level 10',
      icon: '💼',
      type: 'MILESTONE',
      category: 'PROGRESS',
      rarity: 'RARE',
      requirements: { level: 10 },
      creditReward: 250,
      xpReward: 500
    },
    {
      id: 'challenge-master',
      name: 'Challenge Master',
      description: 'Complete 25 challenges',
      icon: '🏆',
      type: 'MILESTONE',
      category: 'PROGRESS',
      rarity: 'RARE',
      requirements: { challengesCompleted: 25 },
      creditReward: 150,
      xpReward: 300
    },
    {
      id: 'community-leader',
      name: 'Community Leader',
      description: 'Help 10 community members with their challenges',
      icon: '👑',
      type: 'ENGAGEMENT',
      category: 'COMMUNITY',
      rarity: 'EPIC',
      requirements: { helpedMembers: 10 },
      creditReward: 300,
      xpReward: 600
    },
    {
      id: 'legendary-marketer',
      name: 'Legendary Marketer',
      description: 'Reach Level 20 and complete 100 challenges',
      icon: '🌟',
      type: 'SPECIAL',
      category: 'SPECIAL',
      rarity: 'LEGENDARY',
      requirements: { level: 20, challengesCompleted: 100 },
      creditReward: 500,
      xpReward: 1000
    }
  ]

  for (const achievement of achievements) {
    try {
      await prisma.achievement.upsert({
        where: { name: achievement.name },
        update: {
          description: achievement.description,
          icon: achievement.icon,
          type: achievement.type as any,
          category: achievement.category as any,
          rarity: achievement.rarity as any,
          requirements: achievement.requirements,
          creditReward: achievement.creditReward,
          xpReward: achievement.xpReward,
          isActive: true,
          isSecret: false
        },
        create: {
          name: achievement.name,
          description: achievement.description,
          icon: achievement.icon,
          type: achievement.type as any,
          category: achievement.category as any,
          rarity: achievement.rarity as any,
          requirements: achievement.requirements,
          creditReward: achievement.creditReward,
          xpReward: achievement.xpReward,
          isActive: true,
          isSecret: false
        }
      })
      console.log(`✅ Created/updated achievement: ${achievement.name}`)
    } catch (error) {
      console.error(`❌ Failed to create achievement ${achievement.name}:`, error)
    }
  }
}

async function createAdminUser() {
  console.log('👤 Creating admin user...')
  
  try {
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        role: 'ADMIN'
      },
      create: {
        email: '<EMAIL>',
        name: 'Soimagine Admin',
        role: 'ADMIN',
        isActive: true,
        isVerified: true
      }
    })

    // Create credit balance for admin
    await prisma.creditBalance.upsert({
      where: { userId: adminUser.id },
      update: {
        totalCredits: 10000,
        dailyCredits: 1000,
        monthlyCredits: 10000,
        dailyLimit: 1000,
        monthlyLimit: 10000
      },
      create: {
        userId: adminUser.id,
        totalCredits: 10000,
        dailyCredits: 1000,
        monthlyCredits: 10000,
        dailyLimit: 1000,
        monthlyLimit: 10000
      }
    })

    // Create user progress for admin
    await prisma.userProgress.upsert({
      where: { userId: adminUser.id },
      update: {
        totalXp: 1000,
        currentLevel: 5
      },
      create: {
        userId: adminUser.id,
        totalXp: 1000,
        currentLevel: 5
      }
    })

    console.log('✅ Admin user created/updated')
  } catch (error) {
    console.error('❌ Failed to create admin user:', error)
  }
}

async function main() {
  console.log('🚀 Starting database seeding...')
  
  try {
    await createAdminUser()
    await seedAchievements()
    await seedChallenges()
    
    console.log('✅ Database seeding completed successfully!')
  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeding
if (require.main === module) {
  main()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}

export { main as seedDatabase }

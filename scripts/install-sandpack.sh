#!/bin/bash

# Install Sandpack for Engineer AI
# This script installs the required Sandpack dependencies for live code preview

echo "🚀 Installing Sandpack dependencies for Engineer AI..."

# Install Sandpack React components
npm install @codesandbox/sandpack-react

# Install additional dependencies for enhanced functionality
npm install @codesandbox/sandpack-client

# Install development dependencies for testing
npm install --save-dev @types/codesandbox__sandpack-react

echo "✅ Sandpack installation complete!"
echo ""
echo "📝 Next steps:"
echo "1. Update SandpackRenderer.tsx to use real Sandpack components"
echo "2. Replace mock components with actual Sandpack imports"
echo "3. Test the Engineer AI with live code preview"
echo ""
echo "🔧 To enable real Sandpack integration:"
echo "1. Remove mock components from SandpackRenderer.tsx"
echo "2. Import actual components from @codesandbox/sandpack-react"
echo "3. Update component props and configuration"
echo ""
echo "📚 Documentation: https://sandpack.codesandbox.io/docs"

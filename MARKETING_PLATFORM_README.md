# Soimagine Marketing Platform

A comprehensive backend system for Soimagine Systems' marketing platform featuring credit management, gamification, and AI-powered challenge generation.

## 🚀 Features

### Core System Architecture
- **Multi-agent chat assistant interface** - Based on existing v0-interface.tsx
- **Credit-based usage system** - Configurable daily and monthly limits
- **Gamification engine** - Community-driven marketing with achievements
- **Challenge generation AI agent** - Learning capabilities for optimal engagement

### Backend Components

#### 1. Credit Management System
- ✅ User credit balance tracking (daily/monthly quotas)
- ✅ Credit transaction logging and history
- ✅ Rate limiting and usage enforcement
- ✅ Credit reward distribution for completed tasks
- ✅ Automatic daily/monthly reset functionality

#### 2. Gamification Backend
- ✅ Task/challenge creation and management system
- ✅ User progress tracking and achievement system
- ✅ Leaderboards and community engagement metrics
- ✅ Reward calculation and distribution logic
- ✅ XP and level progression system

#### 3. Content Marketing Automation
- ✅ Social media sharing task generation with referral link injection
- ✅ Media watermarking and fingerprinting service
- ✅ Content tracking across social platforms
- ✅ Viral coefficient and engagement analytics

#### 4. Challenge Generation Agent
- ✅ AI agent specifically for creating marketing challenges
- ✅ Learning system to analyze challenge performance
- ✅ Dynamic difficulty and engagement optimization
- ✅ Community behavior pattern analysis

#### 5. API Infrastructure
- ✅ RESTful endpoints for all system components
- ✅ Authentication and authorization middleware
- ✅ Database schema design for users, credits, challenges, and analytics
- ✅ Integration with existing v0-interface chat system

## 🛠 Technical Stack

- **Framework**: Next.js 15 with App Router
- **Database**: SQLite (development) / PostgreSQL (production) with Prisma ORM
- **Authentication**: NextAuth.js with credential provider
- **AI Integration**: OpenAI GPT-4o-mini via AI SDK
- **UI Components**: Shadcn/ui with Tailwind CSS
- **Type Safety**: TypeScript with Zod validation
- **Animation**: Framer Motion

## 📁 Project Structure

```
├── app/
│   ├── api/
│   │   ├── auth/
│   │   │   ├── [...nextauth]/route.ts    # NextAuth configuration
│   │   │   └── register/route.ts         # User registration
│   │   ├── credits/
│   │   │   ├── balance/route.ts          # Credit balance management
│   │   │   ├── transaction/route.ts      # Credit transactions
│   │   │   └── history/route.ts          # Transaction history
│   │   ├── challenges/
│   │   │   ├── list/route.ts             # Challenge listing
│   │   │   ├── complete/route.ts         # Challenge completion
│   │   │   └── generate/route.ts         # AI challenge generation
│   │   ├── chat/
│   │   │   └── consultant/route.ts       # Enhanced with credit system
│   │   └── leaderboard/route.ts          # Community leaderboards
│   └── ...
├── components/
│   ├── credits/
│   │   └── credit-display.tsx            # Credit balance UI
│   ├── challenges/
│   │   └── challenge-card.tsx            # Challenge UI component
│   └── ...
├── hooks/
│   ├── use-credits.ts                    # Credit management hook
│   └── use-challenges.ts                 # Challenge management hook
├── lib/
│   ├── prisma.ts                         # Database client
│   ├── auth.ts                           # NextAuth configuration
│   ├── credit-middleware.ts              # Credit validation middleware
│   └── types/marketing-platform.ts      # TypeScript interfaces
├── prisma/
│   └── schema.prisma                     # Database schema
└── ...
```

## 🗄 Database Schema

### Core Models
- **User** - Authentication and profile data
- **CreditBalance** - Credit tracking with daily/monthly limits
- **CreditTransaction** - Transaction history and audit trail
- **Challenge** - Marketing challenges with AI generation metadata
- **ChallengeCompletion** - User challenge progress and rewards
- **UserProgress** - XP, levels, and achievement tracking
- **Achievement** - Gamification achievements and badges
- **SocialShare** - Social media sharing tracking
- **Referral** - Referral system with conversion tracking
- **ApiUsage** - API usage analytics and monitoring

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm
- OpenAI API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd soimagine-digital-agency
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Start the development server**
   ```bash
   pnpm dev
   ```

### Environment Variables

Required:
- `OPENAI_API_KEY` - OpenAI API key for AI features
- `NEXTAUTH_SECRET` - Secret for NextAuth.js sessions
- `DATABASE_URL` - Database connection string

Optional:
- Social media API keys for content tracking
- Email service configuration
- Analytics and monitoring tools

## 📊 API Endpoints

### Credit Management
- `GET /api/credits/balance` - Get user credit balance
- `POST /api/credits/transaction` - Record credit transaction
- `GET /api/credits/history` - Get transaction history

### Challenge System
- `GET /api/challenges/list` - List available challenges
- `POST /api/challenges/complete` - Complete a challenge
- `POST /api/challenges/generate` - Generate new challenge (Admin)

### Gamification
- `GET /api/leaderboard` - Get community leaderboards
- `GET /api/user/progress` - Get user progress and achievements

### Enhanced Chat
- `POST /api/chat/consultant` - AI consultant with credit deduction
- `POST /api/chat/engineer` - AI engineer with credit deduction
- `POST /api/chat/executive` - AI executive with credit deduction

## 🎮 Gamification Features

### Credit System
- **Welcome Bonus**: 100 credits for new users
- **Daily Limits**: Configurable daily credit allowance
- **Monthly Limits**: Configurable monthly credit allowance
- **Automatic Reset**: Daily and monthly limits reset automatically

### Challenge Types
- **Social Share**: Share content on social platforms
- **Content Creation**: Create marketing content
- **Referral**: Invite new users to the platform
- **Engagement**: Interact with community content
- **Learning**: Complete educational modules
- **Community**: Participate in community activities

### Reward System
- **Credits**: Used for AI assistant interactions
- **XP Points**: Experience points for leveling up
- **Achievements**: Unlockable badges and milestones
- **Leaderboards**: Community rankings and competition

## 🤖 AI Challenge Generation

The platform includes an AI agent specifically designed to create engaging marketing challenges:

### Features
- **Performance Learning**: Analyzes past challenge completion rates
- **Dynamic Difficulty**: Adjusts challenge difficulty based on user skill
- **Trend Integration**: Incorporates current marketing trends
- **Brand Promotion**: Subtly promotes Soimagine's services

### Generation Parameters
- Category (Marketing, Social Media, Content, etc.)
- Difficulty level (Beginner to Expert)
- Target audience
- Theme and context
- Reward structure

## 🔒 Security Features

- **Authentication**: Secure user authentication with NextAuth.js
- **Authorization**: Role-based access control
- **Rate Limiting**: Credit-based usage limits
- **Input Validation**: Zod schema validation
- **SQL Injection Protection**: Prisma ORM with parameterized queries
- **CSRF Protection**: Built-in Next.js CSRF protection

## 📈 Analytics & Monitoring

- **API Usage Tracking**: Monitor all API calls and performance
- **Credit Usage Analytics**: Track credit consumption patterns
- **Challenge Performance**: Analyze challenge completion rates
- **User Engagement**: Monitor user activity and retention
- **Error Logging**: Comprehensive error tracking and reporting

## 🚀 Deployment

### Production Setup
1. Set up PostgreSQL database
2. Configure environment variables for production
3. Run database migrations: `npx prisma migrate deploy`
4. Build the application: `pnpm build`
5. Start the production server: `pnpm start`

### Recommended Infrastructure
- **Database**: PostgreSQL with connection pooling
- **Caching**: Redis for session storage and rate limiting
- **File Storage**: AWS S3 for media uploads
- **Monitoring**: Application performance monitoring
- **CDN**: Content delivery network for static assets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is proprietary to Soimagine Digital Agency.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**Built with ❤️ by Soimagine Digital Agency**

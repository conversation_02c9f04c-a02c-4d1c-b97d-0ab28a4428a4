"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, ExternalLink } from "lucide-react"
import Link from "next/link"

interface Project {
  id: string
  title: string
  description: string
  image: string
  tags: string[]
  link?: string
}

export function PortfolioGrid() {
  const [filter, setFilter] = useState<string | null>(null)

  const projects: Project[] = [
    {
      id: "1",
      title: "E-commerce Platform",
      description:
        "A comprehensive e-commerce solution with product management, cart functionality, and payment integration.",
      image: "/placeholder.svg?height=600&width=800",
      tags: ["Web Development", "E-commerce", "React"],
      link: "#",
    },
    {
      id: "2",
      title: "Healthcare Mobile App",
      description: "A mobile application for healthcare providers to manage patient records and appointments.",
      image: "/placeholder.svg?height=600&width=800",
      tags: ["Mobile App", "Healthcare", "React Native"],
      link: "#",
    },
    {
      id: "3",
      title: "AI-Powered Analytics Dashboard",
      description: "An analytics dashboard with AI-driven insights for business intelligence and decision making.",
      image: "/placeholder.svg?height=600&width=800",
      tags: ["Web Development", "AI", "Data Visualization"],
      link: "#",
    },
    {
      id: "4",
      title: "Real Estate Listing Platform",
      description: "A platform for real estate agents to list properties and for buyers to search and filter listings.",
      image: "/placeholder.svg?height=600&width=800",
      tags: ["Web Development", "Real Estate", "Maps Integration"],
      link: "#",
    },
    {
      id: "5",
      title: "Fitness Tracking App",
      description: "A mobile application for tracking workouts, nutrition, and fitness goals with social features.",
      image: "/placeholder.svg?height=600&width=800",
      tags: ["Mobile App", "Fitness", "Health"],
      link: "#",
    },
    {
      id: "6",
      title: "Educational Platform",
      description: "An online learning platform with course management, video lessons, and progress tracking.",
      image: "/placeholder.svg?height=600&width=800",
      tags: ["Web Development", "Education", "Video Streaming"],
      link: "#",
    },
  ]

  const allTags = Array.from(new Set(projects.flatMap((project) => project.tags)))

  const filteredProjects = filter ? projects.filter((project) => project.tags.includes(filter)) : projects

  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap gap-2 justify-center mb-8">
          <Button variant={filter === null ? "default" : "outline"} onClick={() => setFilter(null)} className="mb-2">
            All
          </Button>
          {allTags.map((tag) => (
            <Button
              key={tag}
              variant={filter === tag ? "default" : "outline"}
              onClick={() => setFilter(tag)}
              className="mb-2"
            >
              {tag}
            </Button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ y: -5 }}
            >
              <Card className="overflow-hidden border-border/50 h-full">
                <div className="aspect-video relative overflow-hidden">
                  <img
                    src={project.image || "/placeholder.svg"}
                    alt={project.title}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="flex flex-wrap gap-2 mb-3">
                    {project.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="font-normal">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <h3 className="text-xl font-bold mb-2">{project.title}</h3>
                  <p className="text-muted-foreground mb-4">{project.description}</p>
                  <div className="flex justify-between items-center">
                    <Button asChild variant="ghost" className="p-0 hover:bg-transparent">
                      <Link href={`/portfolio/${project.id}`} className="flex items-center gap-2 text-primary">
                        View Details <ArrowRight className="h-4 w-4" />
                      </Link>
                    </Button>
                    {project.link && (
                      <Button asChild variant="ghost" size="icon" className="h-8 w-8">
                        <a href={project.link} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4" />
                          <span className="sr-only">Visit project</span>
                        </a>
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

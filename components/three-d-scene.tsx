"use client"

import { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"

export function ThreeDScene() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let animationFrameId: number
    let time = 0

    // Set canvas dimensions
    const resizeCanvas = () => {
      const { width, height } = canvas.getBoundingClientRect()
      canvas.width = width
      canvas.height = height
    }

    resizeCanvas()
    window.addEventListener("resize", resizeCanvas)

    // 3D-like visualization
    const draw = () => {
      time += 0.01
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Background
      ctx.fillStyle = "rgba(15, 23, 42, 0.1)"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Grid
      ctx.strokeStyle = "rgba(255, 255, 255, 0.1)"
      ctx.lineWidth = 1

      const gridSize = 20
      const perspectiveStrength = 0.5
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2

      // Horizontal lines
      for (let y = -10; y <= 10; y++) {
        const baseY = centerY + y * gridSize

        ctx.beginPath()
        for (let x = -10; x <= 10; x++) {
          const distFromCenter = Math.abs(x) / 10
          const perspectiveY = baseY + Math.sin(time + y * 0.1) * 5 * (1 - distFromCenter * perspectiveStrength)

          if (x === -10) {
            ctx.moveTo(centerX + x * gridSize, perspectiveY)
          } else {
            ctx.lineTo(centerX + x * gridSize, perspectiveY)
          }
        }
        ctx.stroke()
      }

      // Vertical lines
      for (let x = -10; x <= 10; x++) {
        const baseX = centerX + x * gridSize

        ctx.beginPath()
        for (let y = -10; y <= 10; y++) {
          const distFromCenter = Math.abs(y) / 10
          const perspectiveX = baseX + Math.sin(time + x * 0.1) * 5 * (1 - distFromCenter * perspectiveStrength)

          if (y === -10) {
            ctx.moveTo(perspectiveX, centerY + y * gridSize)
          } else {
            ctx.lineTo(perspectiveX, centerY + y * gridSize)
          }
        }
        ctx.stroke()
      }

      // Data visualization
      const dataPoints = [
        { value: 30 + Math.sin(time * 2) * 10, color: "rgba(233, 30, 99, 0.7)" },
        { value: 50 + Math.sin(time * 2 + 1) * 15, color: "rgba(33, 150, 243, 0.7)" },
        { value: 70 + Math.sin(time * 2 + 2) * 20, color: "rgba(76, 175, 80, 0.7)" },
        { value: 40 + Math.sin(time * 2 + 3) * 15, color: "rgba(255, 152, 0, 0.7)" },
        { value: 60 + Math.sin(time * 2 + 4) * 10, color: "rgba(156, 39, 176, 0.7)" },
      ]

      const barWidth = canvas.width / (dataPoints.length * 2)
      const maxHeight = canvas.height * 0.6

      dataPoints.forEach((point, i) => {
        const x = centerX - (dataPoints.length * barWidth) / 2 + i * barWidth * 2 + barWidth / 2
        const height = (point.value / 100) * maxHeight

        // 3D-like bar
        ctx.fillStyle = point.color
        ctx.beginPath()
        ctx.moveTo(x - barWidth / 2, centerY + 50)
        ctx.lineTo(x + barWidth / 2, centerY + 50)
        ctx.lineTo(x + barWidth / 2, centerY + 50 - height)
        ctx.lineTo(x - barWidth / 2, centerY + 50 - height)
        ctx.closePath()
        ctx.fill()

        // Top of the bar
        ctx.fillStyle = point.color.replace("0.7", "0.9")
        ctx.beginPath()
        ctx.ellipse(x, centerY + 50 - height, barWidth / 2, barWidth / 4, 0, 0, Math.PI * 2)
        ctx.fill()

        // Shadow
        ctx.fillStyle = "rgba(0, 0, 0, 0.2)"
        ctx.beginPath()
        ctx.ellipse(x, centerY + 50, barWidth / 2, barWidth / 4, 0, 0, Math.PI * 2)
        ctx.fill()
      })

      animationFrameId = requestAnimationFrame(draw)
    }

    draw()
    setIsLoaded(true)

    return () => {
      window.removeEventListener("resize", resizeCanvas)
      cancelAnimationFrame(animationFrameId)
    }
  }, [])

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: isLoaded ? 1 : 0 }}
      transition={{ duration: 0.5 }}
      className="w-full h-full relative"
    >
      <canvas ref={canvasRef} className="w-full h-full rounded-md" />
    </motion.div>
  )
}

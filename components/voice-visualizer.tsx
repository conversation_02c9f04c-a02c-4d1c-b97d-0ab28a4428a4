"use client"

import { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"
import { liveKitService } from "@/lib/livekit-service"

interface VoiceVisualizerProps {
  isActive: boolean
  isListening: boolean
  isSpeaking: boolean
  className?: string
}

export function VoiceVisualizer({ isActive, isListening, isSpeaking, className }: VoiceVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const [audioData, setAudioData] = useState<Uint8Array | null>(null)

  useEffect(() => {
    if (!isActive) return

    const updateAudioData = () => {
      const data = liveKitService.getAudioData()
      setAudioData(data)
      animationRef.current = requestAnimationFrame(updateAudioData)
    }

    updateAudioData()

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isActive])

  useEffect(() => {
    if (!canvasRef.current || !audioData) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const { width, height } = canvas
    ctx.clearRect(0, 0, width, height)

    // Create gradient
    const gradient = ctx.createRadialGradient(width / 2, height / 2, 0, width / 2, height / 2, width / 2)
    gradient.addColorStop(0, "rgba(233, 30, 99, 0.8)")
    gradient.addColorStop(0.5, "rgba(233, 30, 99, 0.4)")
    gradient.addColorStop(1, "rgba(233, 30, 99, 0.1)")

    if (isListening) {
      // Draw listening visualization
      const barCount = 32
      const barWidth = width / barCount
      const centerY = height / 2

      for (let i = 0; i < barCount; i++) {
        const dataIndex = Math.floor((i / barCount) * audioData.length)
        const amplitude = audioData[dataIndex] || 0
        const barHeight = (amplitude / 255) * (height / 2) * 0.8

        ctx.fillStyle = gradient
        ctx.fillRect(i * barWidth, centerY - barHeight / 2, barWidth - 2, barHeight)
      }
    } else if (isSpeaking) {
      // Draw speaking visualization
      const time = Date.now() * 0.005
      const centerX = width / 2
      const centerY = height / 2

      for (let i = 0; i < 3; i++) {
        const radius = 20 + i * 15 + Math.sin(time + i) * 10
        const alpha = 0.6 - i * 0.2

        ctx.beginPath()
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(233, 30, 99, ${alpha})`
        ctx.fill()
      }
    } else {
      // Draw idle visualization
      const centerX = width / 2
      const centerY = height / 2
      const time = Date.now() * 0.002

      for (let i = 0; i < 5; i++) {
        const angle = (i / 5) * Math.PI * 2 + time
        const x = centerX + Math.cos(angle) * (20 + Math.sin(time + i) * 5)
        const y = centerY + Math.sin(angle) * (20 + Math.sin(time + i) * 5)

        ctx.beginPath()
        ctx.arc(x, y, 3 + Math.sin(time + i) * 2, 0, Math.PI * 2)
        ctx.fillStyle = "rgba(233, 30, 99, 0.6)"
        ctx.fill()
      }
    }
  }, [audioData, isListening, isSpeaking])

  return (
    <motion.div
      className={`relative ${className}`}
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.8, opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <canvas
        ref={canvasRef}
        width={200}
        height={200}
        className="w-full h-full rounded-full"
        style={{
          filter: isActive ? "blur(0px)" : "blur(2px)",
          transition: "filter 0.3s ease",
        }}
      />

      {/* Status indicator */}
      <div className="absolute top-2 right-2">
        <div
          className={`w-3 h-3 rounded-full ${
            isListening ? "bg-green-500 animate-pulse" : isSpeaking ? "bg-blue-500 animate-pulse" : "bg-gray-400"
          }`}
        />
      </div>

      {/* Activity rings */}
      {isActive && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute inset-0 border-2 border-primary/30 rounded-full"
              initial={{ scale: 0.8, opacity: 0.8 }}
              animate={{
                scale: [0.8, 1.2, 0.8],
                opacity: [0.8, 0, 0.8],
              }}
              transition={{
                duration: 2,
                repeat: Number.POSITIVE_INFINITY,
                delay: i * 0.4,
              }}
            />
          ))}
        </div>
      )}
    </motion.div>
  )
}

"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { ModernTextArea } from '@/components/ui/modern-textarea'
import { 
  EmojiPicker, 
  MarkdownToolbar, 
  AutoComplete, 
  FileDropZone,
  CollaborationCursors 
} from '@/components/ui/modern-textarea-plugins'
import { 
  useModernTextArea, 
  useAutoComplete, 
  useFileHandler, 
  useCollaboration 
} from '@/hooks/use-modern-textarea'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Code, 
  MessageSquare, 
  FileText, 
  Settings,
  Users,
  Palette,
  Zap
} from 'lucide-react'

// Sample data for demonstrations
const SAMPLE_SUGGESTIONS = [
  'component', 'interface', 'function', 'variable', 'import', 'export',
  'useState', 'useEffect', 'useCallback', 'useMemo', 'useRef',
  'typescript', 'javascript', 'react', 'nextjs', 'tailwind'
]

const SAMPLE_COLLABORATORS = [
  { id: '1', name: 'Alice', color: '#3b82f6' },
  { id: '2', name: 'Bob', color: '#10b981' },
  { id: '3', name: 'Charlie', color: '#f59e0b' }
]

export function ModernTextAreaDemo() {
  const [activeTab, setActiveTab] = useState('basic')
  const [showAdvanced, setShowAdvanced] = useState(false)

  // Basic textarea state
  const basicTextArea = useModernTextArea('Welcome to the Modern TextArea component!\n\nThis is a comprehensive, feature-rich text input component.')

  // Code editor state
  const codeTextArea = useModernTextArea(`function ModernTextArea() {
  const [value, setValue] = useState('')
  
  return (
    <textarea
      value={value}
      onChange={(e) => setValue(e.target.value)}
      className="modern-textarea"
    />
  )
}`)

  // Message composer state
  const messageTextArea = useModernTextArea('')

  // Auto-complete functionality
  const autoComplete = useAutoComplete(SAMPLE_SUGGESTIONS)

  // File handling
  const fileHandler = useFileHandler()

  // Collaboration
  const collaboration = useCollaboration()

  // Add sample collaborators
  React.useEffect(() => {
    SAMPLE_COLLABORATORS.forEach(collab => {
      collaboration.addCollaborator(collab)
    })
  }, [])

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-4"
      >
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
          Modern TextArea Component
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          A comprehensive, modular text input component with advanced features like syntax highlighting, 
          auto-complete, collaboration, and more.
        </p>
      </motion.div>

      {/* Feature Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-2 md:grid-cols-4 gap-4"
      >
        {[
          { icon: Code, label: 'Syntax Highlighting', color: 'text-blue-500' },
          { icon: MessageSquare, label: 'Auto-complete', color: 'text-green-500' },
          { icon: Users, label: 'Collaboration', color: 'text-purple-500' },
          { icon: Zap, label: 'Real-time Features', color: 'text-yellow-500' }
        ].map(({ icon: Icon, label, color }) => (
          <Card key={label} className="text-center p-4">
            <Icon className={`h-8 w-8 mx-auto mb-2 ${color}`} />
            <p className="text-sm font-medium">{label}</p>
          </Card>
        ))}
      </motion.div>

      {/* Main Demo */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Usage</TabsTrigger>
            <TabsTrigger value="code">Code Editor</TabsTrigger>
            <TabsTrigger value="message">Message Composer</TabsTrigger>
            <TabsTrigger value="advanced">Advanced Features</TabsTrigger>
          </TabsList>

          {/* Basic Usage */}
          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Basic TextArea
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <ModernTextArea
                  ref={basicTextArea.textareaRef}
                  value={basicTextArea.value}
                  onChange={basicTextArea.setValue}
                  placeholder="Start typing your content here..."
                  showCharCount
                  showWordCount
                  showLineNumbers
                  autoResize
                  size="md"
                  variant="default"
                  className="min-h-[200px]"
                />
                
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-4">
                    <span>Characters: {basicTextArea.stats.characters}</span>
                    <span>Words: {basicTextArea.stats.words}</span>
                    <span>Lines: {basicTextArea.stats.lines}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={basicTextArea.history.undo}
                      disabled={!basicTextArea.history.canUndo}
                    >
                      Undo
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={basicTextArea.history.redo}
                      disabled={!basicTextArea.history.canRedo}
                    >
                      Redo
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={basicTextArea.operations.clear}
                    >
                      Clear
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Code Editor */}
          <TabsContent value="code" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Code Editor Mode
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ModernTextArea
                  ref={codeTextArea.textareaRef}
                  value={codeTextArea.value}
                  onChange={codeTextArea.setValue}
                  placeholder="// Start coding..."
                  showLineNumbers
                  syntaxHighlight
                  language="javascript"
                  variant="code"
                  size="lg"
                  showToolbar
                  className="font-mono"
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Message Composer */}
          <TabsContent value="message" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Message Composer
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FileDropZone
                  onFileDrop={fileHandler.handleFileDrop}
                  accept="image/*,text/*"
                >
                  <ModernTextArea
                    ref={messageTextArea.textareaRef}
                    value={messageTextArea.value}
                    onChange={messageTextArea.setValue}
                    placeholder="Type your message... Use @ for mentions, # for tags"
                    enableMarkdown
                    enableAutoComplete
                    enableDragDrop
                    showToolbar
                    variant="message"
                    size="md"
                    maxLength={2000}
                    showCharCount
                  />
                </FileDropZone>

                {/* Markdown Toolbar */}
                <MarkdownToolbar
                  onInsert={messageTextArea.operations.insertText}
                  onFormat={messageTextArea.operations.formatText}
                />

                {/* File Attachments */}
                {fileHandler.uploadedFiles.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Attachments:</p>
                    <div className="flex flex-wrap gap-2">
                      {fileHandler.uploadedFiles.map((file, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          {file.name}
                          <button
                            onClick={() => fileHandler.removeFile(index)}
                            className="ml-1 text-muted-foreground hover:text-foreground"
                          >
                            ×
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <EmojiPicker onEmojiSelect={messageTextArea.operations.insertText} />
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button>Send Message</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Advanced Features */}
          <TabsContent value="advanced" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Collaboration Demo */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Collaboration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Active collaborators:</p>
                    <div className="flex flex-wrap gap-2">
                      {collaboration.collaborators.map((collab) => (
                        <Badge
                          key={collab.id}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: collab.color }}
                          />
                          {collab.name}
                          {collab.isTyping && (
                            <span className="text-xs">(typing...)</span>
                          )}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <ModernTextArea
                    value="This is a collaborative document. Multiple users can edit simultaneously."
                    enableCollaboration
                    variant="form"
                    size="sm"
                    className="relative"
                  />
                </CardContent>
              </Card>

              {/* Size Variants */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5" />
                    Size Variants
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {(['sm', 'md', 'lg', 'xl'] as const).map((size) => (
                    <div key={size} className="space-y-2">
                      <p className="text-sm font-medium capitalize">{size} Size:</p>
                      <ModernTextArea
                        placeholder={`${size.toUpperCase()} textarea`}
                        size={size}
                        variant="default"
                        minRows={2}
                      />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </motion.div>

      {/* Auto-complete Demo */}
      <AutoComplete
        suggestions={autoComplete.suggestions}
        onSelect={autoComplete.selectSuggestion}
        trigger={autoComplete.trigger}
        position={autoComplete.position}
        visible={autoComplete.isVisible}
      />
    </div>
  )
}

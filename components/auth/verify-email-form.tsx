"use client"
import type React from "react"
import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { motion } from "framer-motion"
import { CheckCircle2, XCircle, Loader2 } from "lucide-react"

export function VerifyEmailForm() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState("")
  const [resendDisabled, setResendDisabled] = useState(false)
  const [countdown, setCountdown] = useState(0)
  
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  
  // Get parameters from the URL
  const token = searchParams.get("token")
  const email = searchParams.get("email")

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token) {
        setError("Invalid verification link")
        setIsLoading(false)
        return
      }

      try {
        const response = await fetch("/api/auth/verify-email", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ token }),
        })

        if (!response.ok) {
          const data = await response.json()
          throw new Error(data.message || "Failed to verify email")
        }

        setIsSuccess(true)
        toast({
          title: "Email verified",
          description: "Your email has been successfully verified. You can now log in.",
        })

        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push("/login")
        }, 3000)
      } catch (error) {
        setError(
          error instanceof Error
            ? error.message
            : "Failed to verify email. Please try again."
        )
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Verification failed",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (token) {
      verifyEmail()
    } else {
      setIsLoading(false)
    }
  }, [token, toast, router])

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(count => count - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      setResendDisabled(false)
    }
  }, [countdown])

  const handleResendVerification = async () => {
    setResendDisabled(true)
    setCountdown(60) // 60 seconds cooldown
    setIsLoading(true)
    
    try {
      if (!email) {
        throw new Error("Email is required to resend verification")
      }
      
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "Failed to resend verification email")
      }

      toast({
        title: "Verification email sent",
        description: "Please check your email for the verification link.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive",
      })
      setResendDisabled(false)
      setCountdown(0)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>Email Verification</CardTitle>
          <CardDescription>
            {isLoading && token
              ? "Verifying your email address..."
              : isSuccess
              ? "Your email has been verified"
              : error
              ? "Email verification failed"
              : email
              ? `Please check ${email} for a verification link`
              : "Check your email"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col items-center justify-center py-6">
            {isLoading ? (
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
            ) : isSuccess ? (
              <CheckCircle2 className="h-12 w-12 text-green-500" />
            ) : (
              <XCircle className="h-12 w-12 text-destructive" />
            )}
            
            <p className="mt-4 text-center text-sm text-muted-foreground">
              {isLoading && token ? (
                "Please wait while we verify your email address..."
              ) : isSuccess ? (
                "Thank you for verifying your email address. You will be redirected to the login page."
              ) : error ? (
                error
              ) : email ? (
                <>
                  We have sent a verification link to <strong>{email}</strong>. 
                  Please check your inbox and click the link to verify your account.
                  If you don't see the email, please check your spam folder.
                </>
              ) : (
                "Please check your email for a verification link."
              )}
            </p>
          </div>

          <div className="flex justify-center gap-4">
            {isSuccess ? (
              <Button onClick={() => router.push("/login")}>
                Go to Login
              </Button>
            ) : !isLoading && (
              <>
                <Button
                  variant="outline"
                  onClick={handleResendVerification}
                  disabled={isLoading || resendDisabled}
                >
                  {resendDisabled
                    ? `Resend in ${countdown}s`
                    : "Resend verification email"}
                </Button>
                <Button onClick={() => router.push("/")}>
                  Return to Home
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

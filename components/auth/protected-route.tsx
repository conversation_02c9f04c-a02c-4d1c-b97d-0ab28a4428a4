"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useAuth } from "./auth-provider"
import { Card } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: "USER" | "ADMIN"
  requireVerified?: boolean
}

export function ProtectedRoute({
  children,
  requiredRole,
  requireVerified = true,
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    if (!isLoading) {
      if (!isAuthenticated) {
        toast({
          title: "Access denied",
          description: "Please log in to access this page",
          variant: "destructive",
        })
        router.push("/login")
        return
      }

      if (requireVerified && !session?.user?.emailVerified) {
        toast({
          title: "Email verification required",
          description: "Please verify your email address to access this page",
          variant: "destructive",
        })
        router.push("/verify-email")
        return
      }

      if (requiredRole && user?.role !== requiredRole) {
        toast({
          title: "Access denied",
          description: "You do not have permission to access this page",
          variant: "destructive",
        })
        router.push("/dashboard")
        return
      }
    }
  }, [isLoading, isAuthenticated, user, session, router, requiredRole, requireVerified, toast])

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="p-8">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <p className="text-lg">Loading...</p>
          </div>
        </Card>
      </div>
    )
  }

  if (!isAuthenticated || 
      (requireVerified && !session?.user?.emailVerified) || 
      (requiredRole && user?.role !== requiredRole)) {
    return null
  }

  return <>{children}</>
}

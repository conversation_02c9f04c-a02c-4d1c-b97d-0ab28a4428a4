"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useSession } from "next-auth/react"

interface AuthContextType {
  isAuthenticated: boolean
  isLoading: boolean
  user: {
    id: string
    name: string
    email: string
    role: "USER" | "ADMIN"
  } | null
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  isLoading: true,
  user: null,
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  // Public routes that don't require authentication
  const publicRoutes = ["/", "/login", "/signup", "/reset-password", "/verify-email"]

  useEffect(() => {
    // Update loading state based on session status
    if (status !== "loading") {
      setIsLoading(false)
    }

    // Redirect logic for protected routes
    if (!isLoading) {
      const isPublicRoute = publicRoutes.some((route) => 
        pathname === route || pathname.startsWith(`${route}/`)
      )

      if (!session && !isPublicRoute) {
        // Redirect to login for protected routes when not authenticated
        router.push(`/login?redirect=${encodeURIComponent(pathname)}`)
      } else if (session && (pathname === "/login" || pathname === "/signup")) {
        // Redirect to dashboard if trying to access auth pages while logged in
        router.push("/dashboard")
      }
    }
  }, [session, status, isLoading, pathname, router])

  const value = {
    isAuthenticated: !!session,
    isLoading: status === "loading" || isLoading,
    user: session?.user as AuthContextType["user"] || null,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Trophy, 
  Clock, 
  Coins, 
  Star, 
  Users, 
  CheckCircle, 
  PlayCircle,
  Target,
  Calendar
} from 'lucide-react'
import { Challenge } from '@/lib/types/marketing-platform'
import { useChallenges } from '@/hooks/use-challenges'
import { cn } from '@/lib/utils'

interface ChallengeCardProps {
  challenge: Challenge
  className?: string
  onComplete?: (challengeId: string) => void
}

export function ChallengeCard({ challenge, className, onComplete }: ChallengeCardProps) {
  const { completeChallenge } = useChallenges()
  const [isCompleting, setIsCompleting] = useState(false)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'BEGINNER': return 'bg-green-100 text-green-800 border-green-200'
      case 'INTERMEDIATE': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'ADVANCED': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'EXPERT': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'MARKETING': return <Target className="h-4 w-4" />
      case 'SOCIAL_MEDIA': return <Users className="h-4 w-4" />
      case 'CONTENT': return <Star className="h-4 w-4" />
      case 'REFERRALS': return <Trophy className="h-4 w-4" />
      default: return <Target className="h-4 w-4" />
    }
  }

  const getStatusInfo = () => {
    if (!challenge.userCompletion) {
      return {
        status: 'available',
        label: 'Available',
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: <PlayCircle className="h-3 w-3" />
      }
    }

    switch (challenge.userCompletion.status) {
      case 'COMPLETED':
        return {
          status: 'completed',
          label: 'Completed',
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle className="h-3 w-3" />
        }
      case 'IN_PROGRESS':
        return {
          status: 'in_progress',
          label: 'In Progress',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: <Clock className="h-3 w-3" />
        }
      default:
        return {
          status: 'available',
          label: 'Available',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: <PlayCircle className="h-3 w-3" />
        }
    }
  }

  const handleStartChallenge = async () => {
    if (challenge.userCompletion?.status === 'COMPLETED') return

    setIsCompleting(true)
    try {
      const success = await completeChallenge(challenge.id)
      if (success && onComplete) {
        onComplete(challenge.id)
      }
    } finally {
      setIsCompleting(false)
    }
  }

  const statusInfo = getStatusInfo()
  const isCompleted = challenge.userCompletion?.status === 'COMPLETED'
  const completionRate = Math.round(challenge.completionRate * 100)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      className={className}
    >
      <Card className={cn(
        "h-full transition-all duration-200 hover:shadow-lg",
        isCompleted && "bg-green-50 border-green-200"
      )}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between gap-2">
            <div className="flex items-center gap-2">
              {getCategoryIcon(challenge.category)}
              <Badge variant="outline" className={getDifficultyColor(challenge.difficulty)}>
                {challenge.difficulty}
              </Badge>
            </div>
            <Badge variant="outline" className={statusInfo.color}>
              {statusInfo.icon}
              {statusInfo.label}
            </Badge>
          </div>
          
          <CardTitle className="text-lg leading-tight">
            {challenge.title}
          </CardTitle>
          
          <p className="text-sm text-muted-foreground line-clamp-2">
            {challenge.description}
          </p>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Rewards */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1 text-sm">
                <Coins className="h-4 w-4 text-yellow-600" />
                <span className="font-medium">{challenge.creditReward}</span>
              </div>
              <div className="flex items-center gap-1 text-sm">
                <Star className="h-4 w-4 text-purple-600" />
                <span className="font-medium">{challenge.xpReward} XP</span>
              </div>
            </div>
            
            {challenge.timeLimit && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Clock className="h-3 w-3" />
                {challenge.timeLimit < 60 
                  ? `${challenge.timeLimit}m`
                  : `${Math.round(challenge.timeLimit / 60)}h`
                }
              </div>
            )}
          </div>

          {/* Progress Stats */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Completion Rate</span>
              <span>{completionRate}%</span>
            </div>
            <Progress value={completionRate} className="h-1" />
            <div className="text-xs text-muted-foreground">
              {challenge.totalAttempts} attempts
            </div>
          </div>

          {/* User Completion Info */}
          {challenge.userCompletion && isCompleted && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span className="text-green-800">Completed</span>
                <div className="flex items-center gap-2 text-green-700">
                  <span>+{challenge.userCompletion.creditsAwarded}</span>
                  <Coins className="h-3 w-3" />
                  <span>+{challenge.userCompletion.xpAwarded} XP</span>
                  <Star className="h-3 w-3" />
                </div>
              </div>
              {challenge.userCompletion.score && (
                <div className="text-xs text-green-600 mt-1">
                  Score: {challenge.userCompletion.score}/100
                </div>
              )}
            </div>
          )}

          {/* Action Button */}
          <Button
            onClick={handleStartChallenge}
            disabled={isCompleted || isCompleting}
            className="w-full"
            variant={isCompleted ? "outline" : "default"}
          >
            {isCompleting ? (
              "Processing..."
            ) : isCompleted ? (
              "Completed"
            ) : statusInfo.status === 'in_progress' ? (
              "Continue Challenge"
            ) : (
              "Start Challenge"
            )}
          </Button>

          {/* Dates */}
          {(challenge.startDate || challenge.endDate) && (
            <div className="text-xs text-muted-foreground space-y-1">
              {challenge.startDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Starts: {new Date(challenge.startDate).toLocaleDateString()}
                </div>
              )}
              {challenge.endDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Ends: {new Date(challenge.endDate).toLocaleDateString()}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

"use client"

import Link from "next/link"
import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ModeToggle } from "@/components/mode-toggle"
import { cn } from "@/lib/utils"
import { Menu, X } from "lucide-react"
import { motion } from "framer-motion"
import { NavbarLogo, CompactNavbarLogo, preloadNavbarLogo, useLogoPerformance } from "@/components/3d-logo"
import { useRouter } from "next/navigation"
import { LogoWithFallback, NavbarLoadingIndicator } from "./svg-logo"
import _ from 'lodash'

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [logoLoaded, setLogoLoaded] = useState(false)
  const [logoError, setLogoError] = useState(false)
  const [logoType, setLogoType] = useState<'3d' | 'svg'>('svg') // Default to SVG for better performance
  const router = useRouter()
  const { metrics } = useLogoPerformance()



  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Preload the 3D logo for better performance
  useEffect(() => {
    preloadNavbarLogo()
  }, [])

  // Handle logo interactions
  const handleLogoClick = () => {
    router.push("/")
    setIsMobileMenuOpen(false)
  }

  const handleLogoLoad = () => {
    setLogoLoaded(true)
  }

  const handleLogoError = useCallback((error: Error) => {
    console.warn("Logo loading error:", error)
    setLogoError(true)
    setLogoLoaded(false)
    // Fallback to SVG if 3D fails
    if (logoType === '3d') {
      setLogoType('svg')
    }
  }, [logoType])

  // Toggle logo type (for development/testing)
  const toggleLogoType = useCallback(() => {
    setLogoType(prev => prev === '3d' ? 'svg' : '3d')
    setLogoLoaded(false)
    setLogoError(false)
  }, [])

  // Close mobile menu when clicking outside or on escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsMobileMenuOpen(false)
      }
    }

    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Element
      if (isMobileMenuOpen && !target.closest('[data-mobile-menu]')) {
        setIsMobileMenuOpen(false)
      }
    }

    if (isMobileMenuOpen) {
      document.addEventListener('keydown', handleEscape)
      document.addEventListener('click', handleClickOutside)
      document.body.style.overflow = 'hidden' // Prevent scroll when menu is open
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.removeEventListener('click', handleClickOutside)
      document.body.style.overflow = 'unset'
    }
  }, [isMobileMenuOpen])

  const navLinks = [
    { href: "/", label: "Home" },
    { href: "/services", label: "Services" },
    { href: "/projects", label: "Projects" },
    { href: "/3d-showcase", label: "3D Showcase" },
    { href: "/navbar-demo", label: "3D Navbar" },
    { href: "/fonts-demo", label: "Fonts Demo" },
    { href: "/about", label: "About" },
    { href: "/contact", label: "Contact" },
    { href: "/assistant", label: "AI Assistant" },
  ]

  return (
    <header
      className={cn(
        "w-full z-50 transition-all duration-300",
        isScrolled ? "bg-background/80 backdrop-blur-md py-2 shadow-md" : "bg-transparent py-4",
      )}
    >
      <div className="container mx-auto px-4 flex items-center justify-between">
        <div className="flex items-center gap-3 group cursor-pointer" onClick={handleLogoClick}>
          <div className="relative h-7 w-10 transition-opacity duration-300 group-hover:opacity-90">
            {/* Position-stable loading indicator */}
            <NavbarLoadingIndicator
              isLoading={!logoLoaded && !logoError}
              hasError={logoError}
              size="sm"
              onRetry={() => {
                setLogoError(false)
                setLogoLoaded(false)
              }}
            />

            {/* Mobile Logo - Use 3D for better visual impact on mobile */}
            <div className="block md:hidden">
              {logoType === '3d' ? (
                <CompactNavbarLogo
                  className="rounded-lg overflow-hidden shadow-sm"
                  enableAnimation={!isScrolled} // Disable animation when scrolled for performance
                  enableHover={true}
                  quality="low" // Lower quality for mobile performance
                  onLoad={handleLogoLoad}
                  onError={handleLogoError}
                  ariaLabel="Soimagine Digital Agency Logo - Go to Home"
                />
              ) : (
                <LogoWithFallback
                  width={32}
                  height={22}
                  onClick={handleLogoClick}
                  onLoad={handleLogoLoad}
                  onError={handleLogoError}
                  ariaLabel="Soimagine Digital Agency Logo - Go to Home"
                  enableEditing={true}
                />
              )}
            </div>

            {/* Desktop Logo - Use SVG for better performance */}
            <div className="hidden md:block">
              {logoType === 'svg' ? (
                <LogoWithFallback
                  width={40}
                  height={28}
                  onClick={handleLogoClick}
                  onLoad={handleLogoLoad}
                  onError={handleLogoError}
                  ariaLabel="Soimagine Digital Agency Logo - Go to Home"
                  enableEditing={true}
                />
              ) : (
                <NavbarLogo
                  className="rounded-lg overflow-hidden shadow-sm"
                  enableAnimation={!isScrolled}
                  enableHover={true}
                  quality="medium"
                  onLoad={handleLogoLoad}
                  onError={handleLogoError}
                  ariaLabel="Soimagine Digital Agency Logo - Go to Home"
                />
              )}
            </div>
          </div>
          <span className="font-bold text-xl font-secondary transition-colors duration-300 group-hover:text-primary">
            Soimagine
          </span>

          {/* Performance indicator (dev only) - positioned below logo */}
          {process.env.NODE_ENV === 'development' && logoLoaded && (
            <div className="absolute -bottom-8 left-0 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded transition-opacity duration-300 opacity-0 group-hover:opacity-100 pointer-events-none z-0">
              Load: {metrics.loadTime.toFixed(0)}ms
            </div>
          )}
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6">
          {navLinks.map((link) => (
            <Link key={link.href} href={link.href} className="text-foreground/80 hover:text-primary transition-colors font-primary">
              {link.label}
            </Link>
          ))}
        </nav>

        <div className="hidden md:flex items-center gap-4">
          <ModeToggle />
          <Button asChild variant="default">
            <Link href="/login">Login</Link>
          </Button>
        </div>

        {/* Mobile Menu Button */}
        <div className="flex md:hidden items-center gap-4">
          <ModeToggle />
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.2 }}
          className="md:hidden bg-background/95 backdrop-blur-md shadow-lg"
          data-mobile-menu
        >
          <div className="container mx-auto px-4 py-4 flex flex-col gap-4">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="text-foreground/80 hover:text-primary py-2 transition-colors font-primary"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.label}
              </Link>
            ))}
            <Button asChild variant="default" className="mt-2">
              <Link href="/login" onClick={() => setIsMobileMenuOpen(false)}>
                Login
              </Link>
            </Button>
          </div>
        </motion.div>
      )}

      {/* Performance metrics and controls in development */}
      {process.env.NODE_ENV === 'development' && false && ( // Set to true to enable dev panel
        <div className="fixed bottom-4 left-4 bg-background/90 backdrop-blur-sm border rounded-lg p-3 text-xs font-mono z-40">
          <div className="space-y-2">
            <div className="font-semibold text-primary">Logo Performance</div>
            {metrics && (
              <div className="space-y-1">
                <div>Load: {metrics.loadTime.toFixed(0)}ms</div>
                <div>Render: {metrics.renderTime.toFixed(0)}ms</div>
                <div>FPS: {metrics.frameRate.toFixed(0)}</div>
              </div>
            )}
            <div className="pt-2 border-t space-y-1">
              <div>Type: {logoType.toUpperCase()}</div>
              <div>Status: {logoLoaded ? '✅ Loaded' : logoError ? '❌ Error' : '⏳ Loading'}</div>
              <button
                onClick={toggleLogoType}
                className="px-2 py-1 bg-primary text-primary-foreground rounded text-xs hover:bg-primary/90 transition-colors"
              >
                Switch to {logoType === '3d' ? 'SVG' : '3D'}
              </button>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}

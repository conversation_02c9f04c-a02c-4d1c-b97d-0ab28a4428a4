"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { liveKitService } from "@/lib/livekit-service"
import { motion, AnimatePresence } from "framer-motion"
import {
  Users,
  Video,
  VideoOff,
  Mic,
  MicOff,
  Monitor,
  MonitorOff,
  MessageCircle,
  FileText,
  Palette,
  Settings,
} from "lucide-react"

interface Participant {
  id: string
  name: string
  isVideoEnabled: boolean
  isAudioEnabled: boolean
  isScreenSharing: boolean
  status: "online" | "away" | "busy"
}

interface CollaborationTool {
  id: string
  name: string
  icon: React.ReactNode
  description: string
  isActive: boolean
}

export function RealTimeCollaboration() {
  const [participants, setParticipants] = useState<Participant[]>([
    {
      id: "1",
      name: "John Doe",
      isVideoEnabled: true,
      isAudioEnabled: true,
      isScreenSharing: false,
      status: "online",
    },
    {
      id: "2",
      name: "Sarah Smith",
      isVideoEnabled: false,
      isAudioEnabled: true,
      isScreenSharing: true,
      status: "online",
    },
    {
      id: "3",
      name: "Mike Johnson",
      isVideoEnabled: true,
      isAudioEnabled: false,
      isScreenSharing: false,
      status: "away",
    },
  ])

  const [localParticipant, setLocalParticipant] = useState<Participant>({
    id: "local",
    name: "You",
    isVideoEnabled: false,
    isAudioEnabled: false,
    isScreenSharing: false,
    status: "online",
  })

  const [collaborationTools, setCollaborationTools] = useState<CollaborationTool[]>([
    {
      id: "chat",
      name: "Team Chat",
      icon: <MessageCircle className="h-4 w-4" />,
      description: "Real-time messaging",
      isActive: false,
    },
    {
      id: "whiteboard",
      name: "Whiteboard",
      icon: <Palette className="h-4 w-4" />,
      description: "Collaborative drawing",
      isActive: false,
    },
    {
      id: "documents",
      name: "Shared Documents",
      icon: <FileText className="h-4 w-4" />,
      description: "Document collaboration",
      isActive: false,
    },
  ])

  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setParticipants((prev) =>
        prev.map((p) => ({
          ...p,
          status: Math.random() > 0.9 ? (p.status === "online" ? "away" : "online") : p.status,
        })),
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const toggleVideo = async () => {
    const stream = await liveKitService.startVideoRecording()
    setLocalParticipant((prev) => ({
      ...prev,
      isVideoEnabled: !prev.isVideoEnabled,
    }))
  }

  const toggleAudio = async () => {
    const stream = await liveKitService.startAudioRecording()
    setLocalParticipant((prev) => ({
      ...prev,
      isAudioEnabled: !prev.isAudioEnabled,
    }))
  }

  const toggleScreenShare = async () => {
    if (localParticipant.isScreenSharing) {
      // Stop screen sharing
      setLocalParticipant((prev) => ({
        ...prev,
        isScreenSharing: false,
      }))
    } else {
      const stream = await liveKitService.shareScreen()
      if (stream) {
        setLocalParticipant((prev) => ({
          ...prev,
          isScreenSharing: true,
        }))
      }
    }
  }

  const connectToRoom = async () => {
    const result = await liveKitService.connect({
      serverUrl: "wss://soimagine.livekit.cloud",
      token: "demo-token",
      roomName: "soimagine-collaboration",
      participantName: "User",
    })

    if (result.success) {
      setIsConnected(true)
    }
  }

  const disconnectFromRoom = async () => {
    await liveKitService.disconnect()
    setIsConnected(false)
  }

  const toggleTool = (toolId: string) => {
    setCollaborationTools((prev) =>
      prev.map((tool) => (tool.id === toolId ? { ...tool, isActive: !tool.isActive } : tool)),
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500"
      case "away":
        return "bg-yellow-500"
      case "busy":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <div className="h-full flex flex-col">
      <Card className="border-border/50 mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Real-Time Collaboration
            </CardTitle>
            <Badge variant={isConnected ? "default" : "secondary"}>{isConnected ? "Connected" : "Disconnected"}</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            {!isConnected ? (
              <Button onClick={connectToRoom} className="gap-2">
                <Users className="h-4 w-4" />
                Join Collaboration Room
              </Button>
            ) : (
              <>
                <Button
                  variant={localParticipant.isVideoEnabled ? "default" : "outline"}
                  size="sm"
                  onClick={toggleVideo}
                  className="gap-2"
                >
                  {localParticipant.isVideoEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                  Video
                </Button>
                <Button
                  variant={localParticipant.isAudioEnabled ? "default" : "outline"}
                  size="sm"
                  onClick={toggleAudio}
                  className="gap-2"
                >
                  {localParticipant.isAudioEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                  Audio
                </Button>
                <Button
                  variant={localParticipant.isScreenSharing ? "default" : "outline"}
                  size="sm"
                  onClick={toggleScreenShare}
                  className="gap-2"
                >
                  {localParticipant.isScreenSharing ? (
                    <Monitor className="h-4 w-4" />
                  ) : (
                    <MonitorOff className="h-4 w-4" />
                  )}
                  Share
                </Button>
                <Button variant="outline" size="sm" onClick={disconnectFromRoom} className="gap-2">
                  Leave Room
                </Button>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="participants" className="flex-1 flex flex-col">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="participants">Participants</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="participants" className="flex-1">
          <ScrollArea className="h-full">
            <div className="space-y-4 p-4">
              <h3 className="font-medium">Active Participants ({participants.length + 1})</h3>

              {/* Local participant */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center gap-3 p-3 rounded-lg border border-border/50 bg-primary/5"
              >
                <div className="relative">
                  <Avatar>
                    <AvatarFallback>You</AvatarFallback>
                  </Avatar>
                  <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full ${getStatusColor("online")}`} />
                </div>
                <div className="flex-1">
                  <p className="font-medium">{localParticipant.name} (You)</p>
                  <div className="flex items-center gap-2 mt-1">
                    {localParticipant.isVideoEnabled ? (
                      <Video className="h-3 w-3 text-green-500" />
                    ) : (
                      <VideoOff className="h-3 w-3 text-gray-400" />
                    )}
                    {localParticipant.isAudioEnabled ? (
                      <Mic className="h-3 w-3 text-green-500" />
                    ) : (
                      <MicOff className="h-3 w-3 text-gray-400" />
                    )}
                    {localParticipant.isScreenSharing && <Monitor className="h-3 w-3 text-blue-500" />}
                  </div>
                </div>
                <Badge variant="outline">Host</Badge>
              </motion.div>

              {/* Remote participants */}
              <AnimatePresence>
                {participants.map((participant, index) => (
                  <motion.div
                    key={participant.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-3 p-3 rounded-lg border border-border/50"
                  >
                    <div className="relative">
                      <Avatar>
                        <AvatarFallback>
                          {participant.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div
                        className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full ${getStatusColor(
                          participant.status,
                        )}`}
                      />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{participant.name}</p>
                      <div className="flex items-center gap-2 mt-1">
                        {participant.isVideoEnabled ? (
                          <Video className="h-3 w-3 text-green-500" />
                        ) : (
                          <VideoOff className="h-3 w-3 text-gray-400" />
                        )}
                        {participant.isAudioEnabled ? (
                          <Mic className="h-3 w-3 text-green-500" />
                        ) : (
                          <MicOff className="h-3 w-3 text-gray-400" />
                        )}
                        {participant.isScreenSharing && <Monitor className="h-3 w-3 text-blue-500" />}
                      </div>
                    </div>
                    <Badge variant="secondary">{participant.status}</Badge>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="tools" className="flex-1">
          <div className="p-4 space-y-4">
            <h3 className="font-medium">Collaboration Tools</h3>
            <div className="grid gap-4">
              {collaborationTools.map((tool) => (
                <Card
                  key={tool.id}
                  className={`cursor-pointer transition-colors ${
                    tool.isActive ? "border-primary bg-primary/5" : "hover:bg-muted/50"
                  }`}
                  onClick={() => toggleTool(tool.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-md ${tool.isActive ? "bg-primary text-white" : "bg-muted"}`}>
                        {tool.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{tool.name}</h4>
                        <p className="text-sm text-muted-foreground">{tool.description}</p>
                      </div>
                      <Badge variant={tool.isActive ? "default" : "outline"}>
                        {tool.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="flex-1">
          <div className="p-4 space-y-4">
            <h3 className="font-medium">Collaboration Settings</h3>
            <div className="space-y-4">
              <Button variant="outline" className="w-full justify-start gap-2">
                <Settings className="h-4 w-4" />
                Configure Room Settings
              </Button>
              <Button variant="outline" className="w-full justify-start gap-2">
                <Users className="h-4 w-4" />
                Manage Participants
              </Button>
              <Button variant="outline" className="w-full justify-start gap-2">
                <Monitor className="h-4 w-4" />
                Recording Settings
              </Button>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Camera,
  FileImage,
  Upload,
  Layout,
  FileText,
  Code,
  Send,
  Settings,
  Share,
  Download,
  Eye,
  ChevronDown,
  Bot,
  Copy,
  Check,
  Mic,
  MicOff
} from "lucide-react"
import { useChat } from '@ai-sdk/react'
import { V0Suggestions } from "@/components/v0-suggestions"

const QUICK_ACTIONS = [
  { icon: Camera, label: "Clone a Screenshot", description: "Upload an image to recreate" },
  { icon: FileImage, label: "Import from Figma", description: "Connect your Figma designs" },
  { icon: Upload, label: "Upload a Project", description: "Import existing code" },
  { icon: Layout, label: "Landing Page", description: "Create a landing page" },
  { icon: FileText, label: "Sign Up Form", description: "Build a registration form" },
  { icon: Code, label: "Component Library", description: "Generate reusable components" }
]

const PROJECTS = [
  { id: "1", name: "Soimagine Digital Agency", type: "Next.js" },
  { id: "2", name: "E-commerce Platform", type: "React" },
  { id: "3", name: "Portfolio Website", type: "Next.js" },
]

export function V0Interface() {
  const [selectedProject, setSelectedProject] = useState<string>("1")
  const [showPreview, setShowPreview] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [copied, setCopied] = useState(false)

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    stop,
    reload,
    error
  } = useChat({
    maxSteps: 5, // Enable multi-step tool calls
    onFinish: (message) => {
      // Check if the message contains tool calls that generate code
      const hasCodeGeneration = message.parts.some(part =>
        part.type === 'tool-invocation' &&
        ['generateComponent', 'generateLandingPage', 'generateForm'].includes(part.toolInvocation.toolName)
      )

      if (hasCodeGeneration) {
        setShowPreview(true)
      }
    },
    onError: (error) => {
      console.error('Chat error:', error)
    }
  })

  const handleQuickAction = (action: typeof QUICK_ACTIONS[0]) => {
    const prompt = `Help me ${action.label.toLowerCase()}: ${action.description}`
    handleSubmit(new Event('submit') as any, {
      data: { message: prompt }
    })
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const toggleRecording = () => {
    setIsRecording(!isRecording)
    // Mock voice recording functionality
  }

  const handleSuggestionClick = (prompt: string) => {
    handleSubmit(new Event('submit') as any, {
      data: { message: prompt }
    })
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b border-border/50 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="bg-emerald-100 text-emerald-700 border-emerald-200">
                  New
                </Badge>
                <span className="text-sm text-muted-foreground">
                  The v0 API is now in beta.
                  <Button variant="link" className="p-0 h-auto text-sm">
                    Learn More →
                  </Button>
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex">
          {/* Chat Area */}
          <div className="flex-1 flex flex-col">
            {messages.length === 0 ? (
              /* Initial State */
              <div className="flex-1 flex flex-col items-center justify-center p-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center max-w-2xl mx-auto"
                >
                  <h1 className="text-4xl font-bold mb-4">What can I help you ship?</h1>
                  <p className="text-muted-foreground mb-8">
                    You are running low on credits. Your limit will reset on June 20.
                    <Button variant="link" className="ml-2 p-0 h-auto text-primary">
                      Upgrade Plan ✕
                    </Button>
                  </p>

                  {/* Input Area */}
                  <div className="relative mb-8">
                    <form onSubmit={handleSubmit}>
                      <div className="flex items-center gap-2 p-3 border border-border rounded-lg bg-background">
                        <Input
                          value={input}
                          onChange={handleInputChange}
                          placeholder="Ask v0 to build..."
                          className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-base"
                        />
                        <div className="flex items-center gap-2">
                          <Select value={selectedProject} onValueChange={setSelectedProject}>
                            <SelectTrigger className="w-auto border-0 bg-transparent">
                              <SelectValue />
                              <ChevronDown className="h-4 w-4 ml-1" />
                            </SelectTrigger>
                            <SelectContent>
                              {PROJECTS.map((project) => (
                                <SelectItem key={project.id} value={project.id}>
                                  {project.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="ghost"
                            size="icon"
                            type="button"
                            onClick={toggleRecording}
                            className={isRecording ? "text-red-500" : ""}
                          >
                            {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                          </Button>
                          <Button size="icon" type="submit" disabled={status !== 'ready'}>
                            <Send className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </form>
                  </div>

                  {/* Quick Actions */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-w-3xl mx-auto">
                    {QUICK_ACTIONS.map((action, index) => (
                      <motion.div
                        key={action.label}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Button
                          variant="outline"
                          className="h-auto p-4 flex flex-col items-center gap-2 text-center hover:bg-muted/50"
                          onClick={() => handleQuickAction(action)}
                        >
                          <action.icon className="h-6 w-6 text-primary" />
                          <div>
                            <div className="font-medium text-sm">{action.label}</div>
                            <div className="text-xs text-muted-foreground">{action.description}</div>
                          </div>
                        </Button>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            ) : (
              /* Chat State */
              <div className="flex-1 flex flex-col">
                <ScrollArea className="flex-1 p-6">
                  <div className="max-w-4xl mx-auto space-y-6">
                    {messages.map((message) => (
                      <motion.div
                        key={message.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="space-y-4"
                      >
                        {message.role === "user" ? (
                          <div className="flex justify-end">
                            <div className="bg-primary text-primary-foreground rounded-lg px-4 py-2 max-w-[80%]">
                              {message.content}
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            <div className="flex items-start gap-3">
                              <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                                <Bot className="h-4 w-4 text-white" />
                              </div>
                              <div className="flex-1 space-y-2">
                                {status === 'streaming' && message.id === messages[messages.length - 1]?.id && (
                                  <div className="flex items-center gap-1">
                                    <div className="w-1 h-1 bg-primary rounded-full animate-pulse" />
                                    <div className="w-1 h-1 bg-primary rounded-full animate-pulse delay-100" />
                                    <div className="w-1 h-1 bg-primary rounded-full animate-pulse delay-200" />
                                  </div>
                                )}

                                {/* Render message parts */}
                                {message.parts.map((part, index) => {
                                  switch (part.type) {
                                    case 'text':
                                      return (
                                        <div key={index} className="prose prose-sm max-w-none">
                                          <p className="whitespace-pre-wrap">{part.text}</p>
                                        </div>
                                      )

                                    case 'tool-invocation':
                                      return (
                                        <Card key={index} className="mt-4">
                                          <CardContent className="p-4">
                                            <div className="flex items-center justify-between mb-2">
                                              <Badge variant="secondary">
                                                <Code className="h-3 w-3 mr-1" />
                                                {part.toolInvocation.toolName}
                                              </Badge>
                                              {part.toolInvocation.state === 'result' && part.toolInvocation.result?.code && (
                                                <Button
                                                  variant="ghost"
                                                  size="sm"
                                                  onClick={() => copyToClipboard(part.toolInvocation.result.code)}
                                                >
                                                  {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                                                </Button>
                                              )}
                                            </div>

                                            {part.toolInvocation.state === 'call' && (
                                              <div className="text-sm text-muted-foreground">
                                                Generating {part.toolInvocation.toolName}...
                                              </div>
                                            )}

                                            {part.toolInvocation.state === 'result' && part.toolInvocation.result?.code && (
                                              <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
                                                <code>{part.toolInvocation.result.code}</code>
                                              </pre>
                                            )}
                                          </CardContent>
                                        </Card>
                                      )

                                    default:
                                      return null
                                  }
                                })}
                              </div>
                            </div>
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>

                {/* Input Area for Chat State */}
                <div className="border-t border-border/50 p-4">
                  <div className="max-w-4xl mx-auto">
                    <form onSubmit={handleSubmit}>
                      <div className="flex items-center gap-2 p-3 border border-border rounded-lg bg-background">
                        <Input
                          value={input}
                          onChange={handleInputChange}
                          placeholder="Ask a follow up..."
                          className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                        />
                        <div className="flex items-center gap-2">
                          <Select value={selectedProject} onValueChange={setSelectedProject}>
                            <SelectTrigger className="w-auto border-0 bg-transparent text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {PROJECTS.map((project) => (
                                <SelectItem key={project.id} value={project.id}>
                                  {project.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            variant="ghost"
                            size="icon"
                            type="button"
                            onClick={toggleRecording}
                            className={isRecording ? "text-red-500" : ""}
                          >
                            {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                          </Button>
                          <Button
                            size="icon"
                            type="submit"
                            disabled={!input.trim() || status === 'streaming'}
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </form>

                    {/* Status and Controls */}
                    <div className="flex items-center justify-between mt-2">
                      <div className="text-xs text-muted-foreground">
                        {status === 'streaming' && 'AI is responding...'}
                        {status === 'ready' && 'AI may make mistakes. Please use with discretion.'}
                        {status === 'error' && 'An error occurred. Please try again.'}
                      </div>

                      <div className="flex items-center gap-2">
                        {(status === 'streaming' || status === 'submitted') && (
                          <Button variant="ghost" size="sm" onClick={stop}>
                            Stop
                          </Button>
                        )}
                        {error && (
                          <Button variant="ghost" size="sm" onClick={reload}>
                            Retry
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Suggestions */}
                  <div className="max-w-4xl mx-auto mt-6">
                    <V0Suggestions onSuggestionClick={handleSuggestionClick} />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Preview Panel */}
          <AnimatePresence>
            {showPreview && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: "50%", opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                className="border-l border-border/50 bg-muted/20"
              >
                <div className="h-full flex flex-col">
                  <div className="border-b border-border/50 p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        <span className="font-medium">Preview</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Tabs defaultValue="preview" className="w-auto">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="preview">Preview</TabsTrigger>
                            <TabsTrigger value="code">Code</TabsTrigger>
                          </TabsList>
                        </Tabs>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowPreview(false)}
                        >
                          ✕
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="flex-1 p-4">
                    <div className="w-full h-full bg-white rounded-lg border border-border/50 flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                          <Layout className="h-8 w-8 text-primary" />
                        </div>
                        <h3 className="font-medium mb-2">Component Preview</h3>
                        <p className="text-sm text-muted-foreground">
                          Generated component will appear here
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}

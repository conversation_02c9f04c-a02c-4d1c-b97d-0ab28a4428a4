"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"
import { useToast } from "@/hooks/use-toast"

type AIContextType = {
  isProcessing: boolean
  startProcessing: () => void
  stopProcessing: () => void
  messages: Message[]
  addMessage: (message: Message) => void
  clearMessages: () => void
}

type Message = {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: Date
}

const AIContext = createContext<AIContextType | undefined>(undefined)

export function AIProvider({ children }: { children: React.ReactNode }) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const { toast } = useToast()

  const startProcessing = () => setIsProcessing(true)
  const stopProcessing = () => setIsProcessing(false)

  const addMessage = (message: Message) => {
    setMessages((prev) => [...prev, message])
  }

  const clearMessages = () => {
    setMessages([])
    toast({
      title: "Conversation cleared",
      description: "All messages have been removed",
    })
  }

  return (
    <AIContext.Provider
      value={{
        isProcessing,
        startProcessing,
        stopProcessing,
        messages,
        addMessage,
        clearMessages,
      }}
    >
      {children}
    </AIContext.Provider>
  )
}

export const useAI = () => {
  const context = useContext(AIContext)
  if (context === undefined) {
    throw new Error("useAI must be used within an AIProvider")
  }
  return context
}

"use client"

import React, { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Smile,
  Search,
  Hash,
  AtSign,
  FileText,
  Image,
  Link,
  Code2,
  Bold,
  Italic,
  List,
  Quote
} from 'lucide-react'

// Emoji Picker Plugin
export interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void
  trigger?: React.ReactNode
}

const EMOJI_CATEGORIES = {
  'Smileys & People': ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'],
  'Animals & Nature': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯', '🦁', '🐮', '🐷', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺'],
  'Food & Drink': ['🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔'],
  'Activities': ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛼', '🛷'],
  'Objects': ['⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️', '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥', '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️']
}

export function EmojiPicker({ onEmojiSelect, trigger }: EmojiPickerProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('Smileys & People')

  const filteredEmojis = searchTerm
    ? Object.values(EMOJI_CATEGORIES).flat().filter(emoji => 
        emoji.includes(searchTerm.toLowerCase())
      )
    : EMOJI_CATEGORIES[selectedCategory as keyof typeof EMOJI_CATEGORIES] || []

  return (
    <Popover>
      <PopoverTrigger asChild>
        {trigger || (
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <Smile className="h-3 w-3" />
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="p-3 border-b">
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
            <Input
              placeholder="Search emojis..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-7 h-8"
            />
          </div>
        </div>
        
        {!searchTerm && (
          <div className="flex flex-wrap gap-1 p-2 border-b">
            {Object.keys(EMOJI_CATEGORIES).map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "ghost"}
                size="sm"
                className="h-6 text-xs"
                onClick={() => setSelectedCategory(category)}
              >
                {category.split(' ')[0]}
              </Button>
            ))}
          </div>
        )}
        
        <ScrollArea className="h-48">
          <div className="grid grid-cols-8 gap-1 p-2">
            {filteredEmojis.map((emoji, index) => (
              <Button
                key={index}
                variant="ghost"
                className="h-8 w-8 p-0 text-lg hover:bg-muted"
                onClick={() => onEmojiSelect(emoji)}
              >
                {emoji}
              </Button>
            ))}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  )
}

// Markdown Toolbar Plugin
export interface MarkdownToolbarProps {
  onInsert: (text: string) => void
  onFormat: (type: string) => void
}

export function MarkdownToolbar({ onInsert, onFormat }: MarkdownToolbarProps) {
  const formatButtons = [
    { icon: Bold, action: 'bold', label: 'Bold', syntax: '**text**' },
    { icon: Italic, action: 'italic', label: 'Italic', syntax: '*text*' },
    { icon: Code2, action: 'code', label: 'Code', syntax: '`code`' },
    { icon: Link, action: 'link', label: 'Link', syntax: '[text](url)' },
    { icon: Image, action: 'image', label: 'Image', syntax: '![alt](url)' },
    { icon: List, action: 'list', label: 'List', syntax: '- item' },
    { icon: Quote, action: 'quote', label: 'Quote', syntax: '> quote' },
    { icon: Hash, action: 'heading', label: 'Heading', syntax: '# heading' }
  ]

  return (
    <div className="flex items-center gap-1 p-2 border-b border-border/50">
      {formatButtons.map(({ icon: Icon, action, label, syntax }) => (
        <Button
          key={action}
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          title={`${label} (${syntax})`}
          onClick={() => onFormat(action)}
        >
          <Icon className="h-3 w-3" />
        </Button>
      ))}
      
      <div className="w-px h-4 bg-border mx-1" />
      
      <EmojiPicker onEmojiSelect={(emoji) => onInsert(emoji)} />
    </div>
  )
}

// Auto-complete Plugin
export interface AutoCompleteProps {
  suggestions: string[]
  onSelect: (suggestion: string) => void
  trigger: string
  position: { top: number; left: number }
  visible: boolean
}

export function AutoComplete({ suggestions, onSelect, trigger, position, visible }: AutoCompleteProps) {
  const [selectedIndex, setSelectedIndex] = useState(0)

  if (!visible || suggestions.length === 0) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="absolute z-50 bg-popover border border-border rounded-md shadow-lg max-w-xs"
      style={{ top: position.top, left: position.left }}
    >
      <ScrollArea className="max-h-48">
        <div className="p-1">
          {suggestions.map((suggestion, index) => (
            <Button
              key={index}
              variant={index === selectedIndex ? "secondary" : "ghost"}
              className="w-full justify-start text-left h-8 px-2"
              onClick={() => onSelect(suggestion)}
            >
              <span className="text-muted-foreground mr-2">{trigger}</span>
              {suggestion}
            </Button>
          ))}
        </div>
      </ScrollArea>
    </motion.div>
  )
}

// File Drop Zone Plugin
export interface FileDropZoneProps {
  onFileDrop: (files: FileList) => void
  accept?: string
  maxSize?: number
  children: React.ReactNode
}

export function FileDropZone({ onFileDrop, accept, maxSize, children }: FileDropZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      onFileDrop(files)
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      onFileDrop(files)
    }
  }

  return (
    <div
      className="relative"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {children}
      
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileSelect}
        className="hidden"
        multiple
      />
      
      <AnimatePresence>
        {isDragOver && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-primary/10 border-2 border-dashed border-primary rounded-lg flex items-center justify-center z-10"
          >
            <div className="text-center">
              <FileText className="h-8 w-8 mx-auto mb-2 text-primary" />
              <p className="text-sm font-medium text-primary">Drop files here</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Collaboration Cursors Plugin
export interface CollaborationCursor {
  id: string
  user: string
  color: string
  position: number
}

export interface CollaborationCursorsProps {
  cursors: CollaborationCursor[]
  textareaRef: React.RefObject<HTMLTextAreaElement>
}

export function CollaborationCursors({ cursors, textareaRef }: CollaborationCursorsProps) {
  return (
    <>
      {cursors.map((cursor) => (
        <motion.div
          key={cursor.id}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="absolute pointer-events-none z-20"
          style={{
            // Position calculation would need to be implemented based on cursor.position
            // This is a simplified version
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          }}
        >
          <div
            className="w-0.5 h-5 animate-pulse"
            style={{ backgroundColor: cursor.color }}
          />
          <Badge
            variant="secondary"
            className="absolute -top-6 left-0 text-xs whitespace-nowrap"
            style={{ backgroundColor: cursor.color, color: 'white' }}
          >
            {cursor.user}
          </Badge>
        </motion.div>
      ))}
    </>
  )
}

// Syntax Highlighter Plugin (simplified)
export interface SyntaxHighlighterProps {
  code: string
  language: string
  className?: string
}

export function SyntaxHighlighter({ code, language, className }: SyntaxHighlighterProps) {
  // This is a simplified syntax highlighter
  // In a real implementation, you'd use a library like Prism.js or highlight.js
  
  const highlightKeywords = (text: string, lang: string) => {
    const keywords = {
      javascript: ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'class', 'import', 'export'],
      typescript: ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return', 'class', 'import', 'export', 'interface', 'type'],
      python: ['def', 'class', 'if', 'else', 'for', 'while', 'return', 'import', 'from', 'try', 'except'],
      css: ['color', 'background', 'margin', 'padding', 'border', 'display', 'position', 'width', 'height']
    }
    
    const langKeywords = keywords[lang as keyof typeof keywords] || []
    let highlightedText = text
    
    langKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g')
      highlightedText = highlightedText.replace(regex, `<span class="text-blue-600 font-semibold">${keyword}</span>`)
    })
    
    return highlightedText
  }

  return (
    <pre
      className={`text-sm font-mono whitespace-pre-wrap ${className}`}
      dangerouslySetInnerHTML={{
        __html: highlightKeywords(code, language)
      }}
    />
  )
}

"use client"

import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Bold,
  Italic,
  Code,
  Link,
  Image,
  Smile,
  Eye,
  EyeOff,
  Copy,
  Undo,
  Redo,
  Maximize2,
  Minimize2,
  Send,
  Loader2,
  Bo<PERSON>,
  Briefcase,
  Wrench,
  Crown
} from 'lucide-react'

// TypeScript interfaces
export interface ModernTextAreaProps {
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  disabled?: boolean
  readOnly?: boolean
  autoResize?: boolean
  showLineNumbers?: boolean
  showCharCount?: boolean
  showWordCount?: boolean
  maxLength?: number
  minRows?: number
  maxRows?: number
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'code' | 'message' | 'form'
  syntaxHighlight?: boolean
  language?: string
  showToolbar?: boolean
  enableMarkdown?: boolean
  enableAutoComplete?: boolean
  enableDragDrop?: boolean
  enableCollaboration?: boolean
  className?: string
  onFocus?: () => void
  onBlur?: () => void
  onKeyDown?: (e: React.KeyboardEvent) => void
  onPaste?: (e: React.ClipboardEvent) => void
  onDrop?: (files: FileList) => void
  // Send button integration
  showSendButton?: boolean
  onSend?: () => void
  sendButtonDisabled?: boolean
  isStreaming?: boolean
  // Assistant selector integration
  showAssistantSelector?: boolean
  assistantType?: string
  onAssistantTypeChange?: (type: string) => void
  assistantOptions?: Array<{
    value: string
    label: string
    description: string
    icon: React.ComponentType<any>
  }>
}

export interface ModernTextAreaRef {
  focus: () => void
  blur: () => void
  insertText: (text: string) => void
  getSelection: () => { start: number; end: number }
  setSelection: (start: number, end: number) => void
  undo: () => void
  redo: () => void
  clear: () => void
}

interface HistoryState {
  value: string
  selection: { start: number; end: number }
}

// Default assistant options
const DEFAULT_ASSISTANT_OPTIONS = [
  {
    value: 'consultant',
    label: 'Consultant',
    description: 'For our services, about us and offerings',
    icon: Briefcase
  },
  {
    value: 'engineer',
    label: 'Engineer',
    description: 'Help get started with your project',
    icon: Wrench
  },
  {
    value: 'executive',
    label: 'Executive',
    description: 'For custom solutions and working/partner with us',
    icon: Crown
  }
]

// Size configurations
const sizeConfig = {
  sm: {
    minHeight: 'min-h-[80px]',
    fontSize: 'text-sm',
    padding: 'p-2',
    toolbarHeight: 'h-8'
  },
  md: {
    minHeight: 'min-h-[120px]',
    fontSize: 'text-base',
    padding: 'p-3',
    toolbarHeight: 'h-10'
  },
  lg: {
    minHeight: 'min-h-[160px]',
    fontSize: 'text-lg',
    padding: 'p-4',
    toolbarHeight: 'h-12'
  },
  xl: {
    minHeight: 'min-h-[200px]',
    fontSize: 'text-xl',
    padding: 'p-5',
    toolbarHeight: 'h-14'
  }
}

// Variant configurations
const variantConfig = {
  default: {
    border: 'border-border',
    background: 'bg-background',
    focusRing: 'focus-within:ring-2 focus-within:ring-primary/20'
  },
  code: {
    border: 'border-border',
    background: 'bg-muted/50',
    focusRing: 'focus-within:ring-2 focus-within:ring-blue-500/20'
  },
  message: {
    border: 'border-border/50',
    background: 'bg-background/80',
    focusRing: 'focus-within:ring-2 focus-within:ring-primary/30'
  },
  form: {
    border: 'border-input',
    background: 'bg-background',
    focusRing: 'focus-within:ring-2 focus-within:ring-ring'
  }
}

export const ModernTextArea = forwardRef<ModernTextAreaRef, ModernTextAreaProps>(
  ({
    value = '',
    onChange,
    placeholder = 'Start typing...',
    disabled = false,
    readOnly = false,
    autoResize = true,
    showLineNumbers = false,
    showCharCount = false,
    showWordCount = false,
    maxLength,
    minRows = 3,
    maxRows = 20,
    size = 'md',
    variant = 'default',
    syntaxHighlight = false,
    language = 'javascript',
    showToolbar = false,
    enableMarkdown = false,
    enableAutoComplete = false,
    enableDragDrop = false,
    enableCollaboration = false,
    className,
    onFocus,
    onBlur,
    onKeyDown,
    onPaste,
    onDrop,
    // Send button props
    showSendButton = false,
    onSend,
    sendButtonDisabled = false,
    isStreaming = false,
    // Assistant selector props
    showAssistantSelector = false,
    assistantType = 'consultant',
    onAssistantTypeChange,
    assistantOptions = DEFAULT_ASSISTANT_OPTIONS,
    ...props
  }, ref) => {
    // State management
    const [internalValue, setInternalValue] = useState(value)
    const [isFocused, setIsFocused] = useState(false)
    const [isExpanded, setIsExpanded] = useState(false)
    const [showPreview, setShowPreview] = useState(false)
    const [history, setHistory] = useState<HistoryState[]>([{ value: '', selection: { start: 0, end: 0 } }])
    const [historyIndex, setHistoryIndex] = useState(0)
    const [isTyping] = useState(false)

    // Refs
    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const containerRef = useRef<HTMLDivElement>(null)
    const previousValueRef = useRef<string>(value)

    // Get current configuration
    const sizeStyles = sizeConfig[size]
    const variantStyles = variantConfig[variant]

    // Controlled vs uncontrolled
    const currentValue = onChange ? value : internalValue

    // Character and word counts
    const charCount = currentValue.length
    const wordCount = currentValue.trim() ? currentValue.trim().split(/\s+/).length : 0
    const lineCount = currentValue.split('\n').length

    // Auto-resize functionality
    useEffect(() => {
      if (autoResize && textareaRef.current) {
        const textarea = textareaRef.current
        textarea.style.height = 'auto'
        const scrollHeight = textarea.scrollHeight
        const maxHeight = parseInt(getComputedStyle(textarea).lineHeight) * maxRows
        textarea.style.height = `${Math.min(scrollHeight, maxHeight)}px`
      }
    }, [currentValue, autoResize, maxRows])

    // Handle value changes
    const handleChange = (newValue: string) => {
      if (maxLength && newValue.length > maxLength) {
        newValue = newValue.slice(0, maxLength)
      }

      if (onChange) {
        onChange(newValue)
      } else {
        setInternalValue(newValue)
      }
    }

    // Debounced history update
    useEffect(() => {
      // Only update history if the value actually changed
      if (currentValue !== previousValueRef.current) {
        const timeoutId = setTimeout(() => {
          if (textareaRef.current) {
            const selection = {
              start: textareaRef.current.selectionStart,
              end: textareaRef.current.selectionEnd
            }

            setHistory(prevHistory => {
              // Only add to history if the value is different from the last entry
              const lastEntry = prevHistory[prevHistory.length - 1]
              if (!lastEntry || lastEntry.value !== currentValue) {
                const newHistory = [...prevHistory, { value: currentValue, selection }]
                // Limit history size to prevent memory issues
                return newHistory.slice(-50) // Keep last 50 entries
              }
              return prevHistory
            })

            setHistoryIndex(prevIndex => prevIndex + 1)
          }
          previousValueRef.current = currentValue
        }, 500) // 500ms debounce

        return () => clearTimeout(timeoutId)
      }
    }, [currentValue])

    // Imperative handle for ref
    useImperativeHandle(ref, () => ({
      focus: () => textareaRef.current?.focus(),
      blur: () => textareaRef.current?.blur(),
      insertText: (text: string) => {
        if (textareaRef.current) {
          const start = textareaRef.current.selectionStart
          const end = textareaRef.current.selectionEnd
          const newValue = currentValue.slice(0, start) + text + currentValue.slice(end)

          if (onChange) {
            onChange(newValue)
          } else {
            setInternalValue(newValue)
          }

          // Set cursor position after inserted text
          setTimeout(() => {
            if (textareaRef.current) {
              textareaRef.current.setSelectionRange(start + text.length, start + text.length)
            }
          }, 0)
        }
      },
      getSelection: () => ({
        start: textareaRef.current?.selectionStart || 0,
        end: textareaRef.current?.selectionEnd || 0
      }),
      setSelection: (start: number, end: number) => {
        textareaRef.current?.setSelectionRange(start, end)
      },
      undo: () => {
        if (historyIndex > 0) {
          const prevState = history[historyIndex - 1]
          setHistoryIndex(historyIndex - 1)

          if (onChange) {
            onChange(prevState.value)
          } else {
            setInternalValue(prevState.value)
          }

          setTimeout(() => {
            if (textareaRef.current) {
              textareaRef.current.setSelectionRange(
                prevState.selection.start,
                prevState.selection.end
              )
            }
          }, 0)
        }
      },
      redo: () => {
        if (historyIndex < history.length - 1) {
          const nextState = history[historyIndex + 1]
          setHistoryIndex(historyIndex + 1)

          if (onChange) {
            onChange(nextState.value)
          } else {
            setInternalValue(nextState.value)
          }

          setTimeout(() => {
            if (textareaRef.current) {
              textareaRef.current.setSelectionRange(
                nextState.selection.start,
                nextState.selection.end
              )
            }
          }, 0)
        }
      },
      clear: () => handleChange('')
    }))

    return (
      <motion.div
        ref={containerRef}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn(
          'relative rounded-lg border transition-all duration-200',
          variantStyles.border,
          variantStyles.background,
          variantStyles.focusRing,
          isFocused && 'ring-2',
          disabled && 'opacity-50 cursor-not-allowed',
          isExpanded && 'fixed inset-4 z-50 shadow-2xl',
          className
        )}
      >
        {/* Toolbar */}
        <AnimatePresence>
          {showToolbar && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className={cn(
                'flex items-center justify-between border-b border-border/50 px-3',
                sizeStyles.toolbarHeight
              )}
            >
              <div className="flex items-center gap-1">
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Bold className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Italic className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Code className="h-3 w-3" />
                </Button>
                <div className="w-px h-4 bg-border mx-1" />
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Link className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Image className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Smile className="h-3 w-3" />
                </Button>
              </div>

              <div className="flex items-center gap-1">
                {enableMarkdown && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => setShowPreview(!showPreview)}
                  >
                    {showPreview ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main content area */}
        <div className="relative flex">
          {/* Line numbers */}
          {showLineNumbers && (
            <div className={cn(
              'flex flex-col items-end pr-2 py-2 text-xs text-muted-foreground border-r border-border/50 bg-muted/30 select-none',
              sizeStyles.fontSize
            )}>
              {Array.from({ length: lineCount }, (_, i) => (
                <div key={i + 1} className="leading-6 min-h-[1.5rem] flex items-center">
                  {i + 1}
                </div>
              ))}
            </div>
          )}

          {/* Textarea container */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={currentValue}
              onChange={(e) => handleChange(e.target.value)}
              onFocus={() => {
                setIsFocused(true)
                onFocus?.()
              }}
              onBlur={() => {
                setIsFocused(false)
                onBlur?.()
              }}
              onKeyDown={onKeyDown}
              onPaste={onPaste}
              placeholder={placeholder}
              disabled={disabled}
              readOnly={readOnly}
              rows={minRows}
              className={cn(
                'w-full resize-none border-0 bg-transparent outline-none placeholder:text-muted-foreground/60',
                sizeStyles.minHeight,
                sizeStyles.fontSize,
                sizeStyles.padding,
                showLineNumbers && 'pl-2'
              )}
              style={{
                minHeight: autoResize ? undefined : `${minRows * 1.5}rem`
              }}
              {...props}
            />

            {/* Placeholder animation */}
            <AnimatePresence>
              {!currentValue && !isFocused && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className={cn(
                    'absolute inset-0 pointer-events-none flex items-start',
                    sizeStyles.padding,
                    sizeStyles.fontSize
                  )}
                >
                  <span className="text-muted-foreground/40">
                    {placeholder}
                  </span>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Collaboration indicators */}
            {enableCollaboration && isTyping && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute bottom-2 right-2"
              >
                <Badge variant="secondary" className="text-xs">
                  <motion.div
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="w-2 h-2 bg-green-500 rounded-full mr-1"
                  />
                  Someone is typing...
                </Badge>
              </motion.div>
            )}

            {/* Send Button and Assistant Selector */}
            {(showSendButton || showAssistantSelector) && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="absolute bottom-2 right-2 flex items-center gap-2"
              >
                {/* Assistant Selector */}
                {showAssistantSelector && (
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="relative"
                  >
                    <Select value={assistantType} onValueChange={onAssistantTypeChange}>
                      <SelectTrigger className="h-8 w-auto min-w-[120px] text-xs border-border/50 bg-background/80 backdrop-blur-sm hover:bg-background/90 transition-colors">
                        <div className="flex items-center gap-1">
                          {assistantOptions.find(opt => opt.value === assistantType)?.icon && (
                            <div className="w-3 h-3">
                              {React.createElement(assistantOptions.find(opt => opt.value === assistantType)!.icon, { className: "w-3 h-3" })}
                            </div>
                          )}
                          <SelectValue />
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        {assistantOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <option.icon className="w-4 h-4" />
                              <div>
                                <div className="font-medium">{option.label}</div>
                                <div className="text-xs text-muted-foreground">{option.description}</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </motion.div>
                )}

                {/* Send Button */}
                {showSendButton && (
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      size="sm"
                      onClick={onSend}
                      disabled={sendButtonDisabled || !currentValue.trim()}
                      className="h-8 w-8 p-0 relative overflow-hidden bg-gradient-to-r from-primary via-blue-500 to-purple-500 hover:from-primary/90 hover:via-blue-500/90 hover:to-purple-500/90 transition-all duration-200"
                    >
                      {isStreaming ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Loader2 className="h-4 w-4 text-white" />
                        </motion.div>
                      ) : (
                        <motion.div
                          whileHover={{ x: 1 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Send className="h-4 w-4 text-white" />
                        </motion.div>
                      )}

                      {/* Gradient animation on hover */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-primary/20 via-blue-500/20 to-purple-500/20"
                        animate={{
                          x: currentValue.trim() ? ["-100%", "100%"] : "-100%"
                        }}
                        transition={{
                          duration: 2,
                          repeat: currentValue.trim() ? Infinity : 0,
                          ease: "linear"
                        }}
                      />
                    </Button>
                  </motion.div>
                )}
              </motion.div>
            )}
          </div>
        </div>

        {/* Footer with stats and actions */}
        <AnimatePresence>
          {(showCharCount || showWordCount || isFocused || showSendButton || showAssistantSelector) && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className={cn(
                "flex items-center justify-between border-t border-border/50 px-3 py-2 text-xs text-muted-foreground",
                (showSendButton || showAssistantSelector) && "pr-20" // Add padding for send button/selector
              )}
            >
              <div className="flex items-center gap-4">
                {showCharCount && (
                  <span className={cn(maxLength && charCount > maxLength * 0.9 && 'text-destructive')}>
                    {charCount}{maxLength && `/${maxLength}`} characters
                  </span>
                )}
                {showWordCount && <span>{wordCount} words</span>}
                <span>{lineCount} lines</span>
              </div>

              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => navigator.clipboard.writeText(currentValue)}
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  disabled={historyIndex <= 0}
                >
                  <Undo className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  disabled={historyIndex >= history.length - 1}
                >
                  <Redo className="h-3 w-3" />
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    )
  }
)

ModernTextArea.displayName = 'ModernTextArea'

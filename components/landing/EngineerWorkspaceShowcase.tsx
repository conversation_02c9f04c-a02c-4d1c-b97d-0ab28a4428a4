'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  CodeBracketIcon, 
  SparklesIcon,
  PlayIcon,
  WrenchScrewdriverIcon,
  QuestionMarkCircleIcon,
  BugAntIcon,
  RocketLaunchIcon,
  ArrowRightIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { EngineerWorkspaceUnified } from '@/components/unified/EngineerWorkspaceUnified';

interface Feature {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  color: string;
  demo: string;
}

const features: Feature[] = [
  {
    icon: CodeBracketIcon,
    title: 'AI Code Generation',
    description: 'Generate complete React components, applications, and utilities with intelligent AI assistance',
    color: 'text-blue-600',
    demo: 'Generate a React todo app with TypeScript and Tailwind CSS'
  },
  {
    icon: WrenchScrewdriverIcon,
    title: 'Smart Code Modification',
    description: 'Intelligently modify existing code with detailed explanations and best practices',
    color: 'text-green-600',
    demo: 'Add authentication to an existing React component'
  },
  {
    icon: QuestionMarkCircleIcon,
    title: 'Code Explanation',
    description: 'Get detailed explanations of how code works, patterns used, and optimization tips',
    color: 'text-purple-600',
    demo: 'Explain how React hooks work with practical examples'
  },
  {
    icon: BugAntIcon,
    title: 'Automated Debugging',
    description: 'Identify and fix issues with AI-powered debugging and error resolution',
    color: 'text-red-600',
    demo: 'Debug API integration issues and provide fixes'
  },
  {
    icon: PlayIcon,
    title: 'Live Preview',
    description: 'See your code running in real-time with integrated Sandpack execution environment',
    color: 'text-orange-600',
    demo: 'Instant preview of generated components in the browser'
  },
  {
    icon: RocketLaunchIcon,
    title: 'Rapid Prototyping',
    description: 'Quickly build and iterate on ideas with AI-powered development workflows',
    color: 'text-indigo-600',
    demo: 'Build a complete dashboard in minutes with AI assistance'
  }
];

export function EngineerWorkspaceShowcase() {
  const [showWorkspace, setShowWorkspace] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState<Feature | null>(null);

  if (showWorkspace) {
    return (
      <div className="fixed inset-0 z-50 bg-background">
        <div className="absolute top-4 right-4 z-10">
          <Button
            variant="outline"
            onClick={() => setShowWorkspace(false)}
            className="bg-background/80 backdrop-blur"
          >
            Close Demo
          </Button>
        </div>
        <EngineerWorkspaceUnified />
      </div>
    );
  }

  return (
    <section className="py-24 bg-gradient-to-br from-background via-muted/20 to-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-6 mb-16"
        >
          <div className="flex items-center justify-center space-x-3">
            <SparklesIcon className="w-12 h-12 text-primary" />
            <h2 className="text-4xl md:text-5xl font-bold text-foreground">
              Engineer AI Workspace
            </h2>
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            A complete AI-powered development environment that transforms how you build, debug, and understand code. 
            Experience the future of software development with intelligent assistance at every step.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              onClick={() => setShowWorkspace(true)}
              className="text-lg px-8 py-3"
            >
              <EyeIcon className="w-5 h-5 mr-2" />
              Try Live Demo
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => window.open('/engineer', '_blank')}
              className="text-lg px-8 py-3"
            >
              <RocketLaunchIcon className="w-5 h-5 mr-2" />
              Open Full Workspace
              <ArrowRightIcon className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                onHoverStart={() => setSelectedFeature(feature)}
                onHoverEnd={() => setSelectedFeature(null)}
              >
                <Card className="h-full hover:shadow-xl transition-all duration-300 group cursor-pointer border-2 hover:border-primary/20">
                  <CardHeader className="pb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-lg bg-muted group-hover:bg-primary/10 transition-colors">
                        <Icon className={`w-6 h-6 ${feature.color} group-hover:scale-110 transition-transform`} />
                      </div>
                      <div>
                        <CardTitle className="text-lg group-hover:text-primary transition-colors">
                          {feature.title}
                        </CardTitle>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <CardDescription className="text-sm leading-relaxed">
                      {feature.description}
                    </CardDescription>
                    <div className="pt-2">
                      <Badge variant="secondary" className="text-xs">
                        Demo: {feature.demo}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Feature Highlight */}
        <AnimatePresence>
          {selectedFeature && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-8 border border-primary/20"
            >
              <div className="flex items-start space-x-4">
                <div className="p-3 rounded-lg bg-primary/10">
                  <selectedFeature.icon className={`w-8 h-8 ${selectedFeature.color}`} />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-foreground mb-2">
                    {selectedFeature.title}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {selectedFeature.description}
                  </p>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-sm">
                      Try: {selectedFeature.demo}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowWorkspace(true)}
                      className="text-primary hover:text-primary/80"
                    >
                      Test in Demo
                      <ArrowRightIcon className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-8"
        >
          {[
            { label: 'AI Models', value: '4+', description: 'Advanced AI engines' },
            { label: 'Frameworks', value: '8+', description: 'Supported frameworks' },
            { label: 'Code Tools', value: '12+', description: 'Development tools' },
            { label: 'Templates', value: '50+', description: 'Ready-to-use templates' }
          ].map((stat, index) => (
            <div key={stat.label} className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
              <div className="text-lg font-medium text-foreground mb-1">{stat.label}</div>
              <div className="text-sm text-muted-foreground">{stat.description}</div>
            </div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="mt-16 text-center"
        >
          <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Ready to Transform Your Development Workflow?
              </h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                Join thousands of developers who are already using AI to build better software faster. 
                Start with our interactive demo or dive straight into the full workspace experience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  onClick={() => setShowWorkspace(true)}
                  className="text-lg px-8 py-3"
                >
                  <PlayIcon className="w-5 h-5 mr-2" />
                  Start Interactive Demo
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => window.open('/engineer', '_blank')}
                  className="text-lg px-8 py-3"
                >
                  Open Full Workspace
                  <ArrowRightIcon className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}

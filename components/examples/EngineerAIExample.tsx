'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useChat } from '@ai-sdk/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  CodeBracketIcon, 
  PlayIcon, 
  WrenchScrewdriverIcon,
  QuestionMarkCircleIcon,
  BugAntIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { EngineerToolRenderer } from '@/lib/ai-engines/engineer-ai';

interface ExamplePrompt {
  title: string;
  description: string;
  prompt: string;
  category: 'generate' | 'modify' | 'explain' | 'debug';
  complexity: 'simple' | 'intermediate' | 'advanced';
}

const examplePrompts: ExamplePrompt[] = [
  {
    title: 'React Todo App',
    description: 'Generate a complete todo application with state management',
    prompt: 'Create a React todo application with TypeScript. Include features like adding, editing, deleting, and marking todos as complete. Use Tailwind CSS for styling and include local storage persistence.',
    category: 'generate',
    complexity: 'intermediate'
  },
  {
    title: 'Vue Dashboard',
    description: 'Build a responsive dashboard with charts and data visualization',
    prompt: 'Generate a Vue.js dashboard component with multiple chart types, responsive grid layout, and dark mode support. Include sample data and use a modern UI library.',
    category: 'generate',
    complexity: 'advanced'
  },
  {
    title: 'Add Authentication',
    description: 'Modify existing code to include user authentication',
    prompt: 'Add user authentication to this React component. Include login/logout functionality, protected routes, and user session management.',
    category: 'modify',
    complexity: 'advanced'
  },
  {
    title: 'Explain React Hooks',
    description: 'Get detailed explanation of React hooks usage',
    prompt: 'Explain how React hooks work, specifically useState, useEffect, and useContext. Include examples and best practices.',
    category: 'explain',
    complexity: 'intermediate'
  },
  {
    title: 'Fix API Integration',
    description: 'Debug and fix API integration issues',
    prompt: 'This code is supposed to fetch user data from an API but it\'s not working. The error is "Cannot read property \'map\' of undefined". Help me debug and fix this issue.',
    category: 'debug',
    complexity: 'simple'
  }
];

const categoryIcons = {
  generate: CodeBracketIcon,
  modify: WrenchScrewdriverIcon,
  explain: QuestionMarkCircleIcon,
  debug: BugAntIcon
};

const categoryColors = {
  generate: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
  modify: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
  explain: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
  debug: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
};

const complexityColors = {
  simple: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
  intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
  advanced: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
};

export function EngineerAIExample() {
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const { messages, input, handleInputChange, handleSubmit, isLoading, error } = useChat({
    api: '/api/chat/engineer',
    onError: (error) => {
      console.error('Engineer AI Error:', error);
    }
  });

  const filteredPrompts = selectedCategory === 'all' 
    ? examplePrompts 
    : examplePrompts.filter(prompt => prompt.category === selectedCategory);

  const handleExamplePrompt = (prompt: string) => {
    setCustomPrompt(prompt);
  };

  const handleCustomSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (customPrompt.trim()) {
      handleSubmit(e, {
        data: { content: customPrompt }
      });
      setCustomPrompt('');
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-4"
      >
        <div className="flex items-center justify-center space-x-2">
          <SparklesIcon className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Engineer AI Assistant
          </h1>
        </div>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Generate, modify, explain, and debug code with AI assistance. 
          Get working prototypes that run in your browser with live preview.
        </p>
      </motion.div>

      <Tabs defaultValue="examples" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="examples">Example Prompts</TabsTrigger>
          <TabsTrigger value="chat">AI Chat</TabsTrigger>
        </TabsList>

        <TabsContent value="examples" className="space-y-6">
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('all')}
            >
              All Categories
            </Button>
            {Object.keys(categoryIcons).map((category) => {
              const Icon = categoryIcons[category as keyof typeof categoryIcons];
              return (
                <Button
                  key={category}
                  variant={selectedCategory === category ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="flex items-center space-x-1"
                >
                  <Icon className="w-4 h-4" />
                  <span className="capitalize">{category}</span>
                </Button>
              );
            })}
          </div>

          {/* Example Prompts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredPrompts.map((example, index) => {
              const Icon = categoryIcons[example.category];
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <Icon className="w-6 h-6 text-gray-600 dark:text-gray-400 group-hover:text-blue-600 transition-colors" />
                        <div className="flex space-x-1">
                          <Badge className={categoryColors[example.category]}>
                            {example.category}
                          </Badge>
                          <Badge className={complexityColors[example.complexity]}>
                            {example.complexity}
                          </Badge>
                        </div>
                      </div>
                      <CardTitle className="text-lg">{example.title}</CardTitle>
                      <CardDescription>{example.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button
                        onClick={() => handleExamplePrompt(example.prompt)}
                        className="w-full"
                        size="sm"
                      >
                        <PlayIcon className="w-4 h-4 mr-2" />
                        Try This Example
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>

          {/* Custom Prompt */}
          <Card>
            <CardHeader>
              <CardTitle>Custom Prompt</CardTitle>
              <CardDescription>
                Enter your own prompt to generate, modify, explain, or debug code
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCustomSubmit} className="space-y-4">
                <Textarea
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="Describe what you want to build, modify, understand, or fix..."
                  className="min-h-[100px]"
                />
                <Button 
                  type="submit" 
                  disabled={!customPrompt.trim() || isLoading}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <SparklesIcon className="w-4 h-4 mr-2" />
                      Send to Engineer AI
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chat" className="space-y-6">
          {/* Chat Interface */}
          <Card className="h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle>Engineer AI Chat</CardTitle>
              <CardDescription>
                Interactive chat with code generation, modification, explanation, and debugging
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              {/* Messages */}
              <ScrollArea className="flex-1 mb-4">
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                      <CodeBracketIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                      <p>Start a conversation with Engineer AI</p>
                      <p className="text-sm">Ask me to generate, modify, explain, or debug code!</p>
                    </div>
                  ) : (
                    messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[80%] rounded-lg p-4 ${
                            message.role === 'user'
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                          }`}
                        >
                          {message.role === 'assistant' && message.toolInvocations ? (
                            <div className="space-y-4">
                              {message.content && (
                                <div className="prose dark:prose-invert max-w-none">
                                  {message.content}
                                </div>
                              )}
                              {message.toolInvocations.map((toolInvocation, index) => (
                                <EngineerToolRenderer
                                  key={index}
                                  toolInvocation={toolInvocation}
                                />
                              ))}
                            </div>
                          ) : (
                            <div className="prose dark:prose-invert max-w-none">
                              {message.content}
                            </div>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>

              {/* Input */}
              <form onSubmit={handleSubmit} className="flex space-x-2">
                <Textarea
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask Engineer AI to generate, modify, explain, or debug code..."
                  className="flex-1 min-h-[60px] max-h-[120px]"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                />
                <Button 
                  type="submit" 
                  disabled={!input.trim() || isLoading}
                  className="self-end"
                >
                  <SparklesIcon className="w-4 h-4" />
                </Button>
              </form>

              {error && (
                <div className="mt-2 p-3 bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
                  <p className="text-red-800 dark:text-red-200 text-sm">
                    Error: {error.message}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

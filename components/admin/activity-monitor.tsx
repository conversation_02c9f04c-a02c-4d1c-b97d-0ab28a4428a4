'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Search, Activity, Users, Code, Trophy, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'

interface ActivityLog {
  id: string
  userId: string
  userName: string
  userEmail: string
  action: string
  resource: string
  details: string
  ipAddress: string
  userAgent: string
  timestamp: string
  status: 'SUCCESS' | 'FAILED' | 'WARNING'
}

interface SystemMetric {
  id: string
  name: string
  value: number
  unit: string
  change: number
  status: 'good' | 'warning' | 'critical'
}

export function ActivityMonitor() {
  const [activities, setActivities] = useState<ActivityLog[]>([])
  const [metrics, setMetrics] = useState<SystemMetric[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [actionFilter, setActionFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'activities' | 'metrics'>('activities')

  useEffect(() => {
    // Mock data - replace with actual API calls
    const mockActivities: ActivityLog[] = [
      {
        id: '1',
        userId: '1',
        userName: 'John Doe',
        userEmail: '<EMAIL>',
        action: 'LOGIN',
        resource: 'Authentication',
        details: 'User logged in successfully',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-20T10:30:00Z',
        status: 'SUCCESS'
      },
      {
        id: '2',
        userId: '2',
        userName: 'Jane Smith',
        userEmail: '<EMAIL>',
        action: 'CHALLENGE_COMPLETE',
        resource: 'Challenge',
        details: 'Completed challenge: Build a React Component',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        timestamp: '2024-01-20T10:25:00Z',
        status: 'SUCCESS'
      },
      {
        id: '3',
        userId: '3',
        userName: 'Bob Wilson',
        userEmail: '<EMAIL>',
        action: 'LOGIN_FAILED',
        resource: 'Authentication',
        details: 'Failed login attempt - invalid password',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        timestamp: '2024-01-20T10:20:00Z',
        status: 'FAILED'
      },
      {
        id: '4',
        userId: '1',
        userName: 'John Doe',
        userEmail: '<EMAIL>',
        action: 'AI_ASSISTANT_USE',
        resource: 'AI Assistant',
        details: 'Generated code using AI assistant',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        timestamp: '2024-01-20T10:15:00Z',
        status: 'SUCCESS'
      },
      {
        id: '5',
        userId: '4',
        userName: 'Alice Johnson',
        userEmail: '<EMAIL>',
        action: 'CREDIT_PURCHASE',
        resource: 'Credits',
        details: 'Purchased 500 credits - Pro Pack',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
        timestamp: '2024-01-20T10:10:00Z',
        status: 'SUCCESS'
      }
    ]

    const mockMetrics: SystemMetric[] = [
      {
        id: '1',
        name: 'Active Users',
        value: 1247,
        unit: 'users',
        change: 12.5,
        status: 'good'
      },
      {
        id: '2',
        name: 'API Response Time',
        value: 245,
        unit: 'ms',
        change: -8.2,
        status: 'good'
      },
      {
        id: '3',
        name: 'Error Rate',
        value: 2.1,
        unit: '%',
        change: 15.3,
        status: 'warning'
      },
      {
        id: '4',
        name: 'Database Connections',
        value: 87,
        unit: 'connections',
        change: 5.7,
        status: 'good'
      },
      {
        id: '5',
        name: 'Memory Usage',
        value: 78.5,
        unit: '%',
        change: 3.2,
        status: 'warning'
      },
      {
        id: '6',
        name: 'CPU Usage',
        value: 45.2,
        unit: '%',
        change: -2.1,
        status: 'good'
      }
    ]
    
    setTimeout(() => {
      setActivities(mockActivities)
      setMetrics(mockMetrics)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredActivities = activities.filter(activity => {
    const matchesSearch = activity.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.details.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAction = actionFilter === 'all' || activity.action.includes(actionFilter.toUpperCase())
    const matchesStatus = statusFilter === 'all' || activity.status === statusFilter
    
    return matchesSearch && matchesAction && matchesStatus
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'FAILED': return <XCircle className="h-4 w-4 text-red-600" />
      case 'WARNING': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS': return 'bg-green-100 text-green-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      case 'WARNING': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Activity Monitor
        </CardTitle>
        <CardDescription>
          Monitor system activity, user actions, and performance metrics
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="activities">Activity Logs</TabsTrigger>
            <TabsTrigger value="metrics">System Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="activities" className="space-y-4">
            {/* Activity Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search activities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={actionFilter} onValueChange={setActionFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Actions</SelectItem>
                  <SelectItem value="login">Login</SelectItem>
                  <SelectItem value="challenge">Challenge</SelectItem>
                  <SelectItem value="ai">AI Assistant</SelectItem>
                  <SelectItem value="credit">Credits</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="SUCCESS">Success</SelectItem>
                  <SelectItem value="FAILED">Failed</SelectItem>
                  <SelectItem value="WARNING">Warning</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Activities Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Resource</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Timestamp</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        Loading activities...
                      </TableCell>
                    </TableRow>
                  ) : filteredActivities.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        No activities found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredActivities.map((activity) => (
                      <TableRow key={activity.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{activity.userName}</div>
                            <div className="text-sm text-muted-foreground">{activity.userEmail}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="text-sm bg-muted px-2 py-1 rounded">
                            {activity.action}
                          </code>
                        </TableCell>
                        <TableCell>{activity.resource}</TableCell>
                        <TableCell className="max-w-xs truncate">{activity.details}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(activity.status)}
                            <Badge className={getStatusColor(activity.status)}>
                              {activity.status}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell className="text-sm">
                          {formatTimestamp(activity.timestamp)}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="metrics" className="space-y-4">
            {/* System Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {loading ? (
                Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                metrics.map((metric) => (
                  <Card key={metric.id}>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">
                            {metric.name}
                          </p>
                          <p className="text-2xl font-bold">
                            {metric.value.toLocaleString()} {metric.unit}
                          </p>
                          <p className={`text-sm ${getMetricStatusColor(metric.status)}`}>
                            {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}% from last hour
                          </p>
                        </div>
                        <div className={`p-2 rounded-full ${
                          metric.status === 'good' ? 'bg-green-100' :
                          metric.status === 'warning' ? 'bg-yellow-100' : 'bg-red-100'
                        }`}>
                          {metric.name.includes('Users') && <Users className={`h-4 w-4 ${getMetricStatusColor(metric.status)}`} />}
                          {metric.name.includes('Response') && <Activity className={`h-4 w-4 ${getMetricStatusColor(metric.status)}`} />}
                          {metric.name.includes('Error') && <AlertTriangle className={`h-4 w-4 ${getMetricStatusColor(metric.status)}`} />}
                          {metric.name.includes('Database') && <Activity className={`h-4 w-4 ${getMetricStatusColor(metric.status)}`} />}
                          {metric.name.includes('Memory') && <Activity className={`h-4 w-4 ${getMetricStatusColor(metric.status)}`} />}
                          {metric.name.includes('CPU') && <Activity className={`h-4 w-4 ${getMetricStatusColor(metric.status)}`} />}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>

            {/* Real-time Status */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">System Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm">API Services: Operational</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm">Database: Operational</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                    <span className="text-sm">AI Services: Degraded</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
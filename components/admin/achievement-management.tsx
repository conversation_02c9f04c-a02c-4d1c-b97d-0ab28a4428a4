'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Search, Plus, Edit, Trash2, Award, Star } from 'lucide-react'

interface Achievement {
  id: string
  title: string
  description: string
  type: 'MILESTONE' | 'STREAK' | 'SPECIAL'
  category: string
  points: number
  requirement: string
  icon: string
  status: 'ACTIVE' | 'INACTIVE'
  unlockedBy: number
  createdAt: string
}

export function AchievementManagement() {
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newAchievement, setNewAchievement] = useState({
    title: '',
    description: '',
    type: 'MILESTONE' as const,
    category: '',
    points: 50,
    requirement: '',
    icon: '🏆'
  })

  useEffect(() => {
    // Mock data - replace with actual API call
    const mockAchievements: Achievement[] = [
      {
        id: '1',
        title: 'First Steps',
        description: 'Complete your first challenge',
        type: 'MILESTONE',
        category: 'Getting Started',
        points: 50,
        requirement: 'Complete 1 challenge',
        icon: '🎯',
        status: 'ACTIVE',
        unlockedBy: 156,
        createdAt: '2024-01-15'
      },
      {
        id: '2',
        title: 'Code Warrior',
        description: 'Complete 10 challenges in a row',
        type: 'STREAK',
        category: 'Consistency',
        points: 200,
        requirement: '10 day streak',
        icon: '⚔️',
        status: 'ACTIVE',
        unlockedBy: 45,
        createdAt: '2024-01-10'
      },
      {
        id: '3',
        title: 'Master Developer',
        description: 'Complete 100 challenges',
        type: 'MILESTONE',
        category: 'Mastery',
        points: 1000,
        requirement: 'Complete 100 challenges',
        icon: '👑',
        status: 'ACTIVE',
        unlockedBy: 12,
        createdAt: '2024-01-05'
      },
      {
        id: '4',
        title: 'Beta Tester',
        description: 'Participated in the beta program',
        type: 'SPECIAL',
        category: 'Special',
        points: 500,
        requirement: 'Beta participation',
        icon: '🚀',
        status: 'INACTIVE',
        unlockedBy: 89,
        createdAt: '2024-01-01'
      }
    ]
    
    setTimeout(() => {
      setAchievements(mockAchievements)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredAchievements = achievements.filter(achievement => {
    const matchesSearch = achievement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         achievement.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === 'all' || achievement.type === typeFilter
    const matchesStatus = statusFilter === 'all' || achievement.status === statusFilter
    
    return matchesSearch && matchesType && matchesStatus
  })

  const handleCreateAchievement = async () => {
    const achievement: Achievement = {
      id: Date.now().toString(),
      ...newAchievement,
      status: 'ACTIVE',
      unlockedBy: 0,
      createdAt: new Date().toISOString().split('T')[0]
    }
    
    setAchievements(prev => [achievement, ...prev])
    setNewAchievement({
      title: '',
      description: '',
      type: 'MILESTONE',
      category: '',
      points: 50,
      requirement: '',
      icon: '🏆'
    })
    setIsCreateDialogOpen(false)
  }

  const handleStatusChange = async (achievementId: string, newStatus: 'ACTIVE' | 'INACTIVE') => {
    setAchievements(prev => prev.map(achievement => 
      achievement.id === achievementId ? { ...achievement, status: newStatus } : achievement
    ))
  }

  const handleDeleteAchievement = async (achievementId: string) => {
    if (confirm('Are you sure you want to delete this achievement?')) {
      setAchievements(prev => prev.filter(achievement => achievement.id !== achievementId))
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'MILESTONE': return 'bg-blue-100 text-blue-800'
      case 'STREAK': return 'bg-orange-100 text-orange-800'
      case 'SPECIAL': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="h-5 w-5" />
          Achievement Management
        </CardTitle>
        <CardDescription>
          Create and manage achievements to motivate users
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search achievements..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="MILESTONE">Milestone</SelectItem>
              <SelectItem value="STREAK">Streak</SelectItem>
              <SelectItem value="SPECIAL">Special</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="ACTIVE">Active</SelectItem>
              <SelectItem value="INACTIVE">Inactive</SelectItem>
            </SelectContent>
          </Select>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Achievement
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New Achievement</DialogTitle>
                <DialogDescription>
                  Add a new achievement to motivate users
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Title</label>
                  <Input
                    value={newAchievement.title}
                    onChange={(e) => setNewAchievement(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Achievement title"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    value={newAchievement.description}
                    onChange={(e) => setNewAchievement(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Achievement description"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Type</label>
                    <Select
                      value={newAchievement.type}
                      onValueChange={(value) => setNewAchievement(prev => ({ ...prev, type: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MILESTONE">Milestone</SelectItem>
                        <SelectItem value="STREAK">Streak</SelectItem>
                        <SelectItem value="SPECIAL">Special</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Points</label>
                    <Input
                      type="number"
                      value={newAchievement.points}
                      onChange={(e) => setNewAchievement(prev => ({ ...prev, points: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Category</label>
                  <Input
                    value={newAchievement.category}
                    onChange={(e) => setNewAchievement(prev => ({ ...prev, category: e.target.value }))}
                    placeholder="e.g., Getting Started, Mastery"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Requirement</label>
                  <Input
                    value={newAchievement.requirement}
                    onChange={(e) => setNewAchievement(prev => ({ ...prev, requirement: e.target.value }))}
                    placeholder="e.g., Complete 5 challenges"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Icon (Emoji)</label>
                  <Input
                    value={newAchievement.icon}
                    onChange={(e) => setNewAchievement(prev => ({ ...prev, icon: e.target.value }))}
                    placeholder="🏆"
                  />
                </div>
                <div className="flex gap-2 pt-4">
                  <Button onClick={handleCreateAchievement} className="flex-1">
                    Create Achievement
                  </Button>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Achievements Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Achievement</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Points</TableHead>
                <TableHead>Unlocked By</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    Loading achievements...
                  </TableCell>
                </TableRow>
              ) : filteredAchievements.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    No achievements found
                  </TableCell>
                </TableRow>
              ) : (
                filteredAchievements.map((achievement) => (
                  <TableRow key={achievement.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{achievement.icon}</span>
                        <div>
                          <div className="font-medium">{achievement.title}</div>
                          <div className="text-sm text-muted-foreground line-clamp-2">
                            {achievement.description}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getTypeColor(achievement.type)}>
                        {achievement.type}
                      </Badge>
                    </TableCell>
                    <TableCell>{achievement.category}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-500" />
                        {achievement.points}
                      </div>
                    </TableCell>
                    <TableCell>{achievement.unlockedBy} users</TableCell>
                    <TableCell>
                      <Badge variant={achievement.status === 'ACTIVE' ? 'default' : 'secondary'}>
                        {achievement.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStatusChange(
                            achievement.id, 
                            achievement.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
                          )}
                        >
                          {achievement.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteAchievement(achievement.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
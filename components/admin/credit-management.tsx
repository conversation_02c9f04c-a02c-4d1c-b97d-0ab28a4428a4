'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Search, Plus, Edit, Trash2, Coins, TrendingUp, TrendingDown } from 'lucide-react'

interface CreditTransaction {
  id: string
  userId: string
  userName: string
  userEmail: string
  type: 'EARNED' | 'SPENT' | 'BONUS' | 'REFUND'
  amount: number
  description: string
  createdAt: string
}

interface CreditPackage {
  id: string
  name: string
  credits: number
  price: number
  description: string
  status: 'ACTIVE' | 'INACTIVE'
  purchases: number
}

export function CreditManagement() {
  const [transactions, setTransactions] = useState<CreditTransaction[]>([])
  const [packages, setPackages] = useState<CreditPackage[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'transactions' | 'packages'>('transactions')
  const [isCreatePackageDialogOpen, setIsCreatePackageDialogOpen] = useState(false)
  const [newPackage, setNewPackage] = useState({
    name: '',
    credits: 100,
    price: 9.99,
    description: ''
  })

  useEffect(() => {
    // Mock data - replace with actual API calls
    const mockTransactions: CreditTransaction[] = [
      {
        id: '1',
        userId: '1',
        userName: 'John Doe',
        userEmail: '<EMAIL>',
        type: 'EARNED',
        amount: 50,
        description: 'Completed challenge: Build a React Component',
        createdAt: '2024-01-20T10:30:00Z'
      },
      {
        id: '2',
        userId: '2',
        userName: 'Jane Smith',
        userEmail: '<EMAIL>',
        type: 'SPENT',
        amount: -25,
        description: 'Used AI Assistant for code generation',
        createdAt: '2024-01-20T09:15:00Z'
      },
      {
        id: '3',
        userId: '1',
        userName: 'John Doe',
        userEmail: '<EMAIL>',
        type: 'BONUS',
        amount: 100,
        description: 'Welcome bonus for new user',
        createdAt: '2024-01-19T14:20:00Z'
      }
    ]

    const mockPackages: CreditPackage[] = [
      {
        id: '1',
        name: 'Starter Pack',
        credits: 100,
        price: 9.99,
        description: 'Perfect for getting started',
        status: 'ACTIVE',
        purchases: 45
      },
      {
        id: '2',
        name: 'Pro Pack',
        credits: 500,
        price: 39.99,
        description: 'For serious developers',
        status: 'ACTIVE',
        purchases: 23
      },
      {
        id: '3',
        name: 'Enterprise Pack',
        credits: 2000,
        price: 149.99,
        description: 'For teams and organizations',
        status: 'ACTIVE',
        purchases: 8
      }
    ]
    
    setTimeout(() => {
      setTransactions(mockTransactions)
      setPackages(mockPackages)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === 'all' || transaction.type === typeFilter
    
    return matchesSearch && matchesType
  })

  const handleCreatePackage = async () => {
    const creditPackage: CreditPackage = {
      id: Date.now().toString(),
      ...newPackage,
      status: 'ACTIVE',
      purchases: 0
    }
    
    setPackages(prev => [creditPackage, ...prev])
    setNewPackage({
      name: '',
      credits: 100,
      price: 9.99,
      description: ''
    })
    setIsCreatePackageDialogOpen(false)
  }

  const handlePackageStatusChange = async (packageId: string, newStatus: 'ACTIVE' | 'INACTIVE') => {
    setPackages(prev => prev.map(pkg => 
      pkg.id === packageId ? { ...pkg, status: newStatus } : pkg
    ))
  }

  const handleDeletePackage = async (packageId: string) => {
    if (confirm('Are you sure you want to delete this credit package?')) {
      setPackages(prev => prev.filter(pkg => pkg.id !== packageId))
    }
  }

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'EARNED': return 'bg-green-100 text-green-800'
      case 'SPENT': return 'bg-red-100 text-red-800'
      case 'BONUS': return 'bg-blue-100 text-blue-800'
      case 'REFUND': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'EARNED': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'SPENT': return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'BONUS': return <Coins className="h-4 w-4 text-blue-600" />
      case 'REFUND': return <TrendingUp className="h-4 w-4 text-yellow-600" />
      default: return <Coins className="h-4 w-4" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Coins className="h-5 w-5" />
          Credit Management
        </CardTitle>
        <CardDescription>
          Manage user credits, transactions, and credit packages
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Tabs */}
        <div className="flex space-x-1 bg-muted p-1 rounded-lg">
          <Button
            variant={activeTab === 'transactions' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('transactions')}
            className="flex-1"
          >
            Transactions
          </Button>
          <Button
            variant={activeTab === 'packages' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('packages')}
            className="flex-1"
          >
            Credit Packages
          </Button>
        </div>

        {activeTab === 'transactions' && (
          <>
            {/* Transaction Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="EARNED">Earned</SelectItem>
                  <SelectItem value="SPENT">Spent</SelectItem>
                  <SelectItem value="BONUS">Bonus</SelectItem>
                  <SelectItem value="REFUND">Refund</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Transactions Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        Loading transactions...
                      </TableCell>
                    </TableRow>
                  ) : filteredTransactions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        No transactions found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{transaction.userName}</div>
                            <div className="text-sm text-muted-foreground">{transaction.userEmail}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getTransactionTypeColor(transaction.type)}>
                            {transaction.type}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getTransactionIcon(transaction.type)}
                            <span className={transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}>
                              {transaction.amount > 0 ? '+' : ''}{transaction.amount}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{transaction.description}</TableCell>
                        <TableCell>
                          {new Date(transaction.createdAt).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </>
        )}

        {activeTab === 'packages' && (
          <>
            {/* Package Actions */}
            <div className="flex justify-between items-center">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search packages..."
                  className="pl-10"
                />
              </div>
              
              <Dialog open={isCreatePackageDialogOpen} onOpenChange={setIsCreatePackageDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Package
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Create Credit Package</DialogTitle>
                    <DialogDescription>
                      Add a new credit package for users to purchase
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Package Name</label>
                      <Input
                        value={newPackage.name}
                        onChange={(e) => setNewPackage(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="e.g., Starter Pack"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Credits</label>
                        <Input
                          type="number"
                          value={newPackage.credits}
                          onChange={(e) => setNewPackage(prev => ({ ...prev, credits: parseInt(e.target.value) }))}
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Price ($)</label>
                        <Input
                          type="number"
                          step="0.01"
                          value={newPackage.price}
                          onChange={(e) => setNewPackage(prev => ({ ...prev, price: parseFloat(e.target.value) }))}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Description</label>
                      <Input
                        value={newPackage.description}
                        onChange={(e) => setNewPackage(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Package description"
                      />
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button onClick={handleCreatePackage} className="flex-1">
                        Create Package
                      </Button>
                      <Button variant="outline" onClick={() => setIsCreatePackageDialogOpen(false)}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Packages Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Package</TableHead>
                    <TableHead>Credits</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Purchases</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        Loading packages...
                      </TableCell>
                    </TableRow>
                  ) : packages.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        No packages found
                      </TableCell>
                    </TableRow>
                  ) : (
                    packages.map((pkg) => (
                      <TableRow key={pkg.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{pkg.name}</div>
                            <div className="text-sm text-muted-foreground">{pkg.description}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Coins className="h-4 w-4 text-yellow-500" />
                            {pkg.credits}
                          </div>
                        </TableCell>
                        <TableCell>${pkg.price}</TableCell>
                        <TableCell>{pkg.purchases} sales</TableCell>
                        <TableCell>
                          <Badge variant={pkg.status === 'ACTIVE' ? 'default' : 'secondary'}>
                            {pkg.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePackageStatusChange(
                                pkg.id, 
                                pkg.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
                              )}
                            >
                              {pkg.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeletePackage(pkg.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Search, Plus, Edit, Trash2, Trophy, Target } from 'lucide-react'

interface Challenge {
  id: string
  title: string
  description: string
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  category: string
  points: number
  status: 'ACTIVE' | 'INACTIVE'
  completions: number
  createdAt: string
}

export function ChallengeManagement() {
  const [challenges, setChallenges] = useState<Challenge[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [difficultyFilter, setDifficultyFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newChallenge, setNewChallenge] = useState({
    title: '',
    description: '',
    difficulty: 'EASY' as const,
    category: '',
    points: 100
  })

  useEffect(() => {
    // Mock data - replace with actual API call
    const mockChallenges: Challenge[] = [
      {
        id: '1',
        title: 'Build a React Component',
        description: 'Create a reusable button component with TypeScript',
        difficulty: 'EASY',
        category: 'Frontend',
        points: 100,
        status: 'ACTIVE',
        completions: 45,
        createdAt: '2024-01-15'
      },
      {
        id: '2',
        title: 'API Integration Challenge',
        description: 'Build a REST API with authentication',
        difficulty: 'MEDIUM',
        category: 'Backend',
        points: 250,
        status: 'ACTIVE',
        completions: 23,
        createdAt: '2024-01-10'
      },
      {
        id: '3',
        title: 'Full Stack Application',
        description: 'Build a complete CRUD application with React and Node.js',
        difficulty: 'HARD',
        category: 'Full Stack',
        points: 500,
        status: 'INACTIVE',
        completions: 8,
        createdAt: '2024-01-05'
      }
    ]
    
    setTimeout(() => {
      setChallenges(mockChallenges)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredChallenges = challenges.filter(challenge => {
    const matchesSearch = challenge.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         challenge.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDifficulty = difficultyFilter === 'all' || challenge.difficulty === difficultyFilter
    const matchesStatus = statusFilter === 'all' || challenge.status === statusFilter
    
    return matchesSearch && matchesDifficulty && matchesStatus
  })

  const handleCreateChallenge = async () => {
    const challenge: Challenge = {
      id: Date.now().toString(),
      ...newChallenge,
      status: 'ACTIVE',
      completions: 0,
      createdAt: new Date().toISOString().split('T')[0]
    }
    
    setChallenges(prev => [challenge, ...prev])
    setNewChallenge({
      title: '',
      description: '',
      difficulty: 'EASY',
      category: '',
      points: 100
    })
    setIsCreateDialogOpen(false)
  }

  const handleStatusChange = async (challengeId: string, newStatus: 'ACTIVE' | 'INACTIVE') => {
    setChallenges(prev => prev.map(challenge => 
      challenge.id === challengeId ? { ...challenge, status: newStatus } : challenge
    ))
  }

  const handleDeleteChallenge = async (challengeId: string) => {
    if (confirm('Are you sure you want to delete this challenge?')) {
      setChallenges(prev => prev.filter(challenge => challenge.id !== challengeId))
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'HARD': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trophy className="h-5 w-5" />
          Challenge Management
        </CardTitle>
        <CardDescription>
          Create and manage coding challenges for users
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search challenges..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Difficulty" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="EASY">Easy</SelectItem>
              <SelectItem value="MEDIUM">Medium</SelectItem>
              <SelectItem value="HARD">Hard</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="ACTIVE">Active</SelectItem>
              <SelectItem value="INACTIVE">Inactive</SelectItem>
            </SelectContent>
          </Select>
          
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Challenge
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New Challenge</DialogTitle>
                <DialogDescription>
                  Add a new coding challenge for users to complete
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Title</label>
                  <Input
                    value={newChallenge.title}
                    onChange={(e) => setNewChallenge(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Challenge title"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    value={newChallenge.description}
                    onChange={(e) => setNewChallenge(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Challenge description"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Difficulty</label>
                    <Select
                      value={newChallenge.difficulty}
                      onValueChange={(value) => setNewChallenge(prev => ({ ...prev, difficulty: value as any }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="EASY">Easy</SelectItem>
                        <SelectItem value="MEDIUM">Medium</SelectItem>
                        <SelectItem value="HARD">Hard</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Points</label>
                    <Input
                      type="number"
                      value={newChallenge.points}
                      onChange={(e) => setNewChallenge(prev => ({ ...prev, points: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Category</label>
                  <Input
                    value={newChallenge.category}
                    onChange={(e) => setNewChallenge(prev => ({ ...prev, category: e.target.value }))}
                    placeholder="e.g., Frontend, Backend, Full Stack"
                  />
                </div>
                <div className="flex gap-2 pt-4">
                  <Button onClick={handleCreateChallenge} className="flex-1">
                    Create Challenge
                  </Button>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Challenges Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Challenge</TableHead>
                <TableHead>Difficulty</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Points</TableHead>
                <TableHead>Completions</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    Loading challenges...
                  </TableCell>
                </TableRow>
              ) : filteredChallenges.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    No challenges found
                  </TableCell>
                </TableRow>
              ) : (
                filteredChallenges.map((challenge) => (
                  <TableRow key={challenge.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{challenge.title}</div>
                        <div className="text-sm text-muted-foreground line-clamp-2">
                          {challenge.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getDifficultyColor(challenge.difficulty)}>
                        {challenge.difficulty}
                      </Badge>
                    </TableCell>
                    <TableCell>{challenge.category}</TableCell>
                    <TableCell>{challenge.points}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Target className="h-4 w-4 text-muted-foreground" />
                        {challenge.completions}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={challenge.status === 'ACTIVE' ? 'default' : 'secondary'}>
                        {challenge.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStatusChange(
                            challenge.id, 
                            challenge.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
                          )}
                        >
                          {challenge.status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteChallenge(challenge.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
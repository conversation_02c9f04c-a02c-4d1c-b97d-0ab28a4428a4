'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Users, 
  Target, 
  Trophy, 
  Coins, 
  TrendingUp, 
  Activity,
  Calendar,
  Zap,
  Star,
  BarChart3
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AdminStatsData {
  users: {
    total: number
    active: number
    newThisWeek: number
    newThisMonth: number
  }
  challenges: {
    total: number
    active: number
    completed: number
    averageCompletion: number
  }
  achievements: {
    total: number
    unlocked: number
    averagePerUser: number
  }
  credits: {
    totalIssued: number
    totalSpent: number
    averageBalance: number
  }
  engagement: {
    dailyActiveUsers: number
    weeklyActiveUsers: number
    averageSessionTime: number
    retentionRate: number
  }
}

interface StatCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  trend?: {
    value: number
    label: string
    positive: boolean
  }
  color?: string
  delay?: number
}

function StatCard({ title, value, subtitle, icon, trend, color = "primary", delay = 0 }: StatCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
    >
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {title}
              </p>
              <div className="flex items-baseline gap-2">
                <p className="text-3xl font-bold">
                  {typeof value === 'number' ? value.toLocaleString() : value}
                </p>
                {subtitle && (
                  <p className="text-sm text-muted-foreground">
                    {subtitle}
                  </p>
                )}
              </div>
              {trend && (
                <div className="flex items-center gap-1">
                  <TrendingUp 
                    className={cn(
                      "h-3 w-3",
                      trend.positive ? "text-green-600" : "text-red-600"
                    )} 
                  />
                  <span className={cn(
                    "text-xs font-medium",
                    trend.positive ? "text-green-600" : "text-red-600"
                  )}>
                    {trend.positive ? '+' : ''}{trend.value}% {trend.label}
                  </span>
                </div>
              )}
            </div>
            <div className={cn(
              "p-3 rounded-full",
              color === "primary" && "bg-primary/10 text-primary",
              color === "green" && "bg-green-100 text-green-600",
              color === "blue" && "bg-blue-100 text-blue-600",
              color === "purple" && "bg-purple-100 text-purple-600",
              color === "orange" && "bg-orange-100 text-orange-600"
            )}>
              {icon}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export function AdminStats() {
  const [stats, setStats] = useState<AdminStatsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Mock data for demonstration
    const mockStats: AdminStatsData = {
      users: {
        total: 1247,
        active: 892,
        newThisWeek: 34,
        newThisMonth: 156
      },
      challenges: {
        total: 45,
        active: 38,
        completed: 2847,
        averageCompletion: 73.2
      },
      achievements: {
        total: 25,
        unlocked: 1834,
        averagePerUser: 4.7
      },
      credits: {
        totalIssued: 125000,
        totalSpent: 89000,
        averageBalance: 287
      },
      engagement: {
        dailyActiveUsers: 234,
        weeklyActiveUsers: 567,
        averageSessionTime: 18.5,
        retentionRate: 68.4
      }
    }

    setTimeout(() => {
      setStats(mockStats)
      setLoading(false)
    }, 1000)
  }, [])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                  <div className="h-8 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !stats) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Failed to load admin statistics</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Platform Overview</h2>
          <p className="text-muted-foreground">
            Real-time statistics and performance metrics
          </p>
        </div>
        <Badge variant="outline" className="gap-1">
          <Calendar className="h-3 w-3" />
          Last updated: {new Date().toLocaleTimeString()}
        </Badge>
      </div>

      {/* User Statistics */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Users className="h-5 w-5" />
          User Statistics
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Users"
            value={stats.users.total}
            icon={<Users className="h-5 w-5" />}
            color="blue"
            trend={{
              value: 12.5,
              label: "this month",
              positive: true
            }}
            delay={0}
          />
          <StatCard
            title="Active Users"
            value={stats.users.active}
            subtitle={`${Math.round((stats.users.active / stats.users.total) * 100)}% of total`}
            icon={<Activity className="h-5 w-5" />}
            color="green"
            trend={{
              value: 8.3,
              label: "this week",
              positive: true
            }}
            delay={0.1}
          />
          <StatCard
            title="New This Week"
            value={stats.users.newThisWeek}
            icon={<TrendingUp className="h-5 w-5" />}
            color="purple"
            trend={{
              value: 15.2,
              label: "vs last week",
              positive: true
            }}
            delay={0.2}
          />
          <StatCard
            title="New This Month"
            value={stats.users.newThisMonth}
            icon={<Calendar className="h-5 w-5" />}
            color="orange"
            trend={{
              value: 22.1,
              label: "vs last month",
              positive: true
            }}
            delay={0.3}
          />
        </div>
      </div>

      {/* Challenge Statistics */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Target className="h-5 w-5" />
          Challenge Performance
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Challenges"
            value={stats.challenges.total}
            subtitle={`${stats.challenges.active} active`}
            icon={<Target className="h-5 w-5" />}
            color="primary"
            delay={0}
          />
          <StatCard
            title="Completions"
            value={stats.challenges.completed}
            icon={<Trophy className="h-5 w-5" />}
            color="green"
            trend={{
              value: 18.7,
              label: "this month",
              positive: true
            }}
            delay={0.1}
          />
          <StatCard
            title="Completion Rate"
            value={`${stats.challenges.averageCompletion}%`}
            icon={<BarChart3 className="h-5 w-5" />}
            color="blue"
            trend={{
              value: 5.2,
              label: "improvement",
              positive: true
            }}
            delay={0.2}
          />
          <StatCard
            title="Avg per User"
            value={Math.round(stats.challenges.completed / stats.users.total * 10) / 10}
            subtitle="challenges"
            icon={<Star className="h-5 w-5" />}
            color="purple"
            delay={0.3}
          />
        </div>
      </div>

      {/* Credit & Achievement Statistics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Credits */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Coins className="h-5 w-5" />
              Credit System
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Total Issued</p>
                <p className="text-2xl font-bold text-green-600">
                  {stats.credits.totalIssued.toLocaleString()}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Total Spent</p>
                <p className="text-2xl font-bold text-blue-600">
                  {stats.credits.totalSpent.toLocaleString()}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Utilization Rate</span>
                <span className="font-medium">
                  {Math.round((stats.credits.totalSpent / stats.credits.totalIssued) * 100)}%
                </span>
              </div>
              <Progress 
                value={(stats.credits.totalSpent / stats.credits.totalIssued) * 100} 
                className="h-2" 
              />
            </div>
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Average Balance</span>
                <span className="font-semibold">{stats.credits.averageBalance}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Achievements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Achievement System
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Total Available</p>
                <p className="text-2xl font-bold text-purple-600">
                  {stats.achievements.total}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Total Unlocked</p>
                <p className="text-2xl font-bold text-orange-600">
                  {stats.achievements.unlocked.toLocaleString()}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Unlock Rate</span>
                <span className="font-medium">
                  {Math.round((stats.achievements.unlocked / (stats.achievements.total * stats.users.total)) * 100)}%
                </span>
              </div>
              <Progress 
                value={(stats.achievements.unlocked / (stats.achievements.total * stats.users.total)) * 100} 
                className="h-2" 
              />
            </div>
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Average per User</span>
                <span className="font-semibold">{stats.achievements.averagePerUser}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Engagement Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Engagement Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Daily Active Users</p>
              <p className="text-2xl font-bold">{stats.engagement.dailyActiveUsers}</p>
              <p className="text-xs text-green-600">+12% vs yesterday</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Weekly Active Users</p>
              <p className="text-2xl font-bold">{stats.engagement.weeklyActiveUsers}</p>
              <p className="text-xs text-green-600">+8% vs last week</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Avg Session Time</p>
              <p className="text-2xl font-bold">{stats.engagement.averageSessionTime}m</p>
              <p className="text-xs text-blue-600">+2.3m vs last week</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Retention Rate</p>
              <p className="text-2xl font-bold">{stats.engagement.retentionRate}%</p>
              <p className="text-xs text-green-600">+5.2% vs last month</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

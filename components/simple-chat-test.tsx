"use client"

import { useState } from 'react'
import { useChat } from '@ai-sdk/react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export function SimpleChatTest() {
  const [testResult, setTestResult] = useState<string>('')
  const { messages, input, handleInputChange, handleSubmit, status } = useChat({
    api: '/api/chat-simple'
  })

  const testAPI = async () => {
    try {
      const response = await fetch('/api/test')
      const data = await response.json()
      setTestResult(JSON.stringify(data, null, 2))
    } catch (error) {
      setTestResult(`Error: ${error}`)
    }
  }

  return (
    <div className="max-w-md mx-auto p-4">
      <h2 className="text-xl font-bold mb-4">Simple Chat Test</h2>

      <div className="mb-4">
        <Button onClick={testAPI} className="mb-2">Test API</Button>
        {testResult && (
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-32">
            {testResult}
          </pre>
        )}
      </div>

      <div className="space-y-4 mb-4">
        {messages.map((message) => (
          <div key={message.id} className={`p-2 rounded ${
            message.role === 'user' ? 'bg-blue-100 ml-8' : 'bg-gray-100 mr-8'
          }`}>
            <strong>{message.role}:</strong> {message.content}
          </div>
        ))}
      </div>

      <form onSubmit={handleSubmit} className="flex gap-2">
        <Input
          value={input}
          onChange={handleInputChange}
          placeholder="Type a message..."
          disabled={status === 'streaming'}
        />
        <Button type="submit" disabled={!input.trim() || status === 'streaming'}>
          {status === 'streaming' ? 'Sending...' : 'Send'}
        </Button>
      </form>

      <div className="mt-2 text-sm text-gray-500">
        Status: {status}
      </div>
    </div>
  )
}

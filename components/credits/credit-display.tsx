'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Coins, Clock, Calendar, TrendingUp, Info } from 'lucide-react'
import { useCredits } from '@/hooks/use-credits'
import { cn } from '@/lib/utils'

interface CreditDisplayProps {
  variant?: 'compact' | 'detailed' | 'minimal'
  className?: string
  showHistory?: boolean
}

export function CreditDisplay({ 
  variant = 'compact', 
  className,
  showHistory = false 
}: CreditDisplayProps) {
  const { balance, loading, error, refreshBalance } = useCredits()

  if (loading) {
    return (
      <div className={cn("animate-pulse", className)}>
        <div className="h-8 bg-muted rounded-md"></div>
      </div>
    )
  }

  if (error || !balance) {
    return (
      <div className={cn("text-sm text-muted-foreground", className)}>
        Credits unavailable
      </div>
    )
  }

  const dailyProgress = (balance.dailyCredits / balance.dailyLimit) * 100
  const monthlyProgress = (balance.monthlyCredits / balance.monthlyLimit) * 100

  if (variant === 'minimal') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge 
              variant="secondary" 
              className={cn("cursor-pointer", className)}
              onClick={refreshBalance}
            >
              <Coins className="h-3 w-3 mr-1" />
              {balance.totalCredits}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-sm">
              <div>Total Credits: {balance.totalCredits}</div>
              <div>Daily: {balance.dailyCredits}/{balance.dailyLimit}</div>
              <div>Monthly: {balance.monthlyCredits}/{balance.monthlyLimit}</div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  if (variant === 'compact') {
    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn("flex items-center gap-2", className)}
      >
        <Badge variant="outline" className="flex items-center gap-1">
          <Coins className="h-3 w-3" />
          {balance.totalCredits}
        </Badge>
        
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge 
                variant={dailyProgress > 20 ? "secondary" : "destructive"}
                className="flex items-center gap-1 cursor-pointer"
              >
                <Clock className="h-3 w-3" />
                {balance.dailyCredits}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              Daily credits: {balance.dailyCredits}/{balance.dailyLimit}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge 
                variant={monthlyProgress > 20 ? "secondary" : "destructive"}
                className="flex items-center gap-1 cursor-pointer"
              >
                <Calendar className="h-3 w-3" />
                {balance.monthlyCredits}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              Monthly credits: {balance.monthlyCredits}/{balance.monthlyLimit}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </motion.div>
    )
  }

  // Detailed variant
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={className}
    >
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Coins className="h-5 w-5 text-primary" />
                <h3 className="font-semibold">Credits</h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshBalance}
                className="h-8 w-8 p-0"
              >
                <TrendingUp className="h-4 w-4" />
              </Button>
            </div>

            {/* Total Credits */}
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">
                {balance.totalCredits}
              </div>
              <div className="text-sm text-muted-foreground">
                Total Credits Available
              </div>
            </div>

            {/* Daily Limit */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Daily Limit
                </span>
                <span>{balance.dailyCredits}/{balance.dailyLimit}</span>
              </div>
              <Progress value={dailyProgress} className="h-2" />
            </div>

            {/* Monthly Limit */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Monthly Limit
                </span>
                <span>{balance.monthlyCredits}/{balance.monthlyLimit}</span>
              </div>
              <Progress value={monthlyProgress} className="h-2" />
            </div>

            {/* Info */}
            <div className="flex items-start gap-2 p-3 bg-muted/50 rounded-lg">
              <Info className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="text-xs text-muted-foreground">
                Credits are used for AI assistant interactions. Daily and monthly limits reset automatically.
              </div>
            </div>

            {/* History Button */}
            {showHistory && (
              <Button variant="outline" className="w-full" size="sm">
                View Credit History
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

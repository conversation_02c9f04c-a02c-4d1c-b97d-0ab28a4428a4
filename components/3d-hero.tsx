"use client"

import { useEffect, useRef, useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import {
  ArrowRight,
  Sparkles,
  Send,
  Mic,
  MicOff,
  Bot,
  MessageSquare,
  Volume2
} from "lucide-react"
import { streamText } from "ai"
import { xai } from "@ai-sdk/xai"
import { generateFallbackResponse } from "@/lib/fallback-ai"

interface Message {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: Date
  isStreaming?: boolean
}

interface LiveKitService {
  startAudioRecording: () => Promise<MediaStream | null>
  stopVoiceRecognition: () => void
  startVoiceRecognition: () => void
  speak: (text: string, options?: { rate?: number; pitch?: number }) => void
}

// Mock LiveKit service for demo purposes
const mockLiveKitService: LiveKitService = {
  startAudioRecording: async () => {
    try {
      return await navigator.mediaDevices.getUserMedia({ audio: true })
    } catch {
      return null
    }
  },
  stopVoiceRecognition: () => console.log("Voice recognition stopped"),
  startVoiceRecognition: () => console.log("Voice recognition started"),
  speak: (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text)
      window.speechSynthesis.speak(utterance)
    }
  }
}

export function ThreeDHero() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [isLoaded, setIsLoaded] = useState(false)

  // AI Chat State
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [showChat, setShowChat] = useState(false)
  const [voiceEnabled, setVoiceEnabled] = useState(false)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages])

  // AI Message Handling
  const handleSendMessage = async () => {
    if (input.trim() === "") return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setInput("")
    setIsProcessing(true)

    try {
      // Create streaming message
      const streamingId = (Date.now() + 1).toString()
      const streamingMsg: Message = {
        id: streamingId,
        role: "assistant",
        content: "",
        timestamp: new Date(),
        isStreaming: true,
      }

      setMessages(prev => [...prev, streamingMsg])

      // Use AI SDK for streaming response
      const result = streamText({
        model: xai("grok-beta"),
        prompt: input,
        system: "You are Soimagine's AI assistant, designed to help with digital agency tasks like web development, design, and content creation. Be helpful, creative, and professional.",
      })

      let fullResponse = ""
      for await (const chunk of result.textStream) {
        fullResponse += chunk
        setMessages(prev =>
          prev.map(msg =>
            msg.id === streamingId
              ? { ...msg, content: fullResponse }
              : msg
          )
        )
      }

      // Mark as complete
      setMessages(prev =>
        prev.map(msg =>
          msg.id === streamingId
            ? { ...msg, isStreaming: false }
            : msg
        )
      )

      // Auto-speak if voice is enabled
      if (voiceEnabled) {
        mockLiveKitService.speak(fullResponse)
      }

    } catch (error) {
      console.error("AI API error, using fallback:", error)

      const fallbackText = generateFallbackResponse(input)
      const fallbackResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: fallbackText,
        timestamp: new Date(),
      }

      setMessages(prev => [...prev, fallbackResponse])
    } finally {
      setIsProcessing(false)
    }
  }

  const toggleRecording = async () => {
    if (!isRecording) {
      const stream = await mockLiveKitService.startAudioRecording()
      if (stream) {
        setIsRecording(true)
        mockLiveKitService.startVoiceRecognition()
      }
    } else {
      setIsRecording(false)
      mockLiveKitService.stopVoiceRecognition()
    }
  }

  useEffect(() => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let animationFrameId: number
    let time = 0
    let particles: Array<{
      x: number
      y: number
      size: number
      speed: number
      color: string
      angle: number
    }> = []

    // Set canvas dimensions
    const resizeCanvas = () => {
      const { width, height } = canvas.getBoundingClientRect()
      canvas.width = width
      canvas.height = height
      initParticles()
    }

    const initParticles = () => {
      particles = []
      const particleCount = Math.floor((canvas.width * canvas.height) / 10000)

      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 2 + 1,
          speed: Math.random() * 0.5 + 0.1,
          color: `rgba(233, 30, 99, ${Math.random() * 0.5 + 0.2})`,
          angle: Math.random() * Math.PI * 2,
        })
      }
    }

    resizeCanvas()
    window.addEventListener("resize", resizeCanvas)

    const draw = () => {
      time += 0.01
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw particles
      particles.forEach((particle) => {
        ctx.fillStyle = particle.color
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fill()

        // Update particle position
        particle.x += Math.cos(particle.angle) * particle.speed
        particle.y += Math.sin(particle.angle) * particle.speed

        // Change direction slightly
        particle.angle += (Math.random() - 0.5) * 0.1

        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width
        if (particle.x > canvas.width) particle.x = 0
        if (particle.y < 0) particle.y = canvas.height
        if (particle.y > canvas.height) particle.y = 0
      })

      // Draw connections between nearby particles
      ctx.strokeStyle = "rgba(233, 30, 99, 0.1)"
      ctx.lineWidth = 0.5

      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x
          const dy = particles[i].y - particles[j].y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 100) {
            ctx.globalAlpha = 1 - distance / 100
            ctx.beginPath()
            ctx.moveTo(particles[i].x, particles[i].y)
            ctx.lineTo(particles[j].x, particles[j].y)
            ctx.stroke()
          }
        }
      }

      ctx.globalAlpha = 1

      animationFrameId = requestAnimationFrame(draw)
    }

    draw()
    setIsLoaded(true)

    return () => {
      window.removeEventListener("resize", resizeCanvas)
      cancelAnimationFrame(animationFrameId)
    }
  }, [])

  return (
    <div className="relative w-full h-[100vh] overflow-hidden">
      <canvas ref={canvasRef} className="absolute inset-0 w-full h-full" />

      <div className="relative z-10 h-full flex flex-col items-center justify-center px-4">
        {!showChat ? (
          // Hero Section
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-center"
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              Where <span className="gradient-text">Imagination</span> Meets{" "}
              <span className="gradient-text">Technology</span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Experience the future of digital agency services with our AI-powered assistant.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="gap-2 text-lg"
                onClick={() => setShowChat(true)}
              >
                <MessageSquare className="h-5 w-5" />
                Start AI Chat
              </Button>
              <Button asChild size="lg" variant="outline" className="gap-2 text-lg">
                <Link href="/services">
                  Explore Services <Sparkles className="h-5 w-5" />
                </Link>
              </Button>
            </div>
          </motion.div>
        ) : (
          // AI Chat Interface
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="w-full max-w-4xl h-[80vh] max-h-[600px]"
          >
            <Card className="h-full border-border/50 shadow-2xl backdrop-blur-sm bg-background/95">
              <CardContent className="p-0 h-full flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-border/50">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
                      <Bot className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold">Soimagine AI Assistant</h3>
                      <p className="text-sm text-muted-foreground">
                        {isProcessing ? "Thinking..." : "Ready to help"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setVoiceEnabled(!voiceEnabled)}
                      className={voiceEnabled ? "text-primary" : ""}
                    >
                      <Volume2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowChat(false)}
                    >
                      <ArrowRight className="h-4 w-4 rotate-180" />
                    </Button>
                  </div>
                </div>

                {/* Messages Area */}
                <ScrollArea className="flex-1 p-4">
                  <div className="space-y-4">
                    {messages.length === 0 ? (
                      <div className="text-center py-8">
                        <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">Welcome to Soimagine AI</h3>
                        <p className="text-muted-foreground max-w-md mx-auto">
                          I'm here to help you with digital agency tasks, web development, design, and content creation.
                        </p>
                        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-2 max-w-md mx-auto">
                          <Button
                            variant="outline"
                            className="flex items-center gap-2 justify-start text-sm"
                            onClick={() => setInput("Tell me about Soimagine's services")}
                          >
                            <Sparkles className="h-4 w-4 text-primary" />
                            Our Services
                          </Button>
                          <Button
                            variant="outline"
                            className="flex items-center gap-2 justify-start text-sm"
                            onClick={() => setInput("Help me create a website")}
                          >
                            <Bot className="h-4 w-4 text-primary" />
                            Web Development
                          </Button>
                        </div>
                      </div>
                    ) : (
                      messages.map((message) => (
                        <motion.div
                          key={message.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className={`flex gap-3 ${message.role === "user" ? "justify-end" : "justify-start"}`}
                        >
                          {message.role === "assistant" && (
                            <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                              <Bot className="h-4 w-4 text-white" />
                            </div>
                          )}

                          <div
                            className={`max-w-[80%] p-3 rounded-lg ${
                              message.role === "user"
                                ? "bg-primary text-primary-foreground rounded-tr-none"
                                : "bg-muted rounded-tl-none"
                            }`}
                          >
                            {message.isStreaming && (
                              <div className="flex items-center gap-1 mb-2">
                                <div className="w-1 h-1 bg-primary rounded-full animate-pulse" />
                                <div className="w-1 h-1 bg-primary rounded-full animate-pulse delay-100" />
                                <div className="w-1 h-1 bg-primary rounded-full animate-pulse delay-200" />
                              </div>
                            )}
                            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                            {message.role === "assistant" && !message.isStreaming && voiceEnabled && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 text-xs mt-2"
                                onClick={() => mockLiveKitService.speak(message.content)}
                              >
                                <Volume2 className="h-3 w-3 mr-1" />
                                Speak
                              </Button>
                            )}
                          </div>

                          {message.role === "user" && (
                            <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center flex-shrink-0">
                              <span className="text-xs font-medium">You</span>
                            </div>
                          )}
                        </motion.div>
                      ))
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>

                {/* Input Area */}
                <div className="p-4 border-t border-border/50">
                  <div className="flex gap-2">
                    <Button
                      variant={isRecording ? "destructive" : "outline"}
                      size="icon"
                      onClick={toggleRecording}
                      className="flex-shrink-0"
                    >
                      {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                    </Button>
                    <Input
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder="Type your message..."
                      className="flex-1"
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault()
                          handleSendMessage()
                        }
                      }}
                    />
                    <Button
                      onClick={handleSendMessage}
                      disabled={input.trim() === "" || isProcessing}
                      className="flex-shrink-0"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                  {isRecording && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mt-2 p-2 bg-muted rounded-md text-sm flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-destructive animate-pulse"></div>
                        <span>Recording audio...</span>
                      </div>
                      <Button variant="ghost" size="sm" onClick={toggleRecording} className="h-auto py-1">
                        Cancel
                      </Button>
                    </motion.div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  )
}
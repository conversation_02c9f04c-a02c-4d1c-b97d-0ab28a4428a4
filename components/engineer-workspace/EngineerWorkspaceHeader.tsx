'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bars3Icon,
  XMarkIcon,
  CogIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { useEngineerWorkspace, useWorkspaceStats } from './hooks/useEngineerWorkspace';

export function EngineerWorkspaceHeader() {
  const { ui, workspace } = useEngineerWorkspace();
  const stats = useWorkspaceStats();

  return (
    <div className="flex items-center justify-between p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Left side - Toggle and title */}
      <div className="flex items-center space-x-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={ui.toggleSidebar}
          className="p-2"
        >
          {ui.sidebarCollapsed ? (
            <Bars3Icon className="w-4 h-4" />
          ) : (
            <XMarkIcon className="w-4 h-4" />
          )}
        </Button>
        
        <div>
          <h1 className="text-lg font-semibold text-foreground">Engineer AI Workspace</h1>
          <p className="text-sm text-muted-foreground">
            AI-powered development environment
          </p>
        </div>
      </div>

      {/* Center - Stats */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <ChartBarIcon className="w-4 h-4 text-muted-foreground" />
          <div className="flex space-x-3">
            <div className="flex items-center space-x-1">
              <span className="text-sm text-muted-foreground">Prototypes:</span>
              <Badge variant="secondary" className="text-xs">
                {stats.prototypes}
              </Badge>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-sm text-muted-foreground">Tools:</span>
              <Badge variant="secondary" className="text-xs">
                {stats.codeTools}
              </Badge>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-sm text-muted-foreground">Sessions:</span>
              <Badge variant="secondary" className="text-xs">
                {stats.sessions}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Actions */}
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            const data = workspace.export();
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `engineer-workspace-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
          }}
          className="text-xs"
        >
          Export
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          className="p-2"
        >
          <CogIcon className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}

import { useCallback } from 'react';
import { useEngineerWorkspaceStore, type PrototypeData, type CodeToolResult } from '../store/engineer-workspace-store';
import type { Message } from '@ai-sdk/react';

/**
 * Main hook for Engineer Workspace functionality
 * Provides a clean API for interacting with the workspace state
 */
export function useEngineerWorkspace() {
  // Use individual selectors to avoid re-renders
  const currentSession = useEngineerWorkspaceStore(state => state.currentSession);
  const chatSessions = useEngineerWorkspaceStore(state => state.chatSessions);
  const isLoading = useEngineerWorkspaceStore(state => state.isLoading);
  const error = useEngineerWorkspaceStore(state => state.error);
  const prototypes = useEngineerWorkspaceStore(state => state.prototypes);
  const activePrototypeId = useEngineerWorkspaceStore(state => state.activePrototypeId);
  const codeToolResults = useEngineerWorkspaceStore(state => state.codeToolResults);
  const activeTab = useEngineerWorkspaceStore(state => state.activeTab);
  const sidebarCollapsed = useEngineerWorkspaceStore(state => state.sidebarCollapsed);
  const settings = useEngineerWorkspaceStore(state => state.settings);
  const previewFiles = useEngineerWorkspaceStore(state => state.previewFiles);
  const previewTemplate = useEngineerWorkspaceStore(state => state.previewTemplate);

  // Get stable action functions
  const createChatSession = useEngineerWorkspaceStore(state => state.createChatSession);
  const switchChatSession = useEngineerWorkspaceStore(state => state.switchChatSession);
  const addMessageToSession = useEngineerWorkspaceStore(state => state.addMessageToSession);
  const updateSessionTitle = useEngineerWorkspaceStore(state => state.updateSessionTitle);
  const deleteChatSession = useEngineerWorkspaceStore(state => state.deleteChatSession);
  const setLoading = useEngineerWorkspaceStore(state => state.setLoading);
  const setError = useEngineerWorkspaceStore(state => state.setError);
  const addPrototype = useEngineerWorkspaceStore(state => state.addPrototype);
  const updatePrototype = useEngineerWorkspaceStore(state => state.updatePrototype);
  const deletePrototype = useEngineerWorkspaceStore(state => state.deletePrototype);
  const setActivePrototype = useEngineerWorkspaceStore(state => state.setActivePrototype);
  const addCodeToolResult = useEngineerWorkspaceStore(state => state.addCodeToolResult);
  const deleteCodeToolResult = useEngineerWorkspaceStore(state => state.deleteCodeToolResult);
  const setActiveTab = useEngineerWorkspaceStore(state => state.setActiveTab);
  const toggleSidebar = useEngineerWorkspaceStore(state => state.toggleSidebar);
  const setSidebarCollapsed = useEngineerWorkspaceStore(state => state.setSidebarCollapsed);
  const updateSettings = useEngineerWorkspaceStore(state => state.updateSettings);
  const setPreview = useEngineerWorkspaceStore(state => state.setPreview);
  const clearPreview = useEngineerWorkspaceStore(state => state.clearPreview);
  const reset = useEngineerWorkspaceStore(state => state.reset);
  const exportWorkspace = useEngineerWorkspaceStore(state => state.exportWorkspace);
  const importWorkspace = useEngineerWorkspaceStore(state => state.importWorkspace);

  // Chat functionality
  const chat = {
    currentSession,
    sessions: chatSessions,
    isLoading,
    error,

    createSession: useCallback(() => {
      return createChatSession();
    }, [createChatSession]),

    switchSession: useCallback((sessionId: string) => {
      switchChatSession(sessionId);
    }, [switchChatSession]),

    addMessage: useCallback((message: Message) => {
      if (currentSession) {
        addMessageToSession(currentSession.id, message);
      }
    }, [addMessageToSession, currentSession]),

    updateSessionTitle: useCallback((sessionId: string, title: string) => {
      updateSessionTitle(sessionId, title);
    }, [updateSessionTitle]),

    deleteSession: useCallback((sessionId: string) => {
      deleteChatSession(sessionId);
    }, [deleteChatSession]),

    setLoading: useCallback((loading: boolean) => {
      setLoading(loading);
    }, [setLoading]),

    setError: useCallback((error: string | null) => {
      setError(error);
    }, [setError]),
  };

  // Prototypes functionality
  const prototypesObj = {
    all: prototypes,
    active: activePrototypeId ? prototypes[activePrototypeId] : null,

    add: useCallback((prototype: Omit<PrototypeData, 'id' | 'createdAt' | 'updatedAt'>) => {
      return addPrototype(prototype);
    }, [addPrototype]),

    update: useCallback((id: string, updates: Partial<PrototypeData>) => {
      updatePrototype(id, updates);
    }, [updatePrototype]),

    delete: useCallback((id: string) => {
      deletePrototype(id);
    }, [deletePrototype]),

    setActive: useCallback((id: string | null) => {
      setActivePrototype(id);
    }, [setActivePrototype]),

    getById: useCallback((id: string) => {
      return prototypes[id];
    }, [prototypes]),

    getByFramework: useCallback((framework: string) => {
      return Object.values(prototypes).filter(p => p.framework === framework);
    }, [prototypes]),

    getRecent: useCallback((limit = 5) => {
      return Object.values(prototypes)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
        .slice(0, limit);
    }, [prototypes]),
  };

  // Code tools functionality
  const codeTools = {
    results: codeToolResults,

    addResult: useCallback((result: Omit<CodeToolResult, 'id' | 'createdAt'>) => {
      return addCodeToolResult(result);
    }, [addCodeToolResult]),

    deleteResult: useCallback((id: string) => {
      deleteCodeToolResult(id);
    }, [deleteCodeToolResult]),

    getByType: useCallback((type: CodeToolResult['type']) => {
      return Object.values(codeToolResults).filter(r => r.type === type);
    }, [codeToolResults]),

    getRecent: useCallback((limit = 10) => {
      return Object.values(codeToolResults)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, limit);
    }, [codeToolResults]),
  };

  // UI functionality
  const ui = {
    activeTab,
    sidebarCollapsed,
    settings,

    setActiveTab: useCallback((tab: typeof activeTab) => {
      setActiveTab(tab);
    }, [setActiveTab]),

    toggleSidebar: useCallback(() => {
      toggleSidebar();
    }, [toggleSidebar]),

    setSidebarCollapsed: useCallback((collapsed: boolean) => {
      setSidebarCollapsed(collapsed);
    }, [setSidebarCollapsed]),

    updateSettings: useCallback((settingsUpdate: Partial<typeof settings>) => {
      updateSettings(settingsUpdate);
    }, [updateSettings]),
  };

  // Preview functionality
  const preview = {
    files: previewFiles,
    template: previewTemplate,

    set: useCallback((files: Record<string, any>, template: string) => {
      setPreview(files, template);
    }, [setPreview]),

    clear: useCallback(() => {
      clearPreview();
    }, [clearPreview]),

    isActive: previewFiles !== null,
  };

  // Workspace utilities
  const workspace = {
    reset: useCallback(() => {
      reset();
    }, [reset]),

    export: useCallback(() => {
      return exportWorkspace();
    }, [exportWorkspace]),

    import: useCallback((data: any) => {
      importWorkspace(data);
    }, [importWorkspace]),

    getStats: useCallback(() => {
      const prototypesCount = Object.keys(prototypes).length;
      const codeToolsCount = Object.keys(codeToolResults).length;
      const sessionsCount = Object.keys(chatSessions).length;

      return {
        prototypes: prototypesCount,
        codeTools: codeToolsCount,
        sessions: sessionsCount,
        totalItems: prototypesCount + codeToolsCount,
      };
    }, [prototypes, codeToolResults, chatSessions]),
  };

  return {
    chat,
    prototypes: prototypesObj,
    codeTools,
    ui,
    preview,
    workspace,
  };
}

/**
 * Hook for accessing workspace statistics
 */
export function useWorkspaceStats() {
  const { workspace } = useEngineerWorkspace();
  return workspace.getStats();
}

/**
 * Hook for managing workspace settings
 */
export function useWorkspaceSettings() {
  const { ui } = useEngineerWorkspace();
  return {
    settings: ui.settings,
    updateSettings: ui.updateSettings,
  };
}

/**
 * Hook for managing prototypes with additional utilities
 */
export function usePrototypeManager() {
  const { prototypes, preview } = useEngineerWorkspace();
  
  const runPrototype = useCallback((id: string) => {
    const prototype = prototypes.getById(id);
    if (prototype && prototype.files) {
      preview.set(prototype.files, prototype.template);
      prototypes.setActive(id);
    }
  }, [prototypes, preview]);
  
  const duplicatePrototype = useCallback((id: string) => {
    const original = prototypes.getById(id);
    if (original) {
      const duplicate = {
        ...original,
        title: `${original.title} (Copy)`,
        tags: [...original.tags, 'duplicate'],
      };
      delete (duplicate as any).id;
      delete (duplicate as any).createdAt;
      delete (duplicate as any).updatedAt;
      
      return prototypes.add(duplicate);
    }
    return null;
  }, [prototypes]);
  
  return {
    ...prototypes,
    runPrototype,
    duplicatePrototype,
  };
}

/**
 * Hook for chat session management with utilities
 */
export function useChatManager() {
  const { chat } = useEngineerWorkspace();
  
  const createSessionWithMessage = useCallback((initialMessage: string) => {
    const sessionId = chat.createSession();
    const message: Message = {
      id: `msg-${Date.now()}`,
      role: 'user',
      content: initialMessage,
      createdAt: new Date(),
    };
    chat.addMessage(message);
    return sessionId;
  }, [chat]);
  
  const getSessionHistory = useCallback(() => {
    return Object.values(chat.sessions)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }, [chat.sessions]);
  
  return {
    ...chat,
    createSessionWithMessage,
    getSessionHistory,
  };
}

import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { Message } from '@ai-sdk/react';

// Engineer Workspace Types
export interface PrototypeData {
  id: string;
  title: string;
  description: string;
  framework: string;
  complexity: 'simple' | 'intermediate' | 'advanced';
  files: Record<string, any>;
  template: string;
  createdAt: Date;
  updatedAt: Date;
  status: 'generating' | 'ready' | 'error';
  tags: string[];
}

export interface CodeToolResult {
  id: string;
  type: 'modify' | 'explain' | 'debug';
  originalCode: string;
  result: any;
  createdAt: Date;
  language?: string;
}

export interface ChatSession {
  id: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  title?: string;
}

export interface WorkspaceSettings {
  sidebarWidth: number;
  showQuickActions: boolean;
  autoSavePrototypes: boolean;
  defaultFramework: string;
  theme: 'light' | 'dark' | 'auto';
}

export interface EngineerWorkspaceState {
  // Chat state
  currentSession: ChatSession | null;
  chatSessions: Record<string, ChatSession>;
  isLoading: boolean;
  error: string | null;

  // Prototypes state
  prototypes: Record<string, PrototypeData>;
  activePrototypeId: string | null;
  
  // Code tools state
  codeToolResults: Record<string, CodeToolResult>;
  
  // UI state
  activeTab: 'welcome' | 'prototypes' | 'code-tools' | 'sandpack';
  sidebarCollapsed: boolean;
  settings: WorkspaceSettings;
  
  // Preview state
  previewFiles: Record<string, any> | null;
  previewTemplate: string | null;
}

export interface EngineerWorkspaceActions {
  // Chat actions
  createChatSession: () => string;
  switchChatSession: (sessionId: string) => void;
  addMessageToSession: (sessionId: string, message: Message) => void;
  updateSessionTitle: (sessionId: string, title: string) => void;
  deleteChatSession: (sessionId: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Prototype actions
  addPrototype: (prototype: Omit<PrototypeData, 'id' | 'createdAt' | 'updatedAt'>) => string;
  updatePrototype: (id: string, updates: Partial<PrototypeData>) => void;
  deletePrototype: (id: string) => void;
  setActivePrototype: (id: string | null) => void;

  // Code tools actions
  addCodeToolResult: (result: Omit<CodeToolResult, 'id' | 'createdAt'>) => string;
  deleteCodeToolResult: (id: string) => void;

  // UI actions
  setActiveTab: (tab: EngineerWorkspaceState['activeTab']) => void;
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  updateSettings: (settings: Partial<WorkspaceSettings>) => void;

  // Preview actions
  setPreview: (files: Record<string, any>, template: string) => void;
  clearPreview: () => void;

  // Utility actions
  reset: () => void;
  exportWorkspace: () => any;
  importWorkspace: (data: any) => void;
}

export type EngineerWorkspaceStore = EngineerWorkspaceState & EngineerWorkspaceActions;

// Initial state
const initialState: EngineerWorkspaceState = {
  // Chat state
  currentSession: null,
  chatSessions: {},
  isLoading: false,
  error: null,

  // Prototypes state
  prototypes: {},
  activePrototypeId: null,
  
  // Code tools state
  codeToolResults: {},
  
  // UI state
  activeTab: 'welcome',
  sidebarCollapsed: false,
  settings: {
    sidebarWidth: 350,
    showQuickActions: true,
    autoSavePrototypes: true,
    defaultFramework: 'react',
    theme: 'auto'
  },
  
  // Preview state
  previewFiles: null,
  previewTemplate: null,
};

// Create the store
export const useEngineerWorkspaceStore = create<EngineerWorkspaceStore>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          ...initialState,

          // Chat actions
          createChatSession: () => {
            const sessionId = `session-${Date.now()}`;
            set((state) => {
              const newSession: ChatSession = {
                id: sessionId,
                messages: [],
                createdAt: new Date(),
                updatedAt: new Date(),
              };
              
              state.chatSessions[sessionId] = newSession;
              state.currentSession = newSession;
            });
            return sessionId;
          },

          switchChatSession: (sessionId: string) => {
            set((state) => {
              const session = state.chatSessions[sessionId];
              if (session) {
                state.currentSession = session;
              }
            });
          },

          addMessageToSession: (sessionId: string, message: Message) => {
            set((state) => {
              const session = state.chatSessions[sessionId];
              if (session) {
                session.messages.push(message);
                session.updatedAt = new Date();
                
                // Auto-generate title from first user message
                if (!session.title && message.role === 'user' && session.messages.length === 1) {
                  session.title = message.content.slice(0, 50) + (message.content.length > 50 ? '...' : '');
                }
              }
            });
          },

          updateSessionTitle: (sessionId: string, title: string) => {
            set((state) => {
              const session = state.chatSessions[sessionId];
              if (session) {
                session.title = title;
                session.updatedAt = new Date();
              }
            });
          },

          deleteChatSession: (sessionId: string) => {
            set((state) => {
              delete state.chatSessions[sessionId];
              
              // Switch to another session if current was deleted
              if (state.currentSession?.id === sessionId) {
                const remainingSessions = Object.values(state.chatSessions);
                state.currentSession = remainingSessions[0] || null;
              }
            });
          },

          setLoading: (loading: boolean) => {
            set((state) => {
              state.isLoading = loading;
            });
          },

          setError: (error: string | null) => {
            set((state) => {
              state.error = error;
            });
          },

          // Prototype actions
          addPrototype: (prototype) => {
            const id = `prototype-${Date.now()}`;
            set((state) => {
              state.prototypes[id] = {
                ...prototype,
                id,
                createdAt: new Date(),
                updatedAt: new Date(),
              };
            });
            return id;
          },

          updatePrototype: (id: string, updates: Partial<PrototypeData>) => {
            set((state) => {
              const prototype = state.prototypes[id];
              if (prototype) {
                Object.assign(prototype, updates);
                prototype.updatedAt = new Date();
              }
            });
          },

          deletePrototype: (id: string) => {
            set((state) => {
              delete state.prototypes[id];
              
              if (state.activePrototypeId === id) {
                state.activePrototypeId = null;
              }
            });
          },

          setActivePrototype: (id: string | null) => {
            set((state) => {
              state.activePrototypeId = id;
            });
          },

          // Code tools actions
          addCodeToolResult: (result) => {
            const id = `result-${Date.now()}`;
            set((state) => {
              state.codeToolResults[id] = {
                ...result,
                id,
                createdAt: new Date(),
              };
            });
            return id;
          },

          deleteCodeToolResult: (id: string) => {
            set((state) => {
              delete state.codeToolResults[id];
            });
          },

          // UI actions
          setActiveTab: (tab) => {
            set((state) => {
              state.activeTab = tab;
            });
          },

          toggleSidebar: () => {
            set((state) => {
              state.sidebarCollapsed = !state.sidebarCollapsed;
            });
          },

          setSidebarCollapsed: (collapsed: boolean) => {
            set((state) => {
              state.sidebarCollapsed = collapsed;
            });
          },

          updateSettings: (settings) => {
            set((state) => {
              Object.assign(state.settings, settings);
            });
          },

          // Preview actions
          setPreview: (files, template) => {
            set((state) => {
              state.previewFiles = files;
              state.previewTemplate = template;
              state.activeTab = 'sandpack';
            });
          },

          clearPreview: () => {
            set((state) => {
              state.previewFiles = null;
              state.previewTemplate = null;
            });
          },

          // Utility actions
          reset: () => {
            set(() => ({ ...initialState }));
          },

          exportWorkspace: () => {
            const state = get();
            return {
              prototypes: state.prototypes,
              codeToolResults: state.codeToolResults,
              settings: state.settings,
              exportedAt: new Date().toISOString(),
            };
          },

          importWorkspace: (data: any) => {
            set((state) => {
              if (data.prototypes) {
                state.prototypes = data.prototypes;
              }
              if (data.codeToolResults) {
                state.codeToolResults = data.codeToolResults;
              }
              if (data.settings) {
                Object.assign(state.settings, data.settings);
              }
            });
          },
        }))
      ),
      {
        name: 'engineer-workspace-store',
        partialize: (state) => ({
          prototypes: state.prototypes,
          codeToolResults: state.codeToolResults,
          settings: state.settings,
          activeTab: state.activeTab,
          sidebarCollapsed: state.sidebarCollapsed,
        }),
      }
    ),
    {
      name: 'engineer-workspace-store',
    }
  )
);

// Selector hooks for performance optimization
export const usePrototypes = () => useEngineerWorkspaceStore(state => state.prototypes);
export const useActivePrototype = () => useEngineerWorkspaceStore(state => 
  state.activePrototypeId ? state.prototypes[state.activePrototypeId] : null
);
export const useCodeToolResults = () => useEngineerWorkspaceStore(state => state.codeToolResults);
export const useWorkspaceSettings = () => useEngineerWorkspaceStore(state => state.settings);
export const useCurrentChatSession = () => useEngineerWorkspaceStore(state => state.currentSession);
export const useChatSessions = () => useEngineerWorkspaceStore(state => state.chatSessions);
export const useWorkspaceUI = () => useEngineerWorkspaceStore(state => ({
  activeTab: state.activeTab,
  sidebarCollapsed: state.sidebarCollapsed,
  isLoading: state.isLoading,
  error: state.error,
}));

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  CodeBracketIcon,
  PlayIcon,
  WrenchScrewdriverIcon,
  QuestionMarkCircleIcon,
  BugAntIcon,
  SparklesIcon,
  RocketLaunchIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { SandpackRenderer } from '@/lib/ai-engines/engineer-ai/components';
import { useEngineerWorkspace, usePrototypeManager } from './hooks/useEngineerWorkspace';

// Welcome Tab Component
function Welcome() {
  const features = [
    {
      icon: CodeBracketIcon,
      title: 'Code Generation',
      description: 'Generate complete components, applications, and utilities with AI assistance',
      color: 'text-blue-600'
    },
    {
      icon: WrenchScrewdriverIcon,
      title: 'Code Modification',
      description: 'Intelligently modify existing code with detailed explanations',
      color: 'text-green-600'
    },
    {
      icon: QuestionMarkCircleIcon,
      title: 'Code Explanation',
      description: 'Get detailed explanations of how code works and best practices',
      color: 'text-purple-600'
    },
    {
      icon: BugAntIcon,
      title: 'Code Debugging',
      description: 'Identify and fix issues with automated debugging assistance',
      color: 'text-red-600'
    },
    {
      icon: PlayIcon,
      title: 'Live Preview',
      description: 'See your code running in real-time with Sandpack integration',
      color: 'text-orange-600'
    },
    {
      icon: RocketLaunchIcon,
      title: 'Rapid Prototyping',
      description: 'Quickly build and iterate on ideas with AI-powered development',
      color: 'text-indigo-600'
    }
  ];

  return (
    <div className="h-full p-6 bg-gradient-to-br from-background to-muted/20">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto space-y-8"
      >
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-3">
            <SparklesIcon className="w-10 h-10 text-primary" />
            <h1 className="text-4xl font-bold text-foreground">
              Engineer AI Workspace
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Your intelligent development companion for generating, modifying, explaining, and debugging code with live preview capabilities.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow group">
                  <CardHeader className="pb-3">
                    <Icon className={`w-8 h-8 ${feature.color} group-hover:scale-110 transition-transform`} />
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <RocketLaunchIcon className="w-5 h-5 text-primary" />
              <span>Getting Started</span>
            </CardTitle>
            <CardDescription>
              Start using Engineer AI to accelerate your development workflow
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">1. Chat with AI</h4>
                <p className="text-sm text-muted-foreground">
                  Use the sidebar to describe what you want to build, modify, or understand.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">2. Review Generated Code</h4>
                <p className="text-sm text-muted-foreground">
                  AI will generate code and explanations in the Prototypes tab.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">3. Test Live</h4>
                <p className="text-sm text-muted-foreground">
                  See your code running immediately in the Live Preview tab.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">4. Iterate & Improve</h4>
                <p className="text-sm text-muted-foreground">
                  Ask for modifications, explanations, or debugging help as needed.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

// Prototypes Tab Component
function Prototypes() {
  const prototypeManager = usePrototypeManager();
  const { ui } = useEngineerWorkspace();

  const prototypesArray = Object.values(prototypeManager.all);

  return (
    <div className="h-full p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-foreground">Generated Prototypes</h2>
            <p className="text-muted-foreground">AI-generated code prototypes and components</p>
          </div>
          <Badge variant="secondary">{prototypesArray.length} prototypes</Badge>
        </div>

        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-4">
            {prototypesArray.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <CodeBracketIcon className="w-12 h-12 text-muted-foreground/50 mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">No prototypes yet</h3>
                  <p className="text-muted-foreground text-center max-w-md">
                    Start a conversation with Engineer AI to generate your first prototype. 
                    Ask for components, applications, or utilities.
                  </p>
                </CardContent>
              </Card>
            ) : (
              prototypesArray.map((prototype, index) => (
                <motion.div
                  key={prototype.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-lg">{prototype.title}</CardTitle>
                          <CardDescription className="mt-1">
                            {prototype.description}
                          </CardDescription>
                        </div>
                        <div className="flex space-x-2">
                          <Badge variant="outline">{prototype.framework}</Badge>
                          <Badge variant="secondary" className="capitalize">{prototype.complexity}</Badge>
                          <Badge
                            variant={prototype.status === 'ready' ? 'default' : 'secondary'}
                            className={prototype.status === 'generating' ? 'animate-pulse' : ''}
                          >
                            {prototype.status}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                          Created {prototype.createdAt.toLocaleDateString()}
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              prototypeManager.setActive(prototype.id);
                              ui.setActiveTab('code-tools');
                            }}
                          >
                            <DocumentTextIcon className="w-4 h-4 mr-2" />
                            View Code
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => prototypeManager.runPrototype(prototype.id)}
                            disabled={prototype.status !== 'ready'}
                          >
                            <PlayIcon className="w-4 h-4 mr-2" />
                            Run Preview
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

// Code Tools Tab Component
function CodeTools() {
  const tools = [
    {
      title: 'Code Modifier',
      description: 'Intelligently modify existing code with AI assistance',
      icon: WrenchScrewdriverIcon,
      action: 'Modify Code'
    },
    {
      title: 'Code Explainer',
      description: 'Get detailed explanations of how code works',
      icon: QuestionMarkCircleIcon,
      action: 'Explain Code'
    },
    {
      title: 'Code Debugger',
      description: 'Identify and fix issues in your code',
      icon: BugAntIcon,
      action: 'Debug Code'
    }
  ];

  return (
    <div className="h-full p-6">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Code Tools</h2>
          <p className="text-muted-foreground">AI-powered tools for code modification, explanation, and debugging</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool, index) => {
            const Icon = tool.icon;
            return (
              <motion.div
                key={tool.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow group cursor-pointer">
                  <CardHeader className="pb-3">
                    <Icon className="w-8 h-8 text-primary group-hover:scale-110 transition-transform" />
                    <CardTitle className="text-lg">{tool.title}</CardTitle>
                    <CardDescription>{tool.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      <SparklesIcon className="w-4 h-4 mr-2" />
                      {tool.action}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>How to Use Code Tools</CardTitle>
            <CardDescription>
              These tools work best when you provide context and specific requirements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">Code Modification</h4>
                <p className="text-sm text-muted-foreground">
                  Paste your code and describe what changes you want. AI will provide intelligent modifications with explanations.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">Code Explanation</h4>
                <p className="text-sm text-muted-foreground">
                  Submit code you want to understand. Get detailed explanations of functionality, patterns, and best practices.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">Code Debugging</h4>
                <p className="text-sm text-muted-foreground">
                  Share problematic code with error messages. AI will identify issues and provide fixes with prevention tips.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Sandpack Preview Tab Component
function SandpackPreview() {
  const { preview, prototypes } = useEngineerWorkspace();
  const hasActivePreview = preview.isActive;

  return (
    <div className="h-full p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-foreground">Live Preview</h2>
            <p className="text-muted-foreground">Real-time code execution with Sandpack</p>
          </div>
          {hasActivePreview && (
            <Badge variant="default">Preview Active</Badge>
          )}
        </div>

        {!hasActivePreview ? (
          <Card className="h-[calc(100vh-200px)]">
            <CardContent className="flex flex-col items-center justify-center h-full">
              <PlayIcon className="w-16 h-16 text-muted-foreground/50 mb-6" />
              <h3 className="text-xl font-medium text-foreground mb-3">No Active Preview</h3>
              <p className="text-muted-foreground text-center max-w-md mb-6">
                Generate a prototype with Engineer AI to see live preview here. 
                Your code will run in a secure sandbox environment.
              </p>
              <Button>
                <SparklesIcon className="w-4 h-4 mr-2" />
                Generate First Prototype
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="h-[calc(100vh-200px)] border border-border rounded-lg overflow-hidden">
            <SandpackRenderer
              files={preview.files || {}}
              template={preview.template || 'react-ts'}
            />
          </div>
        )}
      </div>
    </div>
  );
}

// Export all tab components
export const EngineerTabs = {
  Welcome,
  Prototypes,
  CodeTools,
  SandpackPreview
};

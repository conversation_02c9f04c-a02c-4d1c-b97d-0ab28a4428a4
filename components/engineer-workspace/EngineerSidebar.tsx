'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useChat } from '@ai-sdk/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  CodeBracketIcon,
  SparklesIcon,
  PlayIcon,
  WrenchScrewdriverIcon,
  QuestionMarkCircleIcon,
  BugAntIcon
} from '@heroicons/react/24/outline';
import { EngineerToolRenderer } from '@/lib/ai-engines/engineer-ai';
import { useEngineerWorkspace, useChatManager } from './hooks/useEngineerWorkspace';

interface QuickAction {
  title: string;
  description: string;
  prompt: string;
  icon: React.ComponentType<{ className?: string }>;
  category: 'generate' | 'modify' | 'explain' | 'debug';
}

const quickActions: QuickAction[] = [
  {
    title: 'Generate Component',
    description: 'Create a new React component',
    prompt: 'Generate a React component with TypeScript and Tailwind CSS',
    icon: CodeBracketIcon,
    category: 'generate'
  },
  {
    title: 'Modify Code',
    description: 'Enhance existing code',
    prompt: 'Help me modify this code to add new functionality',
    icon: WrenchScrewdriverIcon,
    category: 'modify'
  },
  {
    title: 'Explain Code',
    description: 'Understand how code works',
    prompt: 'Explain how this code works and what it does',
    icon: QuestionMarkCircleIcon,
    category: 'explain'
  },
  {
    title: 'Debug Issue',
    description: 'Fix code problems',
    prompt: 'Help me debug this code that\'s not working as expected',
    icon: BugAntIcon,
    category: 'debug'
  }
];

const categoryColors = {
  generate: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
  modify: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
  explain: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
  debug: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
};

export function EngineerSidebar() {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { ui, prototypes, preview } = useEngineerWorkspace();
  const chatManager = useChatManager();

  // Initialize chat session if none exists
  useEffect(() => {
    if (!chatManager.currentSession) {
      chatManager.createSession();
    }
  }, [chatManager.currentSession, chatManager.createSession]);

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error
  } = useChat({
    api: '/api/chat/engineer',
    maxSteps: 5,
    onError: (error) => {
      console.error('Engineer AI Error:', error);
      chatManager.setError(error.message);
    },
    onFinish: (message) => {
      // Add message to Zustand store
      if (chatManager.currentSession) {
        chatManager.addMessage(message);
      }

      // Check for tool invocations that generate prototypes
      if (message.toolInvocations) {
        message.toolInvocations.forEach((invocation) => {
          if (invocation.toolName === 'generatePrototype' && invocation.result) {
            const result = invocation.result;
            if (result.project) {
              // Add prototype to store
              const prototypeId = prototypes.add({
                title: result.description || 'Generated Prototype',
                description: result.description || 'AI-generated prototype',
                framework: result.project.template || 'react',
                complexity: 'intermediate',
                files: result.project.files,
                template: result.project.template,
                status: 'ready',
                tags: result.features || [],
              });

              // Set preview
              preview.set(result.project.files, result.project.template);
              prototypes.setActive(prototypeId);
              ui.setActiveTab('sandpack');
            }
          }
        });
      }
    }
  });

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  useEffect(() => {
    chatManager.setLoading(isLoading);
  }, [isLoading, chatManager.setLoading]);

  const handleQuickAction = (action: QuickAction) => {
    const syntheticEvent = new Event('submit') as any;
    handleSubmit(syntheticEvent, {
      data: { content: action.prompt }
    });
    ui.updateSettings({ showQuickActions: false });
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim()) {
      handleSubmit(e);
      ui.updateSettings({ showQuickActions: false });
    }
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <SparklesIcon className="w-5 h-5 text-primary" />
          <h2 className="font-semibold text-foreground">Engineer AI</h2>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          Generate, modify, explain, and debug code
        </p>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {/* Quick Actions - Show when no messages */}
          <AnimatePresence>
            {ui.settings.showQuickActions && messages.length === 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-3"
              >
                <h3 className="text-sm font-medium text-foreground">Quick Actions</h3>
                <div className="grid grid-cols-1 gap-2">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <motion.div
                        key={action.title}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card 
                          className="cursor-pointer hover:shadow-md transition-shadow group"
                          onClick={() => handleQuickAction(action)}
                        >
                          <CardContent className="p-3">
                            <div className="flex items-start space-x-2">
                              <Icon className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors mt-0.5" />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <h4 className="text-sm font-medium text-foreground truncate">
                                    {action.title}
                                  </h4>
                                  <Badge className={`text-xs ${categoryColors[action.category]}`}>
                                    {action.category}
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {action.description}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Chat Messages */}
          {messages.length === 0 && !ui.settings.showQuickActions ? (
            <div className="text-center py-8">
              <CodeBracketIcon className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <p className="text-muted-foreground">Start a conversation</p>
              <p className="text-sm text-muted-foreground/70">Ask me to help with your code!</p>
            </div>
          ) : (
            messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {message.role === 'assistant' && message.toolInvocations ? (
                    <div className="space-y-3">
                      {message.content && (
                        <div className="text-sm">
                          {message.content}
                        </div>
                      )}
                      {message.toolInvocations.map((toolInvocation, index) => (
                        <EngineerToolRenderer
                          key={index}
                          toolInvocation={toolInvocation}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm whitespace-pre-wrap">
                      {message.content}
                    </div>
                  )}
                </div>
              </motion.div>
            ))
          )}

          {/* Loading Indicator */}
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex justify-start"
            >
              <div className="bg-muted rounded-lg p-3">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-muted-foreground/50 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-muted-foreground/50 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-muted-foreground/50 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </div>
            </motion.div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-border">
        <form onSubmit={handleFormSubmit} className="space-y-2">
          <Textarea
            value={input}
            onChange={handleInputChange}
            placeholder="Ask Engineer AI to help with your code..."
            className="min-h-[60px] max-h-[120px] resize-none"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleFormSubmit(e);
              }
            }}
          />
          <div className="flex justify-between items-center">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => ui.updateSettings({ showQuickActions: !ui.settings.showQuickActions })}
              className="text-xs"
            >
              {ui.settings.showQuickActions ? 'Hide' : 'Show'} Quick Actions
            </Button>
            <Button 
              type="submit" 
              disabled={!input.trim() || isLoading}
              size="sm"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <SparklesIcon className="w-4 h-4" />
              )}
            </Button>
          </div>
        </form>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded-md"
          >
            <p className="text-destructive text-xs">
              Error: {error.message}
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
}

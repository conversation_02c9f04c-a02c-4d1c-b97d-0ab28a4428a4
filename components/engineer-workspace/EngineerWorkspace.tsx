'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  Workspace,
  PanelGroup,
  Panel,
  Tab
} from '@/lib/vscode-panels';
import { EngineerSidebar } from './EngineerSidebar';
import { EngineerTabs } from './EngineerTabs';
import { EngineerWorkspaceHeader } from './EngineerWorkspaceHeader';
import { useEngineerWorkspace } from './hooks/useEngineerWorkspace';

export function EngineerWorkspace() {
  const { ui } = useEngineerWorkspace();
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full flex flex-col"
    >
      {/* Workspace Header */}
      <EngineerWorkspaceHeader />

      {/* Main Workspace */}
      <div className="flex-1 overflow-hidden">
        <Workspace
          persistLayout={true}
          persistenceKey="engineer-workspace-layout"
          allowLayoutModification={true}
          theme="auto"
          className="h-full w-full"
        >
        <PanelGroup direction="horizontal">
          {/* Left Sidebar - AI Chat Interface */}
          <Panel
            id="engineer-sidebar"
            title="AI Assistant"
            className={`min-w-[300px] max-w-[500px] ${ui.sidebarCollapsed ? 'hidden' : ''}`}
            style={{
              flex: ui.sidebarCollapsed ? '0 0 0px' : `0 0 ${ui.settings.sidebarWidth}px`,
              width: ui.sidebarCollapsed ? 0 : ui.settings.sidebarWidth
            }}
          >
            <Tab id="chat" title="Engineer AI" pinned>
              <EngineerSidebar />
            </Tab>
          </Panel>

          {/* Main Content Area - VS Code-style Tabs */}
          <Panel 
            id="engineer-main" 
            title="Workspace"
            className="flex-1"
            style={{ flex: '1 1 75%' }}
          >
            <Tab id="welcome" title="Welcome" pinned>
              <EngineerTabs.Welcome />
            </Tab>
            <Tab id="prototypes" title="Prototypes">
              <EngineerTabs.Prototypes />
            </Tab>
            <Tab id="code-tools" title="Code Tools">
              <EngineerTabs.CodeTools />
            </Tab>
            <Tab id="sandpack" title="Live Preview">
              <EngineerTabs.SandpackPreview />
            </Tab>
          </Panel>
        </PanelGroup>
        </Workspace>
      </div>
    </motion.div>
  );
}

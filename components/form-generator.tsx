"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { Plus, Trash2, Download, ArrowRight } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

type FieldType = "text" | "textarea" | "email" | "number" | "select" | "checkbox" | "switch"

interface FormField {
  id: string
  type: FieldType
  label: string
  placeholder?: string
  required: boolean
  options?: string[] // For select fields
}

export function FormGenerator() {
  const [formTitle, setFormTitle] = useState("New Form")
  const [formDescription, setFormDescription] = useState("Enter form description here")
  const [fields, setFields] = useState<FormField[]>([
    {
      id: "1",
      type: "text",
      label: "Name",
      placeholder: "Enter your name",
      required: true,
    },
    {
      id: "2",
      type: "email",
      label: "Email",
      placeholder: "Enter your email",
      required: true,
    },
  ])
  const [activeTab, setActiveTab] = useState("edit")

  const addField = (type: FieldType) => {
    const newField: FormField = {
      id: Date.now().toString(),
      type,
      label: `New ${type} field`,
      placeholder: `Enter ${type}`,
      required: false,
      options: type === "select" ? ["Option 1", "Option 2", "Option 3"] : undefined,
    }
    setFields([...fields, newField])
  }

  const updateField = (id: string, updates: Partial<FormField>) => {
    setFields(fields.map((field) => (field.id === id ? { ...field, ...updates } : field)))
  }

  const removeField = (id: string) => {
    setFields(fields.filter((field) => field.id !== id))
  }

  const renderFieldEditor = (field: FormField) => {
    return (
      <motion.div
        key={field.id}
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: 1, height: "auto" }}
        exit={{ opacity: 0, height: 0 }}
        transition={{ duration: 0.2 }}
        className="border border-border/50 rounded-md p-4 mb-4"
      >
        <div className="flex justify-between items-center mb-4">
          <div className="font-medium">{field.type.charAt(0).toUpperCase() + field.type.slice(1)} Field</div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => removeField(field.id)}
            className="h-8 w-8 text-destructive"
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Remove field</span>
          </Button>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor={`label-${field.id}`}>Label</Label>
            <Input
              id={`label-${field.id}`}
              value={field.label}
              onChange={(e) => updateField(field.id, { label: e.target.value })}
              className="mt-1"
            />
          </div>

          {(field.type === "text" ||
            field.type === "email" ||
            field.type === "number" ||
            field.type === "textarea") && (
            <div>
              <Label htmlFor={`placeholder-${field.id}`}>Placeholder</Label>
              <Input
                id={`placeholder-${field.id}`}
                value={field.placeholder || ""}
                onChange={(e) => updateField(field.id, { placeholder: e.target.value })}
                className="mt-1"
              />
            </div>
          )}

          {field.type === "select" && (
            <div>
              <Label>Options</Label>
              <div className="space-y-2 mt-1">
                {field.options?.map((option, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={option}
                      onChange={(e) => {
                        const newOptions = [...(field.options || [])]
                        newOptions[index] = e.target.value
                        updateField(field.id, { options: newOptions })
                      }}
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        const newOptions = [...(field.options || [])].filter((_, i) => i !== index)
                        updateField(field.id, { options: newOptions })
                      }}
                      className="h-10 w-10"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Remove option</span>
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newOptions = [...(field.options || []), `Option ${(field.options?.length || 0) + 1}`]
                    updateField(field.id, { options: newOptions })
                  }}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Option
                </Button>
              </div>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Checkbox
              id={`required-${field.id}`}
              checked={field.required}
              onCheckedChange={(checked) => updateField(field.id, { required: !!checked })}
            />
            <Label htmlFor={`required-${field.id}`}>Required field</Label>
          </div>
        </div>
      </motion.div>
    )
  }

  const renderPreviewField = (field: FormField) => {
    switch (field.type) {
      case "text":
      case "email":
      case "number":
        return (
          <div className="mb-4" key={field.id}>
            <Label htmlFor={`preview-${field.id}`}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input id={`preview-${field.id}`} type={field.type} placeholder={field.placeholder} className="mt-1" />
          </div>
        )
      case "textarea":
        return (
          <div className="mb-4" key={field.id}>
            <Label htmlFor={`preview-${field.id}`}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Textarea id={`preview-${field.id}`} placeholder={field.placeholder} className="mt-1" />
          </div>
        )
      case "select":
        return (
          <div className="mb-4" key={field.id}>
            <Label htmlFor={`preview-${field.id}`}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Select>
              <SelectTrigger id={`preview-${field.id}`} className="mt-1">
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option, index) => (
                  <SelectItem key={index} value={option.toLowerCase().replace(/\s+/g, "-")}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )
      case "checkbox":
        return (
          <div className="flex items-center gap-2 mb-4" key={field.id}>
            <Checkbox id={`preview-${field.id}`} />
            <Label htmlFor={`preview-${field.id}`}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
          </div>
        )
      case "switch":
        return (
          <div className="flex items-center justify-between mb-4" key={field.id}>
            <Label htmlFor={`preview-${field.id}`}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Switch id={`preview-${field.id}`} />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="px-4 pt-4">
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="edit">Edit Form</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="edit" className="flex-1 p-0 m-0">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="form-title">Form Title</Label>
                <Input
                  id="form-title"
                  value={formTitle}
                  onChange={(e) => setFormTitle(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="form-description">Form Description</Label>
                <Textarea
                  id="form-description"
                  value={formDescription}
                  onChange={(e) => setFormDescription(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div className="border-t border-border/50 pt-4">
                <h3 className="font-medium mb-2">Form Fields</h3>
                <AnimatePresence>{fields.map((field) => renderFieldEditor(field))}</AnimatePresence>
              </div>

              <div className="border-t border-border/50 pt-4">
                <h3 className="font-medium mb-2">Add Field</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  <Button variant="outline" onClick={() => addField("text")}>
                    Text
                  </Button>
                  <Button variant="outline" onClick={() => addField("textarea")}>
                    Textarea
                  </Button>
                  <Button variant="outline" onClick={() => addField("email")}>
                    Email
                  </Button>
                  <Button variant="outline" onClick={() => addField("number")}>
                    Number
                  </Button>
                  <Button variant="outline" onClick={() => addField("select")}>
                    Select
                  </Button>
                  <Button variant="outline" onClick={() => addField("checkbox")}>
                    Checkbox
                  </Button>
                  <Button variant="outline" onClick={() => addField("switch")}>
                    Switch
                  </Button>
                </div>
              </div>
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="preview" className="flex-1 p-0 m-0">
          <ScrollArea className="flex-1 p-4">
            <Card>
              <CardHeader>
                <CardTitle>{formTitle}</CardTitle>
                <CardDescription>{formDescription}</CardDescription>
              </CardHeader>
              <CardContent>{fields.map((field) => renderPreviewField(field))}</CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline">Cancel</Button>
                <Button>Submit</Button>
              </CardFooter>
            </Card>
          </ScrollArea>
        </TabsContent>
      </Tabs>

      <div className="p-4 border-t border-border/50">
        <div className="flex gap-2 justify-end">
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Export Form
          </Button>
          <Button className="gap-2">
            <ArrowRight className="h-4 w-4" />
            Use Form
          </Button>
        </div>
      </div>
    </div>
  )
}

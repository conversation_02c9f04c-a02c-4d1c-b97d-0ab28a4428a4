"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useAI } from "@/components/ai-provider"
import { Bot, Code, FileText, Mic, MicOff, Send, X } from "lucide-react"
import { ChatMessage } from "@/components/chat-message"
import { FormGenerator } from "@/components/form-generator"
import { CodeGenerator } from "@/components/code-generator"

interface AssistantDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AssistantDialog({ open, onOpenChange }: AssistantDialogProps) {
  const [activeTab, setActiveTab] = useState("chat")
  const [input, setInput] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const { messages, addMessage, isProcessing, startProcessing, stopProcessing } = useAI()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages])

  const handleSendMessage = () => {
    if (input.trim() === "") return

    const newMessage = {
      id: Date.now().toString(),
      role: "user" as const,
      content: input,
      timestamp: new Date(),
    }

    addMessage(newMessage)
    setInput("")
    startProcessing()

    // Simulate AI response after a delay
    setTimeout(() => {
      const aiResponse = {
        id: (Date.now() + 1).toString(),
        role: "assistant" as const,
        content: `I've processed your request: "${input}". How can I assist you further?`,
        timestamp: new Date(),
      }
      addMessage(aiResponse)
      stopProcessing()
    }, 2000)
  }

  const toggleRecording = () => {
    setIsRecording(!isRecording)
    // In a real implementation, this would start/stop the LiveKit audio recording
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] md:max-w-[700px] p-0 gap-0 h-[600px] max-h-[80vh]">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between p-4 border-b border-border/50">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <h2 className="font-semibold">Soimagine AI Assistant</h2>
            </div>
            <Button variant="ghost" size="icon" onClick={() => onOpenChange(false)}>
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>

          <Tabs defaultValue="chat" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid grid-cols-3 p-1 mx-4 my-2">
              <TabsTrigger value="chat" className="flex items-center gap-2">
                <Bot className="h-4 w-4" />
                <span>Chat</span>
              </TabsTrigger>
              <TabsTrigger value="forms" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>Forms</span>
              </TabsTrigger>
              <TabsTrigger value="code" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                <span>Code</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="chat" className="flex-1 flex flex-col p-0 m-0">
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <div className="text-center py-8">
                      <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">Welcome to Soimagine AI Assistant</h3>
                      <p className="text-muted-foreground max-w-md mx-auto">
                        I can help you with generating code, creating forms, and providing information about our
                        services.
                      </p>
                    </div>
                  ) : (
                    messages.map((message) => <ChatMessage key={message.id} message={message} />)
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              <div className="p-4 border-t border-border/50">
                <div className="flex gap-2">
                  <Button
                    variant={isRecording ? "destructive" : "outline"}
                    size="icon"
                    onClick={toggleRecording}
                    className="flex-shrink-0"
                  >
                    {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                    <span className="sr-only">{isRecording ? "Stop recording" : "Start recording"}</span>
                  </Button>
                  <Input
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault()
                        handleSendMessage()
                      }
                    }}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={input.trim() === "" || isProcessing}
                    className="flex-shrink-0"
                  >
                    <Send className="h-4 w-4" />
                    <span className="sr-only">Send message</span>
                  </Button>
                </div>
                {isRecording && (
                  <div className="mt-2 p-2 bg-muted rounded-md text-sm flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-destructive animate-pulse"></div>
                      <span>Recording audio...</span>
                    </div>
                    <Button variant="ghost" size="sm" onClick={toggleRecording} className="h-auto py-1">
                      Cancel
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="forms" className="flex-1 p-0 m-0">
              <FormGenerator />
            </TabsContent>

            <TabsContent value="code" className="flex-1 p-0 m-0">
              <CodeGenerator />
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}

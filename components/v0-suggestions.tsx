"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { motion } from "framer-motion"
import {
  Plus,
  Code,
  Palette,
  Layout,
  Smartphone,
  Globe,
  Database,
  Zap,
  ChevronDown,
  ChevronUp
} from "lucide-react"

interface Suggestion {
  id: string
  title: string
  description: string
  icon: any
  category: "integration" | "component" | "template" | "tool"
  prompt: string
}

const SUGGESTIONS: Suggestion[] = [
  {
    id: "1",
    title: "Add Integration",
    description: "Connect external services and APIs",
    icon: Plus,
    category: "integration",
    prompt: "Help me add an integration to my project"
  },
  {
    id: "2", 
    title: "Add 3D model viewer",
    description: "Interactive 3D component with controls",
    icon: Code,
    category: "component",
    prompt: "Create a 3D model viewer component with rotation controls"
  },
  {
    id: "3",
    title: "Implement whiteboard",
    description: "Collaborative drawing and annotation tool",
    icon: Palette,
    category: "component", 
    prompt: "Build a collaborative whiteboard component with drawing tools"
  },
  {
    id: "4",
    title: "Create AI image generator",
    description: "Generate images using AI models",
    icon: Zap,
    category: "tool",
    prompt: "Create an AI image generator interface with prompt input and gallery"
  },
  {
    id: "5",
    title: "Landing Page Template",
    description: "Modern responsive landing page",
    icon: Layout,
    category: "template",
    prompt: "Generate a modern landing page template with hero section, features, and CTA"
  },
  {
    id: "6",
    title: "Mobile App Interface",
    description: "Native-looking mobile UI components",
    icon: Smartphone,
    category: "template",
    prompt: "Create mobile app interface components with native styling"
  },
  {
    id: "7",
    title: "Dashboard Template",
    description: "Analytics and data visualization",
    icon: Globe,
    category: "template",
    prompt: "Build a comprehensive dashboard template with charts and metrics"
  },
  {
    id: "8",
    title: "Database Schema",
    description: "Design and visualize data models",
    icon: Database,
    category: "tool",
    prompt: "Help me design a database schema with relationships and constraints"
  }
]

interface V0SuggestionsProps {
  onSuggestionClick: (prompt: string) => void
  className?: string
}

export function V0Suggestions({ onSuggestionClick, className = "" }: V0SuggestionsProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const categories = Array.from(new Set(SUGGESTIONS.map(s => s.category)))
  const filteredSuggestions = selectedCategory 
    ? SUGGESTIONS.filter(s => s.category === selectedCategory)
    : SUGGESTIONS

  const visibleSuggestions = isExpanded ? filteredSuggestions : filteredSuggestions.slice(0, 6)

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "integration": return "bg-blue-100 text-blue-700 border-blue-200"
      case "component": return "bg-green-100 text-green-700 border-green-200"
      case "template": return "bg-purple-100 text-purple-700 border-purple-200"
      case "tool": return "bg-orange-100 text-orange-700 border-orange-200"
      default: return "bg-gray-100 text-gray-700 border-gray-200"
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Category Filters */}
      <div className="flex items-center gap-2 flex-wrap">
        <span className="text-sm font-medium text-muted-foreground">Suggestions:</span>
        <Button
          variant={selectedCategory === null ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedCategory(null)}
        >
          All
        </Button>
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            className="capitalize"
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Suggestions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {visibleSuggestions.map((suggestion, index) => (
          <motion.div
            key={suggestion.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
          >
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent 
                className="p-4"
                onClick={() => onSuggestionClick(suggestion.prompt)}
              >
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <suggestion.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-sm truncate">{suggestion.title}</h3>
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${getCategoryColor(suggestion.category)}`}
                      >
                        {suggestion.category}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {suggestion.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Show More/Less Button */}
      {filteredSuggestions.length > 6 && (
        <div className="text-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-muted-foreground"
          >
            {isExpanded ? (
              <>
                Show Less <ChevronUp className="h-4 w-4 ml-1" />
              </>
            ) : (
              <>
                Show More ({filteredSuggestions.length - 6} more) <ChevronDown className="h-4 w-4 ml-1" />
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}

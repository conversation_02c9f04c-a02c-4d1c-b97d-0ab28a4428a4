"use client"

import { useState, useEffect, useCallback } from "react"
import { motion } from "framer-motion"
import { Menu, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Navbar<PERSON>ogo, CompactNavbar<PERSON><PERSON> } from "@/components/3d-logo"
import { useRouter } from "next/navigation"
import { NavbarSVGLogo, NavbarLoadingIndicator } from "./svg-logo"

export function CleanNavbar() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [logoLoaded, setLogoLoaded] = useState(false)
  const [logoError, setLogoError] = useState(false)
  const [logoType, setLogoType] = useState<'3d' | 'svg'>('svg') // Default to SVG for better performance
  const router = useRouter()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const handleLogoClick = useCallback(() => {
    router.push("/")
  }, [router])

  const handleLogoLoad = useCallback(() => {
    setLogoLoaded(true)
    setLogoError(false)
  }, [])

  const handleLogoError = useCallback((error: Error) => {
    console.warn("Logo loading error:", error)
    setLogoError(true)
    setLogoLoaded(false)
    // Fallback to SVG if 3D fails
    if (logoType === '3d') {
      setLogoType('svg')
    }
  }, [logoType])

  const navItems = [
    { name: "Services", href: "/services" },
    { name: "Portfolio", href: "/portfolio" },
    { name: "About", href: "/about" },
    { name: "Contact", href: "/contact" },
  ]

  return (
    <header
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-300",
        isScrolled ? "bg-background/80 backdrop-blur-md py-2 shadow-md" : "bg-transparent py-4",
      )}
    >
      <div className="container mx-auto px-4 flex items-center justify-between">
        {/* Logo Section */}
        <div className="flex items-center gap-3 group cursor-pointer" onClick={handleLogoClick}>
          <div className="relative h-7 w-10 transition-opacity duration-300 group-hover:opacity-90">
            {/* Position-stable loading indicator */}
            <NavbarLoadingIndicator
              isLoading={!logoLoaded && !logoError}
              hasError={logoError}
              size="sm"
              onRetry={() => {
                setLogoError(false)
                setLogoLoaded(false)
              }}
            />
            
            {/* Mobile Logo */}
            <div className="block md:hidden">
              {logoType === '3d' ? (
                <CompactNavbarLogo
                  className="rounded-lg overflow-hidden shadow-sm"
                  enableAnimation={!isScrolled}
                  enableHover={true}
                  quality="low"
                  onLoad={handleLogoLoad}
                  onError={handleLogoError}
                  ariaLabel="Soimagine Digital Agency Logo - Go to Home"
                />
              ) : (
                <NavbarSVGLogo
                  width={32}
                  height={22}
                  enableHover={true}
                  enableAmbient={false}
                  onClick={handleLogoClick}
                  showLoading={false}
                  onLoad={handleLogoLoad}
                  onError={handleLogoError}
                  ariaLabel="Soimagine Digital Agency Logo - Go to Home"
                />
              )}
            </div>
            
            {/* Desktop Logo */}
            <div className="hidden md:block">
              {logoType === 'svg' ? (
                <NavbarSVGLogo
                  width={40}
                  height={28}
                  enableHover={true}
                  enableAmbient={!isScrolled}
                  ambientVariant="fadeIn"
                  onClick={handleLogoClick}
                  showLoading={false}
                  onLoad={handleLogoLoad}
                  onError={handleLogoError}
                  ariaLabel="Soimagine Digital Agency Logo - Go to Home"
                />
              ) : (
                <NavbarLogo
                  className="rounded-lg overflow-hidden shadow-sm"
                  enableAnimation={!isScrolled}
                  enableHover={true}
                  quality="medium"
                  onLoad={handleLogoLoad}
                  onError={handleLogoError}
                  ariaLabel="Soimagine Digital Agency Logo - Go to Home"
                />
              )}
            </div>
          </div>

          {/* Company Name */}
          <span className="font-bold text-xl font-secondary transition-colors duration-300 group-hover:text-primary">
            Soimagine
          </span>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6">
          {navItems.map((item) => (
            <a
              key={item.name}
              href={item.href}
              className="text-foreground/80 hover:text-foreground transition-colors duration-200 font-medium"
            >
              {item.name}
            </a>
          ))}
        </nav>

        {/* CTA Button & Mobile Menu */}
        <div className="flex items-center gap-4">
          <Button
            className="hidden sm:inline-flex bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700"
            onClick={() => router.push("/contact")}
          >
            Get Started
          </Button>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          className="md:hidden bg-background/95 backdrop-blur-md border-t"
        >
          <div className="container mx-auto px-4 py-4">
            <nav className="flex flex-col gap-4">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-foreground/80 hover:text-foreground transition-colors duration-200 font-medium py-2"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <Button
                className="mt-4 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700"
                onClick={() => {
                  setIsMobileMenuOpen(false)
                  router.push("/contact")
                }}
              >
                Get Started
              </Button>
            </nav>
          </div>
        </motion.div>
      )}
    </header>
  )
}

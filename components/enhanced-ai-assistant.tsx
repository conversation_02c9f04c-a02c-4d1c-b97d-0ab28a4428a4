"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { VoiceVisualizer } from "@/components/voice-visualizer"
import { advancedAI, AI_CAPABILITIES } from "@/lib/advanced-ai"
import { liveKitService } from "@/lib/livekit-service"
import { motion } from "framer-motion"
import {
  Bo<PERSON>,
  Mi<PERSON>,
  MicOff,
  Send,
  Settings,
  Volume2,
  Zap,
  Brain,
  Sparkles,
  Code,
  PenTool,
  BarChart3,
  Layout,
  MessageSquare,
} from "lucide-react"

interface Message {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: Date
  capability?: string
  isStreaming?: boolean
}

export function EnhancedAIAssistant() {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [selectedCapability, setSelectedCapability] = useState("voice-assistant")
  const [voiceEnabled, setVoiceEnabled] = useState(false)
  const [autoSpeak, setAutoSpeak] = useState(false)
  const [voiceSpeed, setVoiceSpeed] = useState([1])
  const [voicePitch, setVoicePitch] = useState([1])
  const [streamingMessage, setStreamingMessage] = useState("")
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages, streamingMessage])

  useEffect(() => {
    // Register voice commands
    liveKitService.registerVoiceCommand({
      command: "hey soimagine",
      action: () => startListening(),
      description: "Activate voice assistant",
    })

    liveKitService.registerVoiceCommand({
      command: "stop listening",
      action: () => stopListening(),
      description: "Deactivate voice assistant",
    })

    liveKitService.registerVoiceCommand({
      command: "generate code",
      action: () => setSelectedCapability("code-generation"),
      description: "Switch to code generation mode",
    })

    liveKitService.registerVoiceCommand({
      command: "write content",
      action: () => setSelectedCapability("content-writing"),
      description: "Switch to content writing mode",
    })
  }, [])

  const handleSendMessage = async () => {
    if (!input.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input,
      timestamp: new Date(),
      capability: selectedCapability,
    }

    setMessages((prev) => [...prev, userMessage])
    setInput("")
    setIsProcessing(true)

    try {
      // Create streaming message
      const streamingId = (Date.now() + 1).toString()
      const streamingMsg: Message = {
        id: streamingId,
        role: "assistant",
        content: "",
        timestamp: new Date(),
        capability: selectedCapability,
        isStreaming: true,
      }

      setMessages((prev) => [...prev, streamingMsg])

      let fullResponse = ""
      await advancedAI.streamWithContext(
        input,
        selectedCapability,
        (chunk) => {
          fullResponse += chunk
          setMessages((prev) => prev.map((msg) => (msg.id === streamingId ? { ...msg, content: fullResponse } : msg)))
        },
        (finalText) => {
          setMessages((prev) =>
            prev.map((msg) => (msg.id === streamingId ? { ...msg, content: finalText, isStreaming: false } : msg)),
          )

          if (autoSpeak && voiceEnabled) {
            speakText(finalText)
          }
        },
      )
    } catch (error) {
      console.error("Message error:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const startListening = async () => {
    if (!voiceEnabled) return

    setIsListening(true)
    const stream = await liveKitService.startAudioRecording()
    if (stream) {
      liveKitService.startVoiceRecognition()
    }
  }

  const stopListening = () => {
    setIsListening(false)
    liveKitService.stopVoiceRecognition()
  }

  const speakText = (text: string) => {
    setIsSpeaking(true)
    liveKitService.speak(text, {
      rate: voiceSpeed[0],
      pitch: voicePitch[0],
    })

    // Simulate speaking duration
    setTimeout(() => {
      setIsSpeaking(false)
    }, text.length * 50)
  }

  const getCapabilityIcon = (capability: string) => {
    const icons = {
      "code-generation": <Code className="h-4 w-4" />,
      "content-writing": <PenTool className="h-4 w-4" />,
      "project-analysis": <BarChart3 className="h-4 w-4" />,
      "ui-generation": <Layout className="h-4 w-4" />,
      "workflow-automation": <Zap className="h-4 w-4" />,
      "voice-assistant": <MessageSquare className="h-4 w-4" />,
    }
    return icons[capability] || <Bot className="h-4 w-4" />
  }

  return (
    <div className="h-full flex flex-col">
      <Tabs defaultValue="chat" className="flex-1 flex flex-col">
        <TabsList className="grid grid-cols-3 mx-4 my-4">
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Chat
          </TabsTrigger>
          <TabsTrigger value="capabilities" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Capabilities
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="flex-1 flex flex-col p-0 m-0">
          <div className="px-4 pb-2">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="flex items-center gap-1">
                {getCapabilityIcon(selectedCapability)}
                {AI_CAPABILITIES.find((c) => c.id === selectedCapability)?.name}
              </Badge>
              {voiceEnabled && (
                <Badge variant={isListening ? "default" : "secondary"} className="flex items-center gap-1">
                  {isListening ? <Mic className="h-3 w-3" /> : <MicOff className="h-3 w-3" />}
                  Voice {isListening ? "Active" : "Ready"}
                </Badge>
              )}
            </div>
          </div>

          <ScrollArea className="flex-1 px-4">
            <div className="space-y-4 pb-4">
              {messages.length === 0 ? (
                <div className="text-center py-8">
                  <div className="mb-6">
                    <VoiceVisualizer
                      isActive={voiceEnabled}
                      isListening={isListening}
                      isSpeaking={isSpeaking}
                      className="w-32 h-32 mx-auto"
                    />
                  </div>
                  <h3 className="text-lg font-medium mb-2">Enhanced AI Assistant</h3>
                  <p className="text-muted-foreground max-w-md mx-auto mb-6">
                    Experience next-generation AI with voice interaction, real-time streaming, and specialized
                    capabilities.
                  </p>
                  <div className="grid grid-cols-2 gap-2 max-w-md mx-auto">
                    {AI_CAPABILITIES.slice(0, 4).map((capability) => (
                      <Button
                        key={capability.id}
                        variant="outline"
                        className="flex items-center gap-2 justify-start"
                        onClick={() => {
                          setSelectedCapability(capability.id)
                          setInput(`Help me with ${capability.name.toLowerCase()}`)
                        }}
                      >
                        {getCapabilityIcon(capability.id)}
                        <span className="text-xs">{capability.name}</span>
                      </Button>
                    ))}
                  </div>
                </div>
              ) : (
                messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`flex gap-3 ${message.role === "user" ? "justify-end" : "justify-start"}`}
                  >
                    {message.role === "assistant" && (
                      <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                    )}

                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.role === "user"
                          ? "bg-primary text-primary-foreground rounded-tr-none"
                          : "bg-muted rounded-tl-none"
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        {message.capability && (
                          <Badge variant="secondary" className="text-xs">
                            {getCapabilityIcon(message.capability)}
                            {AI_CAPABILITIES.find((c) => c.id === message.capability)?.name}
                          </Badge>
                        )}
                        {message.isStreaming && (
                          <div className="flex items-center gap-1">
                            <div className="w-1 h-1 bg-primary rounded-full animate-pulse" />
                            <div className="w-1 h-1 bg-primary rounded-full animate-pulse delay-100" />
                            <div className="w-1 h-1 bg-primary rounded-full animate-pulse delay-200" />
                          </div>
                        )}
                      </div>
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      {message.role === "assistant" && !message.isStreaming && (
                        <div className="flex items-center gap-2 mt-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 text-xs"
                            onClick={() => speakText(message.content)}
                          >
                            <Volume2 className="h-3 w-3 mr-1" />
                            Speak
                          </Button>
                        </div>
                      )}
                    </div>

                    {message.role === "user" && (
                      <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center flex-shrink-0">
                        <span className="text-xs font-medium">You</span>
                      </div>
                    )}
                  </motion.div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          <div className="p-4 border-t border-border/50">
            <div className="flex gap-2">
              <Button
                variant={isListening ? "destructive" : "outline"}
                size="icon"
                onClick={isListening ? stopListening : startListening}
                disabled={!voiceEnabled}
                className="flex-shrink-0"
              >
                {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </Button>
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Type your message or use voice..."
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault()
                    handleSendMessage()
                  }
                }}
              />
              <Button onClick={handleSendMessage} disabled={!input.trim() || isProcessing} className="flex-shrink-0">
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="capabilities" className="flex-1 p-4">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">AI Capabilities</h3>
            <div className="grid gap-4">
              {AI_CAPABILITIES.map((capability) => (
                <Card
                  key={capability.id}
                  className={`cursor-pointer transition-colors ${
                    selectedCapability === capability.id ? "border-primary bg-primary/5" : "hover:bg-muted/50"
                  }`}
                  onClick={() => setSelectedCapability(capability.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-md bg-primary/10">{getCapabilityIcon(capability.id)}</div>
                      <div className="flex-1">
                        <h4 className="font-medium">{capability.name}</h4>
                        <p className="text-sm text-muted-foreground">{capability.description}</p>
                      </div>
                      <Badge variant="outline">{capability.category}</Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="flex-1 p-4">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Voice Settings</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="voice-enabled">Voice Interaction</Label>
                    <p className="text-sm text-muted-foreground">Enable voice input and output</p>
                  </div>
                  <Switch id="voice-enabled" checked={voiceEnabled} onCheckedChange={setVoiceEnabled} />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="auto-speak">Auto Speak Responses</Label>
                    <p className="text-sm text-muted-foreground">Automatically speak AI responses</p>
                  </div>
                  <Switch id="auto-speak" checked={autoSpeak} onCheckedChange={setAutoSpeak} disabled={!voiceEnabled} />
                </div>

                <div className="space-y-2">
                  <Label>Voice Speed: {voiceSpeed[0]}</Label>
                  <Slider
                    value={voiceSpeed}
                    onValueChange={setVoiceSpeed}
                    min={0.5}
                    max={2}
                    step={0.1}
                    disabled={!voiceEnabled}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Voice Pitch: {voicePitch[0]}</Label>
                  <Slider
                    value={voicePitch}
                    onValueChange={setVoicePitch}
                    min={0.5}
                    max={2}
                    step={0.1}
                    disabled={!voiceEnabled}
                  />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">AI Settings</h3>
              <div className="space-y-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    advancedAI.clearContext()
                    setMessages([])
                  }}
                >
                  Clear Conversation History
                </Button>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">LiveKit Settings</h3>
              <div className="space-y-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    liveKitService.connect({
                      serverUrl: "wss://soimagine.livekit.cloud",
                      token: "demo-token",
                      roomName: "soimagine-room",
                      participantName: "User",
                    })
                  }}
                >
                  Connect to LiveKit Room
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

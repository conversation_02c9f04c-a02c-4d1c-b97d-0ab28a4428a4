'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useChat } from '@ai-sdk/react';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Icons
import { 
  CodeBracketIcon, 
  SparklesIcon,
  PlayIcon,
  WrenchScrewdriverIcon,
  QuestionMarkCircleIcon,
  BugAntIcon,
  Bars3Icon,
  XMarkIcon,
  RocketLaunchIcon,
  DocumentTextIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

// AI Components
import { EngineerToolRenderer } from '@/lib/ai-engines/engineer-ai';

// Types
interface PrototypeData {
  id: string;
  title: string;
  description: string;
  framework: string;
  complexity: 'simple' | 'intermediate' | 'advanced';
  files: Record<string, any>;
  template: string;
  createdAt: Date;
  status: 'generating' | 'ready' | 'error';
  tags: string[];
}

interface WorkspaceState {
  prototypes: Record<string, PrototypeData>;
  activeTab: 'welcome' | 'prototypes' | 'code-tools' | 'preview';
  sidebarCollapsed: boolean;
  showQuickActions: boolean;
  previewFiles: Record<string, any> | null;
  previewTemplate: string | null;
}

interface WorkspaceActions {
  addPrototype: (prototype: Omit<PrototypeData, 'id' | 'createdAt'>) => string;
  setActiveTab: (tab: WorkspaceState['activeTab']) => void;
  toggleSidebar: () => void;
  setShowQuickActions: (show: boolean) => void;
  setPreview: (files: Record<string, any>, template: string) => void;
  clearPreview: () => void;
}

type WorkspaceStore = WorkspaceState & WorkspaceActions;

// Zustand Store
const useWorkspaceStore = create<WorkspaceStore>()(
  devtools(
    persist(
      immer((set) => ({
        // Initial state
        prototypes: {},
        activeTab: 'welcome',
        sidebarCollapsed: false,
        showQuickActions: true,
        previewFiles: null,
        previewTemplate: null,

        // Actions
        addPrototype: (prototype) => {
          const id = `prototype-${Date.now()}`;
          set((state) => {
            state.prototypes[id] = {
              ...prototype,
              id,
              createdAt: new Date(),
            };
          });
          return id;
        },

        setActiveTab: (tab) => {
          set((state) => {
            state.activeTab = tab;
          });
        },

        toggleSidebar: () => {
          set((state) => {
            state.sidebarCollapsed = !state.sidebarCollapsed;
          });
        },

        setShowQuickActions: (show) => {
          set((state) => {
            state.showQuickActions = show;
          });
        },

        setPreview: (files, template) => {
          set((state) => {
            state.previewFiles = files;
            state.previewTemplate = template;
            state.activeTab = 'preview';
          });
        },

        clearPreview: () => {
          set((state) => {
            state.previewFiles = null;
            state.previewTemplate = null;
          });
        },
      })),
      {
        name: 'engineer-workspace-unified',
        partialize: (state) => ({
          prototypes: state.prototypes,
          activeTab: state.activeTab,
          sidebarCollapsed: state.sidebarCollapsed,
          showQuickActions: state.showQuickActions,
        }),
      }
    ),
    { name: 'engineer-workspace-unified' }
  )
);

// Quick Actions Data
const quickActions = [
  {
    title: 'Generate Component',
    description: 'Create a new React component',
    prompt: 'Generate a React component with TypeScript and Tailwind CSS',
    icon: CodeBracketIcon,
    category: 'generate' as const
  },
  {
    title: 'Modify Code',
    description: 'Enhance existing code',
    prompt: 'Help me modify this code to add new functionality',
    icon: WrenchScrewdriverIcon,
    category: 'modify' as const
  },
  {
    title: 'Explain Code',
    description: 'Understand how code works',
    prompt: 'Explain how this code works and what it does',
    icon: QuestionMarkCircleIcon,
    category: 'explain' as const
  },
  {
    title: 'Debug Issue',
    description: 'Fix code problems',
    prompt: 'Help me debug this code that\'s not working as expected',
    icon: BugAntIcon,
    category: 'debug' as const
  }
];

const categoryColors = {
  generate: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
  modify: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
  explain: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
  debug: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
};

// Sidebar Component
function EngineerSidebar() {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { 
    addPrototype, 
    setShowQuickActions, 
    showQuickActions, 
    setPreview, 
    setActiveTab 
  } = useWorkspaceStore();

  const { 
    messages, 
    input, 
    handleInputChange, 
    handleSubmit, 
    isLoading, 
    error 
  } = useChat({
    api: '/api/chat/engineer',
    maxSteps: 5,
    onError: (error) => {
      console.error('Engineer AI Error:', error);
    },
    onFinish: (message) => {
      // Check for tool invocations that generate prototypes
      if (message.toolInvocations) {
        message.toolInvocations.forEach((invocation) => {
          if (invocation.toolName === 'generatePrototype' && invocation.result) {
            const result = invocation.result;
            if (result.project) {
              // Add prototype to store
              addPrototype({
                title: result.description || 'Generated Prototype',
                description: result.description || 'AI-generated prototype',
                framework: result.project.template || 'react',
                complexity: 'intermediate',
                files: result.project.files,
                template: result.project.template,
                status: 'ready',
                tags: result.features || [],
              });
              
              // Set preview
              setPreview(result.project.files, result.project.template);
            }
          }
        });
      }
    }
  });

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleQuickAction = (action: typeof quickActions[0]) => {
    const syntheticEvent = new Event('submit') as any;
    handleSubmit(syntheticEvent, {
      data: { content: action.prompt }
    });
    setShowQuickActions(false);
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim()) {
      handleSubmit(e);
      setShowQuickActions(false);
    }
  };

  return (
    <div className="h-full flex flex-col bg-background border-r border-border">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <SparklesIcon className="w-5 h-5 text-primary" />
          <h2 className="font-semibold text-foreground">Engineer AI</h2>
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          Generate, modify, explain, and debug code
        </p>
      </div>

      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {/* Quick Actions */}
          <AnimatePresence>
            {showQuickActions && messages.length === 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-3"
              >
                <h3 className="text-sm font-medium text-foreground">Quick Actions</h3>
                <div className="grid grid-cols-1 gap-2">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <motion.div
                        key={action.title}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card 
                          className="cursor-pointer hover:shadow-md transition-shadow group"
                          onClick={() => handleQuickAction(action)}
                        >
                          <CardContent className="p-3">
                            <div className="flex items-start space-x-2">
                              <Icon className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors mt-0.5" />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <h4 className="text-sm font-medium text-foreground truncate">
                                    {action.title}
                                  </h4>
                                  <Badge className={`text-xs ${categoryColors[action.category]}`}>
                                    {action.category}
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {action.description}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Chat Messages */}
          {messages.length === 0 && !showQuickActions ? (
            <div className="text-center py-8">
              <CodeBracketIcon className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <p className="text-muted-foreground">Start a conversation</p>
              <p className="text-sm text-muted-foreground/70">Ask me to help with your code!</p>
            </div>
          ) : (
            messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {message.role === 'assistant' && message.toolInvocations ? (
                    <div className="space-y-3">
                      {message.content && (
                        <div className="text-sm">
                          {message.content}
                        </div>
                      )}
                      {message.toolInvocations.map((toolInvocation, index) => (
                        <EngineerToolRenderer
                          key={index}
                          toolInvocation={toolInvocation}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm whitespace-pre-wrap">
                      {message.content}
                    </div>
                  )}
                </div>
              </motion.div>
            ))
          )}

          {/* Loading Indicator */}
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex justify-start"
            >
              <div className="bg-muted rounded-lg p-3">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-muted-foreground/50 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-muted-foreground/50 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-muted-foreground/50 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </div>
            </motion.div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-4 border-t border-border">
        <form onSubmit={handleFormSubmit} className="space-y-2">
          <Textarea
            value={input}
            onChange={handleInputChange}
            placeholder="Ask Engineer AI to help with your code..."
            className="min-h-[60px] max-h-[120px] resize-none"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleFormSubmit(e);
              }
            }}
          />
          <div className="flex justify-between items-center">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowQuickActions(!showQuickActions)}
              className="text-xs"
            >
              {showQuickActions ? 'Hide' : 'Show'} Quick Actions
            </Button>
            <Button 
              type="submit" 
              disabled={!input.trim() || isLoading}
              size="sm"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <SparklesIcon className="w-4 h-4" />
              )}
            </Button>
          </div>
        </form>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded-md"
          >
            <p className="text-destructive text-xs">
              Error: {error.message}
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
}

// Welcome Tab Component
function WelcomeTab() {
  const features = [
    {
      icon: CodeBracketIcon,
      title: 'Code Generation',
      description: 'Generate complete components, applications, and utilities with AI assistance',
      color: 'text-blue-600'
    },
    {
      icon: WrenchScrewdriverIcon,
      title: 'Code Modification',
      description: 'Intelligently modify existing code with detailed explanations',
      color: 'text-green-600'
    },
    {
      icon: QuestionMarkCircleIcon,
      title: 'Code Explanation',
      description: 'Get detailed explanations of how code works and best practices',
      color: 'text-purple-600'
    },
    {
      icon: BugAntIcon,
      title: 'Code Debugging',
      description: 'Identify and fix issues with automated debugging assistance',
      color: 'text-red-600'
    },
    {
      icon: PlayIcon,
      title: 'Live Preview',
      description: 'See your code running in real-time with Sandpack integration',
      color: 'text-orange-600'
    },
    {
      icon: RocketLaunchIcon,
      title: 'Rapid Prototyping',
      description: 'Quickly build and iterate on ideas with AI-powered development',
      color: 'text-indigo-600'
    }
  ];

  return (
    <div className="h-full p-6 bg-gradient-to-br from-background to-muted/20">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto space-y-8"
      >
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-3">
            <SparklesIcon className="w-10 h-10 text-primary" />
            <h1 className="text-4xl font-bold text-foreground">
              Engineer AI Workspace
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Your intelligent development companion for generating, modifying, explaining, and debugging code with live preview capabilities.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow group">
                  <CardHeader className="pb-3">
                    <Icon className={`w-8 h-8 ${feature.color} group-hover:scale-110 transition-transform`} />
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <RocketLaunchIcon className="w-5 h-5 text-primary" />
              <span>Getting Started</span>
            </CardTitle>
            <CardDescription>
              Start using Engineer AI to accelerate your development workflow
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">1. Chat with AI</h4>
                <p className="text-sm text-muted-foreground">
                  Use the sidebar to describe what you want to build, modify, or understand.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">2. Review Generated Code</h4>
                <p className="text-sm text-muted-foreground">
                  AI will generate code and explanations in the Prototypes tab.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">3. Test Live</h4>
                <p className="text-sm text-muted-foreground">
                  See your code running immediately in the Live Preview tab.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">4. Iterate & Improve</h4>
                <p className="text-sm text-muted-foreground">
                  Ask for modifications, explanations, or debugging help as needed.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

// Prototypes Tab Component
function PrototypesTab() {
  const { prototypes, setActiveTab } = useWorkspaceStore();
  const prototypesArray = Object.values(prototypes);

  const runPrototype = (prototype: PrototypeData) => {
    const { setPreview } = useWorkspaceStore.getState();
    if (prototype.files) {
      setPreview(prototype.files, prototype.template);
    }
  };

  return (
    <div className="h-full p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-foreground">Generated Prototypes</h2>
            <p className="text-muted-foreground">AI-generated code prototypes and components</p>
          </div>
          <Badge variant="secondary">{prototypesArray.length} prototypes</Badge>
        </div>

        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-4">
            {prototypesArray.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <CodeBracketIcon className="w-12 h-12 text-muted-foreground/50 mb-4" />
                  <h3 className="text-lg font-medium text-foreground mb-2">No prototypes yet</h3>
                  <p className="text-muted-foreground text-center max-w-md">
                    Start a conversation with Engineer AI to generate your first prototype.
                    Ask for components, applications, or utilities.
                  </p>
                </CardContent>
              </Card>
            ) : (
              prototypesArray.map((prototype, index) => (
                <motion.div
                  key={prototype.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-lg">{prototype.title}</CardTitle>
                          <CardDescription className="mt-1">
                            {prototype.description}
                          </CardDescription>
                        </div>
                        <div className="flex space-x-2">
                          <Badge variant="outline">{prototype.framework}</Badge>
                          <Badge variant="secondary" className="capitalize">{prototype.complexity}</Badge>
                          <Badge
                            variant={prototype.status === 'ready' ? 'default' : 'secondary'}
                            className={prototype.status === 'generating' ? 'animate-pulse' : ''}
                          >
                            {prototype.status}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                          Created {prototype.createdAt.toLocaleDateString()}
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setActiveTab('code-tools')}
                          >
                            <DocumentTextIcon className="w-4 h-4 mr-2" />
                            View Code
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => runPrototype(prototype)}
                            disabled={prototype.status !== 'ready'}
                          >
                            <PlayIcon className="w-4 h-4 mr-2" />
                            Run Preview
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}

// Code Tools Tab Component
function CodeToolsTab() {
  const tools = [
    {
      title: 'Code Modifier',
      description: 'Intelligently modify existing code with AI assistance',
      icon: WrenchScrewdriverIcon,
      action: 'Modify Code'
    },
    {
      title: 'Code Explainer',
      description: 'Get detailed explanations of how code works',
      icon: QuestionMarkCircleIcon,
      action: 'Explain Code'
    },
    {
      title: 'Code Debugger',
      description: 'Identify and fix issues in your code',
      icon: BugAntIcon,
      action: 'Debug Code'
    }
  ];

  return (
    <div className="h-full p-6">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Code Tools</h2>
          <p className="text-muted-foreground">AI-powered tools for code modification, explanation, and debugging</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool, index) => {
            const Icon = tool.icon;
            return (
              <motion.div
                key={tool.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow group cursor-pointer">
                  <CardHeader className="pb-3">
                    <Icon className="w-8 h-8 text-primary group-hover:scale-110 transition-transform" />
                    <CardTitle className="text-lg">{tool.title}</CardTitle>
                    <CardDescription>{tool.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      <SparklesIcon className="w-4 h-4 mr-2" />
                      {tool.action}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        <Card>
          <CardHeader>
            <CardTitle>How to Use Code Tools</CardTitle>
            <CardDescription>
              These tools work best when you provide context and specific requirements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">Code Modification</h4>
                <p className="text-sm text-muted-foreground">
                  Paste your code and describe what changes you want. AI will provide intelligent modifications with explanations.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">Code Explanation</h4>
                <p className="text-sm text-muted-foreground">
                  Submit code you want to understand. Get detailed explanations of functionality, patterns, and best practices.
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-foreground">Code Debugging</h4>
                <p className="text-sm text-muted-foreground">
                  Share problematic code with error messages. AI will identify issues and provide fixes with prevention tips.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Preview Tab Component
function PreviewTab() {
  const { previewFiles, previewTemplate, clearPreview } = useWorkspaceStore();
  const hasActivePreview = previewFiles !== null;

  return (
    <div className="h-full p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-foreground">Live Preview</h2>
            <p className="text-muted-foreground">Real-time code execution with Sandpack</p>
          </div>
          {hasActivePreview && (
            <div className="flex items-center space-x-2">
              <Badge variant="default">Preview Active</Badge>
              <Button variant="outline" size="sm" onClick={clearPreview}>
                Clear Preview
              </Button>
            </div>
          )}
        </div>

        {!hasActivePreview ? (
          <Card className="h-[calc(100vh-200px)]">
            <CardContent className="flex flex-col items-center justify-center h-full">
              <PlayIcon className="w-16 h-16 text-muted-foreground/50 mb-6" />
              <h3 className="text-xl font-medium text-foreground mb-3">No Active Preview</h3>
              <p className="text-muted-foreground text-center max-w-md mb-6">
                Generate a prototype with Engineer AI to see live preview here.
                Your code will run in a secure sandbox environment.
              </p>
              <Button>
                <SparklesIcon className="w-4 h-4 mr-2" />
                Generate First Prototype
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="h-[calc(100vh-200px)] border border-border rounded-lg overflow-hidden bg-white">
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-4">
                <CodeBracketIcon className="w-12 h-12 mx-auto text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-medium text-foreground">Preview Ready</h3>
                  <p className="text-muted-foreground">
                    Template: {previewTemplate} • Files: {Object.keys(previewFiles || {}).length}
                  </p>
                </div>
                <div className="text-sm text-muted-foreground bg-muted p-4 rounded-lg max-w-md">
                  <p className="font-medium mb-2">Preview files:</p>
                  <ul className="space-y-1">
                    {Object.keys(previewFiles || {}).map((file) => (
                      <li key={file} className="font-mono text-xs">
                        {file}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Main Workspace Component
export function EngineerWorkspaceUnified() {
  const {
    activeTab,
    sidebarCollapsed,
    toggleSidebar,
    setActiveTab,
    prototypes
  } = useWorkspaceStore();

  const prototypesCount = Object.keys(prototypes).length;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="h-screen w-full flex flex-col bg-background"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        {/* Left side - Toggle and title */}
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="p-2"
          >
            {sidebarCollapsed ? (
              <Bars3Icon className="w-4 h-4" />
            ) : (
              <XMarkIcon className="w-4 h-4" />
            )}
          </Button>

          <div>
            <h1 className="text-lg font-semibold text-foreground">Engineer AI Workspace</h1>
            <p className="text-sm text-muted-foreground">
              AI-powered development environment
            </p>
          </div>
        </div>

        {/* Center - Stats */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <ChartBarIcon className="w-4 h-4 text-muted-foreground" />
            <div className="flex space-x-3">
              <div className="flex items-center space-x-1">
                <span className="text-sm text-muted-foreground">Prototypes:</span>
                <Badge variant="secondary" className="text-xs">
                  {prototypesCount}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              const data = { prototypes, exportedAt: new Date().toISOString() };
              const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `engineer-workspace-${new Date().toISOString().split('T')[0]}.json`;
              a.click();
              URL.revokeObjectURL(url);
            }}
            className="text-xs"
          >
            Export
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <AnimatePresence>
          {!sidebarCollapsed && (
            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 350, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="flex-shrink-0 overflow-hidden"
            >
              <EngineerSidebar />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Tab Navigation */}
          <div className="border-b border-border bg-background">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
              <TabsList className="h-12 w-full justify-start rounded-none border-0 bg-transparent p-0">
                <TabsTrigger
                  value="welcome"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
                >
                  <RocketLaunchIcon className="w-4 h-4 mr-2" />
                  Welcome
                </TabsTrigger>
                <TabsTrigger
                  value="prototypes"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
                >
                  <CodeBracketIcon className="w-4 h-4 mr-2" />
                  Prototypes
                  {prototypesCount > 0 && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {prototypesCount}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger
                  value="code-tools"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
                >
                  <WrenchScrewdriverIcon className="w-4 h-4 mr-2" />
                  Code Tools
                </TabsTrigger>
                <TabsTrigger
                  value="preview"
                  className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent"
                >
                  <PlayIcon className="w-4 h-4 mr-2" />
                  Live Preview
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} className="h-full">
              <TabsContent value="welcome" className="h-full m-0">
                <WelcomeTab />
              </TabsContent>
              <TabsContent value="prototypes" className="h-full m-0">
                <PrototypesTab />
              </TabsContent>
              <TabsContent value="code-tools" className="h-full m-0">
                <CodeToolsTab />
              </TabsContent>
              <TabsContent value="preview" className="h-full m-0">
                <PreviewTab />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

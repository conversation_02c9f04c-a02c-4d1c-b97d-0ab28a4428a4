"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Braces, Cpu, FileCode, Mic, PenTool, Sparkles } from "lucide-react"

export function Features() {
  const features = [
    {
      icon: <Cpu className="h-10 w-10 text-primary" />,
      title: "AI-Powered Assistant",
      description:
        "Interact with our intelligent assistant that can understand your needs and provide solutions in real-time.",
    },
    {
      icon: <Mic className="h-10 w-10 text-primary" />,
      title: "Voice Interaction",
      description: "Speak naturally with our assistant using advanced speech recognition and synthesis technology.",
    },
    {
      icon: <FileCode className="h-10 w-10 text-primary" />,
      title: "Dynamic Form Generation",
      description: "Create and customize forms on-the-fly to collect exactly the information you need from clients.",
    },
    {
      icon: <Braces className="h-10 w-10 text-primary" />,
      title: "Code Prototyping",
      description: "Generate functional code prototypes to visualize your ideas and accelerate development.",
    },
    {
      icon: <PenTool className="h-10 w-10 text-primary" />,
      title: "Visual Presentation",
      description: "Transform your concepts into visually stunning presentations that captivate your audience.",
    },
    {
      icon: <Sparkles className="h-10 w-10 text-primary" />,
      title: "Seamless Animation",
      description: "Enjoy fluid, responsive animations that enhance user experience and bring your interface to life.",
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  }

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Powerful <span className="gradient-text">Features</span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Our digital agency platform combines cutting-edge technology with intuitive design to deliver an exceptional
            experience.
          </p>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {features.map((feature, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="h-full border-border/50 bg-card/50 backdrop-blur-sm hover:shadow-md transition-all duration-300 hover:border-primary/50">
                <CardHeader>
                  <div className="mb-4">{feature.icon}</div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-muted-foreground text-sm">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

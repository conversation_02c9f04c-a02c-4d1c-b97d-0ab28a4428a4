"use client"

import { useRef, useState, useEffect } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import { 
  OrbitControls, 
  Environment, 
  Float, 
  Text3D, 
  Center,
  useGLTF,
  Html,
  Stars,
  Sparkles
} from "@react-three/drei"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight, Sparkles as SparklesIcon, MessageSquare } from "lucide-react"
import * as THREE from "three"

function AnimatedLogo() {
  const meshRef = useRef<THREE.Group>(null)
  const [hovered, setHovered] = useState(false)
  const [normalizedScale, setNormalizedScale] = useState(1)
  const [centerOffset, setCenterOffset] = useState<[number, number, number]>([0, 0, 0])

  // Load the 3D logo model
  const { scene, materials } = useGLTF("/soimagine/logo.gltf")

  // Calculate normalized scale and center offset for hero display
  useEffect(() => {
    if (scene) {
      const box = new THREE.Box3().setFromObject(scene)
      const size = box.getSize(new THREE.Vector3())
      const center = box.getCenter(new THREE.Vector3())
      const maxDimension = Math.max(size.x, size.y, size.z)

      // Target size for hero logo (larger than navbar)
      const targetSize = 4
      const scale = targetSize / maxDimension

      setNormalizedScale(scale)
      setCenterOffset([-center.x, -center.y, -center.z])
    }
  }, [scene])
  const clonedScene = scene.clone()

  useFrame((state) => {
    if (meshRef.current) {
      // Smooth rotation
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.2
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.2) * 0.1
      
      // Breathing scale effect
      const scale = 1 + Math.sin(state.clock.elapsedTime * 1.5) * 0.05
      meshRef.current.scale.setScalar(scale)
    }
  })

  // Enhance materials
  useEffect(() => {
    if (materials) {
      Object.values(materials).forEach((material: any) => {
        if (material.isMeshStandardMaterial) {
          material.metalness = 0.4
          material.roughness = 0.3
          material.envMapIntensity = 2
          material.emissive = new THREE.Color(0x0a0a0a)
        }
      })
    }
  }, [materials])

  return (
    <Float
      speed={2}
      rotationIntensity={0.3}
      floatIntensity={0.8}
      floatingRange={[-0.2, 0.2]}
    >
      <group
        ref={meshRef}
        scale={[normalizedScale, normalizedScale, normalizedScale]}
        position={centerOffset}
        rotation={[0, 0, 0]}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <primitive object={clonedScene} />
        
        {/* Dynamic lighting effects */}
        <pointLight
          position={[2, 2, 2]}
          intensity={hovered ? 1.5 : 0.8}
          color="#e91e63"
          distance={8}
        />
        <pointLight
          position={[-2, -2, 2]}
          intensity={0.6}
          color="#2196f3"
          distance={6}
        />
        
        {/* Sparkles around the logo */}
        <Sparkles
          count={50}
          scale={[4, 4, 4]}
          size={2}
          speed={0.4}
          opacity={0.6}
          color="#e91e63"
        />
      </group>
    </Float>
  )
}

function Scene3D() {
  return (
    <>
      {/* Ambient lighting */}
      <ambientLight intensity={0.3} />
      
      {/* Main directional light */}
      <directionalLight
        position={[10, 10, 5]}
        intensity={1.2}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      
      {/* Environment and atmosphere */}
      <Environment preset="night" />
      <Stars
        radius={100}
        depth={50}
        count={5000}
        factor={4}
        saturation={0}
        fade
        speed={1}
      />
      
      {/* The animated logo */}
      <AnimatedLogo />
      
      {/* Camera controls */}
      <OrbitControls
        enablePan={false}
        enableZoom={false}
        enableRotate={true}
        autoRotate={true}
        autoRotateSpeed={0.3}
        minPolarAngle={Math.PI / 3}
        maxPolarAngle={Math.PI / 1.5}
      />
    </>
  )
}

interface Logo3DHeroProps {
  onStartChat?: () => void
  showChatButton?: boolean
  className?: string
}

export function Logo3DHero({ 
  onStartChat, 
  showChatButton = true,
  className = ""
}: Logo3DHeroProps) {
  const [isLoaded, setIsLoaded] = useState(false)

  return (
    <div className={`relative w-full h-screen overflow-hidden ${className}`}>
      {/* 3D Canvas Background */}
      <div className="absolute inset-0">
        <Canvas
          camera={{ position: [0, 0, 12], fov: 50 }}
          style={{ width: "100%", height: "100%" }}
          onCreated={() => setIsLoaded(true)}
        >
          <Scene3D />
        </Canvas>
      </div>

      {/* Content Overlay */}
      <div className="relative z-10 h-full flex flex-col items-center justify-center px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: isLoaded ? 1 : 0, y: isLoaded ? 0 : 30 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="text-center max-w-4xl mx-auto"
        >
          {/* Main Heading */}
          <motion.h1 
            className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            Where{" "}
            <span className="bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 bg-clip-text text-transparent">
              Imagination
            </span>{" "}
            Meets{" "}
            <span className="bg-gradient-to-r from-blue-500 via-cyan-500 to-green-500 bg-clip-text text-transparent">
              Technology
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p 
            className="text-lg md:text-xl lg:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
          >
            Experience the future of digital agency services with our AI-powered solutions.
            Transform your ideas into reality with cutting-edge technology and creative innovation.
          </motion.p>

          {/* Action Buttons */}
          <motion.div 
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
          >
            {showChatButton && onStartChat && (
              <Button
                size="lg"
                className="gap-2 text-lg px-8 py-6 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300"
                onClick={onStartChat}
              >
                <MessageSquare className="h-5 w-5" />
                Start AI Chat
              </Button>
            )}
            
            <Button 
              asChild 
              size="lg" 
              variant="outline" 
              className="gap-2 text-lg px-8 py-6 border-2 hover:bg-primary/10 transition-all duration-300"
            >
              <Link href="/portfolio">
                <SparklesIcon className="h-5 w-5" />
                Explore Portfolio
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </motion.div>

          {/* Feature highlights */}
          <motion.div 
            className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.4 }}
          >
            {[
              { icon: "🚀", title: "AI-Powered", desc: "Cutting-edge AI solutions" },
              { icon: "🎨", title: "Creative Design", desc: "Stunning visual experiences" },
              { icon: "⚡", title: "Fast Delivery", desc: "Rapid development cycles" }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                className="text-center p-4 rounded-lg bg-background/20 backdrop-blur-sm border border-border/20"
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-2xl mb-2">{feature.icon}</div>
                <h3 className="font-semibold mb-1">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">{feature.desc}</p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>

      {/* Loading overlay */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-background z-20">
          <div className="flex flex-col items-center gap-4">
            <div className="w-12 h-12 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <p className="text-lg font-medium">Loading Experience...</p>
          </div>
        </div>
      )}
    </div>
  )
}

// Preload the model
useGLTF.preload("/soimagine/logo.gltf")

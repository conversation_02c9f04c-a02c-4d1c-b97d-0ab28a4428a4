"use client"

import { useRef, useState } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import { 
  OrbitControls, 
  Environment, 
  Float, 
  useGLTF,
  ContactShadows,
  Stage,
  PresentationControls,
  RoundedBox,
  Text3D,
  Center,
  Sparkles,
  Stars
} from "@react-three/drei"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import * as THREE from "three"

interface EnvironmentLogoProps {
  environment: string
  logoScale?: number
  showPlatform?: boolean
}

function EnvironmentLogo({ environment, logoScale = 1, showPlatform = true }: EnvironmentLogoProps) {
  const meshRef = useRef<THREE.Group>(null)
  const platformRef = useRef<THREE.Mesh>(null)

  // Load the 3D logo model
  const { scene, materials } = useGLTF("/soimagine/logo.gltf")
  const clonedScene = scene.clone()

  useFrame((state) => {
    if (meshRef.current) {
      // Gentle rotation
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.1
      
      // Subtle floating
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
    
    if (platformRef.current) {
      // Platform gentle rotation
      platformRef.current.rotation.y += 0.002
    }
  })

  return (
    <>
      {/* Platform */}
      {showPlatform && (
        <RoundedBox
          ref={platformRef}
          args={[4, 0.2, 4]}
          position={[0, -1, 0]}
          radius={0.1}
          smoothness={4}
        >
          <meshStandardMaterial
            color="#1a1a1a"
            metalness={0.8}
            roughness={0.2}
            envMapIntensity={1}
          />
        </RoundedBox>
      )}
      
      {/* Logo */}
      <Float
        speed={1.5}
        rotationIntensity={0.2}
        floatIntensity={0.3}
        floatingRange={[-0.1, 0.1]}
      >
        <group
          ref={meshRef}
          scale={[logoScale * 0.8, logoScale * 0.8, logoScale * 0.8]}
          position={[0, 0.5, 0]}
          rotation={[0, 0, 0]}
        >
          <primitive object={clonedScene} />
          
          {/* Environment-specific lighting */}
          {environment === "night" && (
            <>
              <pointLight
                position={[2, 2, 2]}
                intensity={1}
                color="#e91e63"
                distance={6}
              />
              <Sparkles
                count={50}
                scale={[3, 3, 3]}
                size={1}
                speed={0.5}
                opacity={0.6}
                color="#e91e63"
              />
            </>
          )}
          
          {environment === "sunset" && (
            <pointLight
              position={[-2, 2, 2]}
              intensity={0.8}
              color="#ff6b35"
              distance={8}
            />
          )}
          
          {environment === "studio" && (
            <spotLight
              position={[0, 5, 0]}
              angle={0.3}
              penumbra={1}
              intensity={1}
              castShadow
            />
          )}
        </group>
      </Float>
      
      {/* Contact shadows for realism */}
      <ContactShadows
        position={[0, -1, 0]}
        opacity={0.4}
        scale={10}
        blur={2}
        far={4}
      />
    </>
  )
}

const environments = [
  { name: "Studio", preset: "studio", description: "Professional studio lighting" },
  { name: "Sunset", preset: "sunset", description: "Warm golden hour ambiance" },
  { name: "Night", preset: "night", description: "Dark atmospheric mood" },
  { name: "Dawn", preset: "dawn", description: "Soft morning light" },
  { name: "Warehouse", preset: "warehouse", description: "Industrial environment" },
  { name: "City", preset: "city", description: "Urban skyline backdrop" }
]

interface LogoEnvironmentShowcaseProps {
  width?: string | number
  height?: string | number
  className?: string
  defaultEnvironment?: string
  showControls?: boolean
  autoRotate?: boolean
}

export function LogoEnvironmentShowcase({
  width = "100%",
  height = "600px",
  className = "",
  defaultEnvironment = "studio",
  showControls = true,
  autoRotate = true
}: LogoEnvironmentShowcaseProps) {
  const [currentEnvironment, setCurrentEnvironment] = useState(defaultEnvironment)
  const [isLoaded, setIsLoaded] = useState(false)

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`relative ${className}`}
      style={{ width, height }}
    >
      {/* 3D Canvas */}
      <div className="relative w-full h-full rounded-lg overflow-hidden">
        <Canvas
          camera={{ position: [0, 2, 8], fov: 45 }}
          style={{ width: "100%", height: "100%" }}
          onCreated={() => setIsLoaded(true)}
          shadows
        >
          {/* Lighting */}
          <ambientLight intensity={0.2} />
          
          {/* Environment */}
          <Environment preset={currentEnvironment as any} />
          
          {/* Stars for night environment */}
          {currentEnvironment === "night" && (
            <Stars
              radius={100}
              depth={50}
              count={5000}
              factor={4}
              saturation={0}
              fade
              speed={1}
            />
          )}
          
          {/* Logo in environment */}
          <EnvironmentLogo
            environment={currentEnvironment}
            logoScale={0.4}
            showPlatform={true}
          />
          
          {/* Camera controls */}
          {showControls ? (
            <OrbitControls
              enablePan={false}
              enableZoom={true}
              enableRotate={true}
              minDistance={3}
              maxDistance={12}
              autoRotate={autoRotate}
              autoRotateSpeed={0.5}
              minPolarAngle={Math.PI / 6}
              maxPolarAngle={Math.PI / 2}
            />
          ) : (
            <PresentationControls
              global
              rotation={[0.13, 0.1, 0]}
              polar={[-0.4, 0.2]}
              azimuth={[-1, 0.75]}
              config={{ mass: 2, tension: 400 }}
              snap={{ mass: 4, tension: 400 }}
            />
          )}
        </Canvas>
      </div>

      {/* Environment Controls */}
      <div className="absolute top-4 left-4 right-4">
        <Card className="bg-background/80 backdrop-blur-sm border-border/50">
          <CardContent className="p-4">
            <h3 className="text-sm font-semibold mb-3">Environment Presets</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {environments.map((env) => (
                <Button
                  key={env.preset}
                  variant={currentEnvironment === env.preset ? "default" : "outline"}
                  size="sm"
                  className="text-xs"
                  onClick={() => setCurrentEnvironment(env.preset)}
                >
                  {env.name}
                </Button>
              ))}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              {environments.find(env => env.preset === currentEnvironment)?.description}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Info Panel */}
      <div className="absolute bottom-4 right-4">
        <Card className="bg-background/80 backdrop-blur-sm border-border/50">
          <CardContent className="p-3">
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Environment:</span>
                <span className="font-medium capitalize">{currentEnvironment}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Controls:</span>
                <span className="font-medium">{showControls ? "Orbit" : "Presentation"}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Auto Rotate:</span>
                <span className="font-medium">{autoRotate ? "On" : "Off"}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Loading overlay */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm rounded-lg">
          <div className="flex flex-col items-center gap-4">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <p className="text-sm text-muted-foreground">Loading Environment...</p>
          </div>
        </div>
      )}
    </motion.div>
  )
}

// Preload the model
useGLTF.preload("/soimagine/logo.gltf")

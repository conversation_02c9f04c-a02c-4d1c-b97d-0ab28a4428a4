"use client"

import { useRef, useState, useEffect } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import {
  OrbitControls,
  Environment,
  Float,
  useGLTF,
  PresentationControls
} from "@react-three/drei"
import { motion } from "framer-motion"
import * as THREE from "three"

interface LogoMeshProps {
  position?: [number, number, number]
  rotation?: [number, number, number]
  scale?: number
  interactive?: boolean
  autoRotate?: boolean
  floatIntensity?: number
}

function LogoMesh({ 
  position = [0, 0, 0], 
  rotation = [0, 0, 0], 
  scale = 1,
  interactive = true,
  autoRotate = true,
  floatIntensity = 1
}: LogoMeshProps) {
  const meshRef = useRef<THREE.Group>(null)
  const [hovered, setHovered] = useState(false)
  const [clicked, setClicked] = useState(false)

  // Load the 3D logo model
  const { scene, materials } = useGLTF("/soimagine/logo.gltf")

  // Clone the scene to avoid conflicts
  const clonedScene = scene.clone()

  useFrame((state) => {
    if (meshRef.current) {
      // Auto rotation
      if (autoRotate) {
        meshRef.current.rotation.y += 0.005
      }
      
      // Interactive hover effects
      if (interactive) {
        const targetScale = hovered ? scale * 1.1 : scale
        meshRef.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), 0.1)
        
        // Subtle breathing animation
        const breathe = Math.sin(state.clock.elapsedTime * 2) * 0.02
        meshRef.current.position.y = position[1] + breathe
      }
    }
  })

  // Enhance materials with better lighting
  useEffect(() => {
    if (materials) {
      Object.values(materials).forEach((material: any) => {
        if (material.isMeshStandardMaterial) {
          material.metalness = 0.3
          material.roughness = 0.4
          material.envMapIntensity = 1.5
        }
      })
    }
  }, [materials])

  return (
    <Float
      speed={1.5}
      rotationIntensity={floatIntensity * 0.2}
      floatIntensity={floatIntensity * 0.5}
      floatingRange={[-0.1, 0.1]}
    >
      <group
        ref={meshRef}
        position={position}
        rotation={rotation}
        scale={[scale * 0.05, scale * 0.05, scale * 0.05]}
        onPointerOver={() => interactive && setHovered(true)}
        onPointerOut={() => interactive && setHovered(false)}
        onClick={() => interactive && setClicked(!clicked)}
      >
        <primitive object={clonedScene} />
        
        {/* Add subtle glow effect when hovered */}
        {hovered && (
          <pointLight
            position={[0, 0, 2]}
            intensity={0.5}
            color="#e91e63"
            distance={5}
          />
        )}
      </group>
    </Float>
  )
}

interface Interactive3DLogoProps {
  width?: string | number
  height?: string | number
  className?: string
  showControls?: boolean
  autoRotate?: boolean
  interactive?: boolean
  environment?: "studio" | "sunset" | "dawn" | "night" | "warehouse"
  cameraPosition?: [number, number, number]
  logoScale?: number
  floatIntensity?: number
}

export function Interactive3DLogo({
  width = "100%",
  height = "400px",
  className = "",
  showControls = true,
  autoRotate = true,
  interactive = true,
  environment = "studio",
  cameraPosition = [0, 0, 8],
  logoScale = 1,
  floatIntensity = 1
}: Interactive3DLogoProps) {
  const [isLoaded, setIsLoaded] = useState(false)

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`relative ${className}`}
      style={{ width, height }}
    >
      <Canvas
        camera={{ position: cameraPosition, fov: 60 }}
        style={{ width: "100%", height: "100%" }}
        onCreated={() => setIsLoaded(true)}
      >
        {/* Lighting Setup */}
        <ambientLight intensity={0.4} />
        <directionalLight
          position={[10, 10, 5]}
          intensity={1}
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-10, -10, -10]} intensity={0.3} color="#e91e63" />
        
        {/* Environment */}
        <Environment preset={environment} />
        
        {/* Logo */}
        <LogoMesh
          scale={logoScale}
          interactive={interactive}
          autoRotate={autoRotate}
          floatIntensity={floatIntensity}
        />
        
        {/* Controls */}
        {showControls ? (
          <OrbitControls
            enablePan={false}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={10}
            autoRotate={autoRotate}
            autoRotateSpeed={0.5}
          />
        ) : (
          <PresentationControls
            global
            rotation={[0.13, 0.1, 0]}
            polar={[-0.4, 0.2]}
            azimuth={[-1, 0.75]}
          />
        )}
      </Canvas>
      
      {/* Loading indicator */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          <div className="flex flex-col items-center gap-4">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <p className="text-sm text-muted-foreground">Loading 3D Logo...</p>
          </div>
        </div>
      )}
    </motion.div>
  )
}

// Preload the GLTF model
useGLTF.preload("/soimagine/logo.gltf")

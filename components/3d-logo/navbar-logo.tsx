"use client"

import React, { useRef, useState, Suspense, useEffect } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import { Float, useGLTF, Preload } from "@react-three/drei"
import * as THREE from "three"

// Basic types
export type Vector3Tuple = [number, number, number];
export type ColorHex = `#${string}`;

// Viewport configuration
export interface ViewportConfig {
  width: number;
  height: number;
  pixelRatio?: number;
  aspectRatio?: number;
}

// Camera configuration
export interface CameraConfig {
  position: Vector3Tuple;
  fov: number;
  near: number;
  far: number;
  zoom?: number;
  target?: Vector3Tuple;
}

// Model configuration
export interface ModelConfig {
  path: string;
  dimensions: {
    width: number;
    height: number;
    depth?: number;
    scale: number;
  };
  transform: {
    rotation: Vector3Tuple;
    position: Vector3Tuple;
    scale?: Vector3Tuple;
  };
  materials?: {
    metalness?: number;
    roughness?: number;
    envMapIntensity?: number;
  };
}

// Light configuration
export interface LightConfig {
  color: ColorHex;
  intensity: number;
  distance: number;
  castShadow: boolean;
  decay?: number;
  position?: Vector3Tuple;
  target?: Vector3Tuple;
}

// Animation configuration
export interface AnimationConfig {
  enabled: boolean;
  speed?: number;
  rotationIntensity?: number;
  floatIntensity?: number;
  range?: [number, number];
}

// Complete Logo configuration
export interface LogoProps {
  viewport: ViewportConfig;
  camera: CameraConfig;
  model: ModelConfig;
  lighting: LightConfig;
  animation?: AnimationConfig;
  performance?: {
    antialias?: boolean;
    powerPreference?: 'high-performance' | 'low-power';
    precision?: 'highp' | 'mediump' | 'lowp';
  };
}

// Configurable GLTF Logo Component
function GLTFLogoMesh({
  targetSize = 1.5,
  enableAnimation = true,
  enableHover = true,
  lightIntensity = 0.6,
  onLoad,
  onError
}: {
  targetSize?: number
  enableAnimation?: boolean
  enableHover?: boolean
  lightIntensity?: number
  onLoad?: () => void
  onError?: (error: Error) => void
}) {
  const meshRef = useRef<THREE.Group>(null)
  const [hovered, setHovered] = useState(false)
  const [normalizedScale, setNormalizedScale] = useState(1)
  const [centerOffset, setCenterOffset] = useState<[number, number, number]>([0, 0, 0])

  // Optimized GLTF loading with error handling
  const { scene, materials } = useGLTF("/soimagine/logo.gltf", true) // true for draco compression support

  // Handle GLTF loading errors
  useEffect(() => {
    try {
      if (!scene && onError) {
        onError(new Error("Failed to load GLTF model"))
      }
    } catch (err) {
      if (onError) {
        onError(err instanceof Error ? err : new Error("Unknown GLTF loading error"))
      }
    }
  }, [scene, onError])

  // Clone scene to avoid conflicts when used multiple times
  const clonedScene = scene.clone()

  // Calculate normalized scale and center offset based on bounding box
  useEffect(() => {
    if (scene) {
      const box = new THREE.Box3().setFromObject(scene)
      const size = box.getSize(new THREE.Vector3())
      const center = box.getCenter(new THREE.Vector3())
      const maxDimension = Math.max(size.x, size.y, size.z)

      // Use configurable target size
      const scale = targetSize / maxDimension

      setNormalizedScale(scale)
      // Center the model by offsetting its center to origin
      setCenterOffset([-center.x, -center.y, -center.z])

      // Call onLoad callback if provided
      if (onLoad) {
        onLoad()
      }
    }
  }, [scene, targetSize, onLoad])

  // Optimize materials for better performance
  useEffect(() => {
    if (materials) {
      Object.values(materials).forEach((material: any) => {
        if (material.isMeshStandardMaterial) {
          // Optimize material properties
          material.metalness = 0.3
          material.roughness = 0.4
          material.envMapIntensity = 1.5
          // Enable frustum culling for better performance
          material.side = THREE.FrontSide
          // Reduce precision for mobile
          material.precision = 'mediump'
        }
      })
    }
  }, [materials])

  useFrame(() => {
    if (meshRef.current) {
      // Conditional auto rotation based on enableAnimation
      if (enableAnimation) {
        meshRef.current.rotation.y += 0.008
      }

      // Conditional hover effect based on enableHover
      if (enableHover) {
        const targetScale = hovered ? 1.1 : 1
        meshRef.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), 0.1)
      }
    }
  })

  return (
    <Float
      speed={1}
      rotationIntensity={0.1}
      floatIntensity={0.2}
      floatingRange={[-0.05, 0.05]}
    >
      <group
        ref={meshRef}
        scale={[normalizedScale, normalizedScale, normalizedScale]}
        rotation={[0, 0, 0]}
        position={centerOffset}
        onPointerOver={() => enableHover && setHovered(true)}
        onPointerOut={() => enableHover && setHovered(false)}
        dispose={null} // Prevent disposal for better performance
      >
        <primitive object={clonedScene} />

        {/* Enhanced lighting for GLTF model */}
        <pointLight
          position={[1, 1, 1]}
          intensity={hovered && enableHover ? lightIntensity * 1.5 : lightIntensity}
          color="#e91e63"
          distance={4}
          decay={2}
        />
        <pointLight
          position={[-1, -1, 1]}
          intensity={lightIntensity * 0.5}
          color="#3b82f6"
          distance={3}
          decay={2}
        />
      </group>
    </Float>
  )
}

// Fallback 3D Logo using basic geometry
function FallbackLogoMesh() {
  const meshRef = useRef<THREE.Group>(null)
  const [hovered, setHovered] = useState(false)

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.008
      const targetScale = hovered ? 1.1 : 1
      meshRef.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), 0.1)
    }
  })

  return (
    <Float
      speed={1}
      rotationIntensity={0.1}
      floatIntensity={0.2}
      floatingRange={[-0.05, 0.05]}
    >
      <group
        ref={meshRef}
        scale={1.2}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Stylized "S" shape logo */}
        <group>
          <mesh position={[0, 0.3, 0]}>
            <boxGeometry args={[0.8, 0.12, 0.12]} />
            <meshStandardMaterial
              color={hovered ? "#f91e63" : "#e91e63"}
              metalness={0.4}
              roughness={0.3}
              emissive={hovered ? "#330011" : "#220008"}
            />
          </mesh>
          <mesh position={[-0.25, 0.1, 0]}>
            <boxGeometry args={[0.12, 0.3, 0.12]} />
            <meshStandardMaterial
              color={hovered ? "#f91e63" : "#e91e63"}
              metalness={0.4}
              roughness={0.3}
              emissive={hovered ? "#330011" : "#220008"}
            />
          </mesh>
          <mesh position={[0, 0, 0]}>
            <boxGeometry args={[0.6, 0.12, 0.12]} />
            <meshStandardMaterial
              color={hovered ? "#f91e63" : "#e91e63"}
              metalness={0.4}
              roughness={0.3}
              emissive={hovered ? "#330011" : "#220008"}
            />
          </mesh>
          <mesh position={[0.25, -0.1, 0]}>
            <boxGeometry args={[0.12, 0.3, 0.12]} />
            <meshStandardMaterial
              color={hovered ? "#f91e63" : "#e91e63"}
              metalness={0.4}
              roughness={0.3}
              emissive={hovered ? "#330011" : "#220008"}
            />
          </mesh>
          <mesh position={[0, -0.3, 0]}>
            <boxGeometry args={[0.8, 0.12, 0.12]} />
            <meshStandardMaterial
              color={hovered ? "#f91e63" : "#e91e63"}
              metalness={0.4}
              roughness={0.3}
              emissive={hovered ? "#330011" : "#220008"}
            />
          </mesh>
        </group>

        <pointLight
          position={[1, 1, 1]}
          intensity={hovered ? 1.2 : 0.8}
          color="#e91e63"
          distance={4}
        />
      </group>
    </Float>
  )
}

// Error Boundary Component for GLTF loading
function LogoErrorBoundary({ children, fallback }: { children: React.ReactNode, fallback: React.ReactNode }) {
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    const handleError = () => setHasError(true)
    window.addEventListener('error', handleError)
    return () => window.removeEventListener('error', handleError)
  }, [])

  if (hasError) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

function FallbackLogo() {
  return (
    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg">
      <span className="text-primary font-bold text-lg">S</span>
    </div>
  )
}

interface NavbarLogoProps {
  width?: string | number
  height?: string | number
  className?: string
  // Performance options
  enableAnimation?: boolean
  enableHover?: boolean
  quality?: 'low' | 'medium' | 'high'
  // Accessibility
  alt?: string
  ariaLabel?: string
  // Callbacks
  onLoad?: () => void
  onError?: (error: Error) => void
  onClick?: () => void
  // Advanced configuration
  targetSize?: number
  cameraDistance?: number
  lightIntensity?: number
}

export function NavbarLogo({
  width = "40px",
  height = "28px",
  className = "",
  enableAnimation = true,
  enableHover = true,
  quality = 'medium',
  alt = "Soimagine Logo",
  ariaLabel = "Soimagine Digital Agency Logo",
  onLoad,
  onError,
  onClick,
  targetSize = 0.3,
  cameraDistance = 6,
  lightIntensity = 0.6
}: NavbarLogoProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [loadError, setLoadError] = useState<Error | null>(null)

  // Handle load completion
  const handleLoad = () => {
    setIsLoaded(true)
    if (onLoad) {
      onLoad()
    }
  }

  // Handle errors
  const handleError = (error: Error) => {
    setLoadError(error)
    if (onError) {
      onError(error)
    }
  }

  // Get quality-based settings
  const getQualitySettings = () => {
    switch (quality) {
      case 'low':
        return {
          antialias: false,
          dpr: [1, 1] as [number, number],
          powerPreference: 'low-power' as const,
          precision: 'lowp' as const
        }
      case 'high':
        return {
          antialias: true,
          dpr: [1, 3] as [number, number],
          powerPreference: 'high-performance' as const,
          precision: 'highp' as const
        }
      default: // medium
        return {
          antialias: false,
          dpr: [1, 2] as [number, number],
          powerPreference: 'low-power' as const,
          precision: 'mediump' as const
        }
    }
  }

  const qualitySettings = getQualitySettings()

  return (
    <div
      className={`relative ${className}`}
      style={{ width, height }}
      onClick={onClick}
      role="img"
      aria-label={ariaLabel}
      title={alt}
    >
      <LogoErrorBoundary fallback={<FallbackLogo />}>
        <Suspense fallback={<FallbackLogo />}>
          <Canvas
            camera={{ position: [0, 0, cameraDistance], fov: 45 }}
            style={{ width: "100%", height: "100%" }}
            onCreated={handleLoad}
            gl={{
              antialias: qualitySettings.antialias,
              alpha: true,
              powerPreference: qualitySettings.powerPreference,
              preserveDrawingBuffer: false,
              stencil: false,
              depth: true,
              precision: qualitySettings.precision
            }}
            dpr={qualitySettings.dpr}
          >
            {/* Optimized lighting setup */}
            <ambientLight intensity={0.4} />
            <directionalLight
              position={[2, 2, 1]}
              intensity={0.6}
              castShadow={false} // Disable shadows for performance
            />

            {/* Try GLTF first, fallback to basic geometry */}
            <Suspense fallback={<FallbackLogoMesh />}>
              <GLTFLogoMesh
                targetSize={targetSize}
                enableAnimation={enableAnimation}
                enableHover={enableHover}
                lightIntensity={lightIntensity}
                onLoad={handleLoad}
                onError={handleError}
              />
            </Suspense>

            {/* Preload assets for better performance */}
            <Preload all />
          </Canvas>
        </Suspense>
      </LogoErrorBoundary>

      {/* Loading indicator */}
      {!isLoaded && !loadError && (
        <div className="absolute inset-0 flex items-center justify-center">
          <FallbackLogo />
        </div>
      )}

      {/* Error indicator */}
      {loadError && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 dark:bg-red-950 rounded-lg">
          <span className="text-red-600 dark:text-red-400 text-xs font-medium">
            Logo Error
          </span>
        </div>
      )}
    </div>
  )
}

// Preload the GLTF model for optimal performance
useGLTF.preload("/soimagine/logo.gltf")

// Export the preload function for external use
export const preloadNavbarLogo = () => useGLTF.preload("/soimagine/logo.gltf")

// Utility function to create logo configurations
export const createLogoConfig = (overrides: Partial<LogoProps>): LogoProps => {
  const defaultConfig: LogoProps = {
    viewport: {
      width: 40,
      height: 28,
      pixelRatio: 2,
      aspectRatio: 40/28
    },
    camera: {
      position: [0, 0, 4],
      fov: 45,
      near: 0.1,
      far: 1000
    },
    model: {
      path: "/soimagine/logo.gltf",
      dimensions: {
        width: 1.5,
        height: 1.5,
        scale: 1
      },
      transform: {
        rotation: [0, 0, 0],
        position: [0, 0, 0]
      }
    },
    lighting: {
      color: "#e91e63",
      intensity: 0.6,
      distance: 4,
      castShadow: false
    },
    animation: {
      enabled: true,
      speed: 1,
      rotationIntensity: 0.1,
      floatIntensity: 0.2
    }
  }

  return { ...defaultConfig, ...overrides }
}

// Performance monitoring hook
export const useLogoPerformance = () => {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    frameRate: 0
  })

  const startLoad = () => {
    const startTime = performance.now()
    return () => {
      const loadTime = performance.now() - startTime
      setMetrics(prev => ({ ...prev, loadTime }))
    }
  }

  return { metrics, startLoad }
}

// Export component variants for different use cases
export const CompactNavbarLogo = (props: Partial<NavbarLogoProps>) => (
  <NavbarLogo
    width="32px"
    height="24px"
    quality="low"
    enableAnimation={false}
    targetSize={0.2}
    {...props}
  />
)

export const AnimatedNavbarLogo = (props: Partial<NavbarLogoProps>) => (
  <NavbarLogo
    width="48px"
    height="36px"
    quality="high"
    enableAnimation={true}
    enableHover={true}
    {...props}
  />
)

// Default export
export default NavbarLogo

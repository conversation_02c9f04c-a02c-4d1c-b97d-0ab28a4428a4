# 3D Logo Components

Creative implementations of the Soimagine 3D logo using Three.js, React Three Fiber, and Drei.

## Overview

This collection provides multiple creative ways to display and interact with the 3D logo model (`public/soimagine/logo.glb`), ranging from simple interactive displays to complex particle systems and environmental showcases.

## Components

### 1. Interactive3DLogo
**File:** `interactive-logo.tsx`

A fully interactive 3D logo with orbit controls, hover effects, and customizable environments.

**Features:**
- Mouse-controlled orbit navigation
- Hover effects with scaling and lighting
- Multiple environment presets
- Auto-rotation option
- Presentation mode with constraints
- Loading states and error handling

**Usage:**
```tsx
import { Interactive3DLogo } from "@/components/3d-logo"

<Interactive3DLogo
  height="400px"
  showControls={true}
  autoRotate={true}
  interactive={true}
  environment="studio"
  logoScale={1.2}
  floatIntensity={1}
/>
```

### 2. Logo3DHero
**File:** `logo-hero.tsx`

Full-screen hero section with animated 3D logo, starfield background, and call-to-action elements.

**Features:**
- Animated 3D logo with sparkles
- Starfield background with environment mapping
- Responsive design with CTAs
- Smooth entrance animations
- Interactive chat integration

**Usage:**
```tsx
import { Logo3DHero } from "@/components/3d-logo"

<Logo3DHero
  showChatButton={true}
  onStartChat={() => console.log("Chat started")}
/>
```

### 3. LogoParticles
**File:** `logo-particles.tsx`

Logo represented as animated particles with morphing capabilities and multiple color schemes.

**Features:**
- Configurable particle count (1000-5000+)
- Multiple color schemes: primary, rainbow, monochrome
- Morphing between sphere and logo formations
- Additive blending for glowing effects
- Performance optimized

**Usage:**
```tsx
import { LogoParticles } from "@/components/3d-logo"

<LogoParticles
  height="400px"
  particleCount={2000}
  colorScheme="rainbow"
  morphing={true}
  animationSpeed={1.5}
/>
```

### 4. Logo3DLoader
**File:** `logo-loader.tsx`

Animated loading screen with 3D logo assembly animation and progress tracking.

**Features:**
- Three-stage loading animation
- Progress bar with smooth transitions
- Logo piece assembly animation
- Customizable loading text
- Completion callbacks

**Usage:**
```tsx
import { Logo3DLoader } from "@/components/3d-logo"

<Logo3DLoader
  isLoading={loading}
  progress={loadingProgress}
  onComplete={() => setLoading(false)}
/>
```

### 5. LogoEnvironmentShowcase
**File:** `logo-environment.tsx`

3D logo displayed in various environmental presets with realistic lighting and shadows.

**Features:**
- Multiple environment presets
- Realistic contact shadows and platform
- Environment-specific lighting effects
- Interactive environment switching
- Camera controls

**Usage:**
```tsx
import { LogoEnvironmentShowcase } from "@/components/3d-logo"

<LogoEnvironmentShowcase
  height="500px"
  defaultEnvironment="studio"
  showControls={true}
  autoRotate={true}
/>
```

## Installation

The components require the following dependencies:

```bash
pnpm add three @react-three/fiber @react-three/drei
pnpm add -D @types/three
```

## 3D Model Requirements

- **File:** `public/soimagine/logo.glb`
- **Format:** GLTF/GLB (optimized for web)
- **Recommended:** Under 2MB for optimal loading
- **Materials:** PBR materials work best with the lighting setups

## Performance Considerations

1. **Model Optimization:** Ensure the GLB file is optimized for web delivery
2. **Particle Count:** Adjust particle counts based on target devices
3. **Environment Maps:** Some environments are more performance-intensive
4. **Loading Strategy:** Use `useGLTF.preload()` for better UX

## Browser Support

- **WebGL 2.0:** Required for optimal performance
- **Modern Browsers:** Chrome 51+, Firefox 51+, Safari 10+, Edge 79+
- **Mobile:** iOS Safari 10+, Chrome Mobile 51+

## Demo Pages

- **Full Demo:** `/3d-logo-demo` - Comprehensive showcase of all components
- **Integration Example:** `/3d-showcase` - Real-world usage examples

## Customization

### Environment Presets
Available environments: `studio`, `sunset`, `dawn`, `night`, `warehouse`, `city`

### Color Schemes (Particles)
- `primary`: Brand colors (pink/purple)
- `rainbow`: Full spectrum colors
- `monochrome`: Grayscale variations

### Animation Speeds
- `0.5`: Slow, meditative
- `1.0`: Normal speed
- `1.5+`: Fast, energetic

## Integration Examples

### With Existing Hero Components
Replace canvas-based animations in existing hero components:

```tsx
// Before: Canvas 2D animation
<canvas ref={canvasRef} />

// After: 3D Logo Hero
<Logo3DHero onStartChat={handleChat} />
```

### As Loading Screen
```tsx
const [loading, setLoading] = useState(true)

return (
  <>
    <Logo3DLoader
      isLoading={loading}
      progress={loadingProgress}
      onComplete={() => setLoading(false)}
    />
    {!loading && <MainContent />}
  </>
)
```

### In Dashboard Components
```tsx
<Card>
  <CardHeader>
    <CardTitle>Brand Showcase</CardTitle>
  </CardHeader>
  <CardContent>
    <Interactive3DLogo
      height="300px"
      environment="studio"
      showControls={false}
    />
  </CardContent>
</Card>
```

## Troubleshooting

### Common Issues

1. **Model Not Loading**
   - Verify GLB file exists at `/public/soimagine/logo.glb`
   - Check browser console for CORS or loading errors
   - Ensure file is properly formatted GLB

2. **Performance Issues**
   - Reduce particle count for lower-end devices
   - Use simpler environments (studio vs. night)
   - Consider using `React.memo()` for optimization

3. **TypeScript Errors**
   - Ensure `@types/three` is installed
   - Check Three.js version compatibility
   - Verify React Three Fiber types

### Browser Compatibility
If WebGL is not supported, components will show fallback loading states. Consider implementing a 2D fallback for older browsers.

## Contributing

When adding new 3D logo variations:
1. Follow the existing component structure
2. Include proper TypeScript interfaces
3. Add loading states and error handling
4. Update this README with usage examples
5. Test on multiple devices and browsers

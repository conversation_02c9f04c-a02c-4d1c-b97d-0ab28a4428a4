"use client"

import { useRef, useState, useEffect } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import { 
  Environment, 
  Float, 
  Text3D, 
  Center,
  useGLTF,
  Sparkles,
  RoundedBox
} from "@react-three/drei"
import { motion, AnimatePresence } from "framer-motion"
import * as THREE from "three"

interface LoadingLogoProps {
  progress: number
  stage: "loading" | "assembling" | "complete"
}

function LoadingLogo({ progress, stage }: LoadingLogoProps) {
  const meshRef = useRef<THREE.Group>(null)
  const [pieces, setPieces] = useState<THREE.Object3D[]>([])

  // Load the 3D logo model
  const { scene, materials } = useGLTF("/soimagine/logo.gltf")

  // Break logo into pieces for assembly animation
  useEffect(() => {
    if (scene) {
      const clonedScene = scene.clone()
      const logoMeshes: THREE.Object3D[] = []
      
      clonedScene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          logoMeshes.push(child)
        }
      })
      
      setPieces(logoMeshes)
    }
  }, [scene])

  useFrame((state) => {
    if (meshRef.current) {
      const time = state.clock.elapsedTime
      
      switch (stage) {
        case "loading":
          // Spinning and pulsing
          meshRef.current.rotation.y = time * 2
          const scale = 0.8 + Math.sin(time * 4) * 0.2
          meshRef.current.scale.setScalar(scale)
          break
          
        case "assembling":
          // Pieces flying in from different directions
          pieces.forEach((piece, index) => {
            if (piece) {
              const assemblyProgress = Math.min(progress * 2, 1) // Faster assembly
              const startDistance = 5
              const targetPosition = new THREE.Vector3(0, 0, 0)
              
              // Different starting positions for each piece
              const angle = (index / pieces.length) * Math.PI * 2
              const startPos = new THREE.Vector3(
                Math.cos(angle) * startDistance,
                Math.sin(angle + time) * 2,
                Math.sin(angle) * startDistance
              )
              
              piece.position.lerpVectors(startPos, targetPosition, assemblyProgress)
              piece.rotation.x = (1 - assemblyProgress) * Math.PI * 2
              piece.rotation.y = (1 - assemblyProgress) * Math.PI * 4
            }
          })
          break
          
        case "complete":
          // Final reveal with gentle rotation
          meshRef.current.rotation.y = time * 0.3
          meshRef.current.scale.setScalar(1)
          break
      }
    }
  })

  // Enhance materials based on stage
  useEffect(() => {
    if (materials) {
      Object.values(materials).forEach((material: any) => {
        if (material.isMeshStandardMaterial) {
          switch (stage) {
            case "loading":
              material.emissive = new THREE.Color(0x331122)
              material.metalness = 0.8
              material.roughness = 0.2
              break
            case "assembling":
              material.emissive = new THREE.Color(0x221133)
              material.metalness = 0.6
              material.roughness = 0.3
              break
            case "complete":
              material.emissive = new THREE.Color(0x000000)
              material.metalness = 0.4
              material.roughness = 0.4
              break
          }
        }
      })
    }
  }, [materials, stage])

  return (
    <Float
      speed={stage === "loading" ? 3 : 1}
      rotationIntensity={stage === "loading" ? 0.5 : 0.1}
      floatIntensity={stage === "loading" ? 1 : 0.3}
    >
      <group ref={meshRef} scale={[0.6, 0.6, 0.6]}>
        {stage === "assembling" ? (
          // Render individual pieces during assembly
          pieces.map((piece, index) => (
            <primitive key={index} object={piece} />
          ))
        ) : (
          // Render complete logo
          <primitive object={scene.clone()} />
        )}
        
        {/* Dynamic lighting based on stage */}
        {stage === "loading" && (
          <>
            <pointLight
              position={[2, 2, 2]}
              intensity={1 + Math.sin(Date.now() * 0.01) * 0.5}
              color="#e91e63"
              distance={6}
            />
            <Sparkles
              count={30}
              scale={[3, 3, 3]}
              size={1}
              speed={2}
              opacity={0.8}
              color="#e91e63"
            />
          </>
        )}
        
        {stage === "complete" && (
          <pointLight
            position={[0, 2, 2]}
            intensity={1.2}
            color="#ffffff"
            distance={8}
          />
        )}
      </group>
    </Float>
  )
}

function ProgressBar({ progress }: { progress: number }) {
  return (
    <div className="w-64 h-2 bg-muted rounded-full overflow-hidden">
      <motion.div
        className="h-full bg-gradient-to-r from-pink-500 to-purple-600 rounded-full"
        initial={{ width: 0 }}
        animate={{ width: `${progress * 100}%` }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      />
    </div>
  )
}

interface Logo3DLoaderProps {
  isLoading: boolean
  progress?: number
  loadingText?: string
  onComplete?: () => void
  className?: string
}

export function Logo3DLoader({
  isLoading,
  progress = 0,
  loadingText = "Loading...",
  onComplete,
  className = ""
}: Logo3DLoaderProps) {
  const [stage, setStage] = useState<"loading" | "assembling" | "complete">("loading")
  const [displayProgress, setDisplayProgress] = useState(0)

  // Animate progress smoothly
  useEffect(() => {
    const timer = setInterval(() => {
      setDisplayProgress(prev => {
        const target = progress
        const diff = target - prev
        const step = diff * 0.1
        return prev + step
      })
    }, 16)

    return () => clearInterval(timer)
  }, [progress])

  // Update stage based on progress
  useEffect(() => {
    if (displayProgress < 0.3) {
      setStage("loading")
    } else if (displayProgress < 0.9) {
      setStage("assembling")
    } else {
      setStage("complete")
      if (displayProgress >= 1 && onComplete) {
        setTimeout(onComplete, 1000) // Delay before calling onComplete
      }
    }
  }, [displayProgress, onComplete])

  const getStageText = () => {
    switch (stage) {
      case "loading":
        return "Initializing 3D Engine..."
      case "assembling":
        return "Assembling Components..."
      case "complete":
        return "Ready!"
      default:
        return loadingText
    }
  }

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className={`fixed inset-0 z-50 flex items-center justify-center bg-background ${className}`}
        >
          {/* 3D Canvas */}
          <div className="absolute inset-0">
            <Canvas
              camera={{ position: [0, 0, 10], fov: 45 }}
              style={{ width: "100%", height: "100%" }}
            >
              <ambientLight intensity={0.3} />
              <directionalLight
                position={[10, 10, 5]}
                intensity={1}
                castShadow
              />
              <Environment preset="night" />
              
              <LoadingLogo progress={displayProgress} stage={stage} />
            </Canvas>
          </div>

          {/* UI Overlay */}
          <div className="relative z-10 text-center">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-bold mb-2">Soimagine</h2>
              <p className="text-muted-foreground">{getStageText()}</p>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="space-y-4"
            >
              <ProgressBar progress={displayProgress} />
              <p className="text-sm text-muted-foreground">
                {Math.round(displayProgress * 100)}%
              </p>
            </motion.div>

            {/* Loading dots animation */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="flex justify-center space-x-1 mt-6"
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-primary rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2
                  }}
                />
              ))}
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Preload the model
useGLTF.preload("/soimagine/logo.gltf")

"use client"

import { useRef, useState, useEffect, useMemo } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import { 
  Points, 
  PointMaterial, 
  Environment,
  useGLTF,
  Float
} from "@react-three/drei"
import { motion } from "framer-motion"
import * as THREE from "three"

interface ParticleLogoProps {
  particleCount?: number
  animationSpeed?: number
  particleSize?: number
  colorScheme?: "primary" | "rainbow" | "monochrome"
  morphing?: boolean
}

function ParticleLogo({ 
  particleCount = 2000,
  animationSpeed = 1,
  particleSize = 0.02,
  colorScheme = "primary",
  morphing = true
}: ParticleLogoProps) {
  const pointsRef = useRef<THREE.Points>(null)
  const [hovered, setHovered] = useState(false)
  
  // Load the 3D logo model to extract geometry
  const { scene } = useGLTF("/soimagine/logo.gltf")
  
  // Generate particle positions based on logo geometry
  const { positions, colors } = useMemo(() => {
    const positions = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)
    
    // Create a sphere of particles that will morph to logo shape
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3
      
      // Initial random sphere distribution
      const radius = 2 + Math.random() * 2
      const theta = Math.random() * Math.PI * 2
      const phi = Math.acos(2 * Math.random() - 1)
      
      positions[i3] = radius * Math.sin(phi) * Math.cos(theta)
      positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta)
      positions[i3 + 2] = radius * Math.cos(phi)
      
      // Color based on scheme
      let r, g, b
      switch (colorScheme) {
        case "rainbow":
          const hue = (i / particleCount) * 360
          const color = new THREE.Color().setHSL(hue / 360, 0.8, 0.6)
          r = color.r
          g = color.g
          b = color.b
          break
        case "monochrome":
          const intensity = 0.3 + Math.random() * 0.7
          r = g = b = intensity
          break
        default: // primary
          r = 0.91 + Math.random() * 0.09 // Pink variations
          g = 0.12 + Math.random() * 0.08
          b = 0.39 + Math.random() * 0.1
      }
      
      colors[i3] = r
      colors[i3 + 1] = g
      colors[i3 + 2] = b
    }
    
    return { positions, colors }
  }, [particleCount, colorScheme])

  useFrame((state) => {
    if (pointsRef.current) {
      const time = state.clock.elapsedTime * animationSpeed
      const positions = pointsRef.current.geometry.attributes.position.array as Float32Array
      
      // Animate particles
      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3
        
        if (morphing) {
          // Create morphing effect between sphere and logo-like formation
          const morphFactor = (Math.sin(time * 0.5) + 1) * 0.5
          
          // Original sphere position
          const radius = 2 + Math.random() * 2
          const theta = (i / particleCount) * Math.PI * 2 + time * 0.1
          const phi = Math.acos(2 * (i / particleCount) - 1)
          
          const sphereX = radius * Math.sin(phi) * Math.cos(theta)
          const sphereY = radius * Math.sin(phi) * Math.sin(theta)
          const sphereZ = radius * Math.cos(phi)
          
          // Logo-like formation (simplified)
          const logoX = Math.sin(theta) * (1 + Math.cos(phi * 2)) * 1.5
          const logoY = Math.cos(theta) * (1 + Math.cos(phi * 2)) * 1.5
          const logoZ = Math.sin(phi * 2) * 0.5
          
          // Interpolate between sphere and logo
          positions[i3] = THREE.MathUtils.lerp(sphereX, logoX, morphFactor)
          positions[i3 + 1] = THREE.MathUtils.lerp(sphereY, logoY, morphFactor)
          positions[i3 + 2] = THREE.MathUtils.lerp(sphereZ, logoZ, morphFactor)
        } else {
          // Simple rotation animation
          const originalX = positions[i3]
          const originalZ = positions[i3 + 2]
          
          positions[i3] = originalX * Math.cos(time * 0.1) - originalZ * Math.sin(time * 0.1)
          positions[i3 + 2] = originalX * Math.sin(time * 0.1) + originalZ * Math.cos(time * 0.1)
          
          // Add floating motion
          positions[i3 + 1] += Math.sin(time + i * 0.01) * 0.001
        }
      }
      
      pointsRef.current.geometry.attributes.position.needsUpdate = true
      
      // Scale effect on hover
      const targetScale = hovered ? 1.2 : 1
      pointsRef.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), 0.1)
    }
  })

  return (
    <Float
      speed={1}
      rotationIntensity={0.1}
      floatIntensity={0.3}
    >
      <Points
        ref={pointsRef}
        positions={positions}
        colors={colors}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <PointMaterial
          size={particleSize}
          sizeAttenuation={true}
          vertexColors={true}
          transparent={true}
          opacity={0.8}
          blending={THREE.AdditiveBlending}
        />
      </Points>
    </Float>
  )
}

interface LogoParticlesProps {
  width?: string | number
  height?: string | number
  className?: string
  particleCount?: number
  animationSpeed?: number
  particleSize?: number
  colorScheme?: "primary" | "rainbow" | "monochrome"
  morphing?: boolean
  environment?: "studio" | "sunset" | "dawn" | "night"
  cameraPosition?: [number, number, number]
}

export function LogoParticles({
  width = "100%",
  height = "400px",
  className = "",
  particleCount = 2000,
  animationSpeed = 1,
  particleSize = 0.02,
  colorScheme = "primary",
  morphing = true,
  environment = "night",
  cameraPosition = [0, 0, 10]
}: LogoParticlesProps) {
  const [isLoaded, setIsLoaded] = useState(false)

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1 }}
      className={`relative ${className}`}
      style={{ width, height }}
    >
      <Canvas
        camera={{ position: cameraPosition, fov: 50 }}
        style={{ width: "100%", height: "100%" }}
        onCreated={() => setIsLoaded(true)}
      >
        {/* Lighting */}
        <ambientLight intensity={0.2} />
        <pointLight position={[10, 10, 10]} intensity={0.5} />
        <pointLight position={[-10, -10, -10]} intensity={0.3} color="#e91e63" />
        
        {/* Environment */}
        <Environment preset={environment} />
        
        {/* Particle Logo */}
        <ParticleLogo
          particleCount={particleCount}
          animationSpeed={animationSpeed}
          particleSize={particleSize}
          colorScheme={colorScheme}
          morphing={morphing}
        />
      </Canvas>
      
      {/* Loading indicator */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          <div className="flex flex-col items-center gap-4">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <p className="text-sm text-muted-foreground">Generating Particles...</p>
          </div>
        </div>
      )}
      
      {/* Controls overlay */}
      <div className="absolute bottom-4 left-4 text-xs text-muted-foreground bg-background/80 backdrop-blur-sm rounded-lg p-2">
        <p>Particles: {particleCount.toLocaleString()}</p>
        <p>Mode: {morphing ? "Morphing" : "Rotating"}</p>
        <p>Scheme: {colorScheme}</p>
      </div>
    </motion.div>
  )
}

// Preload the model
useGLTF.preload("/soimagine/logo.gltf")

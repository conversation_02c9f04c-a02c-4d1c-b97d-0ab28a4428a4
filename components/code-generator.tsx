"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Copy, Download, Play, Code, FileCode, RefreshCw } from "lucide-react"
import { useAI } from "@/components/ai-provider"
import { useToast } from "@/hooks/use-toast"

export function CodeGenerator() {
  const [prompt, setPrompt] = useState("")
  const [language, setLanguage] = useState("javascript")
  const [generatedCode, setGeneratedCode] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [activeTab, setActiveTab] = useState("code")
  const [previewHtml, setPreviewHtml] = useState("")
  const { startProcessing, stopProcessing } = useAI()
  const { toast } = useToast()

  const languages = [
    { value: "javascript", label: "JavaScript" },
    { value: "typescript", label: "TypeScript" },
    { value: "html", label: "HTML" },
    { value: "css", label: "CSS" },
    { value: "react", label: "React" },
    { value: "python", label: "Python" },
  ]

  const generateCode = () => {
    if (!prompt.trim()) {
      toast({
        title: "Empty prompt",
        description: "Please enter a description of what you want to generate",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true)
    startProcessing()

    // Example code templates based on language
    const codeExamples = {
      javascript: `// Generated JavaScript code based on: "${prompt}"
function main() {
  console.log("Hello from Soimagine AI!");
  
  // Main functionality
  const data = fetchData();
  processData(data);
  displayResults();
}

function fetchData() {
  // Simulating data fetch
  return { items: [1, 2, 3, 4, 5] };
}

function processData(data) {
  // Processing logic
  const results = data.items.map(item => item * 2);
  console.log("Processed data:", results);
  return results;
}

function displayResults() {
  // Display logic
  const output = document.getElementById("output");
  if (output) {
    output.innerHTML = "Processing complete!";
  }
}

// Execute the main function
main();`,
      typescript: `// Generated TypeScript code based on: "${prompt}"
interface DataItem {
  id: number;
  value: string;
}

interface ProcessedResult {
  originalId: number;
  transformedValue: string;
}

class DataProcessor {
  private data: DataItem[] = [];

  constructor(initialData?: DataItem[]) {
    if (initialData) {
      this.data = initialData;
    }
  }

  public addItem(item: DataItem): void {
    this.data.push(item);
  }

  public processData(): ProcessedResult[] {
    return this.data.map(item => ({
      originalId: item.id,
      transformedValue: item.value.toUpperCase()
    }));
  }

  public getResults(): string {
    const results = this.processData();
    return JSON.stringify(results, null, 2);
  }
}

// Usage example
const processor = new DataProcessor([
  { id: 1, value: "hello" },
  { id: 2, value: "world" }
]);
console.log(processor.getResults());`,
      html: `<!-- Generated HTML code based on: "${prompt}" -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Soimagine Generated Page</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #e91e63;
      color: white;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 2rem;
    }
    .container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    footer {
      margin-top: 2rem;
      text-align: center;
      color: #666;
    }
  </style>
</head>
<body>
  <header>
    <h1>Welcome to Soimagine</h1>
    <p>Where imagination meets technology</p>
  </header>
  
  <main>
    <div class="container">
      <div class="card">
        <h2>Web Development</h2>
        <p>Custom web applications that streamline processes and enhance efficiency.</p>
      </div>
      <div class="card">
        <h2>Mobile Applications</h2>
        <p>Native and cross-platform mobile apps for seamless experiences.</p>
      </div>
      <div class="card">
        <h2>AI Solutions</h2>
        <p>Intelligent systems that automate processes and provide insights.</p>
      </div>
    </div>
  </main>
  
  <footer>
    <p>&copy; 2023 Soimagine. All rights reserved.</p>
  </footer>
</body>
</html>`,
      css: `/* Generated CSS code based on: "${prompt}" */
:root {
  --primary: #e91e63;
  --primary-dark: #c2185b;
  --secondary: #2196f3;
  --dark: #121212;
  --light: #f5f5f5;
  --gray: #757575;
  --success: #4caf50;
  --warning: #ff9800;
  --error: #f44336;
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--dark);
  background-color: var(--light);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

p {
  margin-bottom: 1rem;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1rem;
}

.col {
  flex: 1;
  padding: 0 1rem;
}

/* Components */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  background-color: var(--primary);
  color: white;
  text-decoration: none;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn:hover {
  background-color: var(--primary-dark);
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
  .row {
    flex-direction: column;
  }
  
  h1 {
    font-size: 2rem;
  }
}`,
      react: `// Generated React component based on: "${prompt}"
import React, { useState, useEffect } from 'react';

interface DataItem {
  id: number;
  title: string;
  description: string;
  imageUrl?: string;
}

const SoimagineApp: React.FC = () => {
  const [data, setData] = useState<DataItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    // Simulate API fetch
    const fetchData = async () => {
      try {
        setLoading(true);
        // In a real app, this would be an API call
        const mockData: DataItem[] = [
          {
            id: 1,
            title: 'Web Development',
            description: 'Custom web applications that streamline processes.',
            imageUrl: 'https://example.com/web.jpg'
          },
          {
            id: 2,
            title: 'Mobile Applications',
            description: 'Native and cross-platform mobile apps.',
            imageUrl: 'https://example.com/mobile.jpg'
          },
          {
            id: 3,
            title: 'AI Solutions',
            description: 'Intelligent systems for automation and insights.',
            imageUrl: 'https://example.com/ai.jpg'
          }
        ];
        
        // Simulate network delay
        setTimeout(() => {
          setData(mockData);
          setLoading(false);
        }, 1000);
      } catch (err) {
        setError('Failed to fetch data');
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  if (loading) {
    return <div className="loading">Loading...</div>;
  }
  
  if (error) {
    return <div className="error">{error}</div>;
  }
  
  return (
    <div className="soimagine-app">
      <header className="app-header">
        <h1>Soimagine Digital Solutions</h1>
        <p>Where imagination meets technology</p>
      </header>
      
      <main className="app-content">
        <div className="card-grid">
          {data.map((item) => (
            <div key={item.id} className="card">
              {item.imageUrl && (
                <div className="card-image">
                  <img src={item.imageUrl || "/placeholder.svg"} alt={item.title} />
                </div>
              )}
              <div className="card-content">
                <h2>{item.title}</h2>
                <p>{item.description}</p>
                <button className="btn-primary">Learn More</button>
              </div>
            </div>
          ))}
        </div>
      </main>
      
      <footer className="app-footer">
        <p>&copy; {new Date().getFullYear()} Soimagine. All rights reserved.</p>
      </footer>
    </div>
  );
};

export default SoimagineApp;`,
      python: `# Generated Python code based on: "${prompt}"
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

class SoimagineDataProcessor:
    """
    A data processing utility for Soimagine Digital Agency.
    This class handles data transformation and analysis.
    """
    
    def __init__(self, data_source: Optional[str] = None):
        self.data_source = data_source
        self.data = []
        self.processed_results = []
        self.timestamp = datetime.now()
        
        if data_source:
            self.load_data()
    
    def load_data(self) -> None:
        """Load data from the specified source."""
        try:
            # In a real implementation, this would load from a file or API
            # For this example, we'll use mock data
            self.data = [
                {"id": 1, "name": "Web Development", "complexity": "high"},
                {"id": 2, "name": "Mobile App", "complexity": "medium"},
                {"id": 3, "name": "UI/UX Design", "complexity": "medium"},
                {"id": 4, "name": "Data Analysis", "complexity": "high"}
            ]
            print(f"Loaded {len(self.data)} items from {self.data_source}")
        except Exception as e:
            print(f"Error loading data: {e}")
    
    def process_data(self) -> List[Dict[str, Any]]:
        """Process the loaded data and return results."""
        if not self.data:
            return []
            
        self.processed_results = []
        
        for item in self.data:
            # Apply some transformations
            processed_item = {
                "original_id": item["id"],
                "service_name": item["name"].upper(),
                "complexity_score": self._calculate_complexity_score(item["complexity"]),
                "processing_date": self.timestamp.strftime("%Y-%m-%d")
            }
            self.processed_results.append(processed_item)
            
        return self.processed_results
    
    def _calculate_complexity_score(self, complexity: str) -> int:
        """Convert complexity string to numerical score."""
        scores = {"low": 1, "medium": 2, "high": 3}
        return scores.get(complexity.lower(), 0)
    
    def get_summary(self) -> Dict[str, Any]:
        """Generate a summary of the processed data."""
        if not self.processed_results:
            self.process_data()
            
        total_complexity = sum(item["complexity_score"] for item in self.processed_results)
        avg_complexity = total_complexity / len(self.processed_results) if self.processed_results else 0
        
        return {
            "total_items": len(self.processed_results),
            "average_complexity": round(avg_complexity, 2),
            "processing_timestamp": self.timestamp.isoformat(),
            "highest_complexity_service": self._get_highest_complexity_service()
        }
    
    def _get_highest_complexity_service(self) -> str:
        """Find the service with the highest complexity."""
        if not self.processed_results:
            return "No data available"
            
        highest = max(self.processed_results, key=lambda x: x["complexity_score"])
        return highest["service_name"]
    
    def export_results(self, format_type: str = "json") -> str:
        """Export the results in the specified format."""
        if format_type.lower() == "json":
            return json.dumps(self.processed_results, indent=2)
        else:
            return str(self.processed_results)

# Example usage
if __name__ == "__main__":
    processor = SoimagineDataProcessor("mock_data")
    processor.process_data()
    summary = processor.get_summary()
    
    print("Data Processing Summary:")
    print(json.dumps(summary, indent=2))
    
    print("\\nExported Results:")
    print(processor.export_results())`,
    }

    // Simulate code generation with a delay
    setTimeout(() => {
      const generatedCode = codeExamples[language] || codeExamples.javascript
      setGeneratedCode(generatedCode)

      // For HTML, also set preview content
      if (language === "html") {
        setPreviewHtml(generatedCode)
      } else {
        setPreviewHtml("")
      }

      setIsGenerating(false)
      stopProcessing()

      toast({
        title: "Code generated",
        description: "Your code has been generated successfully",
      })
    }, 2000)
  }

  const copyCode = () => {
    navigator.clipboard.writeText(generatedCode)
    toast({
      title: "Copied to clipboard",
      description: "The code has been copied to your clipboard",
    })
  }

  const downloadCode = () => {
    const fileExtensions = {
      javascript: "js",
      typescript: "ts",
      html: "html",
      css: "css",
      react: "jsx",
      python: "py",
    }

    const extension = fileExtensions[language] || "txt"
    const blob = new Blob([generatedCode], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `soimagine-generated-code.${extension}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "Code downloaded",
      description: `File saved as soimagine-generated-code.${extension}`,
    })
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-border/50">
        <div className="space-y-4">
          <div>
            <Label htmlFor="code-prompt">Describe what you want to generate</Label>
            <div className="flex gap-2 mt-1">
              <Input
                id="code-prompt"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="E.g., A landing page with a hero section and three feature cards"
                className="flex-1"
              />
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Language" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <Button onClick={generateCode} disabled={isGenerating || !prompt.trim()} className="w-full gap-2">
            {isGenerating ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Code className="h-4 w-4" />
                Generate Code
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="px-4 pt-4">
          <TabsList className="grid grid-cols-2">
            <TabsTrigger value="code" className="flex items-center gap-2">
              <FileCode className="h-4 w-4" />
              Code
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2" disabled={!previewHtml}>
              <Play className="h-4 w-4" />
              Preview
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="code" className="flex-1 p-0 m-0 flex flex-col">
          {generatedCode ? (
            <>
              <div className="flex items-center justify-between px-4 py-2 bg-muted/50">
                <div className="text-sm font-medium">Generated Code</div>
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm" onClick={copyCode} className="h-8">
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                  <Button variant="ghost" size="sm" onClick={downloadCode} className="h-8">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
              <ScrollArea className="flex-1 p-4 font-mono text-sm bg-muted/30">
                <pre className="whitespace-pre-wrap">{generatedCode}</pre>
              </ScrollArea>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center p-4">
              <div className="text-center text-muted-foreground">
                <FileCode className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Code Generated Yet</h3>
                <p className="max-w-md">
                  Enter a description of what you want to create and click "Generate Code" to get started.
                </p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="preview" className="flex-1 p-0 m-0">
          {previewHtml ? (
            <div className="flex-1 bg-white">
              <iframe
                srcDoc={previewHtml}
                title="Code Preview"
                className="w-full h-full border-0"
                sandbox="allow-scripts"
              />
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center p-4">
              <div className="text-center text-muted-foreground">
                <Play className="h-12 w-12 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Preview Available</h3>
                <p className="max-w-md">Generate HTML code to see a live preview.</p>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

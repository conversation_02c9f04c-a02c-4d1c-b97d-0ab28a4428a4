"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { AssistantDialog } from "@/components/assistant-dialog"
import { Bo<PERSON> } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

export function AssistantButton() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-6 right-6 z-50"
          >
            <Button onClick={() => setIsOpen(true)} size="lg" className="rounded-full h-14 w-14 shadow-lg glow">
              <Bot className="h-6 w-6" />
              <span className="sr-only">Open AI Assistant</span>
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
      <AssistantDialog open={isOpen} onOpenChange={setIsOpen} />
    </>
  )
}

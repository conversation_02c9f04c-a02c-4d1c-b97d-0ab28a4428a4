'use client'

import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import ReactQuill from 'react-quill'
import 'react-quill/dist/quill.snow.css'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Bold, 
  Italic, 
  Underline, 
  Link, 
  Image, 
  List, 
  AlignLeft, 
  AlignCenter, 
  AlignRight,
  Hash,
  AtSign,
  Smile,
  Type,
  Palette
} from 'lucide-react'
import { SocialPlatform } from '@prisma/client'

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  platform?: SocialPlatform
  maxLength?: number
  placeholder?: string
  showFormatting?: boolean
  showSocialFeatures?: boolean
  onHashtagAdd?: (hashtag: string) => void
  onMentionAdd?: (mention: string) => void
  className?: string
}

interface PlatformLimits {
  maxLength: number
  maxHashtags: number
  supportsFormatting: boolean
  supportsMedia: boolean
}

const PLATFORM_LIMITS: Record<SocialPlatform, PlatformLimits> = {
  [SocialPlatform.TWITTER]: {
    maxLength: 280,
    maxHashtags: 10,
    supportsFormatting: false,
    supportsMedia: true
  },
  [SocialPlatform.FACEBOOK]: {
    maxLength: 63206,
    maxHashtags: 30,
    supportsFormatting: true,
    supportsMedia: true
  },
  [SocialPlatform.INSTAGRAM]: {
    maxLength: 2200,
    maxHashtags: 30,
    supportsFormatting: false,
    supportsMedia: true
  },
  [SocialPlatform.LINKEDIN]: {
    maxLength: 3000,
    maxHashtags: 20,
    supportsFormatting: true,
    supportsMedia: true
  },
  [SocialPlatform.TIKTOK]: {
    maxLength: 300,
    maxHashtags: 20,
    supportsFormatting: false,
    supportsMedia: true
  },
  [SocialPlatform.YOUTUBE]: {
    maxLength: 5000,
    maxHashtags: 15,
    supportsFormatting: true,
    supportsMedia: true
  },
  [SocialPlatform.REDDIT]: {
    maxLength: 40000,
    maxHashtags: 10,
    supportsFormatting: true,
    supportsMedia: true
  },
  [SocialPlatform.DISCORD]: {
    maxLength: 2000,
    maxHashtags: 10,
    supportsFormatting: true,
    supportsMedia: true
  },
  [SocialPlatform.OTHER]: {
    maxLength: 1000,
    maxHashtags: 10,
    supportsFormatting: true,
    supportsMedia: true
  }
}

export function RichTextEditor({
  value,
  onChange,
  platform,
  maxLength,
  placeholder = 'What\'s on your mind?',
  showFormatting = true,
  showSocialFeatures = true,
  onHashtagAdd,
  onMentionAdd,
  className = ''
}: RichTextEditorProps) {
  const [hashtags, setHashtags] = useState<string[]>([])
  const [mentions, setMentions] = useState<string[]>([])
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  const platformLimits = platform ? PLATFORM_LIMITS[platform] : null
  const characterLimit = maxLength || platformLimits?.maxLength || 1000
  const currentLength = value.replace(/<[^>]*>/g, '').length // Strip HTML tags for count

  const modules = useMemo(() => {
    const baseModules = {
      toolbar: showFormatting && (!platform || platformLimits?.supportsFormatting) ? [
        [{ 'header': [1, 2, 3, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'align': [] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['link', 'image'],
        ['clean']
      ] : false,
      clipboard: {
        matchVisual: false,
      }
    }

    return baseModules
  }, [showFormatting, platform, platformLimits])

  const formats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'align', 'list', 'bullet',
    'link', 'image'
  ]

  const handleChange = useCallback((content: string) => {
    // Check character limit
    const textLength = content.replace(/<[^>]*>/g, '').length
    if (textLength <= characterLimit) {
      onChange(content)
      
      // Extract hashtags and mentions
      const hashtagMatches = content.match(/#\w+/g) || []
      const mentionMatches = content.match(/@\w+/g) || []
      
      setHashtags(hashtagMatches.map(tag => tag.substring(1)))
      setMentions(mentionMatches.map(mention => mention.substring(1)))
    }
  }, [onChange, characterLimit])

  const insertHashtag = useCallback(() => {
    const hashtag = prompt('Enter hashtag (without #):')
    if (hashtag) {
      const newContent = `${value} #${hashtag}`
      handleChange(newContent)
      onHashtagAdd?.(hashtag)
    }
  }, [value, handleChange, onHashtagAdd])

  const insertMention = useCallback(() => {
    const mention = prompt('Enter username (without @):')
    if (mention) {
      const newContent = `${value} @${mention}`
      handleChange(newContent)
      onMentionAdd?.(mention)
    }
  }, [value, handleChange, onMentionAdd])

  const insertEmoji = useCallback((emoji: string) => {
    const newContent = `${value} ${emoji}`
    handleChange(newContent)
    setShowEmojiPicker(false)
  }, [value, handleChange])

  const commonEmojis = ['😀', '😂', '😍', '🤔', '👍', '❤️', '🔥', '💯', '🎉', '🚀']

  const getCharacterLimitColor = () => {
    const percentage = (currentLength / characterLimit) * 100
    if (percentage >= 90) return 'text-red-500'
    if (percentage >= 75) return 'text-yellow-500'
    return 'text-gray-500'
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Content Editor</CardTitle>
          {platform && (
            <Badge variant="outline" className="capitalize">
              {platform.toLowerCase()}
            </Badge>
          )}
        </div>
        
        {showSocialFeatures && (
          <div className="flex items-center gap-2 pt-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={insertHashtag}
              className="flex items-center gap-1"
            >
              <Hash className="h-3 w-3" />
              Hashtag
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={insertMention}
              className="flex items-center gap-1"
            >
              <AtSign className="h-3 w-3" />
              Mention
            </Button>
            <div className="relative">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                className="flex items-center gap-1"
              >
                <Smile className="h-3 w-3" />
                Emoji
              </Button>
              {showEmojiPicker && (
                <div className="absolute top-full left-0 mt-1 p-2 bg-white border rounded-lg shadow-lg z-10 grid grid-cols-5 gap-1">
                  {commonEmojis.map((emoji, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => insertEmoji(emoji)}
                      className="p-1 hover:bg-gray-100 rounded text-lg"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="min-h-[200px]">
          <ReactQuill
            theme="snow"
            value={value}
            onChange={handleChange}
            modules={modules}
            formats={formats}
            placeholder={placeholder}
            style={{ height: '150px' }}
          />
        </div>

        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            {hashtags.length > 0 && (
              <div className="flex items-center gap-1">
                <Hash className="h-3 w-3 text-blue-500" />
                <span className="text-blue-500">{hashtags.length}</span>
              </div>
            )}
            {mentions.length > 0 && (
              <div className="flex items-center gap-1">
                <AtSign className="h-3 w-3 text-green-500" />
                <span className="text-green-500">{mentions.length}</span>
              </div>
            )}
          </div>
          
          <div className={`font-mono ${getCharacterLimitColor()}`}>
            {currentLength}/{characterLimit}
          </div>
        </div>

        {(hashtags.length > 0 || mentions.length > 0) && (
          <>
            <Separator />
            <div className="space-y-2">
              {hashtags.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Hashtags:</p>
                  <div className="flex flex-wrap gap-1">
                    {hashtags.map((hashtag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        #{hashtag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {mentions.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Mentions:</p>
                  <div className="flex flex-wrap gap-1">
                    {mentions.map((mention, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        @{mention}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {platform && platformLimits && (
          <div className="text-xs text-gray-500 space-y-1">
            <p>Platform limits: {platformLimits.maxLength} characters, {platformLimits.maxHashtags} hashtags</p>
            {!platformLimits.supportsFormatting && (
              <p className="text-yellow-600">⚠️ This platform doesn't support rich text formatting</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

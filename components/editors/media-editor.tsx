'use client'

import React, { useState, useCallback, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import Cropper from 'react-cropper'
import 'cropperjs/dist/cropper.css'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { 
  Upload, 
  Image as ImageIcon, 
  Video, 
  Crop, 
  RotateCw, 
  Palette, 
  Brightness4, 
  Contrast, 
  Download,
  Trash2,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Scissors
} from 'lucide-react'
import { MediaType, SocialPlatform } from '@prisma/client'

interface MediaFile {
  id: string
  file: File
  url: string
  type: MediaType
  thumbnail?: string
  duration?: number
  dimensions?: { width: number; height: number }
  size: number
}

interface MediaEditorProps {
  files: MediaFile[]
  onChange: (files: MediaFile[]) => void
  platform?: SocialPlatform
  maxFiles?: number
  maxFileSize?: number // in MB
  acceptedTypes?: string[]
  showEditor?: boolean
  className?: string
}

interface ImageFilters {
  brightness: number
  contrast: number
  saturation: number
  blur: number
  sepia: number
}

const PLATFORM_MEDIA_LIMITS = {
  [SocialPlatform.TWITTER]: { maxFiles: 4, maxSize: 5, types: ['image', 'video', 'gif'] },
  [SocialPlatform.FACEBOOK]: { maxFiles: 10, maxSize: 10, types: ['image', 'video'] },
  [SocialPlatform.INSTAGRAM]: { maxFiles: 10, maxSize: 100, types: ['image', 'video'] },
  [SocialPlatform.LINKEDIN]: { maxFiles: 9, maxSize: 200, types: ['image', 'video', 'document'] },
  [SocialPlatform.TIKTOK]: { maxFiles: 1, maxSize: 500, types: ['video'] },
  [SocialPlatform.YOUTUBE]: { maxFiles: 1, maxSize: 2000, types: ['video'] },
  [SocialPlatform.REDDIT]: { maxFiles: 20, maxSize: 20, types: ['image', 'video', 'gif'] },
  [SocialPlatform.DISCORD]: { maxFiles: 10, maxSize: 8, types: ['image', 'video', 'gif'] },
  [SocialPlatform.OTHER]: { maxFiles: 5, maxSize: 10, types: ['image', 'video'] }
}

export function MediaEditor({
  files,
  onChange,
  platform,
  maxFiles = 10,
  maxFileSize = 10,
  acceptedTypes = ['image/*', 'video/*'],
  showEditor = true,
  className = ''
}: MediaEditorProps) {
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null)
  const [cropperData, setCropperData] = useState<string>('')
  const [imageFilters, setImageFilters] = useState<ImageFilters>({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
    sepia: 0
  })
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  
  const cropperRef = useRef<HTMLImageElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)

  const platformLimits = platform ? PLATFORM_MEDIA_LIMITS[platform] : null
  const effectiveMaxFiles = Math.min(maxFiles, platformLimits?.maxFiles || maxFiles)
  const effectiveMaxSize = Math.min(maxFileSize, platformLimits?.maxSize || maxFileSize)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: MediaFile[] = []

    acceptedFiles.forEach((file) => {
      if (files.length + newFiles.length >= effectiveMaxFiles) return
      if (file.size > effectiveMaxSize * 1024 * 1024) return

      const mediaFile: MediaFile = {
        id: Math.random().toString(36).substr(2, 9),
        file,
        url: URL.createObjectURL(file),
        type: file.type.startsWith('video/') ? MediaType.VIDEO : 
              file.type.startsWith('image/') ? MediaType.IMAGE : MediaType.IMAGE,
        size: file.size
      }

      // Get image/video dimensions
      if (file.type.startsWith('image/')) {
        const img = new Image()
        img.onload = () => {
          mediaFile.dimensions = { width: img.width, height: img.height }
          onChange([...files, ...newFiles, mediaFile])
        }
        img.src = mediaFile.url
      } else if (file.type.startsWith('video/')) {
        const video = document.createElement('video')
        video.onloadedmetadata = () => {
          mediaFile.dimensions = { width: video.videoWidth, height: video.videoHeight }
          mediaFile.duration = video.duration
          
          // Create thumbnail
          const canvas = document.createElement('canvas')
          canvas.width = 200
          canvas.height = 150
          const ctx = canvas.getContext('2d')
          if (ctx) {
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
            mediaFile.thumbnail = canvas.toDataURL()
          }
          
          onChange([...files, ...newFiles, mediaFile])
        }
        video.src = mediaFile.url
      } else {
        newFiles.push(mediaFile)
      }
    })

    if (newFiles.length > 0) {
      onChange([...files, ...newFiles])
    }
  }, [files, onChange, effectiveMaxFiles, effectiveMaxSize])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    maxFiles: effectiveMaxFiles - files.length,
    maxSize: effectiveMaxSize * 1024 * 1024
  })

  const removeFile = (fileId: string) => {
    const updatedFiles = files.filter(f => f.id !== fileId)
    onChange(updatedFiles)
    if (selectedFile?.id === fileId) {
      setSelectedFile(null)
    }
  }

  const cropImage = () => {
    const cropper = (cropperRef.current as any)?.cropper
    if (cropper && selectedFile) {
      const croppedCanvas = cropper.getCroppedCanvas()
      croppedCanvas.toBlob((blob: Blob) => {
        const croppedFile = new File([blob], selectedFile.file.name, { type: selectedFile.file.type })
        const updatedFile: MediaFile = {
          ...selectedFile,
          file: croppedFile,
          url: URL.createObjectURL(croppedFile),
          size: blob.size
        }
        
        const updatedFiles = files.map(f => f.id === selectedFile.id ? updatedFile : f)
        onChange(updatedFiles)
        setSelectedFile(updatedFile)
      })
    }
  }

  const applyFilters = () => {
    if (!selectedFile || selectedFile.type !== MediaType.IMAGE) return

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      
      if (ctx) {
        // Apply filters
        ctx.filter = `
          brightness(${imageFilters.brightness}%) 
          contrast(${imageFilters.contrast}%) 
          saturate(${imageFilters.saturation}%) 
          blur(${imageFilters.blur}px) 
          sepia(${imageFilters.sepia}%)
        `
        ctx.drawImage(img, 0, 0)
        
        canvas.toBlob((blob: Blob) => {
          const filteredFile = new File([blob], selectedFile.file.name, { type: selectedFile.file.type })
          const updatedFile: MediaFile = {
            ...selectedFile,
            file: filteredFile,
            url: URL.createObjectURL(filteredFile),
            size: blob.size
          }
          
          const updatedFiles = files.map(f => f.id === selectedFile.id ? updatedFile : f)
          onChange(updatedFiles)
          setSelectedFile(updatedFile)
        })
      }
    }
    
    img.src = selectedFile.url
  }

  const resetFilters = () => {
    setImageFilters({
      brightness: 100,
      contrast: 100,
      saturation: 100,
      blur: 0,
      sepia: 0
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Media Editor
          </CardTitle>
          {platform && (
            <Badge variant="outline" className="capitalize">
              {platform.toLowerCase()}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Upload Area */}
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-600">
            {isDragActive ? 'Drop files here...' : 'Drag & drop files here, or click to select'}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Max {effectiveMaxFiles} files, {effectiveMaxSize}MB each
          </p>
        </div>

        {/* Platform Limits Info */}
        {platformLimits && (
          <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
            <strong>{platform}:</strong> Max {platformLimits.maxFiles} files, {platformLimits.maxSize}MB each
            <br />
            Supported: {platformLimits.types.join(', ')}
          </div>
        )}

        {/* File List */}
        {files.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {files.map((file) => (
              <div key={file.id} className="relative group">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  {file.type === MediaType.VIDEO ? (
                    <div className="relative w-full h-full">
                      {file.thumbnail ? (
                        <img
                          src={file.thumbnail}
                          alt="Video thumbnail"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Video className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                      <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                        <Play className="h-6 w-6 text-white" />
                      </div>
                      {file.duration && (
                        <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
                          {formatDuration(file.duration)}
                        </div>
                      )}
                    </div>
                  ) : (
                    <img
                      src={file.url}
                      alt="Upload preview"
                      className="w-full h-full object-cover"
                    />
                  )}
                </div>
                
                <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => removeFile(file.id)}
                    className="h-6 w-6 p-0"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>

                <div className="mt-1 text-xs text-gray-500">
                  <p className="truncate">{file.file.name}</p>
                  <p>{formatFileSize(file.size)}</p>
                  {file.dimensions && (
                    <p>{file.dimensions.width}×{file.dimensions.height}</p>
                  )}
                </div>

                {showEditor && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedFile(file)}
                    className="w-full mt-1 text-xs"
                  >
                    Edit
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Editor Modal/Panel */}
        {showEditor && selectedFile && (
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-lg">
                Edit {selectedFile.type === MediaType.VIDEO ? 'Video' : 'Image'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue={selectedFile.type === MediaType.VIDEO ? 'video' : 'crop'}>
                <TabsList>
                  {selectedFile.type === MediaType.IMAGE && (
                    <>
                      <TabsTrigger value="crop">Crop</TabsTrigger>
                      <TabsTrigger value="filters">Filters</TabsTrigger>
                    </>
                  )}
                  {selectedFile.type === MediaType.VIDEO && (
                    <TabsTrigger value="video">Video</TabsTrigger>
                  )}
                </TabsList>

                {selectedFile.type === MediaType.IMAGE && (
                  <>
                    <TabsContent value="crop" className="space-y-4">
                      <div className="max-h-96 overflow-hidden">
                        <Cropper
                          ref={cropperRef}
                          src={selectedFile.url}
                          style={{ height: 300, width: '100%' }}
                          initialAspectRatio={1}
                          guides={false}
                          crop={() => {}}
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button onClick={cropImage}>
                          <Crop className="h-4 w-4 mr-2" />
                          Apply Crop
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="filters" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-4">
                          <div>
                            <Label>Brightness: {imageFilters.brightness}%</Label>
                            <Slider
                              value={[imageFilters.brightness]}
                              onValueChange={([value]) => setImageFilters(prev => ({ ...prev, brightness: value }))}
                              min={0}
                              max={200}
                              step={1}
                            />
                          </div>
                          
                          <div>
                            <Label>Contrast: {imageFilters.contrast}%</Label>
                            <Slider
                              value={[imageFilters.contrast]}
                              onValueChange={([value]) => setImageFilters(prev => ({ ...prev, contrast: value }))}
                              min={0}
                              max={200}
                              step={1}
                            />
                          </div>

                          <div>
                            <Label>Saturation: {imageFilters.saturation}%</Label>
                            <Slider
                              value={[imageFilters.saturation]}
                              onValueChange={([value]) => setImageFilters(prev => ({ ...prev, saturation: value }))}
                              min={0}
                              max={200}
                              step={1}
                            />
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <Label>Blur: {imageFilters.blur}px</Label>
                            <Slider
                              value={[imageFilters.blur]}
                              onValueChange={([value]) => setImageFilters(prev => ({ ...prev, blur: value }))}
                              min={0}
                              max={10}
                              step={0.1}
                            />
                          </div>

                          <div>
                            <Label>Sepia: {imageFilters.sepia}%</Label>
                            <Slider
                              value={[imageFilters.sepia]}
                              onValueChange={([value]) => setImageFilters(prev => ({ ...prev, sepia: value }))}
                              min={0}
                              max={100}
                              step={1}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button onClick={applyFilters}>
                          <Palette className="h-4 w-4 mr-2" />
                          Apply Filters
                        </Button>
                        <Button variant="outline" onClick={resetFilters}>
                          Reset
                        </Button>
                      </div>

                      <div className="mt-4">
                        <img
                          src={selectedFile.url}
                          alt="Preview"
                          className="max-w-full h-auto rounded"
                          style={{
                            filter: `brightness(${imageFilters.brightness}%) contrast(${imageFilters.contrast}%) saturate(${imageFilters.saturation}%) blur(${imageFilters.blur}px) sepia(${imageFilters.sepia}%)`
                          }}
                        />
                      </div>
                    </TabsContent>
                  </>
                )}

                {selectedFile.type === MediaType.VIDEO && (
                  <TabsContent value="video" className="space-y-4">
                    <div className="relative">
                      <video
                        ref={videoRef}
                        src={selectedFile.url}
                        className="w-full max-h-96 rounded"
                        controls
                        muted={isMuted}
                      />
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          if (videoRef.current) {
                            if (isPlaying) {
                              videoRef.current.pause()
                            } else {
                              videoRef.current.play()
                            }
                            setIsPlaying(!isPlaying)
                          }
                        }}
                      >
                        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          if (videoRef.current) {
                            videoRef.current.muted = !isMuted
                            setIsMuted(!isMuted)
                          }
                        }}
                      >
                        {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                      </Button>
                    </div>
                  </TabsContent>
                )}
              </Tabs>

              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setSelectedFile(null)}>
                  Close
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  )
}

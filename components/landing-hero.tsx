"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { motion } from "framer-motion"
import { ArrowRight, Code, Cpu, Layers, Sparkles } from "lucide-react"

export function LandingHero() {
  return (
    <section className="relative pt-32 pb-16 md:pt-40 md:pb-24 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Where <span className="gradient-text">Imagination</span> and{" "}
              <span className="gradient-text">Technology</span> Come Together
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Soimagine is a digital agency that specializes in creating custom applications powered by AI to streamline
              processes, enhance efficiency, and foster growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="gap-2">
                <Link href="/assistant">
                  Try Our AI Assistant <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="gap-2">
                <Link href="/services">
                  Explore Services <Sparkles className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="mt-16 relative"
          >
            <div className="relative w-full max-w-5xl mx-auto rounded-lg overflow-hidden border border-border/50 shadow-xl">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-background/80 z-0"></div>
              <div className="relative z-10 p-1">
                <div className="bg-card rounded-md overflow-hidden">
                  <div className="flex items-center gap-2 px-4 py-2 border-b border-border/50 bg-muted">
                    <div className="flex gap-1.5">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-xs text-muted-foreground">Soimagine AI Assistant</div>
                  </div>
                  <div className="p-6 space-y-4">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                        <Cpu className="w-4 h-4 text-white" />
                      </div>
                      <div className="bg-muted p-3 rounded-lg rounded-tl-none">
                        <p className="text-sm">Hello! I'm your Soimagine AI assistant. I can help you with:</p>
                        <ul className="mt-2 space-y-1 text-sm">
                          <li className="flex items-center gap-2">
                            <Code className="w-4 h-4 text-primary" />
                            <span>Generating code prototypes</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <Layers className="w-4 h-4 text-primary" />
                            <span>Creating visual presentations</span>
                          </li>
                          <li className="flex items-center gap-2">
                            <Sparkles className="w-4 h-4 text-primary" />
                            <span>Collecting information through forms</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div className="flex items-start gap-4 justify-end">
                      <div className="bg-primary/10 p-3 rounded-lg rounded-tr-none">
                        <p className="text-sm">Can you help me create a landing page for my business?</p>
                      </div>
                      <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center flex-shrink-0">
                        <span className="text-xs font-medium">You</span>
                      </div>
                    </div>
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center flex-shrink-0">
                        <Cpu className="w-4 h-4 text-white" />
                      </div>
                      <div className="bg-muted p-3 rounded-lg rounded-tl-none">
                        <p className="text-sm">
                          I'd be happy to help you create a landing page. Let's start by gathering some information
                          about your business and goals...
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-10 -left-10 w-40 h-40 bg-primary/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-primary/20 rounded-full blur-3xl"></div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

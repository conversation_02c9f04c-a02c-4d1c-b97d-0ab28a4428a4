"use client"

import { useEffect, useRef } from "react"

interface Particle {
  x: number
  y: number
  size: number
  speed: number
  color: string
  angle: number
  opacity: number
  pulse: number
}

interface V0ParticleBackgroundProps {
  intensity?: "low" | "medium" | "high"
  state?: "idle" | "active" | "processing"
  className?: string
}

export function V0ParticleBackground({
  intensity = "medium",
  state = "idle",
  className = ""
}: V0ParticleBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>(null)
  const particlesRef = useRef<Particle[]>([])
  const timeRef = useRef(0)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    let particles = particlesRef.current

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width
      canvas.height = rect.height
      canvas.style.width = rect.width + 'px'
      canvas.style.height = rect.height + 'px'
      initParticles()
    }

    const getParticleCount = () => {
      const baseCount = Math.floor((canvas.width * canvas.height) / 15000)
      switch (intensity) {
        case "low": return Math.max(baseCount * 0.5, 20)
        case "medium": return Math.max(baseCount, 40)
        case "high": return Math.max(baseCount * 1.5, 60)
        default: return baseCount
      }
    }

    const getStateColors = () => {
      switch (state) {
        case "idle":
          return [
            "rgba(59, 130, 246, 0.3)", // blue
            "rgba(147, 51, 234, 0.3)", // purple
            "rgba(236, 72, 153, 0.3)"  // pink
          ]
        case "active":
          return [
            "rgba(34, 197, 94, 0.4)",  // green
            "rgba(59, 130, 246, 0.4)", // blue
            "rgba(168, 85, 247, 0.4)"  // purple
          ]
        case "processing":
          return [
            "rgba(249, 115, 22, 0.5)", // orange
            "rgba(239, 68, 68, 0.5)",  // red
            "rgba(245, 158, 11, 0.5)"  // amber
          ]
        default:
          return ["rgba(59, 130, 246, 0.3)"]
      }
    }

    const initParticles = () => {
      particles = []
      const particleCount = getParticleCount()
      const colors = getStateColors()

      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 3 + 1,
          speed: Math.random() * 0.5 + 0.1,
          color: colors[Math.floor(Math.random() * colors.length)],
          angle: Math.random() * Math.PI * 2,
          opacity: Math.random() * 0.5 + 0.2,
          pulse: Math.random() * Math.PI * 2
        })
      }
      particlesRef.current = particles
    }

    const drawConnections = () => {
      const connectionDistance = 100
      const maxConnections = 3

      particles.forEach((particle, i) => {
        let connections = 0

        for (let j = i + 1; j < particles.length && connections < maxConnections; j++) {
          const other = particles[j]
          const dx = particle.x - other.x
          const dy = particle.y - other.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < connectionDistance) {
            const opacity = (1 - distance / connectionDistance) * 0.2
            ctx.strokeStyle = `rgba(59, 130, 246, ${opacity})`
            ctx.lineWidth = 0.5
            ctx.beginPath()
            ctx.moveTo(particle.x, particle.y)
            ctx.lineTo(other.x, other.y)
            ctx.stroke()
            connections++
          }
        }
      })
    }

    const animate = () => {
      timeRef.current += 0.01
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Update and draw particles
      particles.forEach((particle) => {
        // Update pulse for breathing effect
        particle.pulse += 0.02
        const pulseSize = particle.size + Math.sin(particle.pulse) * 0.5

        // Update position
        particle.x += Math.cos(particle.angle) * particle.speed
        particle.y += Math.sin(particle.angle) * particle.speed

        // Add slight random movement
        particle.angle += (Math.random() - 0.5) * 0.02

        // Wrap around edges within canvas bounds
        if (particle.x < 0) particle.x = canvas.width
        if (particle.x > canvas.width) particle.x = 0
        if (particle.y < 0) particle.y = canvas.height
        if (particle.y > canvas.height) particle.y = 0

        // Draw particle with controlled glow effect
        const maxRadius = Math.max(pulseSize, 1)

        // Only draw if particle is within canvas bounds
        if (particle.x >= -maxRadius && particle.x <= canvas.width + maxRadius &&
            particle.y >= -maxRadius && particle.y <= canvas.height + maxRadius) {

          const gradient = ctx.createRadialGradient(
            particle.x, particle.y, 0,
            particle.x, particle.y, maxRadius * 1.5
          )
          gradient.addColorStop(0, particle.color)
          gradient.addColorStop(0.7, particle.color.replace(/[\d.]+\)$/g, '0.1)'))
          gradient.addColorStop(1, 'transparent')

          ctx.fillStyle = gradient
          ctx.beginPath()
          ctx.arc(particle.x, particle.y, maxRadius, 0, Math.PI * 2)
          ctx.fill()
        }
      })

      // Draw connections between nearby particles
      if (state === "active" || state === "processing") {
        drawConnections()
      }

      animationRef.current = requestAnimationFrame(animate)
    }

    resizeCanvas()
    window.addEventListener("resize", resizeCanvas)
    animate()

    return () => {
      window.removeEventListener("resize", resizeCanvas)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [intensity, state])

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 pointer-events-none v0-particle-canvas ${className}`}
      style={{
        width: "100%",
        height: "100%",
        display: "block"
      }}
    />
  )
}

"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent } from "@/components/ui/card"
import { useAI } from "@/components/ai-provider"
import { Bot, Code, FileText, Mic, MicOff, Send, Sparkles } from "lucide-react"
import { motion } from "framer-motion"
import { ChatMessage } from "@/components/chat-message"
import { FormGenerator } from "@/components/form-generator"
import { CodeGenerator } from "@/components/code-generator"
import { generateText } from "ai"
import { xai } from "@ai-sdk/xai"
import { generateFallbackResponse } from "@/lib/fallback-ai"

export function AssistantInterface() {
  const [activeTab, setActiveTab] = useState("chat")
  const [input, setInput] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const { messages, addMessage, isProcessing, startProcessing, stopProcessing } = useAI()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages])

  const handleSendMessage = async () => {
    if (input.trim() === "") return

    const userMessage = {
      id: Date.now().toString(),
      role: "user" as const,
      content: input,
      timestamp: new Date(),
    }

    addMessage(userMessage)
    setInput("")
    startProcessing()

    try {
      // Try to use AI SDK first
      const { text } = await generateText({
        model: xai("grok-beta"),
        prompt: input,
        system:
          "You are Soimagine's AI assistant, designed to help with digital agency tasks like web development, design, and content creation. Be helpful, creative, and professional.",
      })

      const aiResponse = {
        id: (Date.now() + 1).toString(),
        role: "assistant" as const,
        content: text,
        timestamp: new Date(),
      }

      addMessage(aiResponse)
    } catch (error) {
      console.error("AI API error, using fallback:", error)

      // Use fallback AI service
      const fallbackText = generateFallbackResponse(input)

      const fallbackResponse = {
        id: (Date.now() + 1).toString(),
        role: "assistant" as const,
        content: fallbackText,
        timestamp: new Date(),
      }

      addMessage(fallbackResponse)
    } finally {
      stopProcessing()
    }
  }

  const toggleRecording = () => {
    setIsRecording(!isRecording)
    // In a real implementation, this would start/stop the LiveKit audio recording
  }

  return (
    <Card className="border-border/50 shadow-lg overflow-hidden h-[700px] max-h-[80vh]">
      <CardContent className="p-0">
        <div className="flex flex-col h-full">
          <Tabs defaultValue="chat" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid grid-cols-3 p-1 mx-4 my-4">
              <TabsTrigger value="chat" className="flex items-center gap-2">
                <Bot className="h-4 w-4" />
                <span>Chat</span>
              </TabsTrigger>
              <TabsTrigger value="forms" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>Forms</span>
              </TabsTrigger>
              <TabsTrigger value="code" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                <span>Code</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="chat" className="flex-1 flex flex-col p-0 m-0">
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <div className="text-center py-8">
                      <Bot className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">Welcome to Soimagine AI Assistant</h3>
                      <p className="text-muted-foreground max-w-md mx-auto">
                        I can help you with generating code, creating forms, and providing information about our
                        services.
                      </p>
                      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-2 max-w-md mx-auto">
                        <Button
                          variant="outline"
                          className="flex items-center gap-2 justify-start"
                          onClick={() => setInput("Tell me about Soimagine's services")}
                        >
                          <Sparkles className="h-4 w-4 text-primary" />
                          <span>Soimagine's services</span>
                        </Button>
                        <Button
                          variant="outline"
                          className="flex items-center gap-2 justify-start"
                          onClick={() => setInput("Create a contact form")}
                        >
                          <FileText className="h-4 w-4 text-primary" />
                          <span>Create a contact form</span>
                        </Button>
                        <Button
                          variant="outline"
                          className="flex items-center gap-2 justify-start"
                          onClick={() => setInput("Generate a landing page")}
                        >
                          <Code className="h-4 w-4 text-primary" />
                          <span>Generate a landing page</span>
                        </Button>
                        <Button
                          variant="outline"
                          className="flex items-center gap-2 justify-start"
                          onClick={() => setInput("How can AI help my business?")}
                        >
                          <Bot className="h-4 w-4 text-primary" />
                          <span>AI for business</span>
                        </Button>
                      </div>
                    </div>
                  ) : (
                    messages.map((message) => <ChatMessage key={message.id} message={message} />)
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              <div className="p-4 border-t border-border/50">
                <div className="flex gap-2">
                  <Button
                    variant={isRecording ? "destructive" : "outline"}
                    size="icon"
                    onClick={toggleRecording}
                    className="flex-shrink-0"
                  >
                    {isRecording ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                    <span className="sr-only">{isRecording ? "Stop recording" : "Start recording"}</span>
                  </Button>
                  <Input
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1"
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault()
                        handleSendMessage()
                      }
                    }}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={input.trim() === "" || isProcessing}
                    className="flex-shrink-0"
                  >
                    <Send className="h-4 w-4" />
                    <span className="sr-only">Send message</span>
                  </Button>
                </div>
                {isRecording && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="mt-2 p-2 bg-muted rounded-md text-sm flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-destructive animate-pulse"></div>
                      <span>Recording audio...</span>
                    </div>
                    <Button variant="ghost" size="sm" onClick={toggleRecording} className="h-auto py-1">
                      Cancel
                    </Button>
                  </motion.div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="forms" className="flex-1 p-0 m-0">
              <FormGenerator />
            </TabsContent>

            <TabsContent value="code" className="flex-1 p-0 m-0">
              <CodeGenerator />
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  )
}

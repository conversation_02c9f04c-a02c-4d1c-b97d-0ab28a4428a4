"use client"

import React, { useState, useCallback, useEffect } from 'react'
import { AnimatedSVGLogo, AnimationVariant } from './animated-svg-logo'
import { LoadingState } from './animations'
import { LoadingIndicator, usePerformanceMonitor } from './loading'
import { cn } from '@/lib/utils'

interface NavbarSVGLogoProps {
  /** Width of the logo */
  width?: string | number
  /** Height of the logo */
  height?: string | number
  /** Additional CSS classes */
  className?: string
  /** Whether to enable hover animations */
  enableHover?: boolean
  /** Whether to enable subtle ambient animation */
  enableAmbient?: boolean

  /** Animation variant for ambient */
  ambientVariant?: AnimationVariant
  /** Callback when logo is clicked */
  onClick?: () => void
  /** Callback when logo loads */
  onLoad?: () => void
  /** Callback when logo errors */
  onError?: (error: Error) => void
  /** Aria label for accessibility */
  ariaLabel?: string
  /** Whether to show loading state */
  showLoading?: boolean
}

export function NavbarSVGLogo({
  width = 40,
  height = 28,
  className = "",
  enableHover = true,
  enableAmbient = false,
  ambientVariant = 'breathe',
  onClick,
  onLoad,
  onError,
  ariaLabel = "Soimagine Logo",
  showLoading = false
}: NavbarSVGLogoProps) {
  // State management
  const [isHovered, setIsHovered] = useState(false)
  const [loadingState, setLoadingState] = useState<LoadingState>('idle')
  const [retryCount, setRetryCount] = useState(0)

  // Performance monitoring
  const {
    startMonitoring,
    stopMonitoring,
    measureLoadTime
  } = usePerformanceMonitor({
    enabled: process.env.NODE_ENV === 'development',
    sampleRate: 0.1
  })

  // Initialize component
  useEffect(() => {
    let isMounted = true

    const initializeNavbarLogo = () => {
      if (!isMounted) return

      startMonitoring()
      setLoadingState('loading')

      // Simulate loading process
      const loadTimer = setTimeout(() => {
        if (isMounted) {
          setLoadingState('loaded')
          measureLoadTime()
          onLoad?.()
        }
      }, 100) // Fast loading for navbar

      return () => {
        clearTimeout(loadTimer)
      }
    }

    const cleanup = initializeNavbarLogo()

    return () => {
      isMounted = false
      stopMonitoring()
      cleanup?.()
    }
  }, []) // Empty dependency array to run only once

  // Error handling
  const handleError = useCallback((error: Error) => {
    setLoadingState('error')
    onError?.(error)
  }, [onError])

  // Retry logic
  const handleRetry = useCallback(() => {
    if (retryCount < 3) {
      setRetryCount(prev => prev + 1)
      setLoadingState('loading')

      setTimeout(() => {
        setLoadingState('loaded')
        onLoad?.()
      }, 500)
    }
  }, [retryCount, onLoad])

  // Get animation variant based on state (stable - no hover variant changes)
  const getAnimationVariant = useCallback((): AnimationVariant => {
    if (loadingState === 'loading') return 'loading'
    if (loadingState === 'error') return 'error'
    if (enableAmbient) return ambientVariant
    return 'fadeIn' // Position-stable variant - no layout shifts
  }, [loadingState, enableAmbient, ambientVariant])



  // Handle loading state
  if (loadingState === 'loading' && showLoading) {
    return (
      <div
        className={cn("relative inline-block", className)}
        style={{ width, height }}
      >
        <LoadingIndicator
          state="loading"
          size="sm"
          showText={false}
        />
      </div>
    )
  }

  // Handle error state
  if (loadingState === 'error') {
    return (
      <div
        className={cn("relative inline-block", className)}
        style={{ width, height }}
      >
        <LoadingIndicator
          state="error"
          size="sm"
          showText={false}
          onRetry={retryCount < 3 ? handleRetry : undefined}
        />
      </div>
    )
  }

  // Main render
  return (
    <div
      className={cn(
        "relative inline-block cursor-pointer",
        // Remove scale transforms to prevent positioning issues
        "transition-opacity duration-300",
        className
      )}
      onClick={onClick}
      onMouseEnter={() => enableHover && setIsHovered(true)}
      onMouseLeave={() => enableHover && setIsHovered(false)}
      role="button"
      tabIndex={0}
      aria-label={ariaLabel}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          onClick?.()
        }
      }}
      style={{
        // Fixed dimensions to prevent layout shifts
        width: typeof width === 'number' ? `${width}px` : width,
        height: typeof height === 'number' ? `${height}px` : height
      }}
    >
      <AnimatedSVGLogo
        width={width}
        height={height}
        variant={getAnimationVariant()}
        colorScheme="default"
        speed="fast"
        autoPlay={enableAmbient}
        playOnHover={false} // We handle hover state manually
        loop={enableAmbient}
        onLoad={() => setLoadingState('loaded')}
        onError={handleError}
        loadingConfig={{
          showSpinner: showLoading,
          timeout: 5000,
          retryCount: 3
        }}
        performanceConfig={{
          enabled: process.env.NODE_ENV === 'development',
          sampleRate: 0.1
        }}
        ariaLabel={ariaLabel}
        className={cn(
          "transition-opacity duration-300",
          enableHover && isHovered && loadingState === 'loaded' && "opacity-80"
        )}
      />

      {/* Position-stable hover glow effect */}
      <div
        className={cn(
          "absolute inset-0 bg-primary/10 rounded-md blur-sm -z-10 transition-opacity duration-300",
          enableHover && isHovered && loadingState === 'loaded'
            ? "opacity-100"
            : "opacity-0"
        )}
      />
    </div>
  )
}

// Compact version for mobile
export function CompactNavbarSVGLogo({
  className = "",
  ...props
}: Omit<NavbarSVGLogoProps, 'width' | 'height'> & { className?: string }) {
  return (
    <NavbarSVGLogo
      width={32}
      height={22}
      className={cn("md:hidden", className)}
      enableAmbient={false} // Disable ambient on mobile for performance
      {...props}
    />
  )
}

// Animated version with more effects
export function AnimatedNavbarSVGLogo({
  className = "",
  ...props
}: NavbarSVGLogoProps) {
  return (
    <NavbarSVGLogo
      enableAmbient={true}
      ambientVariant="breathe"
      className={cn("drop-shadow-sm", className)}
      {...props}
    />
  )
}

// Utility function to preload the SVG logo
export const preloadNavbarSVGLogo = () => {
  // Since we're using inline SVG, no preloading needed
  // This is here for API consistency with the 3D logo
  return Promise.resolve()
}

// Performance monitoring hook
export function useSVGLogoPerformance() {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    animationFrames: 0
  })

  const startTiming = () => {
    const startTime = performance.now()
    return {
      end: () => {
        const endTime = performance.now()
        setMetrics(prev => ({
          ...prev,
          loadTime: endTime - startTime
        }))
      }
    }
  }

  return { metrics, startTiming }
}

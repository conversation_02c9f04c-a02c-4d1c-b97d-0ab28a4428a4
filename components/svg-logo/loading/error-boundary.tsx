"use client"

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON><PERSON>, Bug } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AnimationError } from '../animations/types'

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  retryCount: number
}

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  maxRetries?: number
  resetOnPropsChange?: boolean
  resetKeys?: Array<string | number>
}

interface ErrorDisplayProps {
  error: Error
  errorInfo?: ErrorInfo
  onRetry?: () => void
  onReport?: () => void
  retryCount: number
  maxRetries: number
  isDevelopment?: boolean
}

export class SVGLogoErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private resetTimeoutId: number | null = null

  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo)

    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error('SVG Logo Error Boundary caught an error:', error, errorInfo)
    }

    // Report error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo)
    }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps) {
    const { resetKeys, resetOnPropsChange } = this.props
    const { hasError } = this.state

    // Reset error boundary when resetKeys change
    if (hasError && resetKeys && prevProps.resetKeys) {
      const hasResetKeyChanged = resetKeys.some(
        (key, index) => key !== prevProps.resetKeys?.[index]
      )
      
      if (hasResetKeyChanged) {
        this.resetErrorBoundary()
      }
    }

    // Reset error boundary when props change (if enabled)
    if (hasError && resetOnPropsChange && prevProps !== this.props) {
      this.resetErrorBoundary()
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId)
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to your error reporting service
    // e.g., Sentry, LogRocket, Bugsnag, etc.
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // Example: Send to monitoring service
    // errorReportingService.captureException(error, { extra: errorReport })
    
    console.warn('Error report generated:', errorReport)
  }

  private resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId)
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    })
  }

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props
    const { retryCount } = this.state

    if (retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }))

      // Auto-reset after a delay to prevent infinite retry loops
      this.resetTimeoutId = window.setTimeout(() => {
        this.resetErrorBoundary()
      }, 5000)
    }
  }

  private handleReport = () => {
    const { error, errorInfo } = this.state
    if (error && errorInfo) {
      this.reportError(error, errorInfo)
    }
  }

  render() {
    const { hasError, error, errorInfo, retryCount } = this.state
    const { children, fallback, maxRetries = 3 } = this.props

    if (hasError && error) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback
      }

      // Default error display
      return (
        <ErrorDisplay
          error={error}
          errorInfo={errorInfo || undefined}
          onRetry={retryCount < maxRetries ? this.handleRetry : undefined}
          onReport={this.handleReport}
          retryCount={retryCount}
          maxRetries={maxRetries}
          isDevelopment={process.env.NODE_ENV === 'development'}
        />
      )
    }

    return children
  }
}

function ErrorDisplay({
  error,
  errorInfo,
  onRetry,
  onReport,
  retryCount,
  maxRetries,
  isDevelopment = false
}: ErrorDisplayProps) {
  const isAnimationError = error instanceof Error && error.name === 'AnimationError'
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="flex items-center justify-center p-4"
    >
      <Card className="w-full max-w-md border-destructive/20">
        <CardHeader className="text-center">
          <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-lg">
            {isAnimationError ? 'Animation Error' : 'Logo Loading Error'}
          </CardTitle>
          <CardDescription>
            {isAnimationError 
              ? 'There was a problem with the logo animation'
              : 'Failed to load the logo component'
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Error message */}
          <div className="rounded-md bg-muted p-3">
            <p className="text-sm font-medium text-foreground">
              {error.message || 'An unexpected error occurred'}
            </p>
          </div>

          {/* Retry information */}
          {retryCount > 0 && (
            <div className="text-center text-sm text-muted-foreground">
              Retry attempt: {retryCount} of {maxRetries}
            </div>
          )}

          {/* Action buttons */}
          <div className="flex gap-2">
            {onRetry && (
              <Button
                onClick={onRetry}
                variant="default"
                size="sm"
                className="flex-1"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            )}
            
            {isDevelopment && (
              <Button
                onClick={onReport}
                variant="outline"
                size="sm"
                className="flex-1"
              >
                <Bug className="mr-2 h-4 w-4" />
                Report
              </Button>
            )}
          </div>

          {/* Development details */}
          {isDevelopment && errorInfo && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-muted-foreground">
                Technical Details
              </summary>
              <div className="mt-2 rounded-md bg-muted p-3">
                <pre className="text-xs text-muted-foreground overflow-auto max-h-32">
                  {error.stack}
                </pre>
                {errorInfo.componentStack && (
                  <pre className="mt-2 text-xs text-muted-foreground overflow-auto max-h-32">
                    Component Stack:{errorInfo.componentStack}
                  </pre>
                )}
              </div>
            </details>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Higher-order component for easier usage
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <SVGLogoErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </SVGLogoErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook for error reporting
export function useErrorReporting() {
  const reportError = (error: Error, context?: Record<string, unknown>) => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    if (process.env.NODE_ENV === 'development') {
      console.error('Error reported:', errorReport)
    } else {
      // Send to error reporting service
      // errorReportingService.captureException(error, { extra: errorReport })
    }
  }

  return { reportError }
}

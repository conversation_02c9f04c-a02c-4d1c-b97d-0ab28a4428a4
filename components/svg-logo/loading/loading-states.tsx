"use client"

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Loader2, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'
import { LoadingState } from '../animations/types'

interface LoadingIndicatorProps {
  state: LoadingState
  size?: 'sm' | 'md' | 'lg'
  className?: string
  showText?: boolean
  text?: {
    loading?: string
    error?: string
    loaded?: string
  }
  onRetry?: () => void
}

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
  variant?: 'default' | 'dots' | 'pulse' | 'bars'
}

interface LoadingProgressProps {
  progress: number
  size?: 'sm' | 'md' | 'lg'
  className?: string
  showPercentage?: boolean
  variant?: 'linear' | 'circular'
}

interface ErrorFallbackProps {
  error?: Error
  onRetry?: () => void
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

// Size configurations
const SIZE_CONFIGS = {
  sm: {
    spinner: 'w-4 h-4',
    icon: 'w-4 h-4',
    text: 'text-xs',
    container: 'p-2',
    progress: 'h-1'
  },
  md: {
    spinner: 'w-6 h-6',
    icon: 'w-5 h-5',
    text: 'text-sm',
    container: 'p-3',
    progress: 'h-2'
  },
  lg: {
    spinner: 'w-8 h-8',
    icon: 'w-6 h-6',
    text: 'text-base',
    container: 'p-4',
    progress: 'h-3'
  }
}

export function LoadingSpinner({ 
  size = 'md', 
  className = "", 
  variant = 'default' 
}: LoadingSpinnerProps) {
  const sizeClass = SIZE_CONFIGS[size].spinner

  const spinnerVariants = {
    default: (
      <Loader2 className={cn(sizeClass, "animate-spin text-primary", className)} />
    ),
    dots: (
      <div className={cn("flex space-x-1", className)}>
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-primary rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: i * 0.2
            }}
          />
        ))}
      </div>
    ),
    pulse: (
      <motion.div
        className={cn(sizeClass, "bg-primary rounded-full", className)}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.5, 1, 0.5]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    ),
    bars: (
      <div className={cn("flex space-x-1", className)}>
        {[0, 1, 2, 3].map((i) => (
          <motion.div
            key={i}
            className="w-1 bg-primary rounded-full"
            style={{ height: size === 'sm' ? '16px' : size === 'md' ? '24px' : '32px' }}
            animate={{
              scaleY: [1, 2, 1]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: i * 0.1
            }}
          />
        ))}
      </div>
    )
  }

  return spinnerVariants[variant]
}

export function LoadingProgress({ 
  progress, 
  size = 'md', 
  className = "", 
  showPercentage = false,
  variant = 'linear'
}: LoadingProgressProps) {
  const sizeConfig = SIZE_CONFIGS[size]
  const percentage = Math.min(Math.max(progress * 100, 0), 100)

  if (variant === 'circular') {
    const radius = size === 'sm' ? 16 : size === 'md' ? 20 : 24
    const circumference = 2 * Math.PI * radius
    const strokeDashoffset = circumference - (percentage / 100) * circumference

    return (
      <div className={cn("relative inline-flex items-center justify-center", className)}>
        <svg
          className={cn(
            size === 'sm' ? 'w-10 h-10' : size === 'md' ? 'w-12 h-12' : 'w-16 h-16',
            "transform -rotate-90"
          )}
        >
          <circle
            cx="50%"
            cy="50%"
            r={radius}
            stroke="currentColor"
            strokeWidth="2"
            fill="transparent"
            className="text-muted-foreground/20"
          />
          <motion.circle
            cx="50%"
            cy="50%"
            r={radius}
            stroke="currentColor"
            strokeWidth="2"
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="text-primary"
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          />
        </svg>
        {showPercentage && (
          <span className={cn("absolute inset-0 flex items-center justify-center", sizeConfig.text)}>
            {Math.round(percentage)}%
          </span>
        )}
      </div>
    )
  }

  return (
    <div className={cn("w-full", className)}>
      <div className={cn("bg-muted rounded-full overflow-hidden", sizeConfig.progress)}>
        <motion.div
          className="h-full bg-primary rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
        />
      </div>
      {showPercentage && (
        <div className={cn("mt-1 text-center", sizeConfig.text)}>
          {Math.round(percentage)}%
        </div>
      )}
    </div>
  )
}

export function ErrorFallback({ 
  error, 
  onRetry, 
  className = "", 
  size = 'md' 
}: ErrorFallbackProps) {
  const sizeConfig = SIZE_CONFIGS[size]

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        "flex flex-col items-center justify-center text-center",
        sizeConfig.container,
        className
      )}
    >
      <AlertCircle className={cn(sizeConfig.icon, "text-destructive mb-2")} />
      <p className={cn(sizeConfig.text, "text-muted-foreground mb-2")}>
        {error?.message || "Failed to load logo"}
      </p>
      {onRetry && (
        <button
          onClick={onRetry}
          className={cn(
            "flex items-center gap-1 text-primary hover:text-primary/80 transition-colors",
            sizeConfig.text
          )}
        >
          <RefreshCw className="w-3 h-3" />
          Retry
        </button>
      )}
    </motion.div>
  )
}

export function LoadingIndicator({
  state,
  size = 'md',
  className = "",
  showText = true,
  text = {
    loading: "Loading logo...",
    error: "Failed to load",
    loaded: "Logo loaded"
  },
  onRetry
}: LoadingIndicatorProps) {
  const sizeConfig = SIZE_CONFIGS[size]

  const getStateContent = () => {
    switch (state) {
      case 'loading':
        return (
          <div className="flex flex-col items-center gap-2">
            <LoadingSpinner size={size} />
            {showText && (
              <span className={cn(sizeConfig.text, "text-muted-foreground")}>
                {text.loading}
              </span>
            )}
          </div>
        )
      
      case 'error':
        return (
          <ErrorFallback 
            error={new Error(text.error)} 
            onRetry={onRetry} 
            size={size}
          />
        )
      
      case 'loaded':
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex flex-col items-center gap-2"
          >
            <CheckCircle className={cn(sizeConfig.icon, "text-green-500")} />
            {showText && (
              <span className={cn(sizeConfig.text, "text-muted-foreground")}>
                {text.loaded}
              </span>
            )}
          </motion.div>
        )
      
      default:
        return null
    }
  }

  return (
    <div className={cn(
      "flex items-center justify-center",
      sizeConfig.container,
      className
    )}>
      <AnimatePresence mode="wait">
        {state !== 'idle' && (
          <motion.div
            key={state}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.15 }}
            className="flex items-center justify-center"
          >
            {getStateContent()}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Skeleton loader for logo placeholder
export function LogoSkeleton({
  width = 40,
  height = 28,
  className = ""
}: {
  width?: number
  height?: number
  className?: string
}) {
  return (
    <div
      className={cn("bg-muted rounded animate-pulse", className)}
      style={{ width, height }}
    >
      <div className="w-full h-full bg-gradient-to-r from-muted via-muted-foreground/10 to-muted animate-shimmer" />
    </div>
  )
}

// Simple loading spinner without AnimatePresence for navbar use
export function SimpleLoadingSpinner({
  size = 'md',
  className = ""
}: {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}) {
  const sizeClass = SIZE_CONFIGS[size].spinner

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <Loader2 className={cn(sizeClass, "animate-spin text-primary")} />
    </div>
  )
}

// Position-stable loading indicator for navbar
export function NavbarLoadingIndicator({
  isLoading,
  hasError,
  size = 'sm',
  className = "",
  onRetry
}: {
  isLoading: boolean
  hasError: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
  onRetry?: () => void
}) {
  const sizeConfig = SIZE_CONFIGS[size]

  if (!isLoading && !hasError) return null

  return (
    <div className={cn(
      "absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm rounded z-10",
      className
    )}>
      {isLoading && (
        <Loader2 className={cn(sizeConfig.spinner, "animate-spin text-primary")} />
      )}
      {hasError && (
        <div className="flex flex-col items-center gap-1">
          <AlertCircle className={cn(sizeConfig.icon, "text-destructive")} />
          {onRetry && (
            <button
              onClick={onRetry}
              className="text-xs text-primary hover:text-primary/80 transition-colors"
            >
              Retry
            </button>
          )}
        </div>
      )}
    </div>
  )
}

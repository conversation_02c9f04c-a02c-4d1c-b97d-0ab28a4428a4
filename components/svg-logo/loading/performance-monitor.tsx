"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { AnimationMetrics, PerformanceConfig } from '../animations/types'

// Default performance configuration
const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  enabled: process.env.NODE_ENV === 'development',
  sampleRate: 0.1, // 10% sampling in production
  thresholds: {
    loadTime: 1000, // 1 second
    fps: 30, // 30 FPS minimum
    memoryUsage: 50 * 1024 * 1024 // 50MB
  }
}

interface PerformanceEntry {
  timestamp: number
  metric: string
  value: number
  context?: Record<string, unknown>
}

interface PerformanceReport {
  entries: PerformanceEntry[]
  summary: {
    averageLoadTime: number
    averageFPS: number
    memoryUsage: number
    errorRate: number
  }
  recommendations: string[]
}

export function usePerformanceMonitor(config: Partial<PerformanceConfig> = {}) {
  const fullConfig = { ...DEFAULT_PERFORMANCE_CONFIG, ...config }
  const [metrics, setMetrics] = useState<AnimationMetrics>({
    loadTime: 0,
    renderTime: 0,
    animationFrames: 0,
    memoryUsage: 0,
    fps: 0
  })
  
  const [isMonitoring, setIsMonitoring] = useState(false)
  const performanceEntries = useRef<PerformanceEntry[]>([])
  const frameCount = useRef(0)
  const lastFrameTime = useRef(0)
  const animationFrameId = useRef<number>()
  const startTime = useRef<number>()

  // Memory usage monitoring
  const getMemoryUsage = useCallback((): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return memory.usedJSHeapSize || 0
    }
    return 0
  }, [])

  // FPS monitoring
  const measureFPS = useCallback((timestamp: number) => {
    if (!lastFrameTime.current) {
      lastFrameTime.current = timestamp
    }

    const delta = timestamp - lastFrameTime.current
    if (delta >= 1000) { // Calculate FPS every second
      const fps = Math.round((frameCount.current * 1000) / delta)
      
      setMetrics(prev => ({ ...prev, fps }))
      
      // Check FPS threshold
      if (fps < fullConfig.thresholds.fps) {
        fullConfig.onThresholdExceeded?.('fps', fps)
      }
      
      frameCount.current = 0
      lastFrameTime.current = timestamp
    } else {
      frameCount.current++
    }

    if (isMonitoring) {
      animationFrameId.current = requestAnimationFrame(measureFPS)
    }
  }, [isMonitoring, fullConfig])

  // Start monitoring
  const startMonitoring = useCallback(() => {
    if (!fullConfig.enabled) return

    // Should we sample this session?
    if (Math.random() > fullConfig.sampleRate && process.env.NODE_ENV === 'production') {
      return
    }

    setIsMonitoring(true)
    startTime.current = performance.now()
    frameCount.current = 0
    lastFrameTime.current = 0
    
    // Start FPS monitoring
    animationFrameId.current = requestAnimationFrame(measureFPS)
    
    // Initial memory measurement
    const initialMemory = getMemoryUsage()
    setMetrics(prev => ({ ...prev, memoryUsage: initialMemory }))
  }, [fullConfig, measureFPS, getMemoryUsage])

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false)
    
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current)
    }

    // Calculate final metrics
    if (startTime.current) {
      const totalTime = performance.now() - startTime.current
      setMetrics(prev => ({
        ...prev,
        renderTime: totalTime,
        animationFrames: frameCount.current
      }))
    }
  }, [])

  // Record performance entry
  const recordEntry = useCallback((metric: string, value: number, context?: Record<string, unknown>) => {
    if (!fullConfig.enabled) return

    const entry: PerformanceEntry = {
      timestamp: performance.now(),
      metric,
      value,
      context
    }

    performanceEntries.current.push(entry)

    // Check thresholds
    if (metric === 'loadTime' && value > fullConfig.thresholds.loadTime) {
      fullConfig.onThresholdExceeded?.('loadTime', value)
    }
    
    if (metric === 'memoryUsage' && value > fullConfig.thresholds.memoryUsage) {
      fullConfig.onThresholdExceeded?.('memoryUsage', value)
    }
  }, [fullConfig])

  // Measure load time
  const measureLoadTime = useCallback(() => {
    if (!startTime.current) return 0

    const loadTime = performance.now() - startTime.current
    setMetrics(prev => ({ ...prev, loadTime }))
    recordEntry('loadTime', loadTime)
    
    return loadTime
  }, [recordEntry])

  // Generate performance report
  const generateReport = useCallback((): PerformanceReport => {
    const entries = performanceEntries.current
    const loadTimes = entries.filter(e => e.metric === 'loadTime').map(e => e.value)
    const fpsValues = entries.filter(e => e.metric === 'fps').map(e => e.value)
    
    const averageLoadTime = loadTimes.length > 0 
      ? loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length 
      : 0
    
    const averageFPS = fpsValues.length > 0 
      ? fpsValues.reduce((a, b) => a + b, 0) / fpsValues.length 
      : 0

    const recommendations: string[] = []
    
    if (averageLoadTime > fullConfig.thresholds.loadTime) {
      recommendations.push('Consider optimizing SVG complexity or using a simpler animation variant')
    }
    
    if (averageFPS < fullConfig.thresholds.fps) {
      recommendations.push('Reduce animation complexity or disable animations on low-end devices')
    }
    
    if (metrics.memoryUsage > fullConfig.thresholds.memoryUsage) {
      recommendations.push('Monitor memory usage and consider implementing cleanup strategies')
    }

    return {
      entries,
      summary: {
        averageLoadTime,
        averageFPS,
        memoryUsage: metrics.memoryUsage,
        errorRate: 0 // Would be calculated from error entries
      },
      recommendations
    }
  }, [metrics, fullConfig])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current)
      }
    }
  }, [])

  // Monitor memory usage periodically
  useEffect(() => {
    if (!isMonitoring) return

    const interval = setInterval(() => {
      const memoryUsage = getMemoryUsage()
      setMetrics(prev => ({ ...prev, memoryUsage }))
      recordEntry('memoryUsage', memoryUsage)
    }, 5000) // Every 5 seconds

    return () => clearInterval(interval)
  }, [isMonitoring, getMemoryUsage, recordEntry])

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    measureLoadTime,
    recordEntry,
    generateReport,
    config: fullConfig
  }
}

// Performance monitoring component
export function PerformanceMonitor({ 
  children, 
  config,
  onReport 
}: { 
  children: React.ReactNode
  config?: Partial<PerformanceConfig>
  onReport?: (report: PerformanceReport) => void
}) {
  const {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    generateReport
  } = usePerformanceMonitor(config)

  useEffect(() => {
    startMonitoring()
    return () => {
      stopMonitoring()
      if (onReport) {
        const report = generateReport()
        onReport(report)
      }
    }
  }, [startMonitoring, stopMonitoring, generateReport, onReport])

  // Development performance overlay
  if (process.env.NODE_ENV === 'development' && isMonitoring) {
    return (
      <div className="relative">
        {children}
        <div className="fixed bottom-4 right-4 bg-background/90 backdrop-blur-sm border rounded-lg p-3 text-xs font-mono z-50">
          <div className="space-y-1">
            <div>Load: {metrics.loadTime.toFixed(0)}ms</div>
            <div>FPS: {metrics.fps}</div>
            <div>Memory: {(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB</div>
            <div>Frames: {metrics.animationFrames}</div>
          </div>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// Hook for component-level performance tracking
export function useComponentPerformance(componentName: string) {
  const { recordEntry, measureLoadTime } = usePerformanceMonitor()
  const renderStartTime = useRef<number>()

  const startRender = useCallback(() => {
    renderStartTime.current = performance.now()
  }, [])

  const endRender = useCallback(() => {
    if (renderStartTime.current) {
      const renderTime = performance.now() - renderStartTime.current
      recordEntry(`${componentName}_render`, renderTime)
    }
  }, [componentName, recordEntry])

  const trackEvent = useCallback((eventName: string, value?: number) => {
    recordEntry(`${componentName}_${eventName}`, value || performance.now())
  }, [componentName, recordEntry])

  return {
    startRender,
    endRender,
    trackEvent,
    measureLoadTime
  }
}

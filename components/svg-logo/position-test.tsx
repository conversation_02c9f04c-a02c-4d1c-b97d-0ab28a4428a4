"use client"

import React, { useState } from 'react'
import { NavbarLoadingIndicator, LoadingIndicator } from './loading'
import { Button } from '@/components/ui/button'

export function PositionTest() {
  const [showOldLoader, setShowOldLoader] = useState(false)
  const [showNewLoader, setShowNewLoader] = useState(false)
  const [hasError, setHasError] = useState(false)

  return (
    <div className="p-8 space-y-8">
      <h2 className="text-2xl font-bold">Loading Position Test</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Old LoadingIndicator with AnimatePresence */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Old LoadingIndicator (with AnimatePresence)</h3>
          <div className="relative w-40 h-28 border-2 border-dashed border-gray-300 rounded">
            <div className="absolute inset-2 bg-blue-100 rounded flex items-center justify-center text-xs">
              Logo Area
            </div>
            {showOldLoader && (
              <LoadingIndicator
                state="loading"
                size="sm"
                showText={false}
              />
            )}
          </div>
          <Button 
            onClick={() => setShowOldLoader(!showOldLoader)}
            variant="outline"
          >
            {showOldLoader ? 'Hide' : 'Show'} Old Loader
          </Button>
        </div>

        {/* New NavbarLoadingIndicator */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">New NavbarLoadingIndicator (position-stable)</h3>
          <div className="relative w-40 h-28 border-2 border-dashed border-gray-300 rounded">
            <div className="absolute inset-2 bg-green-100 rounded flex items-center justify-center text-xs">
              Logo Area
            </div>
            <NavbarLoadingIndicator
              isLoading={showNewLoader && !hasError}
              hasError={hasError}
              size="sm"
              onRetry={() => setHasError(false)}
            />
          </div>
          <div className="space-x-2">
            <Button 
              onClick={() => {
                setShowNewLoader(!showNewLoader)
                setHasError(false)
              }}
              variant="outline"
            >
              {showNewLoader ? 'Hide' : 'Show'} New Loader
            </Button>
            <Button 
              onClick={() => setHasError(!hasError)}
              variant="outline"
              disabled={!showNewLoader}
            >
              Toggle Error
            </Button>
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-muted rounded-lg">
        <h4 className="font-semibold mb-2">Test Instructions:</h4>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Toggle the old loader on/off and watch for layout shifts</li>
          <li>Toggle the new loader on/off - should be position-stable</li>
          <li>Test error state with the new loader</li>
          <li>The new loader should overlay without affecting layout</li>
        </ol>
      </div>
    </div>
  )
}

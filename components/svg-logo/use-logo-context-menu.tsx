"use client"

import { useState, useCallback, useRef } from 'react'
import { AnimationVariant, ColorScheme, AnimationSpeed, EasingType, LoopType, DelayType, RandomizationType } from './animations'
import { ANIMATION_PRESETS, AnimationPreset } from './animations/advanced-controls'
import { FineGrainedControls, DEFAULT_FINE_GRAINED_CONTROLS } from './controls'

interface LogoContextMenuState {
  isOpen: boolean
  position: { x: number; y: number }
  variant: AnimationVariant
  colorScheme: ColorScheme
  speed: AnimationSpeed
  easing: EasingType
  loopType: LoopType
  delayType: DelayType
  randomization: RandomizationType
  loopCount: number
  delayAmount: number
  isPlaying: boolean
  fineGrainedControls: FineGrainedControls
}

interface UseLogoContextMenuProps {
  initialVariant?: AnimationVariant
  initialColorScheme?: ColorScheme
  initialSpeed?: AnimationSpeed
  initialEasing?: EasingType
  initialLoopType?: LoopType
  initialDelayType?: DelayType
  initialRandomization?: RandomizationType
  initialLoopCount?: number
  initialDelayAmount?: number
  initialPlaying?: boolean
  initialFineGrainedControls?: FineGrainedControls
  onVariantChange?: (variant: AnimationVariant) => void
  onColorSchemeChange?: (scheme: ColorScheme) => void
  onSpeedChange?: (speed: AnimationSpeed) => void
  onEasingChange?: (easing: EasingType) => void
  onLoopTypeChange?: (loopType: LoopType) => void
  onDelayTypeChange?: (delayType: DelayType) => void
  onRandomizationChange?: (randomization: RandomizationType) => void
  onLoopCountChange?: (count: number) => void
  onDelayAmountChange?: (amount: number) => void
  onFineGrainedControlChange?: (key: keyof FineGrainedControls, value: number | [number, number, number, number]) => void
  onResetControls?: () => void
  onPresetApply?: (preset: AnimationPreset) => void
  onPlayToggle?: (playing: boolean) => void
  onRestart?: () => void
  onExportSVG?: () => void
  onCopyCode?: () => void
  onShowMetrics?: () => void
}

export function useLogoContextMenu({
  initialVariant = 'fadeIn',
  initialColorScheme = 'default',
  initialSpeed = 'fast',
  initialEasing = 'easeInOut',
  initialLoopType = 'none',
  initialDelayType = 'none',
  initialRandomization = 'none',
  initialLoopCount = 3,
  initialDelayAmount = 0.2,
  initialPlaying = false,
  initialFineGrainedControls = DEFAULT_FINE_GRAINED_CONTROLS,
  onVariantChange,
  onColorSchemeChange,
  onSpeedChange,
  onEasingChange,
  onLoopTypeChange,
  onDelayTypeChange,
  onRandomizationChange,
  onLoopCountChange,
  onDelayAmountChange,
  onFineGrainedControlChange,
  onResetControls,
  onPresetApply,
  onPlayToggle,
  onRestart,
  onExportSVG,
  onCopyCode,
  onShowMetrics
}: UseLogoContextMenuProps = {}) {
  const [state, setState] = useState<LogoContextMenuState>({
    isOpen: false,
    position: { x: 0, y: 0 },
    variant: initialVariant,
    colorScheme: initialColorScheme,
    speed: initialSpeed,
    easing: initialEasing,
    loopType: initialLoopType,
    delayType: initialDelayType,
    randomization: initialRandomization,
    loopCount: initialLoopCount,
    delayAmount: initialDelayAmount,
    isPlaying: initialPlaying,
    fineGrainedControls: initialFineGrainedControls
  })

  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Handle right-click to open context menu
  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    const x = event.clientX
    const y = event.clientY

    setState(prev => ({
      ...prev,
      isOpen: true,
      position: { x, y }
    }))
  }, [])

  // Close context menu
  const closeContextMenu = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: false
    }))
  }, [])

  // Handle variant change
  const handleVariantChange = useCallback((variant: AnimationVariant) => {
    setState(prev => ({ ...prev, variant }))
    onVariantChange?.(variant)
  }, [onVariantChange])

  // Handle color scheme change
  const handleColorSchemeChange = useCallback((colorScheme: ColorScheme) => {
    setState(prev => ({ ...prev, colorScheme }))
    onColorSchemeChange?.(colorScheme)
  }, [onColorSchemeChange])

  // Handle speed change
  const handleSpeedChange = useCallback((speed: AnimationSpeed) => {
    setState(prev => ({ ...prev, speed }))
    onSpeedChange?.(speed)
  }, [onSpeedChange])

  // Handle play/pause toggle
  const handlePlayToggle = useCallback(() => {
    const newPlaying = !state.isPlaying
    setState(prev => ({ ...prev, isPlaying: newPlaying }))
    onPlayToggle?.(newPlaying)
  }, [state.isPlaying, onPlayToggle])

  // Handle restart
  const handleRestart = useCallback(() => {
    setState(prev => ({ ...prev, isPlaying: true }))
    onRestart?.()
  }, [onRestart])

  // Handle export SVG
  const handleExportSVG = useCallback(() => {
    onExportSVG?.()
    closeContextMenu()
  }, [onExportSVG, closeContextMenu])

  // Handle copy code
  const handleCopyCode = useCallback(() => {
    const codeSnippet = `<AnimatedSVGLogo
  variant="${state.variant}"
  colorScheme="${state.colorScheme}"
  speed="${state.speed}"
  autoPlay={${state.isPlaying}}
  width={40}
  height={28}
/>`
    
    navigator.clipboard.writeText(codeSnippet).then(() => {
      console.log('Code copied to clipboard!')
      // You could add a toast notification here
    }).catch(err => {
      console.error('Failed to copy code:', err)
    })
    
    onCopyCode?.()
    closeContextMenu()
  }, [state, onCopyCode, closeContextMenu])

  // Handle show metrics
  const handleShowMetrics = useCallback(() => {
    onShowMetrics?.()
    closeContextMenu()
  }, [onShowMetrics, closeContextMenu])

  // Handle easing change
  const handleEasingChange = useCallback((easing: EasingType) => {
    setState(prev => ({ ...prev, easing }))
    onEasingChange?.(easing)
  }, [onEasingChange])

  // Handle loop type change
  const handleLoopTypeChange = useCallback((loopType: LoopType) => {
    setState(prev => ({ ...prev, loopType }))
    onLoopTypeChange?.(loopType)
  }, [onLoopTypeChange])

  // Handle delay type change
  const handleDelayTypeChange = useCallback((delayType: DelayType) => {
    setState(prev => ({ ...prev, delayType }))
    onDelayTypeChange?.(delayType)
  }, [onDelayTypeChange])

  // Handle randomization change
  const handleRandomizationChange = useCallback((randomization: RandomizationType) => {
    setState(prev => ({ ...prev, randomization }))
    onRandomizationChange?.(randomization)
  }, [onRandomizationChange])

  // Handle loop count change
  const handleLoopCountChange = useCallback((count: number) => {
    setState(prev => ({ ...prev, loopCount: count }))
    onLoopCountChange?.(count)
  }, [onLoopCountChange])

  // Handle delay amount change
  const handleDelayAmountChange = useCallback((amount: number) => {
    setState(prev => ({ ...prev, delayAmount: amount }))
    onDelayAmountChange?.(amount)
  }, [onDelayAmountChange])

  // Handle fine-grained control change
  const handleFineGrainedControlChange = useCallback((
    key: keyof FineGrainedControls,
    value: number | [number, number, number, number]
  ) => {
    setState(prev => ({
      ...prev,
      fineGrainedControls: {
        ...prev.fineGrainedControls,
        [key]: value
      }
    }))
    onFineGrainedControlChange?.(key, value)
  }, [onFineGrainedControlChange])

  // Handle reset controls
  const handleResetControls = useCallback(() => {
    setState(prev => ({
      ...prev,
      fineGrainedControls: DEFAULT_FINE_GRAINED_CONTROLS
    }))
    onResetControls?.()
  }, [onResetControls])

  // Handle preset apply
  const handlePresetApply = useCallback((preset: AnimationPreset) => {
    const presetConfig = ANIMATION_PRESETS[preset]
    setState(prev => ({
      ...prev,
      easing: presetConfig.easing,
      loopType: presetConfig.loopType,
      delayType: presetConfig.delayType,
      delayAmount: (presetConfig as any).delayAmount || prev.delayAmount,
      randomization: presetConfig.randomization
    }))
    onPresetApply?.(preset)
  }, [onPresetApply])

  // Update state from external changes
  const updateState = useCallback((updates: Partial<Omit<LogoContextMenuState, 'isOpen' | 'position'>>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  // Auto-close after delay (optional)
  const scheduleAutoClose = useCallback((delay: number = 5000) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      closeContextMenu()
    }, delay)
  }, [closeContextMenu])

  return {
    // State
    isOpen: state.isOpen,
    position: state.position,
    currentVariant: state.variant,
    currentColorScheme: state.colorScheme,
    currentSpeed: state.speed,
    currentEasing: state.easing,
    currentLoopType: state.loopType,
    currentDelayType: state.delayType,
    currentRandomization: state.randomization,
    loopCount: state.loopCount,
    delayAmount: state.delayAmount,
    isPlaying: state.isPlaying,
    fineGrainedControls: state.fineGrainedControls,

    // Actions
    handleContextMenu,
    closeContextMenu,
    handleVariantChange,
    handleColorSchemeChange,
    handleSpeedChange,
    handleEasingChange,
    handleLoopTypeChange,
    handleDelayTypeChange,
    handleRandomizationChange,
    handleLoopCountChange,
    handleDelayAmountChange,
    handleFineGrainedControlChange,
    handleResetControls,
    handlePresetApply,
    handlePlayToggle,
    handleRestart,
    handleExportSVG,
    handleCopyCode,
    handleShowMetrics,
    updateState,
    scheduleAutoClose,

    // Props for LogoContextMenu component
    contextMenuProps: {
      isOpen: state.isOpen,
      position: state.position,
      currentVariant: state.variant,
      currentColorScheme: state.colorScheme,
      currentSpeed: state.speed,
      currentEasing: state.easing,
      currentLoopType: state.loopType,
      currentDelayType: state.delayType,
      currentRandomization: state.randomization,
      loopCount: state.loopCount,
      delayAmount: state.delayAmount,
      isPlaying: state.isPlaying,
      fineGrainedControls: state.fineGrainedControls,
      onClose: closeContextMenu,
      onVariantChange: handleVariantChange,
      onColorSchemeChange: handleColorSchemeChange,
      onSpeedChange: handleSpeedChange,
      onEasingChange: handleEasingChange,
      onLoopTypeChange: handleLoopTypeChange,
      onDelayTypeChange: handleDelayTypeChange,
      onRandomizationChange: handleRandomizationChange,
      onLoopCountChange: handleLoopCountChange,
      onDelayAmountChange: handleDelayAmountChange,
      onFineGrainedControlChange: handleFineGrainedControlChange,
      onResetControls: handleResetControls,
      onPresetApply: handlePresetApply,
      onPlayToggle: handlePlayToggle,
      onRestart: handleRestart,
      onExportSVG: handleExportSVG,
      onCopyCode: handleCopyCode,
      onShowMetrics: handleShowMetrics
    }
  }
}

// Utility function to generate code snippet
export function generateLogoCodeSnippet(
  variant: AnimationVariant,
  colorScheme: ColorScheme,
  speed: AnimationSpeed,
  isPlaying: boolean,
  width: number = 40,
  height: number = 28
): string {
  return `<AnimatedSVGLogo
  variant="${variant}"
  colorScheme="${colorScheme}"
  speed="${speed}"
  autoPlay={${isPlaying}}
  width={${width}}
  height={${height}}
  className="your-custom-class"
/>`
}

// Utility function to export SVG
export function exportLogoAsSVG(
  svgElement: SVGElement | null,
  filename: string = 'soimagine-logo.svg'
): void {
  if (!svgElement) {
    console.error('SVG element not found')
    return
  }

  try {
    // Clone the SVG to avoid modifying the original
    const clonedSvg = svgElement.cloneNode(true) as SVGElement
    
    // Ensure proper SVG attributes
    clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg')
    clonedSvg.setAttribute('xmlns:xlink', 'http://www.w3.org/1999/xlink')
    
    // Get the SVG string
    const svgString = new XMLSerializer().serializeToString(clonedSvg)
    
    // Create blob and download
    const blob = new Blob([svgString], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export SVG:', error)
  }
}

// Development helper to log logo state
export function logLogoState(
  variant: AnimationVariant,
  colorScheme: ColorScheme,
  speed: AnimationSpeed,
  isPlaying: boolean
): void {
  if (process.env.NODE_ENV === 'development') {
    console.group('🎨 Logo State')
    console.log('Variant:', variant)
    console.log('Color Scheme:', colorScheme)
    console.log('Speed:', speed)
    console.log('Playing:', isPlaying)
    console.groupEnd()
  }
}

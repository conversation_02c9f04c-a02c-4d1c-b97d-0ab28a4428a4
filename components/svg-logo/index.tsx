// SVG Logo Components Export
export { AnimatedSVG<PERSON>ogo } from "./animated-svg-logo"
export { SVGLogoDemo } from "./svg-logo-demo"
export { SVGHeroExample } from "./svg-hero-example"
export {
  Navbar<PERSON><PERSON><PERSON>ogo,
  CompactNav<PERSON><PERSON><PERSON><PERSON><PERSON>,
  AnimatedNavbarSV<PERSON><PERSON>ogo,
  preloadNavbarSV<PERSON><PERSON><PERSON>,
  useSVGLogoPerformance
} from "./navbar-svg-logo"
export {
  NavbarWithSVGLogo,
  SimpleNavbar<PERSON>ithSVGLogo,
  DevNavbarWithSVGLogo
} from "./navbar-integration-example"
export {
  LayoutStableLogo,
  NavbarLayoutStableLogo,
  HeroLayoutStableLogo,
  CompactLayoutStableLogo,
  MonitoredLayoutStableLogo,
  useLayoutStableLogoDimensions
} from "./layout-stable-logo"
export { LogoContextMenu } from "./logo-context-menu"
export {
  useLogoContextMenu,
  generateLogoCodeSnippet,
  exportLogoAsSVG,
  logLogoState
} from "./use-logo-context-menu"
export {
  FineGrainedPanel,
  TimingControls,
  TransformControls,
  AppearanceControls,
  EasingControls,
  SliderControl,
  DEFAULT_FINE_GRAINED_CONTROLS,
  CONTROL_CONFIGS,
  CONTROL_DESCRIPTIONS
} from "./controls"
export type {
  FineGrainedControls,
  ControlSectionProps,
  SliderControlProps,
  NumberInputProps
} from "./controls"
export {
  LogoContextMenuDemo,
  LogoWithContextMenu
} from "./logo-context-menu-demo"
export { AnimationTestSuite } from "./animation-test-suite"
export { AnimationValidator } from "./animation-validator"
export { AllAnimationsDemo } from "./all-animations-demo"
export { AdvancedControlsDemo } from "./advanced-controls-demo"
export { FineGrainedControlsDemo } from "./fine-grained-controls-demo"
export {
  EditableLogo,
  EditableNavbarLogo,
  EditableCompactLogo,
  EditableHeroLogo,
  EditableSimpleLogo,
  useEditableLogo
} from "./editable-logo"
export {
  CreativeStudio,
  ColorStudio,
  PathEditor,
  VariationGallery,
  EnhancedEditableLogo,
  EnhancedEditableNavbarLogo,
  EnhancedEditableHeroLogo,
  DEFAULT_LOGO_CUSTOMIZATION
} from "./creative-studio"
export type {
  LogoVariation,
  LogoCustomization,
  ColorCustomization,
  PathCustomization,
  GradientDefinition,
  PatternDefinition,
  TextCustomization,
  TextElement,
  EffectsCustomization,
  LayoutCustomization,
  AnimationCustomization,
  AnimationStep
} from "./creative-studio"
export { CreativeStudioDemo } from "./creative-studio-demo"
export { LogoEditTest, QuickLogoEditTest } from "./logo-edit-test"
export {
  SimpleEditableLogo,
  FallbackLogo,
  LogoWithFallback,
  DebugLogo
} from "./simple-editable-logo"

// Animation system exports
export * from "./animations"
export * from "./animations/advanced-controls"

// Loading system exports
export * from "./loading"

export type {
  AnimatedSVGLogoProps,
  ColorScheme
} from "./animated-svg-logo"

// Re-export for convenience
export { AnimatedSVGLogo as SVGLogo } from "./animated-svg-logo"

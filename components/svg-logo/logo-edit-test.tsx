"use client"

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { MousePointer2, Settings, Zap } from 'lucide-react'
import { 
  Editable<PERSON>ogo, 
  EditableNav<PERSON><PERSON>ogo, 
  EditableCompact<PERSON>ogo, 
  EditableHeroLogo,
  EditableSimpleLogo 
} from './editable-logo'

export function LogoEditTest() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Logo Edit Test
          </CardTitle>
          <CardDescription>
            Test the right-click context menu functionality on different logo variants.
            This feature is only available in development mode.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Navbar Logo Test */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Zap className="w-4 h-4" />
              Navbar Logo (Layout-Stable)
            </h3>
            <div className="flex items-center gap-4 p-4 border-2 border-dashed border-muted rounded-lg bg-muted/20">
              <EditableNavbarLogo
                width={40}
                height={28}
                initialVariant="fadeIn"
                onVariantChange={(variant) => console.log('Navbar variant changed:', variant)}
                onColorSchemeChange={(scheme) => console.log('Navbar color changed:', scheme)}
                onSpeedChange={(speed) => console.log('Navbar speed changed:', speed)}
              />
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MousePointer2 className="w-4 h-4" />
                Right-click to edit
                <Badge variant="secondary">Layout Stable</Badge>
              </div>
            </div>
          </div>

          {/* Compact Logo Test */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Compact Logo (Mobile)</h3>
            <div className="flex items-center gap-4 p-4 border-2 border-dashed border-muted rounded-lg bg-muted/20">
              <EditableCompactLogo
                initialVariant="pathDraw"
                onVariantChange={(variant) => console.log('Compact variant changed:', variant)}
              />
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MousePointer2 className="w-4 h-4" />
                Right-click to edit
                <Badge variant="secondary">32x22</Badge>
              </div>
            </div>
          </div>

          {/* Hero Logo Test */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Hero Logo (Dramatic)</h3>
            <div className="flex items-center gap-4 p-4 border-2 border-dashed border-muted rounded-lg bg-muted/20">
              <EditableHeroLogo
                width={80}
                height={56}
                initialVariant="combined"
                onVariantChange={(variant) => console.log('Hero variant changed:', variant)}
              />
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MousePointer2 className="w-4 h-4" />
                Right-click to edit
                <Badge variant="secondary">Layout Shifts OK</Badge>
              </div>
            </div>
          </div>

          {/* Simple Logo Test */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Simple Logo (Basic)</h3>
            <div className="flex items-center gap-4 p-4 border-2 border-dashed border-muted rounded-lg bg-muted/20">
              <EditableSimpleLogo
                width={60}
                height={42}
                initialVariant="glow"
                initialColorScheme="rainbow"
                onVariantChange={(variant) => console.log('Simple variant changed:', variant)}
                onColorSchemeChange={(scheme) => console.log('Simple color changed:', scheme)}
              />
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MousePointer2 className="w-4 h-4" />
                Right-click to edit
                <Badge variant="secondary">No Constraints</Badge>
              </div>
            </div>
          </div>

          {/* Custom Editable Logo */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Custom Editable Logo</h3>
            <div className="flex items-center gap-4 p-4 border-2 border-dashed border-muted rounded-lg bg-muted/20">
              <EditableLogo
                width={50}
                height={35}
                initialVariant="breathe"
                initialColorScheme="brand"
                initialSpeed="slow"
                preventLayoutShifts={true}
                enableEditing={true}
                onVariantChange={(variant) => console.log('Custom variant changed:', variant)}
                onColorSchemeChange={(scheme) => console.log('Custom color changed:', scheme)}
                onSpeedChange={(speed) => console.log('Custom speed changed:', speed)}
              />
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MousePointer2 className="w-4 h-4" />
                Right-click to edit
                <Badge variant="secondary">Custom Settings</Badge>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">How to Test</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Context Menu Features</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Right-click any logo above</li>
                    <li>• Choose animation variants</li>
                    <li>• Change color schemes</li>
                    <li>• Adjust animation speed</li>
                    <li>• Play/pause/restart controls</li>
                    <li>• Copy code snippets</li>
                    <li>• Export SVG files</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Development Only</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Only works in development mode</li>
                    <li>• Check browser console for logs</li>
                    <li>• Blue dot indicates editable logos</li>
                    <li>• Changes apply immediately</li>
                    <li>• Settings persist during session</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-start gap-3">
                  <Settings className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                  <div className="space-y-1">
                    <h5 className="font-medium text-blue-900 dark:text-blue-100">Development Feature</h5>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      The context menu editing is only available when <code>NODE_ENV=development</code>. 
                      In production, right-clicking shows the standard browser context menu.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-start gap-3">
                  <Zap className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
                  <div className="space-y-1">
                    <h5 className="font-medium text-green-900 dark:text-green-100">Expected Behavior</h5>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      Right-clicking should open a context menu with animation controls, color options, 
                      and developer tools. Changes should apply immediately to the logo.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}

// Simple test component for quick verification
export function QuickLogoEditTest() {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold">Quick Logo Edit Test</h2>
      <p className="text-muted-foreground">Right-click the logo below to test editing:</p>
      
      <div className="flex items-center gap-4 p-6 border rounded-lg">
        <EditableNavbarLogo />
        <span className="text-sm text-muted-foreground">← Right-click to edit</span>
      </div>
      
      <p className="text-xs text-muted-foreground">
        Check the browser console for change logs. Only works in development mode.
      </p>
    </div>
  )
}

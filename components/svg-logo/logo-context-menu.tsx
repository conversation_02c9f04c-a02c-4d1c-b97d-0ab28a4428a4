"use client"

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Setting<PERSON>,
  <PERSON>lette,
  Zap,
  <PERSON>otateCcw,
  Play,
  Pause,
  Download,
  Copy,
  Eye,
  Code,
  Sliders,
  X,
  ChevronRight
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { AnimationVariant, ColorScheme, AnimationSpeed, EasingType, LoopType, DelayType, RandomizationType } from './animations'
import { CONTROL_DESCRIPTIONS, ANIMATION_PRESETS, AnimationPreset } from './animations/advanced-controls'
import { FineGrainedPanel, FineGrainedControls } from './controls'

interface LogoContextMenuProps {
  isOpen: boolean
  position: { x: number; y: number }
  onClose: () => void
  currentVariant: AnimationVariant
  currentColorScheme: ColorScheme
  currentSpeed: AnimationSpeed
  currentEasing: EasingType
  currentLoopType: LoopType
  currentDelayType: DelayType
  currentRandomization: RandomizationType
  loopCount: number
  delayAmount: number
  isPlaying: boolean
  fineGrainedControls: FineGrainedControls
  onVariantChange: (variant: AnimationVariant) => void
  onColorSchemeChange: (scheme: ColorScheme) => void
  onSpeedChange: (speed: AnimationSpeed) => void
  onEasingChange: (easing: EasingType) => void
  onLoopTypeChange: (loopType: LoopType) => void
  onDelayTypeChange: (delayType: DelayType) => void
  onRandomizationChange: (randomization: RandomizationType) => void
  onLoopCountChange: (count: number) => void
  onDelayAmountChange: (amount: number) => void
  onFineGrainedControlChange: (key: keyof FineGrainedControls, value: number | [number, number, number, number]) => void
  onPresetApply: (preset: AnimationPreset) => void
  onPlayToggle: () => void
  onRestart: () => void
  onResetControls: () => void
  onExportSVG?: () => void
  onCopyCode?: () => void
  onShowMetrics?: () => void
}

interface MenuSection {
  title: string
  icon: React.ReactNode
  items: Array<{
    label: string
    value: string
    description?: string
    badge?: string
  }>
}

export function LogoContextMenu({
  isOpen,
  position,
  onClose,
  currentVariant,
  currentColorScheme,
  currentSpeed,
  currentEasing,
  currentLoopType,
  currentDelayType,
  currentRandomization,
  loopCount,
  delayAmount,
  isPlaying,
  fineGrainedControls,
  onVariantChange,
  onColorSchemeChange,
  onSpeedChange,
  onEasingChange,
  onLoopTypeChange,
  onDelayTypeChange,
  onRandomizationChange,
  onLoopCountChange,
  onDelayAmountChange,
  onFineGrainedControlChange,
  onPresetApply,
  onPlayToggle,
  onRestart,
  onResetControls,
  onExportSVG,
  onCopyCode,
  onShowMetrics
}: LogoContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null)
  const sidePanelRef = useRef<HTMLDivElement>(null)
  const [activeSection, setActiveSection] = useState<string | null>(null)
  const [showSidePanel, setShowSidePanel] = useState(false)

  // Close menu on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        sidePanelRef.current &&
        !sidePanelRef.current.contains(event.target as Node)
      ) {
        onClose()
        setShowSidePanel(false)
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
      // Prevent context menu from appearing
      document.addEventListener('contextmenu', (e) => e.preventDefault())
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
      document.removeEventListener('contextmenu', (e) => e.preventDefault())
    }
  }, [isOpen, onClose])

  // Adjust position to keep menu in viewport
  const getAdjustedPosition = () => {
    if (!menuRef.current) return position

    const rect = menuRef.current.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let { x, y } = position

    // Adjust horizontal position
    if (x + rect.width > viewportWidth) {
      x = viewportWidth - rect.width - 10
    }

    // Adjust vertical position
    if (y + rect.height > viewportHeight) {
      y = viewportHeight - rect.height - 10
    }

    return { x: Math.max(10, x), y: Math.max(10, y) }
  }

  const animationVariants: MenuSection = {
    title: 'Animation',
    icon: <Zap className="w-4 h-4" />,
    items: [
      { label: 'Path Draw', value: 'pathDraw', description: 'Progressive path drawing' },
      { label: 'Fade In', value: 'fadeIn', description: 'Simple opacity fade', badge: 'Stable' },
      { label: 'Staggered', value: 'staggeredReveal', description: 'Staggered element reveal' },
      { label: 'Color Wave', value: 'colorWave', description: 'Animated color transitions' },
      { label: 'Morphing', value: 'morphing', description: 'Scale and rotation effects' },
      { label: 'Glow', value: 'glow', description: 'Pulsing glow animation' },
      { label: 'Rotate', value: 'rotate', description: 'Spinning rotation' },
      { label: 'Breathe', value: 'breathe', description: 'Gentle breathing motion' },
      { label: 'Combined', value: 'combined', description: 'Multiple effects', badge: 'Pro' }
    ]
  }

  const colorSchemes: MenuSection = {
    title: 'Colors',
    icon: <Palette className="w-4 h-4" />,
    items: [
      { label: 'Default', value: 'default', description: 'Original brand colors' },
      { label: 'Brand', value: 'brand', description: 'Primary brand palette' },
      { label: 'Monochrome', value: 'monochrome', description: 'Grayscale theme' },
      { label: 'Rainbow', value: 'rainbow', description: 'Full spectrum colors' }
    ]
  }

  const speedOptions: MenuSection = {
    title: 'Speed',
    icon: <Sliders className="w-4 h-4" />,
    items: [
      { label: 'Slow', value: 'slow', description: '4s duration' },
      { label: 'Normal', value: 'normal', description: '2.5s duration' },
      { label: 'Fast', value: 'fast', description: '1.5s duration', badge: 'Navbar' }
    ]
  }

  const adjustedPosition = getAdjustedPosition()

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/20"
            onClick={onClose}
          />

          {/* Context Menu */}
          <motion.div
            ref={menuRef}
            initial={{ opacity: 0, scale: 0.95, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 10 }}
            transition={{ duration: 0.15 }}
            className="fixed z-900 bg-background border rounded-lg shadow-lg min-w-[280px] max-w-[320px]"
            style={{
              left: adjustedPosition.x,
              top: adjustedPosition.y,
            }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-3 border-b">
              <div className="flex items-center gap-2">
                <Settings className="w-4 h-4 text-primary" />
                <span className="font-semibold text-sm">Logo Editor</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-6 w-6 p-0"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>

            {/* Quick Actions */}
            <div className="p-3 border-b">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-xs font-medium text-muted-foreground">Quick Actions</span>
              </div>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onPlayToggle}
                  className="flex-1"
                >
                  {isPlaying ? <Pause className="w-3 h-3 mr-1" /> : <Play className="w-3 h-3 mr-1" />}
                  {isPlaying ? 'Pause' : 'Play'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRestart}
                  className="flex-1"
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Restart
                </Button>
              </div>
            </div>

            {/* Animation Section */}
            <div className="p-3 border-b">
              <div className="flex items-center gap-2 mb-2">
                {animationVariants.icon}
                <span className="text-xs font-medium text-muted-foreground">{animationVariants.title}</span>
                <Badge variant="secondary" className="text-xs">
                  {currentVariant}
                </Badge>
              </div>
              <div className="grid grid-cols-3 gap-1">
                {animationVariants.items.slice(0, 6).map((item) => (
                  <Button
                    key={item.value}
                    variant={currentVariant === item.value ? "default" : "ghost"}
                    size="sm"
                    onClick={() => onVariantChange(item.value as AnimationVariant)}
                    className="text-xs h-8 relative"
                  >
                    {item.label}
                    {item.badge && (
                      <Badge className="absolute -top-1 -right-1 text-xs px-1 py-0 h-4">
                        {item.badge}
                      </Badge>
                    )}
                  </Button>
                ))}
              </div>
              {animationVariants.items.length > 6 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveSection(activeSection === 'animations' ? null : 'animations')}
                  className="w-full mt-1 text-xs"
                >
                  {activeSection === 'animations' ? 'Show Less' : `+${animationVariants.items.length - 6} More`}
                </Button>
              )}
              
              {/* Expanded animations */}
              <AnimatePresence>
                {activeSection === 'animations' && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="grid grid-cols-3 gap-1 mt-2"
                  >
                    {animationVariants.items.slice(6).map((item) => (
                      <Button
                        key={item.value}
                        variant={currentVariant === item.value ? "default" : "ghost"}
                        size="sm"
                        onClick={() => onVariantChange(item.value as AnimationVariant)}
                        className="text-xs h-8"
                      >
                        {item.label}
                      </Button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Color & Speed */}
            <div className="p-3 border-b">
              <div className="grid grid-cols-2 gap-3">
                {/* Colors */}
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    {colorSchemes.icon}
                    <span className="text-xs font-medium text-muted-foreground">Colors</span>
                  </div>
                  <div className="space-y-1">
                    {colorSchemes.items.map((item) => (
                      <Button
                        key={item.value}
                        variant={currentColorScheme === item.value ? "default" : "ghost"}
                        size="sm"
                        onClick={() => onColorSchemeChange(item.value as ColorScheme)}
                        className="w-full justify-start text-xs h-7"
                      >
                        {item.label}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Speed */}
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    {speedOptions.icon}
                    <span className="text-xs font-medium text-muted-foreground">Speed</span>
                  </div>
                  <div className="space-y-1">
                    {speedOptions.items.map((item) => (
                      <Button
                        key={item.value}
                        variant={currentSpeed === item.value ? "default" : "ghost"}
                        size="sm"
                        onClick={() => onSpeedChange(item.value as AnimationSpeed)}
                        className="w-full justify-start text-xs h-7"
                      >
                        {item.label}
                        {item.badge && (
                          <Badge variant="secondary" className="ml-auto text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Advanced Controls */}
            <div className="p-3 border-b">
              <div className="flex items-center gap-2 mb-2">
                <Sliders className="w-4 h-4" />
                <span className="text-xs font-medium text-muted-foreground">Advanced</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSidePanel(!showSidePanel)}
                  className="ml-auto h-6 w-6 p-0"
                  title="Fine-grained Controls"
                >
                  <ChevronRight className="w-3 h-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveSection(activeSection === 'advanced' ? null : 'advanced')}
                  className="h-5 w-5 p-0"
                >
                  {activeSection === 'advanced' ? '−' : '+'}
                </Button>
              </div>

              <AnimatePresence>
                {activeSection === 'advanced' && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-3"
                  >
                    {/* Easing Controls */}
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs font-medium">Easing</span>
                        <Badge variant="outline" className="text-xs">
                          {currentEasing}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-1">
                        {(['easeIn', 'easeOut', 'easeInOut', 'bounceOut'] as EasingType[]).map((easing) => (
                          <Button
                            key={easing}
                            variant={currentEasing === easing ? "default" : "ghost"}
                            size="sm"
                            onClick={() => onEasingChange(easing)}
                            className="text-xs h-6"
                            title={CONTROL_DESCRIPTIONS.easing[easing]}
                          >
                            {easing}
                          </Button>
                        ))}
                      </div>
                    </div>

                    {/* Loop Controls */}
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs font-medium">Loop</span>
                        <Badge variant="outline" className="text-xs">
                          {currentLoopType}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-1">
                        {(['none', 'loop', 'reverse', 'pingPong'] as LoopType[]).map((loopType) => (
                          <Button
                            key={loopType}
                            variant={currentLoopType === loopType ? "default" : "ghost"}
                            size="sm"
                            onClick={() => onLoopTypeChange(loopType)}
                            className="text-xs h-6"
                            title={CONTROL_DESCRIPTIONS.loopType[loopType]}
                          >
                            {loopType}
                          </Button>
                        ))}
                      </div>
                      {currentLoopType === 'count' && (
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs">Count:</span>
                          <input
                            type="number"
                            value={loopCount}
                            onChange={(e) => onLoopCountChange(parseInt(e.target.value) || 1)}
                            className="w-12 h-6 text-xs border rounded px-1"
                            min="1"
                            max="10"
                          />
                        </div>
                      )}
                    </div>

                    {/* Delay Controls */}
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs font-medium">Delay</span>
                        <Badge variant="outline" className="text-xs">
                          {currentDelayType}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-1">
                        {(['none', 'fixed', 'staggered', 'random'] as DelayType[]).map((delayType) => (
                          <Button
                            key={delayType}
                            variant={currentDelayType === delayType ? "default" : "ghost"}
                            size="sm"
                            onClick={() => onDelayTypeChange(delayType)}
                            className="text-xs h-6"
                            title={CONTROL_DESCRIPTIONS.delayType[delayType]}
                          >
                            {delayType}
                          </Button>
                        ))}
                      </div>
                      {currentDelayType !== 'none' && (
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs">Amount:</span>
                          <input
                            type="number"
                            value={delayAmount}
                            onChange={(e) => onDelayAmountChange(parseFloat(e.target.value) || 0)}
                            className="w-16 h-6 text-xs border rounded px-1"
                            min="0"
                            max="2"
                            step="0.1"
                          />
                          <span className="text-xs text-muted-foreground">s</span>
                        </div>
                      )}
                    </div>

                    {/* Randomization Controls */}
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs font-medium">Random</span>
                        <Badge variant="outline" className="text-xs">
                          {currentRandomization}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-1">
                        {(['none', 'timing', 'easing', 'full'] as RandomizationType[]).map((randomization) => (
                          <Button
                            key={randomization}
                            variant={currentRandomization === randomization ? "default" : "ghost"}
                            size="sm"
                            onClick={() => onRandomizationChange(randomization)}
                            className="text-xs h-6"
                            title={CONTROL_DESCRIPTIONS.randomization[randomization]}
                          >
                            {randomization}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Presets */}
            <div className="p-3 border-b">
              <div className="flex items-center gap-2 mb-2">
                <Settings className="w-4 h-4" />
                <span className="text-xs font-medium text-muted-foreground">Presets</span>
              </div>
              <div className="grid grid-cols-2 gap-1">
                {(Object.keys(ANIMATION_PRESETS) as AnimationPreset[]).map((preset) => (
                  <Button
                    key={preset}
                    variant="outline"
                    size="sm"
                    onClick={() => onPresetApply(preset)}
                    className="text-xs h-6 capitalize"
                  >
                    {preset}
                  </Button>
                ))}
              </div>
            </div>

            {/* Developer Actions */}
            {process.env.NODE_ENV === 'development' && (
              <div className="p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Code className="w-4 h-4" />
                  <span className="text-xs font-medium text-muted-foreground">Developer</span>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  {onShowMetrics && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onShowMetrics}
                      className="text-xs h-7"
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      Metrics
                    </Button>
                  )}
                  {onCopyCode && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onCopyCode}
                      className="text-xs h-7"
                    >
                      <Copy className="w-3 h-3 mr-1" />
                      Copy Code
                    </Button>
                  )}
                  {onExportSVG && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onExportSVG}
                      className="text-xs h-7 col-span-2"
                    >
                      <Download className="w-3 h-3 mr-1" />
                      Export SVG
                    </Button>
                  )}
                </div>
              </div>
            )}
          </motion.div>

          {/* Fine-Grained Controls Panel */}
          <FineGrainedPanel
            isOpen={showSidePanel}
            onClose={() => setShowSidePanel(false)}
            controls={fineGrainedControls}
            onControlChange={onFineGrainedControlChange}
            onReset={onResetControls}
            position={position}
          />
        </>
      )}
    </AnimatePresence>
  )
}

"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { <PERSON><PERSON><PERSON>, Settings, Zap, MousePointer2 } from 'lucide-react'
import { LogoWithFallback } from './simple-editable-logo'
import { 
  EasingType, 
  LoopType, 
  DelayType, 
  RandomizationType,
  AnimationPreset,
  CONTROL_DESCRIPTIONS,
  ANIMATION_PRESETS
} from './animations/advanced-controls'

export function AdvancedControlsDemo() {
  const [currentEasing, setCurrentEasing] = useState<EasingType>('easeInOut')
  const [currentLoopType, setCurrentLoopType] = useState<LoopType>('none')
  const [currentDelayType, setCurrentDelayType] = useState<DelayType>('none')
  const [currentRandomization, setCurrentRandomization] = useState<RandomizationType>('none')
  const [loopCount, setLoopCount] = useState(3)
  const [delayAmount, setDelayAmount] = useState(0.2)

  const handlePresetApply = (preset: AnimationPreset) => {
    const presetConfig = ANIMATION_PRESETS[preset]
    setCurrentEasing(presetConfig.easing)
    setCurrentLoopType(presetConfig.loopType)
    setCurrentDelayType(presetConfig.delayType)
    setDelayAmount(presetConfig.delayAmount || 0.2)
    setCurrentRandomization(presetConfig.randomization)
    console.log(`Applied preset: ${preset}`, presetConfig)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sliders className="w-5 h-5" />
            Advanced Animation Controls Demo
          </CardTitle>
          <CardDescription>
            Comprehensive demonstration of easing, loops, delays, and randomization controls.
            Right-click the logo to access these controls in the context menu.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Demo Logo with Advanced Controls */}
          <div className="flex flex-col items-center space-y-4">
            <div className="p-8 border-2 border-dashed border-muted rounded-lg bg-muted/20">
              <LogoWithFallback
                width={80}
                height={56}
                enableEditing={true}
                className="transition-all duration-300"
              />
            </div>
            
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <MousePointer2 className="w-4 h-4" />
              Right-click the logo above to access advanced controls
            </div>
          </div>

          {/* Control Sections */}
          <Tabs defaultValue="easing" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="easing">Easing</TabsTrigger>
              <TabsTrigger value="loops">Loops</TabsTrigger>
              <TabsTrigger value="delays">Delays</TabsTrigger>
              <TabsTrigger value="random">Random</TabsTrigger>
              <TabsTrigger value="presets">Presets</TabsTrigger>
            </TabsList>

            {/* Easing Controls */}
            <TabsContent value="easing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Easing Functions</CardTitle>
                  <CardDescription>
                    Control the acceleration and deceleration of animations
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {(Object.keys(CONTROL_DESCRIPTIONS.easing) as EasingType[]).map((easing) => (
                      <Button
                        key={easing}
                        variant={currentEasing === easing ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentEasing(easing)}
                        className="flex flex-col h-auto p-3"
                        title={CONTROL_DESCRIPTIONS.easing[easing]}
                      >
                        <span className="font-medium">{easing}</span>
                        <span className="text-xs text-muted-foreground">
                          {CONTROL_DESCRIPTIONS.easing[easing]}
                        </span>
                      </Button>
                    ))}
                  </div>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">
                      <strong>Current:</strong> {currentEasing} - {CONTROL_DESCRIPTIONS.easing[currentEasing]}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Loop Controls */}
            <TabsContent value="loops" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Loop Types</CardTitle>
                  <CardDescription>
                    Control how animations repeat and cycle
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {(Object.keys(CONTROL_DESCRIPTIONS.loopType) as LoopType[]).map((loopType) => (
                      <Button
                        key={loopType}
                        variant={currentLoopType === loopType ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentLoopType(loopType)}
                        className="flex flex-col h-auto p-3"
                        title={CONTROL_DESCRIPTIONS.loopType[loopType]}
                      >
                        <span className="font-medium">{loopType}</span>
                        <span className="text-xs text-muted-foreground">
                          {CONTROL_DESCRIPTIONS.loopType[loopType]}
                        </span>
                      </Button>
                    ))}
                  </div>
                  
                  {currentLoopType === 'count' && (
                    <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                      <label className="text-sm font-medium">Loop Count:</label>
                      <input
                        type="number"
                        value={loopCount}
                        onChange={(e) => setLoopCount(parseInt(e.target.value) || 1)}
                        className="w-20 h-8 text-sm border rounded px-2"
                        min="1"
                        max="10"
                      />
                      <span className="text-sm text-muted-foreground">times</span>
                    </div>
                  )}
                  
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">
                      <strong>Current:</strong> {currentLoopType} - {CONTROL_DESCRIPTIONS.loopType[currentLoopType]}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Delay Controls */}
            <TabsContent value="delays" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Delay Types</CardTitle>
                  <CardDescription>
                    Control timing and sequencing of animation elements
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {(Object.keys(CONTROL_DESCRIPTIONS.delayType) as DelayType[]).map((delayType) => (
                      <Button
                        key={delayType}
                        variant={currentDelayType === delayType ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentDelayType(delayType)}
                        className="flex flex-col h-auto p-3"
                        title={CONTROL_DESCRIPTIONS.delayType[delayType]}
                      >
                        <span className="font-medium">{delayType}</span>
                        <span className="text-xs text-muted-foreground">
                          {CONTROL_DESCRIPTIONS.delayType[delayType]}
                        </span>
                      </Button>
                    ))}
                  </div>
                  
                  {currentDelayType !== 'none' && (
                    <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                      <label className="text-sm font-medium">Delay Amount:</label>
                      <input
                        type="number"
                        value={delayAmount}
                        onChange={(e) => setDelayAmount(parseFloat(e.target.value) || 0)}
                        className="w-20 h-8 text-sm border rounded px-2"
                        min="0"
                        max="2"
                        step="0.1"
                      />
                      <span className="text-sm text-muted-foreground">seconds</span>
                    </div>
                  )}
                  
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">
                      <strong>Current:</strong> {currentDelayType} - {CONTROL_DESCRIPTIONS.delayType[currentDelayType]}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Randomization Controls */}
            <TabsContent value="random" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Randomization</CardTitle>
                  <CardDescription>
                    Add organic variation to animations
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {(Object.keys(CONTROL_DESCRIPTIONS.randomization) as RandomizationType[]).map((randomization) => (
                      <Button
                        key={randomization}
                        variant={currentRandomization === randomization ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentRandomization(randomization)}
                        className="flex flex-col h-auto p-3"
                        title={CONTROL_DESCRIPTIONS.randomization[randomization]}
                      >
                        <span className="font-medium">{randomization}</span>
                        <span className="text-xs text-muted-foreground">
                          {CONTROL_DESCRIPTIONS.randomization[randomization]}
                        </span>
                      </Button>
                    ))}
                  </div>
                  
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">
                      <strong>Current:</strong> {currentRandomization} - {CONTROL_DESCRIPTIONS.randomization[currentRandomization]}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Presets */}
            <TabsContent value="presets" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Animation Presets</CardTitle>
                  <CardDescription>
                    Pre-configured combinations for common animation styles
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {(Object.keys(ANIMATION_PRESETS) as AnimationPreset[]).map((preset) => (
                      <Button
                        key={preset}
                        variant="outline"
                        size="sm"
                        onClick={() => handlePresetApply(preset)}
                        className="flex flex-col h-auto p-3 capitalize"
                      >
                        <span className="font-medium">{preset}</span>
                        <span className="text-xs text-muted-foreground">
                          {ANIMATION_PRESETS[preset].easing} + {ANIMATION_PRESETS[preset].loopType}
                        </span>
                      </Button>
                    ))}
                  </div>
                  
                  <div className="p-3 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Current Settings:</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>Easing: <Badge variant="secondary">{currentEasing}</Badge></div>
                      <div>Loop: <Badge variant="secondary">{currentLoopType}</Badge></div>
                      <div>Delay: <Badge variant="secondary">{currentDelayType}</Badge></div>
                      <div>Random: <Badge variant="secondary">{currentRandomization}</Badge></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Usage Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="w-5 h-5" />
                How to Use Advanced Controls
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Context Menu Access</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Right-click any logo in development mode</li>
                    <li>• Click "Advanced" section to expand</li>
                    <li>• Adjust easing, loops, delays, and randomization</li>
                    <li>• Use presets for quick configurations</li>
                    <li>• Changes apply immediately</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Best Practices</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Use "smooth" preset for professional look</li>
                    <li>• Use "bouncy" for playful interactions</li>
                    <li>• Use "organic" for natural movement</li>
                    <li>• Use "chaotic" for creative emphasis</li>
                    <li>• Test on different devices and speeds</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON>les, 
  Palette, 
  Wand2, 
  Gallery, 
  Download, 
  Share2,
  Star,
  Heart,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { 
  EnhancedEditableLogo,
  EnhancedEditableNavbarLogo,
  EnhancedEditableHeroLogo,
  LogoVariation,
  LogoCustomization,
  DEFAULT_LOGO_CUSTOMIZATION
} from './creative-studio'

export function CreativeStudioDemo() {
  const [savedVariations, setSavedVariations] = useState<LogoVariation[]>([])
  const [currentCustomization, setCurrentCustomization] = useState<LogoCustomization>(DEFAULT_LOGO_CUSTOMIZATION)

  const handleVariationSave = (variation: LogoVariation) => {
    setSavedVariations(prev => [...prev, variation])
    console.log('Variation saved:', variation)
  }

  const handleVariationLoad = (customization: LogoCustomization) => {
    setCurrentCustomization(customization)
    console.log('Variation loaded:', customization)
  }

  const handleExport = (customization: LogoCustomization, format: 'svg' | 'png' | 'json') => {
    console.log('Exporting:', { customization, format })
    // In a real implementation, this would trigger the actual export
    alert(`Exported as ${format.toUpperCase()}!`)
  }

  const features = [
    {
      icon: Palette,
      title: 'Advanced Color Studio',
      description: 'Professional color tools with harmony generation, trending palettes, and real-time effects',
      highlights: ['Color Harmony Generator', 'Trending Palettes', 'HSB Adjustments', 'Gradient Support']
    },
    {
      icon: Wand2,
      title: 'Path Editor',
      description: 'Direct SVG path manipulation with transform controls, stroke customization, and fill options',
      highlights: ['Transform Controls', 'Stroke Customization', 'Fill Options', 'Real-time Preview']
    },
    {
      icon: Gallery,
      title: 'Variation Gallery',
      description: 'Save, organize, and share your custom logo variations with community features',
      highlights: ['Save Variations', 'Tag System', 'Search & Filter', 'Community Sharing']
    },
    {
      icon: Sparkles,
      title: 'Creative Effects',
      description: 'Advanced effects including shadows, glows, 3D transforms, and particle systems',
      highlights: ['Drop Shadows', 'Glow Effects', '3D Transforms', 'Particle Systems']
    }
  ]

  const useCases = [
    {
      title: 'Navbar Logo',
      description: 'Compact, fast-loading logo for navigation',
      component: (
        <EnhancedEditableNavbarLogo
          onCustomizationChange={setCurrentCustomization}
          savedVariations={savedVariations}
          onVariationSave={handleVariationSave}
          onVariationLoad={handleVariationLoad}
          onExport={handleExport}
        />
      )
    },
    {
      title: 'Hero Section',
      description: 'Large, dramatic logo for hero sections',
      component: (
        <EnhancedEditableHeroLogo
          onCustomizationChange={setCurrentCustomization}
          savedVariations={savedVariations}
          onVariationSave={handleVariationSave}
          onVariationLoad={handleVariationLoad}
          onExport={handleExport}
        />
      )
    },
    {
      title: 'Standard Logo',
      description: 'Versatile logo for general use',
      component: (
        <EnhancedEditableLogo
          width={250}
          height={160}
          onCustomizationChange={setCurrentCustomization}
          savedVariations={savedVariations}
          onVariationSave={handleVariationSave}
          onVariationLoad={handleVariationLoad}
          onExport={handleExport}
        />
      )
    }
  ]

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center gap-3"
        >
          <div className="relative">
            <Sparkles className="w-8 h-8 text-purple-500" />
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0"
            >
              <Zap className="w-8 h-8 text-blue-500 opacity-50" />
            </motion.div>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Creative Studio
          </h1>
          <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
            Enhanced
          </Badge>
        </motion.div>
        
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Create stunning variations of the Soimagine logo with professional-grade tools. 
          Advanced color controls, path editing, effects, and variation management.
        </p>

        <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-500" />
            <span>Professional Tools</span>
          </div>
          <div className="flex items-center gap-1">
            <Heart className="w-4 h-4 text-red-500" />
            <span>User-Friendly</span>
          </div>
          <div className="flex items-center gap-1">
            <Zap className="w-4 h-4 text-blue-500" />
            <span>Real-time Preview</span>
          </div>
        </div>
      </div>

      {/* Features Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5" />
            Creative Features
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="space-y-3"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <feature.icon className="w-5 h-5 text-primary" />
                  </div>
                  <h3 className="font-semibold">{feature.title}</h3>
                </div>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
                <div className="flex flex-wrap gap-1">
                  {feature.highlights.map((highlight, i) => (
                    <Badge key={i} variant="outline" className="text-xs">
                      {highlight}
                    </Badge>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Interactive Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="w-5 h-5" />
            Interactive Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="navbar" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="navbar">Navbar</TabsTrigger>
              <TabsTrigger value="hero">Hero</TabsTrigger>
              <TabsTrigger value="standard">Standard</TabsTrigger>
            </TabsList>

            {useCases.map((useCase, index) => (
              <TabsContent key={index} value={useCase.title.toLowerCase().replace(' ', '')} className="space-y-4">
                <div className="text-center space-y-2">
                  <h3 className="text-lg font-semibold">{useCase.title}</h3>
                  <p className="text-sm text-muted-foreground">{useCase.description}</p>
                </div>
                
                <div className="flex justify-center p-8 bg-muted/50 rounded-lg">
                  {useCase.component}
                </div>

                <div className="text-center space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Right-click the logo for context menu • Hover for quick actions
                  </p>
                  <div className="flex justify-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      <Palette className="w-3 h-3 mr-1" />
                      Color Studio
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      <Wand2 className="w-3 h-3 mr-1" />
                      Path Editor
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      <Gallery className="w-3 h-3 mr-1" />
                      Variations
                    </Badge>
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{savedVariations.length}</div>
            <div className="text-sm text-muted-foreground">Saved Variations</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">50+</div>
            <div className="text-sm text-muted-foreground">Color Palettes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">∞</div>
            <div className="text-sm text-muted-foreground">Possibilities</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">100%</div>
            <div className="text-sm text-muted-foreground">Creative Freedom</div>
          </CardContent>
        </Card>
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold flex items-center gap-2">
                <span className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs">1</span>
                Right-click any logo
              </h4>
              <p className="text-sm text-muted-foreground">
                Access the enhanced context menu with basic animation and color controls.
              </p>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold flex items-center gap-2">
                <span className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs">2</span>
                Use quick actions
              </h4>
              <p className="text-sm text-muted-foreground">
                Hover over logos to reveal quick action buttons for instant access to tools.
              </p>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold flex items-center gap-2">
                <span className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs">3</span>
                Open Creative Studio
              </h4>
              <p className="text-sm text-muted-foreground">
                Click the sparkles icon to access the full Creative Studio with advanced tools.
              </p>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold flex items-center gap-2">
                <span className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs">4</span>
                Save & share
              </h4>
              <p className="text-sm text-muted-foreground">
                Save your variations and access them through the Variation Gallery.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

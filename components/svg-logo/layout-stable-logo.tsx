"use client"

import React, { forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { AnimatedSVGLogo, AnimatedSVGLogoProps } from './animated-svg-logo'
import { NavbarSVGLogo } from './navbar-svg-logo'
import { AnimationVariant } from './animations'

interface LayoutStableLogoProps extends Omit<AnimatedSVGLogoProps, 'className'> {
  /** Additional CSS classes */
  className?: string
  /** Whether to use navbar-optimized version */
  navbarOptimized?: boolean
  /** Whether to prevent all layout shifts */
  preventLayoutShifts?: boolean
  /** Whether to enable hover effects */
  enableHover?: boolean
  /** Whether to enable ambient animations */
  enableAmbient?: boolean
  /** Animation variant for ambient effects */
  ambientVariant?: AnimationVariant
  /** Click handler */
  onClick?: () => void
}

/**
 * Layout-stable logo wrapper that prevents any layout shifts during animations
 * Perfect for navbar use where layout stability is critical
 */
export const LayoutStableLogo = forwardRef<HTMLDivElement, LayoutStableLogoProps>(
  ({
    width = 40,
    height = 28,
    className = "",
    navbarOptimized = false,
    preventLayoutShifts = true,
    variant = 'fadeIn',
    ...props
  }, ref) => {
    // Calculate fixed dimensions
    const fixedWidth = typeof width === 'number' ? width : 40
    const fixedHeight = typeof height === 'number' ? height : 28

    const containerStyle = preventLayoutShifts ? {
      // Fixed dimensions to prevent layout shifts
      width: `${fixedWidth}px`,
      height: `${fixedHeight}px`,
      minWidth: `${fixedWidth}px`,
      minHeight: `${fixedHeight}px`,
      // Prevent any overflow from affecting layout
      overflow: 'hidden',
      // Ensure consistent box model
      boxSizing: 'border-box' as const
    } : {}

    const LogoComponent = navbarOptimized ? NavbarSVGLogo : AnimatedSVGLogo

    return (
      <div
        ref={ref}
        className={cn(
          "relative inline-block",
          // Prevent any transforms from affecting layout
          preventLayoutShifts && "transform-gpu",
          className
        )}
        style={containerStyle}
      >
        {/* Layout-stable logo container */}
        <div 
          className="absolute inset-0 flex items-center justify-center"
          style={{
            // Ensure logo is centered and doesn't affect layout
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%'
          }}
        >
          <LogoComponent
            width={width}
            height={height}
            variant={variant}
            className="max-w-full max-h-full"
            {...props}
          />
        </div>
      </div>
    )
  }
)

LayoutStableLogo.displayName = 'LayoutStableLogo'

/**
 * Navbar-specific layout-stable logo
 * Optimized for navigation bar use with zero layout shifts
 */
export function NavbarLayoutStableLogo({
  className = "",
  ...props
}: Omit<LayoutStableLogoProps, 'navbarOptimized' | 'preventLayoutShifts'>) {
  return (
    <LayoutStableLogo
      navbarOptimized={true}
      preventLayoutShifts={true}
      variant="fadeIn"
      speed="fast"
      className={cn("navbar-logo", className)}
      {...props}
    />
  )
}

/**
 * Hero section layout-stable logo
 * Allows more dramatic animations while maintaining layout stability
 */
export function HeroLayoutStableLogo({
  className = "",
  variant = 'combined',
  ...props
}: LayoutStableLogoProps) {
  return (
    <LayoutStableLogo
      navbarOptimized={false}
      preventLayoutShifts={true}
      variant={variant}
      speed="normal"
      className={cn("hero-logo", className)}
      {...props}
    />
  )
}

/**
 * Compact layout-stable logo for mobile
 */
export function CompactLayoutStableLogo({
  className = "",
  ...props
}: Omit<LayoutStableLogoProps, 'width' | 'height'>) {
  return (
    <LayoutStableLogo
      width={32}
      height={22}
      navbarOptimized={true}
      preventLayoutShifts={true}
      variant="fadeIn"
      speed="fast"
      className={cn("compact-logo md:hidden", className)}
      {...props}
    />
  )
}

/**
 * Hook to get layout-stable logo dimensions
 */
export function useLayoutStableLogoDimensions(
  width: string | number = 40,
  height: string | number = 28
) {
  const fixedWidth = typeof width === 'number' ? width : 40
  const fixedHeight = typeof height === 'number' ? height : 28

  return {
    width: fixedWidth,
    height: fixedHeight,
    style: {
      width: `${fixedWidth}px`,
      height: `${fixedHeight}px`,
      minWidth: `${fixedWidth}px`,
      minHeight: `${fixedHeight}px`
    }
  }
}

/**
 * Layout-stable logo with performance monitoring
 */
export function MonitoredLayoutStableLogo({
  onLayoutShift,
  ...props
}: LayoutStableLogoProps & {
  onLayoutShift?: (shift: number) => void
}) {
  const logoRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    if (!logoRef.current || !onLayoutShift) return

    // Monitor for layout shifts using ResizeObserver
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect
        const expectedWidth = typeof props.width === 'number' ? props.width : 40
        const expectedHeight = typeof props.height === 'number' ? props.height : 28
        
        const widthShift = Math.abs(width - expectedWidth)
        const heightShift = Math.abs(height - expectedHeight)
        const totalShift = widthShift + heightShift
        
        if (totalShift > 1) { // Threshold for significant shift
          onLayoutShift(totalShift)
        }
      }
    })

    resizeObserver.observe(logoRef.current)

    return () => {
      resizeObserver.disconnect()
    }
  }, [onLayoutShift, props.width, props.height])

  return <LayoutStableLogo ref={logoRef} {...props} />
}

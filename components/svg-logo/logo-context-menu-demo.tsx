"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>ointer2, <PERSON><PERSON><PERSON>, Zap } from 'lucide-react'
import { AnimatedSVGLogo } from './animated-svg-logo'
import { LogoContextMenu, useLogoContextMenu } from './index'
import { AnimationVariant, ColorScheme, AnimationSpeed } from './animations'

export function LogoContextMenuDemo() {
  const [currentVariant, setCurrentVariant] = useState<AnimationVariant>('fadeIn')
  const [currentColorScheme, setCurrentColorScheme] = useState<ColorScheme>('default')
  const [currentSpeed, setCurrentSpeed] = useState<AnimationSpeed>('fast')
  const [isPlaying, setIsPlaying] = useState(true)

  const logoContextMenu = useLogoContextMenu({
    initialVariant: currentVariant,
    initialColorScheme: currentColorScheme,
    initialSpeed: currentSpeed,
    initialPlaying: isPlaying,
    onVariantChange: (variant) => {
      setCurrentVariant(variant)
      setIsPlaying(true) // Restart animation when variant changes
    },
    onColorSchemeChange: (scheme) => {
      setCurrentColorScheme(scheme)
    },
    onSpeedChange: (speed) => {
      setCurrentSpeed(speed)
    },
    onPlayToggle: (playing) => {
      setIsPlaying(playing)
    },
    onRestart: () => {
      setIsPlaying(false)
      setTimeout(() => setIsPlaying(true), 100) // Restart animation
    },
    onExportSVG: () => {
      console.log('Exporting SVG...')
      // In a real implementation, you'd export the actual SVG
    },
    onCopyCode: () => {
      console.log('Code copied to clipboard!')
    },
    onShowMetrics: () => {
      console.log('Showing performance metrics...')
    }
  })

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Logo Context Menu Demo
          </CardTitle>
          <CardDescription>
            Right-click on the logo below to open the mini editor context menu. 
            This feature is only available in development mode.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Demo Logo */}
          <div className="flex flex-col items-center space-y-4">
            <div className="p-8 border-2 border-dashed border-muted rounded-lg bg-muted/20">
              <div
                className="relative cursor-pointer"
                onContextMenu={logoContextMenu.handleContextMenu}
                title="Right-click to open context menu"
              >
                <AnimatedSVGLogo
                  width={80}
                  height={56}
                  variant={currentVariant}
                  colorScheme={currentColorScheme}
                  speed={currentSpeed}
                  autoPlay={isPlaying}
                  loop={true}
                  className="transition-all duration-300"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <MousePointer2 className="w-4 h-4" />
              Right-click the logo above to open the context menu
            </div>
          </div>

          {/* Current Settings Display */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Animation
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <Badge variant="secondary" className="text-xs">
                  {currentVariant}
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Color Scheme</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <Badge variant="secondary" className="text-xs">
                  {currentColorScheme}
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Speed</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <Badge variant="secondary" className="text-xs">
                  {currentSpeed}
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Manual Controls */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Manual Controls</h4>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={logoContextMenu.handlePlayToggle}
              >
                {isPlaying ? 'Pause' : 'Play'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={logoContextMenu.handleRestart}
              >
                Restart
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => logoContextMenu.handleVariantChange('pathDraw')}
              >
                Path Draw
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => logoContextMenu.handleVariantChange('glow')}
              >
                Glow
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => logoContextMenu.handleVariantChange('breathe')}
              >
                Breathe
              </Button>
            </div>
          </div>

          {/* Instructions */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">How to Use</h4>
            <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
              <li>Right-click on any logo to open the context menu</li>
              <li>Choose from different animation variants</li>
              <li>Adjust color schemes and animation speed</li>
              <li>Use quick actions to play/pause or restart animations</li>
              <li>Copy code snippets or export SVG (development mode)</li>
              <li>View performance metrics for optimization</li>
            </ul>
          </div>

          {/* Feature List */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Context Menu Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div className="space-y-2">
                <h5 className="font-medium">Animation Controls</h5>
                <ul className="text-muted-foreground space-y-1">
                  <li>• 9 animation variants</li>
                  <li>• Play/pause/restart controls</li>
                  <li>• Speed adjustment (slow/normal/fast)</li>
                  <li>• Real-time preview</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h5 className="font-medium">Developer Tools</h5>
                <ul className="text-muted-foreground space-y-1">
                  <li>• Code snippet generation</li>
                  <li>• SVG export functionality</li>
                  <li>• Performance metrics</li>
                  <li>• Color scheme switching</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Development Note */}
          <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-start gap-3">
              <Settings className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
              <div className="space-y-1">
                <h5 className="font-medium text-blue-900 dark:text-blue-100">Development Feature</h5>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  The context menu is only available in development mode. In production, 
                  right-clicking on logos will show the standard browser context menu.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Context Menu Component */}
      <LogoContextMenu {...logoContextMenu.contextMenuProps} />
    </div>
  )
}

// Example of how to integrate with existing components
export function LogoWithContextMenu({
  width = 40,
  height = 28,
  className = "",
  ...props
}: {
  width?: number
  height?: number
  className?: string
  variant?: AnimationVariant
  colorScheme?: ColorScheme
  speed?: AnimationSpeed
}) {
  const logoContextMenu = useLogoContextMenu({
    initialVariant: props.variant || 'fadeIn',
    initialColorScheme: props.colorScheme || 'default',
    initialSpeed: props.speed || 'fast'
  })

  return (
    <div className="relative">
      <div
        onContextMenu={process.env.NODE_ENV === 'development' ? logoContextMenu.handleContextMenu : undefined}
        className={className}
      >
        <AnimatedSVGLogo
          width={width}
          height={height}
          variant={logoContextMenu.currentVariant}
          colorScheme={logoContextMenu.currentColorScheme}
          speed={logoContextMenu.currentSpeed}
          autoPlay={logoContextMenu.isPlaying}
          {...props}
        />
      </div>
      
      {/* Context Menu (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <LogoContextMenu {...logoContextMenu.contextMenuProps} />
      )}
    </div>
  )
}

"use client"

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { AnimatedSVGLogo } from './animated-svg-logo'
import { LogoContextMenu, useLogoContextMenu } from './index'
import { AnimationVariant, ColorScheme, AnimationSpeed } from './animations'

interface SimpleEditableLogoProps {
  width?: number
  height?: number
  className?: string
  onClick?: () => void
  onLoad?: () => void
  onError?: (error: Error) => void
  ariaLabel?: string
  enableEditing?: boolean
}

/**
 * Simple editable logo that definitely works
 * Minimal implementation to debug logo visibility issues
 */
export function SimpleEditableLogo({
  width = 40,
  height = 28,
  className = "",
  onClick,
  onLoad,
  onError,
  ariaLabel = "Soimagine Logo",
  enableEditing = process.env.NODE_ENV === 'development'
}: SimpleEditableLogoProps) {
  const [variant, setVariant] = useState<AnimationVariant>('fadeIn')
  const [colorScheme, setColorScheme] = useState<ColorScheme>('default')
  const [speed, setSpeed] = useState<AnimationSpeed>('fast')
  const [isPlaying, setIsPlaying] = useState(true)

  // Context menu
  const logoContextMenu = useLogoContextMenu({
    initialVariant: variant,
    initialColorScheme: colorScheme,
    initialSpeed: speed,
    initialPlaying: isPlaying,
    onVariantChange: (newVariant) => {
      setVariant(newVariant)
      console.log('Variant changed to:', newVariant)
    },
    onColorSchemeChange: (newScheme) => {
      setColorScheme(newScheme)
      console.log('Color scheme changed to:', newScheme)
    },
    onSpeedChange: (newSpeed) => {
      setSpeed(newSpeed)
      console.log('Speed changed to:', newSpeed)
    },
    onPlayToggle: (playing) => {
      setIsPlaying(playing)
      console.log('Playing:', playing)
    },
    onRestart: () => {
      setIsPlaying(false)
      setTimeout(() => setIsPlaying(true), 100)
      console.log('Animation restarted')
    }
  })

  return (
    <>
      <div
        className={cn(
          "relative inline-block cursor-pointer",
          enableEditing && "cursor-context-menu",
          className
        )}
        style={{
          width: `${width}px`,
          height: `${height}px`,
          minWidth: `${width}px`,
          minHeight: `${height}px`
        }}
        onClick={onClick}
        onContextMenu={enableEditing ? logoContextMenu.handleContextMenu : undefined}
        title={enableEditing ? "Right-click to edit logo" : undefined}
      >
        <AnimatedSVGLogo
          width={width}
          height={height}
          variant={variant}
          colorScheme={colorScheme}
          speed={speed}
          autoPlay={isPlaying}
          loop={true}
          onLoad={onLoad}
          onError={onError}
          ariaLabel={ariaLabel}
          className="w-full h-full"
        />
        
        {/* Development indicator */}
        {enableEditing && process.env.NODE_ENV === 'development' && (
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full opacity-75" />
        )}
      </div>

      {/* Context Menu */}
      {enableEditing && (
        <LogoContextMenu {...logoContextMenu.contextMenuProps} />
      )}
    </>
  )
}

/**
 * Fallback logo that always shows something
 * For debugging when the main logo doesn't appear
 */
export function FallbackLogo({
  width = 40,
  height = 28,
  className = "",
  onClick,
  children = "LOGO"
}: {
  width?: number
  height?: number
  className?: string
  onClick?: () => void
  children?: React.ReactNode
}) {
  return (
    <div
      className={cn(
        "relative inline-flex items-center justify-center",
        "bg-primary text-primary-foreground rounded",
        "font-bold text-sm cursor-pointer",
        "hover:bg-primary/90 transition-colors",
        className
      )}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        minWidth: `${width}px`,
        minHeight: `${height}px`
      }}
      onClick={onClick}
    >
      {children}
    </div>
  )
}

/**
 * Logo with fallback - shows fallback if main logo fails
 */
export function LogoWithFallback({
  width = 40,
  height = 28,
  className = "",
  onClick,
  onLoad,
  onError,
  ariaLabel = "Soimagine Logo",
  enableEditing = process.env.NODE_ENV === 'development'
}: SimpleEditableLogoProps) {
  const [hasError, setHasError] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  const handleLoad = () => {
    setIsLoaded(true)
    setHasError(false)
    onLoad?.()
  }

  const handleError = (error: Error) => {
    setHasError(true)
    setIsLoaded(false)
    onError?.(error)
    console.error('Logo failed to load:', error)
  }

  if (hasError) {
    return (
      <FallbackLogo
        width={width}
        height={height}
        className={className}
        onClick={onClick}
      >
        SI
      </FallbackLogo>
    )
  }

  return (
    <SimpleEditableLogo
      width={width}
      height={height}
      className={className}
      onClick={onClick}
      onLoad={handleLoad}
      onError={handleError}
      ariaLabel={ariaLabel}
      enableEditing={enableEditing}
    />
  )
}

/**
 * Debug logo that shows loading states
 */
export function DebugLogo({
  width = 40,
  height = 28,
  className = "",
  onClick
}: {
  width?: number
  height?: number
  className?: string
  onClick?: () => void
}) {
  const [status, setStatus] = useState<'loading' | 'loaded' | 'error'>('loading')

  React.useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setStatus('loaded')
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const getStatusColor = () => {
    switch (status) {
      case 'loading': return 'bg-yellow-500'
      case 'loaded': return 'bg-green-500'
      case 'error': return 'bg-red-500'
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'loading': return '...'
      case 'loaded': return 'SI'
      case 'error': return '❌'
    }
  }

  return (
    <div
      className={cn(
        "relative inline-flex items-center justify-center",
        "text-white rounded font-bold text-sm cursor-pointer",
        "hover:opacity-90 transition-opacity",
        getStatusColor(),
        className
      )}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        minWidth: `${width}px`,
        minHeight: `${height}px`
      }}
      onClick={onClick}
      title={`Logo status: ${status}`}
    >
      {getStatusText()}
      
      {/* Status indicator */}
      <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full opacity-75" />
    </div>
  )
}

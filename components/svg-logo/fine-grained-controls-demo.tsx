"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Settings, Play, Pause, RotateCcw } from 'lucide-react'
import { AnimatedSVGLogo } from './animated-svg-logo'
import { LogoContextMenu } from './logo-context-menu'
import { 
  FineGrainedControls, 
  DEFAULT_FINE_GRAINED_CONTROLS,
  FineGrainedPanel 
} from './controls'
import { 
  AnimationVariant, 
  ColorScheme, 
  AnimationSpeed, 
  EasingType, 
  LoopType, 
  DelayType, 
  RandomizationType 
} from './animations'
import { AnimationPreset } from './animations/advanced-controls'

export function FineGrainedControlsDemo() {
  // Logo animation state
  const [currentVariant, setCurrentVariant] = useState<AnimationVariant>('pathDraw')
  const [currentColorScheme, setCurrentColorScheme] = useState<ColorScheme>('default')
  const [currentSpeed, setCurrentSpeed] = useState<AnimationSpeed>('normal')
  const [currentEasing, setCurrentEasing] = useState<EasingType>('easeInOut')
  const [currentLoopType, setCurrentLoopType] = useState<LoopType>('loop')
  const [currentDelayType, setCurrentDelayType] = useState<DelayType>('none')
  const [currentRandomization, setCurrentRandomization] = useState<RandomizationType>('none')
  const [loopCount, setLoopCount] = useState(3)
  const [delayAmount, setDelayAmount] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)

  // Fine-grained controls state
  const [fineGrainedControls, setFineGrainedControls] = useState<FineGrainedControls>(
    DEFAULT_FINE_GRAINED_CONTROLS
  )

  // Context menu state
  const [contextMenuOpen, setContextMenuOpen] = useState(false)
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 })
  const [showFineGrainedPanel, setShowFineGrainedPanel] = useState(false)

  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault()
    setContextMenuPosition({ x: event.clientX, y: event.clientY })
    setContextMenuOpen(true)
  }

  const handleFineGrainedControlChange = (
    key: keyof FineGrainedControls, 
    value: number | [number, number, number, number]
  ) => {
    setFineGrainedControls(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleResetControls = () => {
    setFineGrainedControls(DEFAULT_FINE_GRAINED_CONTROLS)
  }

  const handlePresetApply = (preset: AnimationPreset) => {
    // Apply preset logic here
    console.log('Applying preset:', preset)
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Fine-Grained Logo Controls</h1>
        <p className="text-muted-foreground">
          Advanced animation controls with sliders, inputs, and real-time preview
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Logo Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Interactive Logo Preview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-center p-8 bg-muted/50 rounded-lg">
              <div
                onContextMenu={handleContextMenu}
                className="cursor-context-menu"
                title="Right-click for controls"
              >
                <AnimatedSVGLogo
                  width={300}
                  height={200}
                  variant={currentVariant}
                  colorScheme={currentColorScheme}
                  speed={currentSpeed}
                  autoPlay={isPlaying}
                  loop={currentLoopType !== 'none'}
                />
              </div>
            </div>

            <div className="flex items-center justify-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
              >
                {isPlaying ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
                {isPlaying ? 'Pause' : 'Play'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFineGrainedPanel(!showFineGrainedPanel)}
              >
                <Settings className="w-4 h-4 mr-1" />
                Fine Controls
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetControls}
              >
                <RotateCcw className="w-4 h-4 mr-1" />
                Reset
              </Button>
            </div>

            <div className="text-center text-sm text-muted-foreground">
              Right-click the logo for context menu or use the Fine Controls button
            </div>
          </CardContent>
        </Card>

        {/* Current Settings Display */}
        <Card>
          <CardHeader>
            <CardTitle>Current Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Animation</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Variant:</span>
                    <Badge variant="secondary">{currentVariant}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Speed:</span>
                    <Badge variant="secondary">{currentSpeed}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Easing:</span>
                    <Badge variant="secondary">{currentEasing}</Badge>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">Fine Controls</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Duration:</span>
                    <span className="font-mono">{fineGrainedControls.duration.toFixed(1)}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Stagger:</span>
                    <span className="font-mono">{fineGrainedControls.stagger.toFixed(2)}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Rotation:</span>
                    <span className="font-mono">{fineGrainedControls.rotationAngle}°</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-medium mb-2">Transform</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex justify-between">
                  <span>Scale X:</span>
                  <span className="font-mono">{fineGrainedControls.scaleX.toFixed(2)}x</span>
                </div>
                <div className="flex justify-between">
                  <span>Scale Y:</span>
                  <span className="font-mono">{fineGrainedControls.scaleY.toFixed(2)}x</span>
                </div>
                <div className="flex justify-between">
                  <span>Opacity:</span>
                  <span className="font-mono">{fineGrainedControls.opacity}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Color:</span>
                  <span className="font-mono">{fineGrainedControls.colorIntensity}%</span>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-medium mb-2">Custom Easing</h4>
              <div className="text-xs font-mono bg-muted p-2 rounded">
                cubic-bezier({fineGrainedControls.customEasing.map(v => v.toFixed(2)).join(', ')})
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Context Menu */}
      <LogoContextMenu
        isOpen={contextMenuOpen}
        position={contextMenuPosition}
        onClose={() => setContextMenuOpen(false)}
        currentVariant={currentVariant}
        currentColorScheme={currentColorScheme}
        currentSpeed={currentSpeed}
        currentEasing={currentEasing}
        currentLoopType={currentLoopType}
        currentDelayType={currentDelayType}
        currentRandomization={currentRandomization}
        loopCount={loopCount}
        delayAmount={delayAmount}
        isPlaying={isPlaying}
        fineGrainedControls={fineGrainedControls}
        onVariantChange={setCurrentVariant}
        onColorSchemeChange={setCurrentColorScheme}
        onSpeedChange={setCurrentSpeed}
        onEasingChange={setCurrentEasing}
        onLoopTypeChange={setCurrentLoopType}
        onDelayTypeChange={setCurrentDelayType}
        onRandomizationChange={setCurrentRandomization}
        onLoopCountChange={setLoopCount}
        onDelayAmountChange={setDelayAmount}
        onFineGrainedControlChange={handleFineGrainedControlChange}
        onPresetApply={handlePresetApply}
        onPlayToggle={() => setIsPlaying(!isPlaying)}
        onRestart={() => {/* restart logic */}}
        onResetControls={handleResetControls}
      />

      {/* Standalone Fine-Grained Panel */}
      <FineGrainedPanel
        isOpen={showFineGrainedPanel}
        onClose={() => setShowFineGrainedPanel(false)}
        controls={fineGrainedControls}
        onControlChange={handleFineGrainedControlChange}
        onReset={handleResetControls}
        position={{ x: 100, y: 100 }}
      />
    </div>
  )
}

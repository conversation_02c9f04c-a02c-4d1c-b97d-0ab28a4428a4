import { Variants } from 'framer-motion'

// Animation variant types
export type AnimationVariant =
  | 'pathDraw'
  | 'staggeredReveal'
  | 'colorWave'
  | 'morphing'
  | 'glow'
  | 'rotate'
  | 'breathe'
  | 'combined'
  | 'loading'
  | 'error'
  | 'success'
  | 'fadeIn'

// Animation speed types
export type AnimationSpeed = 'slow' | 'normal' | 'fast' | 'custom'

// Color scheme types
export type ColorScheme = 'default' | 'brand' | 'monochrome' | 'rainbow'

// Easing types
export type EasingType =
  | 'linear'
  | 'easeIn'
  | 'easeOut'
  | 'easeInOut'
  | 'circIn'
  | 'circOut'
  | 'circInOut'
  | 'backIn'
  | 'backOut'
  | 'backInOut'
  | 'anticipate'
  | 'bounceIn'
  | 'bounceOut'
  | 'bounceInOut'
  | 'custom'

// Loop types
export type LoopType =
  | 'none'        // No loop
  | 'loop'        // Infinite loop
  | 'reverse'     // Loop with reverse
  | 'mirror'      // Mirror animation
  | 'pingPong'    // Ping pong effect
  | 'count'       // Specific count

// Delay types
export type DelayType =
  | 'none'        // No delay
  | 'fixed'       // Fixed delay
  | 'staggered'   // Staggered delay
  | 'random'      // Random delay
  | 'progressive' // Progressive delay

// Randomization types
export type RandomizationType =
  | 'none'        // No randomization
  | 'timing'      // Randomize timing
  | 'easing'      // Randomize easing
  | 'sequence'    // Randomize sequence
  | 'properties'  // Randomize properties
  | 'full'        // Full randomization

// Loading state types
export type LoadingState = 'idle' | 'loading' | 'loaded' | 'error'

// Animation timing configuration
export interface AnimationTiming {
  duration: number
  stagger: number
  delay?: number
  ease?: string | number[]
  easing?: EasingType
  loopType?: LoopType
  loopCount?: number
  delayType?: DelayType
  delayAmount?: number
  randomization?: RandomizationType
  randomSeed?: number
}

// Animation configuration
export interface AnimationConfig {
  variant: AnimationVariant
  timing: AnimationTiming
  pathIndex: number
  pathLength?: number
  loop?: boolean
  autoPlay?: boolean
}

// Color configuration
export interface ColorConfig {
  primary: string
  secondary: string
  accent: string
}

// Animation factory interface
export interface AnimationFactory {
  createVariants(config: AnimationConfig): Variants
  getDefaultTiming(speed: AnimationSpeed): AnimationTiming
  validateConfig(config: AnimationConfig): boolean
}

// Performance metrics
export interface AnimationMetrics {
  loadTime: number
  renderTime: number
  animationFrames: number
  memoryUsage?: number
  fps?: number
}

// Error types
export interface AnimationError extends Error {
  code: 'ANIMATION_FAILED' | 'INVALID_CONFIG' | 'PERFORMANCE_ISSUE'
  details?: Record<string, unknown>
}

// Loading state configuration
export interface LoadingStateConfig {
  showSpinner?: boolean
  showProgress?: boolean
  showText?: boolean
  fallbackComponent?: React.ComponentType
  timeout?: number
  retryCount?: number
}

// Animation event handlers
export interface AnimationEventHandlers {
  onStart?: () => void
  onComplete?: () => void
  onError?: (error: AnimationError) => void
  onProgress?: (progress: number) => void
  onRetry?: (attempt: number) => void
}

// Preload configuration
export interface PreloadConfig {
  enabled: boolean
  priority: 'high' | 'normal' | 'low'
  timeout: number
  fallback: boolean
}

// Performance monitoring configuration
export interface PerformanceConfig {
  enabled: boolean
  sampleRate: number
  thresholds: {
    loadTime: number
    fps: number
    memoryUsage: number
  }
  onThresholdExceeded?: (metric: keyof PerformanceConfig['thresholds'], value: number) => void
}

// Animation context
export interface AnimationContext {
  isPlaying: boolean
  isPaused: boolean
  isLoading: boolean
  hasError: boolean
  progress: number
  metrics: AnimationMetrics
  retry: () => void
  reset: () => void
}

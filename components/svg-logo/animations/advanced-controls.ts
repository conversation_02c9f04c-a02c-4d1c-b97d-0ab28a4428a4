import { EasingType, LoopType, DelayType, RandomizationType, AnimationTiming } from './types'

// Easing function mappings
export const EASING_FUNCTIONS = {
  linear: 'linear',
  easeIn: 'easeIn',
  easeOut: 'easeOut',
  easeInOut: 'easeInOut',
  circIn: 'circIn',
  circOut: 'circOut',
  circInOut: 'circInOut',
  backIn: 'backIn',
  backOut: 'backOut',
  backInOut: 'backInOut',
  anticipate: 'anticipate',
  bounceIn: [0.68, -0.55, 0.265, 1.55],
  bounceOut: [0.175, 0.885, 0.32, 1.275],
  bounceInOut: [0.68, -0.55, 0.265, 1.55],
  custom: 'easeInOut'
} as const

// Loop configuration mappings
export const LOOP_CONFIGS = {
  none: { repeat: 0, repeatType: 'loop' as const },
  loop: { repeat: Infinity, repeatType: 'loop' as const },
  reverse: { repeat: Infinity, repeatType: 'reverse' as const },
  mirror: { repeat: Infinity, repeatType: 'mirror' as const },
  pingPong: { repeat: Infinity, repeatType: 'reverse' as const, repeatDelay: 0.5 },
  count: { repeat: 3, repeatType: 'loop' as const }
} as const

// Delay calculation functions
export const DELAY_CALCULATORS = {
  none: () => 0,
  fixed: (amount: number) => amount,
  staggered: (index: number, amount: number) => index * amount,
  random: (amount: number) => Math.random() * amount,
  progressive: (index: number, amount: number) => Math.pow(index, 1.5) * amount
} as const

// Randomization utilities
export class AnimationRandomizer {
  private seed: number
  private rng: () => number

  constructor(seed?: number) {
    this.seed = seed || Date.now()
    this.rng = this.createSeededRandom(this.seed)
  }

  private createSeededRandom(seed: number): () => number {
    let state = seed
    return () => {
      state = (state * 1664525 + 1013904223) % 4294967296
      return state / 4294967296
    }
  }

  randomizeEasing(): EasingType {
    const easings: EasingType[] = [
      'easeIn', 'easeOut', 'easeInOut', 'circIn', 'circOut', 
      'backIn', 'backOut', 'bounceIn', 'bounceOut'
    ]
    return easings[Math.floor(this.rng() * easings.length)]
  }

  randomizeTiming(baseDuration: number): number {
    return baseDuration * (0.5 + this.rng() * 1.0) // 50% to 150% of base
  }

  randomizeDelay(maxDelay: number): number {
    return this.rng() * maxDelay
  }

  randomizeSequence<T>(array: T[]): T[] {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(this.rng() * (i + 1))
      ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  randomizeProperties(properties: Record<string, number>): Record<string, number> {
    const randomized: Record<string, number> = {}
    for (const [key, value] of Object.entries(properties)) {
      randomized[key] = value * (0.8 + this.rng() * 0.4) // 80% to 120% of original
    }
    return randomized
  }
}

// Advanced timing calculator
export function calculateAdvancedTiming(
  baseTiming: AnimationTiming,
  pathIndex: number = 0,
  totalPaths: number = 1
): AnimationTiming {
  const timing = { ...baseTiming }
  
  // Apply easing
  if (timing.easing && timing.easing !== 'custom') {
    timing.ease = EASING_FUNCTIONS[timing.easing]
  }

  // Apply delay
  if (timing.delayType && timing.delayAmount) {
    const calculator = DELAY_CALCULATORS[timing.delayType]
    if (timing.delayType === 'none') {
      timing.delay = 0
    } else if (timing.delayType === 'fixed') {
      timing.delay = timing.delayAmount
    } else if (timing.delayType === 'staggered') {
      timing.delay = pathIndex * timing.delayAmount
    } else if (timing.delayType === 'random') {
      timing.delay = Math.random() * timing.delayAmount
    } else if (timing.delayType === 'progressive') {
      timing.delay = Math.pow(pathIndex, 1.5) * timing.delayAmount
    }
  }

  // Apply randomization
  if (timing.randomization && timing.randomization !== 'none') {
    const randomizer = new AnimationRandomizer(timing.randomSeed)
    
    switch (timing.randomization) {
      case 'timing':
        timing.duration = randomizer.randomizeTiming(timing.duration)
        break
      case 'easing':
        timing.easing = randomizer.randomizeEasing()
        timing.ease = EASING_FUNCTIONS[timing.easing]
        break
      case 'sequence':
        // This would be handled at a higher level
        break
      case 'properties':
        // This would be handled in the animation variants
        break
      case 'full':
        timing.duration = randomizer.randomizeTiming(timing.duration)
        timing.easing = randomizer.randomizeEasing()
        timing.ease = EASING_FUNCTIONS[timing.easing]
        if (timing.delayAmount) {
          timing.delay = randomizer.randomizeDelay(timing.delayAmount)
        }
        break
    }
  }

  return timing
}

// Loop configuration generator
export function generateLoopConfig(loopType: LoopType, loopCount?: number) {
  const baseConfig = LOOP_CONFIGS[loopType]
  
  if (loopType === 'count' && loopCount) {
    return { ...baseConfig, repeat: loopCount }
  }
  
  return baseConfig
}

// Preset configurations for common use cases
export const ANIMATION_PRESETS = {
  // Smooth and professional
  smooth: {
    easing: 'easeInOut' as EasingType,
    loopType: 'none' as LoopType,
    delayType: 'none' as DelayType,
    randomization: 'none' as RandomizationType
  },
  
  // Bouncy and playful
  bouncy: {
    easing: 'bounceOut' as EasingType,
    loopType: 'loop' as LoopType,
    delayType: 'staggered' as DelayType,
    delayAmount: 0.1,
    randomization: 'none' as RandomizationType
  },
  
  // Organic and natural
  organic: {
    easing: 'easeOut' as EasingType,
    loopType: 'reverse' as LoopType,
    delayType: 'random' as DelayType,
    delayAmount: 0.3,
    randomization: 'timing' as RandomizationType
  },
  
  // Mechanical and precise
  mechanical: {
    easing: 'linear' as EasingType,
    loopType: 'loop' as LoopType,
    delayType: 'fixed' as DelayType,
    delayAmount: 0.5,
    randomization: 'none' as RandomizationType
  },
  
  // Chaotic and random
  chaotic: {
    easing: 'backInOut' as EasingType,
    loopType: 'mirror' as LoopType,
    delayType: 'random' as DelayType,
    delayAmount: 1.0,
    randomization: 'full' as RandomizationType,
    randomSeed: Math.random() * 1000
  },
  
  // Elegant and sophisticated
  elegant: {
    easing: 'anticipate' as EasingType,
    loopType: 'pingPong' as LoopType,
    delayType: 'progressive' as DelayType,
    delayAmount: 0.2,
    randomization: 'none' as RandomizationType
  }
} as const

export type AnimationPreset = keyof typeof ANIMATION_PRESETS

// Utility to apply preset to timing
export function applyPreset(
  baseTiming: AnimationTiming, 
  preset: AnimationPreset
): AnimationTiming {
  const presetConfig = ANIMATION_PRESETS[preset]
  return {
    ...baseTiming,
    ...presetConfig
  }
}

// Validation functions
export function validateEasing(easing: string): easing is EasingType {
  return Object.keys(EASING_FUNCTIONS).includes(easing)
}

export function validateLoopType(loopType: string): loopType is LoopType {
  return Object.keys(LOOP_CONFIGS).includes(loopType)
}

export function validateDelayType(delayType: string): delayType is DelayType {
  return Object.keys(DELAY_CALCULATORS).includes(delayType)
}

// Helper to get human-readable descriptions
export const CONTROL_DESCRIPTIONS = {
  easing: {
    linear: 'Constant speed throughout',
    easeIn: 'Starts slow, speeds up',
    easeOut: 'Starts fast, slows down',
    easeInOut: 'Slow start and end, fast middle',
    circIn: 'Circular acceleration',
    circOut: 'Circular deceleration',
    circInOut: 'Circular ease in and out',
    backIn: 'Backs up before moving forward',
    backOut: 'Overshoots then settles',
    backInOut: 'Backs up and overshoots',
    anticipate: 'Anticipates movement',
    bounceIn: 'Bounces at start',
    bounceOut: 'Bounces at end',
    bounceInOut: 'Bounces at both ends',
    custom: 'Custom easing curve'
  },
  loopType: {
    none: 'Play once',
    loop: 'Repeat infinitely',
    reverse: 'Alternate direction',
    mirror: 'Mirror animation',
    pingPong: 'Ping pong effect',
    count: 'Repeat specific times'
  },
  delayType: {
    none: 'No delay',
    fixed: 'Same delay for all',
    staggered: 'Incremental delay',
    random: 'Random delay',
    progressive: 'Exponential delay'
  },
  randomization: {
    none: 'No randomization',
    timing: 'Random timing',
    easing: 'Random easing',
    sequence: 'Random sequence',
    properties: 'Random properties',
    full: 'Full randomization'
  }
} as const

import { Variants } from 'framer-motion'
import { 
  AnimationFactory, 
  AnimationConfig, 
  AnimationTiming, 
  AnimationSpeed,
  AnimationError 
} from './types'

// Speed configurations
const SPEED_CONFIGS: Record<AnimationSpeed, Omit<AnimationTiming, 'delay'>> = {
  slow: { duration: 4, stagger: 0.8, ease: "easeInOut" },
  normal: { duration: 2.5, stagger: 0.5, ease: "easeOut" },
  fast: { duration: 1.5, stagger: 0.3, ease: "easeOut" },
  custom: { duration: 2.5, stagger: 0.5, ease: "easeOut" }
}

// Easing presets
const EASING_PRESETS = {
  smooth: [0.25, 0.1, 0.25, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
  elastic: [0.175, 0.885, 0.32, 1.275],
  sharp: [0.4, 0, 0.2, 1],
  linear: [0, 0, 1, 1]
} as const

export class SVGAnimationFactory implements AnimationFactory {
  private static instance: SVGAnimationFactory
  
  static getInstance(): SVGAnimationFactory {
    if (!SVGAnimationFactory.instance) {
      SVGAnimationFactory.instance = new SVGAnimationFactory()
    }
    return SVGAnimationFactory.instance
  }

  createVariants(config: AnimationConfig): Variants {
    if (!this.validateConfig(config)) {
      throw new Error(`Invalid animation config for variant: ${config.variant}`)
    }

    const { variant, timing, pathIndex } = config
    const baseDelay = pathIndex * timing.stagger + (timing.delay || 0)

    switch (variant) {
      case 'pathDraw':
        return this.createPathDrawVariants(timing, baseDelay)
      
      case 'staggeredReveal':
        return this.createStaggeredRevealVariants(timing, baseDelay)
      
      case 'colorWave':
        return this.createColorWaveVariants(timing, baseDelay)
      
      case 'morphing':
        return this.createMorphingVariants(timing, baseDelay)
      
      case 'glow':
        return this.createGlowVariants(timing, baseDelay)
      
      case 'rotate':
        return this.createRotateVariants(timing, baseDelay)
      
      case 'breathe':
        return this.createBreatheVariants(timing, baseDelay)
      
      case 'combined':
        return this.createCombinedVariants(timing, baseDelay)
      
      case 'loading':
        return this.createLoadingVariants(timing, baseDelay)
      
      case 'error':
        return this.createErrorVariants(timing, baseDelay)
      
      case 'success':
        return this.createSuccessVariants(timing, baseDelay)

      case 'fadeIn':
        return this.createFadeInVariants(timing, baseDelay)

      default:
        return this.createDefaultVariants(timing, baseDelay)
    }
  }

  getDefaultTiming(speed: AnimationSpeed): AnimationTiming {
    const baseConfig = SPEED_CONFIGS[speed] || SPEED_CONFIGS.normal
    return {
      ...baseConfig,
      delay: 0
    }
  }

  validateConfig(config: AnimationConfig): boolean {
    if (!config.variant || !config.timing) {
      return false
    }
    
    if (config.timing.duration <= 0 || config.timing.stagger < 0) {
      return false
    }
    
    if (config.pathIndex < 0) {
      return false
    }
    
    return true
  }

  // Individual animation variant creators
  private createPathDrawVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        pathLength: 0,
        opacity: 0
      },
      visible: {
        pathLength: 1,
        opacity: 1,
        transition: {
          pathLength: {
            duration: timing.duration,
            delay: baseDelay,
            ease: timing.ease || "easeInOut"
          },
          opacity: {
            duration: 0.3,
            delay: baseDelay
          }
        }
      }
    }
  }

  private createStaggeredRevealVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        opacity: 0
        // Removed scale and y transforms to prevent layout shifts
      },
      visible: {
        opacity: 1,
        transition: {
          duration: timing.duration * 0.6,
          delay: baseDelay,
          ease: timing.ease || "easeOut"
        }
      }
    }
  }

  private createColorWaveVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        opacity: 0.3
      },
      visible: {
        opacity: [0.3, 1, 0.7, 1],
        filter: [
          "hue-rotate(0deg) brightness(1)",
          "hue-rotate(180deg) brightness(1.2)",
          "hue-rotate(360deg) brightness(1)",
          "hue-rotate(0deg) brightness(1)"
        ],
        transition: {
          duration: timing.duration,
          delay: baseDelay,
          repeat: Infinity,
          ease: timing.ease || "easeInOut"
        }
      }
    }
  }

  private createMorphingVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        scale: 0.5,
        rotate: -180,
        opacity: 0
      },
      visible: {
        scale: [0.5, 1.2, 1],
        rotate: [-180, 0, 0],
        opacity: [0, 0.8, 1],
        transition: {
          duration: timing.duration,
          delay: baseDelay,
          ease: timing.ease || "easeOut"
        }
      }
    }
  }

  private createGlowVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        opacity: 0.5,
        filter: "drop-shadow(0 0 0px currentColor)"
      },
      visible: {
        opacity: [0.5, 1, 0.8, 1],
        filter: [
          "drop-shadow(0 0 0px currentColor)",
          "drop-shadow(0 0 10px currentColor)",
          "drop-shadow(0 0 20px currentColor)",
          "drop-shadow(0 0 10px currentColor)"
        ],
        transition: {
          duration: timing.duration,
          delay: baseDelay,
          repeat: Infinity,
          ease: timing.ease || "easeInOut"
        }
      }
    }
  }

  private createRotateVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        rotate: 0,
        scale: 0.8
      },
      visible: {
        rotate: [0, 360, 720],
        scale: [0.8, 1.1, 1],
        transition: {
          duration: timing.duration,
          delay: baseDelay,
          ease: timing.ease || "easeInOut"
        }
      }
    }
  }

  private createBreatheVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        opacity: 0.8
        // Removed scale to prevent layout shifts
      },
      visible: {
        opacity: [0.8, 1, 0.8],
        // Use filter effects instead of scale for visual breathing
        filter: [
          "brightness(0.95)",
          "brightness(1.05)",
          "brightness(0.95)"
        ],
        transition: {
          duration: timing.duration,
          delay: baseDelay,
          repeat: Infinity,
          ease: timing.ease || "easeInOut"
        }
      }
    }
  }

  private createCombinedVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        pathLength: 0,
        opacity: 0,
        scale: 0.8,
        rotate: -45
      },
      visible: {
        pathLength: 1,
        opacity: [0, 1, 0.9, 1],
        scale: [0.8, 1.1, 1],
        rotate: [-45, 0, 0],
        filter: [
          "drop-shadow(0 0 0px currentColor)",
          "drop-shadow(0 0 5px currentColor)",
          "drop-shadow(0 0 10px currentColor)",
          "drop-shadow(0 0 5px currentColor)"
        ],
        transition: {
          pathLength: {
            duration: timing.duration * 0.7,
            delay: baseDelay,
            ease: "easeOut"
          },
          opacity: {
            duration: timing.duration,
            delay: baseDelay,
            ease: "easeInOut"
          },
          scale: {
            duration: timing.duration * 0.8,
            delay: baseDelay + 0.2,
            ease: "easeOut"
          },
          rotate: {
            duration: timing.duration * 0.6,
            delay: baseDelay,
            ease: "easeOut"
          },
          filter: {
            duration: timing.duration,
            delay: baseDelay + 0.5,
            ease: "easeInOut"
          }
        }
      }
    }
  }

  private createLoadingVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        opacity: 0,
        scale: 0.8
      },
      visible: {
        opacity: [0, 0.5, 1, 0.5],
        scale: [0.8, 1, 1.1, 1],
        rotate: [0, 180, 360],
        transition: {
          duration: timing.duration,
          delay: baseDelay,
          repeat: Infinity,
          ease: "linear"
        }
      }
    }
  }

  private createErrorVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        opacity: 0,
        x: 0
      },
      visible: {
        opacity: [0, 1, 1, 0.7],
        x: [0, -5, 5, -5, 5, 0],
        transition: {
          duration: timing.duration * 0.5,
          delay: baseDelay,
          ease: "easeInOut"
        }
      }
    }
  }

  private createSuccessVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        opacity: 0,
        scale: 0.5
      },
      visible: {
        opacity: [0, 1, 1],
        scale: [0.5, 1.2, 1],
        filter: [
          "drop-shadow(0 0 0px #22c55e)",
          "drop-shadow(0 0 20px #22c55e)",
          "drop-shadow(0 0 10px #22c55e)"
        ],
        transition: {
          duration: timing.duration * 0.8,
          delay: baseDelay,
          ease: EASING_PRESETS.bounce
        }
      }
    }
  }

  private createFadeInVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: {
        opacity: 0
        // No transforms to prevent layout shifts - perfect for navbar use
      },
      visible: {
        opacity: 1,
        transition: {
          duration: timing.duration * 0.4, // Faster for navbar
          delay: baseDelay,
          ease: timing.ease || "easeOut"
        }
      }
    }
  }

  private createDefaultVariants(timing: AnimationTiming, baseDelay: number): Variants {
    return {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          duration: timing.duration,
          delay: baseDelay,
          ease: timing.ease || "easeOut"
        }
      }
    }
  }
}

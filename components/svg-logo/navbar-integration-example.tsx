"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { Menu, X, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { NavbarSVGLogo, CompactNavbarSVGLogo } from './navbar-svg-logo'
import { cn } from '@/lib/utils'

interface NavbarWithSVGLogoProps {
  className?: string
  variant?: 'default' | 'transparent' | 'solid'
  showMobileMenu?: boolean
  onLogoClick?: () => void
}

export function NavbarWithSVGLogo({
  className = "",
  variant = 'default',
  showMobileMenu = true,
  onLogoClick
}: NavbarWithSVGLogoProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  // Handle scroll effect
  React.useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navVariants = {
    default: "bg-background/95 backdrop-blur-sm border-b",
    transparent: "bg-transparent",
    solid: "bg-background border-b"
  }

  const navigation = [
    { name: 'Services', href: '/services', hasDropdown: true },
    { name: 'Portfolio', href: '/portfolio' },
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' }
  ]

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        navVariants[variant],
        isScrolled && variant === 'transparent' && "bg-background/95 backdrop-blur-sm border-b",
        className
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="flex items-center space-x-4"
          >
            {/* Desktop Logo */}
            <div className="hidden md:block">
              <NavbarSVGLogo
                width={48}
                height={32}
                enableHover={true}
                enableAmbient={false}
                hoverVariant="breathe"
                onClick={onLogoClick}
                showLoading={true}
                className="transition-transform duration-300 hover:scale-110"
              />
            </div>

            {/* Mobile Logo */}
            <div className="md:hidden">
              <CompactNavbarSVGLogo
                enableHover={true}
                onClick={onLogoClick}
                showLoading={true}
              />
            </div>

            {/* Company Name */}
            <Link 
              href="/" 
              className="text-xl font-bold bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent"
            >
              Soimagine
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="hidden md:flex items-center space-x-8"
          >
            {navigation.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              >
                {item.hasDropdown ? (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="flex items-center space-x-1 text-foreground hover:text-primary"
                      >
                        <span>{item.name}</span>
                        <ChevronDown className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-48">
                      <DropdownMenuItem asChild>
                        <Link href="/services/web-design">Web Design</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/services/development">Development</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/services/branding">Branding</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/services/consulting">Consulting</Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Link
                    href={item.href}
                    className="text-foreground hover:text-primary transition-colors duration-200 font-medium"
                  >
                    {item.name}
                  </Link>
                )}
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Button & Mobile Menu */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="flex items-center space-x-4"
          >
            {/* CTA Button */}
            <Button
              asChild
              className="hidden sm:inline-flex bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700"
            >
              <Link href="/contact">Get Started</Link>
            </Button>

            {/* Mobile Menu Button */}
            {showMobileMenu && (
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5" />
                ) : (
                  <Menu className="w-5 h-5" />
                )}
              </Button>
            )}
          </motion.div>
        </div>

        {/* Mobile Menu */}
        {showMobileMenu && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ 
              opacity: isMobileMenuOpen ? 1 : 0,
              height: isMobileMenuOpen ? 'auto' : 0
            }}
            transition={{ duration: 0.3 }}
            className="md:hidden overflow-hidden"
          >
            <div className="py-4 space-y-4 border-t">
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ 
                    opacity: isMobileMenuOpen ? 1 : 0,
                    x: isMobileMenuOpen ? 0 : -20
                  }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className="block py-2 text-foreground hover:text-primary transition-colors duration-200"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                </motion.div>
              ))}
              
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ 
                  opacity: isMobileMenuOpen ? 1 : 0,
                  x: isMobileMenuOpen ? 0 : -20
                }}
                transition={{ duration: 0.3, delay: 0.4 }}
                className="pt-4 border-t"
              >
                <Button
                  asChild
                  className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700"
                >
                  <Link href="/contact" onClick={() => setIsMobileMenuOpen(false)}>
                    Get Started
                  </Link>
                </Button>
              </motion.div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.nav>
  )
}

// Simplified navbar for specific use cases
export function SimpleNavbarWithSVGLogo({
  onLogoClick,
  className = ""
}: {
  onLogoClick?: () => void
  className?: string
}) {
  return (
    <nav className={cn("flex items-center justify-between p-4", className)}>
      <NavbarSVGLogo
        width={40}
        height={28}
        enableHover={true}
        enableAmbient={true}
        ambientVariant="breathe"
        hoverVariant="glow"
        onClick={onLogoClick}
        showLoading={true}
      />
      
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm">
          Menu
        </Button>
      </div>
    </nav>
  )
}

// Navbar with performance monitoring (development only)
export function DevNavbarWithSVGLogo(props: NavbarWithSVGLogoProps) {
  if (process.env.NODE_ENV === 'development') {
    return (
      <div className="relative">
        <NavbarWithSVGLogo {...props} />
        {/* Performance overlay would be added here */}
      </div>
    )
  }
  
  return <NavbarWithSVGLogo {...props} />
}

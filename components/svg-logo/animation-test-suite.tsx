"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { CheckCircle, XCircle, Play, Pause, RotateCcw, AlertTriangle } from 'lucide-react'
import { AnimatedSVGLogo } from './animated-svg-logo'
import { AnimationVariant, ColorScheme, AnimationSpeed } from './animations'

interface AnimationTest {
  variant: AnimationVariant
  description: string
  expectedBehavior: string
  category: 'basic' | 'advanced' | 'special'
  layoutStable: boolean
}

const ANIMATION_TESTS: AnimationTest[] = [
  {
    variant: 'fadeIn',
    description: 'Simple opacity fade-in animation',
    expectedBehavior: 'Logo fades in smoothly without layout shifts',
    category: 'basic',
    layoutStable: true
  },
  {
    variant: 'pathDraw',
    description: 'Progressive SVG path drawing',
    expectedBehavior: 'Paths draw from 0 to 100% with opacity fade',
    category: 'basic',
    layoutStable: true
  },
  {
    variant: 'staggeredReveal',
    description: 'Staggered element reveal (layout-stable)',
    expectedBehavior: 'Elements appear with staggered timing, no layout shifts',
    category: 'basic',
    layoutStable: true
  },
  {
    variant: 'colorWave',
    description: 'Animated color transitions with hue rotation',
    expectedBehavior: 'Colors cycle through hue spectrum infinitely',
    category: 'advanced',
    layoutStable: true
  },
  {
    variant: 'glow',
    description: 'Pulsing glow effect with drop shadows',
    expectedBehavior: 'Logo pulses with growing/shrinking glow infinitely',
    category: 'advanced',
    layoutStable: true
  },
  {
    variant: 'breathe',
    description: 'Gentle breathing motion (layout-stable)',
    expectedBehavior: 'Brightness oscillates to simulate breathing',
    category: 'advanced',
    layoutStable: true
  },
  {
    variant: 'morphing',
    description: 'Scale and rotation transformations',
    expectedBehavior: 'Logo scales and rotates with complex timing',
    category: 'advanced',
    layoutStable: false
  },
  {
    variant: 'rotate',
    description: 'Spinning rotation animation',
    expectedBehavior: 'Logo rotates 720 degrees with scale changes',
    category: 'advanced',
    layoutStable: false
  },
  {
    variant: 'combined',
    description: 'Multiple effects combined',
    expectedBehavior: 'Path drawing + scaling + rotation + glow effects',
    category: 'advanced',
    layoutStable: false
  },
  {
    variant: 'loading',
    description: 'Loading state animation',
    expectedBehavior: 'Continuous rotation with opacity/scale changes',
    category: 'special',
    layoutStable: false
  },
  {
    variant: 'error',
    description: 'Error state shake animation',
    expectedBehavior: 'Horizontal shake motion with opacity changes',
    category: 'special',
    layoutStable: true
  },
  {
    variant: 'success',
    description: 'Success state celebration',
    expectedBehavior: 'Scale bounce with green glow effect',
    category: 'special',
    layoutStable: false
  }
]

export function AnimationTestSuite() {
  const [currentTest, setCurrentTest] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [autoTest, setAutoTest] = useState(false)
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})
  const [colorScheme, setColorScheme] = useState<ColorScheme>('default')
  const [speed, setSpeed] = useState<AnimationSpeed>('normal')

  const currentAnimation = ANIMATION_TESTS[currentTest]

  // Auto-advance through tests
  useEffect(() => {
    if (!autoTest) return

    const timer = setTimeout(() => {
      setCurrentTest((prev) => (prev + 1) % ANIMATION_TESTS.length)
    }, 4000) // 4 seconds per test

    return () => clearTimeout(timer)
  }, [currentTest, autoTest])

  const markTestResult = (variant: AnimationVariant, passed: boolean) => {
    setTestResults(prev => ({ ...prev, [variant]: passed }))
  }

  const resetTests = () => {
    setTestResults({})
    setCurrentTest(0)
    setIsPlaying(true)
  }

  const getTestStatus = (variant: AnimationVariant) => {
    if (testResults[variant] === true) return 'passed'
    if (testResults[variant] === false) return 'failed'
    return 'pending'
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'advanced': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'special': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const passedTests = Object.values(testResults).filter(Boolean).length
  const totalTests = ANIMATION_TESTS.length
  const progressPercentage = (passedTests / totalTests) * 100

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Animation Test Suite</span>
            <Badge variant="outline">
              {passedTests}/{totalTests} Passed
            </Badge>
          </CardTitle>
          <CardDescription>
            Comprehensive testing of all logo animation variants to ensure they work correctly.
          </CardDescription>
          <Progress value={progressPercentage} className="w-full" />
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Controls */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
              {isPlaying ? 'Pause' : 'Play'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoTest(!autoTest)}
            >
              {autoTest ? 'Stop Auto' : 'Auto Test'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetTests}
            >
              <RotateCcw className="w-4 h-4 mr-1" />
              Reset
            </Button>
          </div>

          {/* Current Test Display */}
          <Card className="border-2 border-primary">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  Current Test: {currentAnimation.variant}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge className={getCategoryColor(currentAnimation.category)}>
                    {currentAnimation.category}
                  </Badge>
                  {currentAnimation.layoutStable ? (
                    <Badge variant="secondary" className="text-green-600">
                      Layout Stable
                    </Badge>
                  ) : (
                    <Badge variant="secondary" className="text-orange-600">
                      <AlertTriangle className="w-3 h-3 mr-1" />
                      Layout Shifts
                    </Badge>
                  )}
                </div>
              </div>
              <CardDescription>{currentAnimation.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Logo Display */}
              <div className="flex justify-center p-8 border-2 border-dashed border-muted rounded-lg bg-muted/20">
                <div className="relative">
                  <AnimatedSVGLogo
                    width={80}
                    height={56}
                    variant={currentAnimation.variant}
                    colorScheme={colorScheme}
                    speed={speed}
                    autoPlay={isPlaying}
                    loop={true}
                    key={`${currentAnimation.variant}-${isPlaying}`} // Force re-render
                  />
                </div>
              </div>

              {/* Expected Behavior */}
              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium text-sm mb-1">Expected Behavior:</h4>
                <p className="text-sm text-muted-foreground">{currentAnimation.expectedBehavior}</p>
              </div>

              {/* Test Result Buttons */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => markTestResult(currentAnimation.variant, true)}
                  className="flex-1 text-green-600 border-green-200 hover:bg-green-50"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Works Correctly
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => markTestResult(currentAnimation.variant, false)}
                  className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                >
                  <XCircle className="w-4 h-4 mr-1" />
                  Has Issues
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Test Navigation */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={() => setCurrentTest(Math.max(0, currentTest - 1))}
              disabled={currentTest === 0}
            >
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Test {currentTest + 1} of {ANIMATION_TESTS.length}
            </span>
            <Button
              variant="outline"
              onClick={() => setCurrentTest(Math.min(ANIMATION_TESTS.length - 1, currentTest + 1))}
              disabled={currentTest === ANIMATION_TESTS.length - 1}
            >
              Next
            </Button>
          </div>

          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Color Scheme</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex gap-1">
                  {(['default', 'brand', 'monochrome', 'rainbow'] as ColorScheme[]).map((scheme) => (
                    <Button
                      key={scheme}
                      variant={colorScheme === scheme ? "default" : "outline"}
                      size="sm"
                      onClick={() => setColorScheme(scheme)}
                      className="text-xs"
                    >
                      {scheme}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Animation Speed</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex gap-1">
                  {(['slow', 'normal', 'fast'] as AnimationSpeed[]).map((spd) => (
                    <Button
                      key={spd}
                      variant={speed === spd ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSpeed(spd)}
                      className="text-xs"
                    >
                      {spd}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Test Results Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Test Results Summary</CardTitle>
          <CardDescription>
            Click on any animation to jump to its test
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {ANIMATION_TESTS.map((test, index) => {
              const status = getTestStatus(test.variant)
              return (
                <Button
                  key={test.variant}
                  variant="outline"
                  className={`h-auto p-3 justify-start ${
                    currentTest === index ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setCurrentTest(index)}
                >
                  <div className="flex items-center gap-2 w-full">
                    <div className="flex-shrink-0">
                      {status === 'passed' && <CheckCircle className="w-4 h-4 text-green-600" />}
                      {status === 'failed' && <XCircle className="w-4 h-4 text-red-600" />}
                      {status === 'pending' && <div className="w-4 h-4 rounded-full border-2 border-muted" />}
                    </div>
                    <div className="text-left flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">{test.variant}</div>
                      <div className="text-xs text-muted-foreground truncate">{test.description}</div>
                    </div>
                    <Badge className={getCategoryColor(test.category)} variant="secondary">
                      {test.category}
                    </Badge>
                  </div>
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

// Import modularized animation system
import {
  SVGAnimationFactory,
  AnimationVariant,
  AnimationSpeed,
  LoadingState,
  PerformanceConfig
} from './animations'

// Import loading system
import {
  LoadingIndicator,
  LogoSkeleton,
  SVGLogoErrorBoundary,
  usePerformanceMonitor,
  useComponentPerformance,
  useErrorReporting
} from './loading'

// Color scheme types
export type ColorScheme = 'default' | 'brand' | 'monochrome' | 'rainbow' | 'custom'

// Props interface
export interface AnimatedSVGLogoProps {
  /** Width of the logo */
  width?: string | number
  /** Height of the logo */
  height?: string | number
  /** Additional CSS classes */
  className?: string
  /** Animation variant to use */
  variant?: AnimationVariant
  /** Color scheme */
  colorScheme?: ColorScheme
  /** Custom colors for paths */
  customColors?: {
    primary?: string
    secondary?: string
    accent?: string
  }
  /** Animation speed */
  speed?: AnimationSpeed
  /** Custom animation duration in seconds */
  customDuration?: number
  /** Whether to loop the animation */
  loop?: boolean
  /** Whether to auto-play animation */
  autoPlay?: boolean
  /** Whether to play animation on hover */
  playOnHover?: boolean
  /** Delay before animation starts (in seconds) */
  delay?: number
  /** Callback when animation completes */
  onAnimationComplete?: () => void
  /** Callback when animation starts */
  onAnimationStart?: () => void
  /** Callback when logo loads successfully */
  onLoad?: () => void
  /** Callback when logo fails to load */
  onError?: (error: Error) => void
  /** Whether to show controls for debugging */
  showControls?: boolean
  /** Loading state configuration */
  loadingConfig?: {
    showSpinner?: boolean
    showProgress?: boolean
    timeout?: number
    retryCount?: number
  }
  /** Performance monitoring configuration */
  performanceConfig?: Partial<PerformanceConfig>

  /** Accessibility label */
  ariaLabel?: string
}

// Color schemes configuration
const colorSchemes = {
  default: {
    primary: '#010101',
    secondary: '#f80000',
    accent: '#e91e63'
  },
  brand: {
    primary: '#e91e63',
    secondary: '#2196f3',
    accent: '#9c27b0'
  },
  monochrome: {
    primary: '#000000',
    secondary: '#666666',
    accent: '#999999'
  },
  rainbow: {
    primary: '#ff0000',
    secondary: '#00ff00',
    accent: '#0000ff'
  },
  custom: {
    primary: '#010101',
    secondary: '#f80000',
    accent: '#e91e63'
  }
}



// SVG path data (extracted from the original SVG)
const logoPath1 = "m 132.39399,197.37198 c -0.60823,-1.74192 -0.082,-3.93672 -0.25818,-5.8511 l -0.52192,-37.16466 -10.66083,0.011 15.93722,-30.55155 c 5.65464,10.22669 8.99085,20.17111 14.27462,30.58722 l -9.30712,-0.0284 0.0428,30.60364 c 5.4239,-0.9485 10.59041,-3.10725 15.17674,-6.11498 3.45277,-2.31517 6.33827,-5.35111 8.96014,-8.54222 2.4701,-3.28786 4.25736,-7.03863 5.71899,-10.85553 0.83236,-3.14339 1.98384,-6.24462 1.59298,-9.54963 -0.15603,-3.97463 0.76312,-7.59052 -0.70605,-11.39198 -0.78608,-4.05471 -3.04293,-7.58348 -4.84261,-11.23739 -2.15407,-3.17379 -4.77743,-5.92717 -7.64719,-8.45981 -10.59346,-9.22986 -27.18922,-10.05494 -33.62085,-7.57632 -3.82378,0.82656 -7.34574,2.67424 -10.72257,4.57977 -3.48824,2.13344 -6.47272,5.00475 -9.17115,8.04248 -2.33623,2.63759 -3.91184,5.74632 -5.37798,8.92287 -1.537277,2.53108 -1.854532,5.5153 -2.681719,8.31012 H 87.041892 c 0.153327,-4.15156 1.719901,-8.14495 3.083133,-12.03251 2.312026,-5.15975 5.174164,-10.09442 9.046652,-14.24904 3.344423,-3.95296 7.620693,-7.02508 11.986493,-9.76189 3.69904,-1.9143 7.43435,-3.7385 11.49174,-4.76272 3.91543,-1.122135 7.96215,-1.94486 12.05607,-1.669633 3.7202,-0.01194 7.45945,-0.218765 11.07643,0.825164 4.6541,0.969169 9.18094,2.557499 13.35655,4.844359 4.62698,2.32655 8.73588,5.62683 12.42692,9.22715 3.88325,4.23548 7.23475,8.96252 9.52734,14.24968 2.19262,4.65582 3.39578,9.74658 4.18948,14.8132 -0.17794,5.09645 0.54845,10.2813 -0.82516,15.26087 -0.96917,4.65411 -2.5575,9.18094 -4.84436,13.35655 -2.32655,4.62698 -5.62683,8.73589 -9.22715,12.42692 -4.23548,3.88324 -8.96251,7.23476 -14.24967,9.52734 -4.54658,2.14795 -9.51206,3.33737 -14.46873,4.09762 -3.00515,0.12837 -6.0103,0.25675 -9.01545,0.38513 l -0.19213,-0.20225 z"

const logoPath2 = "m 67.937215,199.24991 c -6.787478,-0.67978 -13.440544,-2.76516 -19.354686,-6.18215 -3.179708,-1.64213 -6.054669,-3.90442 -8.689608,-6.31009 -3.009037,-2.77169 -5.759995,-5.76881 -8.055916,-9.16341 -2.645731,-4.0484 -4.805938,-8.47731 -6.243533,-13.10819 -1.133224,-4.01292 -1.875522,-8.09193 -2.092622,-12.25973 -0.413155,-3.64677 0.350544,-7.32823 0.71754,-10.95292 1.101363,-5.35084 2.776504,-10.59369 5.570946,-15.31643 1.649529,-3.28354 3.972016,-6.23079 6.437103,-8.93702 2.771686,-3.00904 5.768815,-5.75999 9.163413,-8.05592 4.048401,-2.64571 8.477299,-4.80596 13.108191,-6.24352 3.945893,-1.0971 7.942905,-1.88709 12.043247,-2.0563 2.070155,-0.35409 4.126094,-0.21607 6.173749,0.20886 v 42.21949 c 3.673493,-0.006 7.346985,-0.0123 11.020478,-0.0185 l -16.140749,30.7337 C 67.786401,162.92227 62.28809,153.38812 57.093889,143.08834 l 9.638526,0.0388 2e-6,-30.57526 c -3.574684,0.58713 -6.997267,1.9701 -10.36987,3.22464 -3.315021,1.70964 -6.383443,3.77975 -9.167749,6.26518 -2.440754,2.04675 -4.302494,4.75961 -6.179717,7.31202 -2.064405,3.13407 -3.440848,6.58483 -4.580538,10.14458 -1.184832,3.95841 -1.547041,8.11017 -1.37114,12.23524 0.013,4.21756 1.030676,8.39621 2.594748,12.3028 1.513898,4.5019 4.365191,8.39471 7.432453,11.97035 2.044167,2.31232 4.64739,4.15236 7.136084,5.96233 3.134072,2.0644 6.584825,3.44085 10.144567,4.58053 3.958413,1.18485 8.11017,1.54705 12.235242,1.37115 4.217563,-0.013 8.396215,-1.03068 12.302807,-2.59475 4.83527,-1.5974 8.877028,-4.81326 12.747483,-8.02755 2.857343,-3.07984 5.261193,-6.51112 7.305463,-10.18126 1.44425,-3.25023 2.68643,-6.6491 3.28507,-10.177 h 11.56164 c -0.15037,3.01456 -1.19846,5.96545 -1.97364,8.87442 -1.39977,4.06664 -3.35893,7.95254 -5.65808,11.57025 -2.34015,3.42145 -5.07327,6.49965 -8.13232,9.29314 -3.12494,2.90827 -6.625296,5.47894 -10.505209,7.29684 -6.215943,3.41205 -13.272137,5.04567 -20.312588,5.51441 -2.420127,0.27808 -4.877051,0.016 -7.289906,-0.23933 z"

export function AnimatedSVGLogo({
  width = 200,
  height = 125,
  className = "",
  variant = 'pathDraw',
  colorScheme = 'default',
  customColors,
  speed = 'normal',
  customDuration,
  loop = true,
  autoPlay = true,
  playOnHover = false,
  delay = 0,
  onAnimationComplete,
  onAnimationStart,
  onLoad,
  onError,
  showControls = false,
  loadingConfig = {},
  performanceConfig = {},
  ariaLabel = "Soimagine Logo"
}: AnimatedSVGLogoProps) {
  // State management
  const [loadingState, setLoadingState] = useState<LoadingState>('idle')
  const [isPlaying, setIsPlaying] = useState(autoPlay)
  const [currentVariant, setCurrentVariant] = useState(variant)
  const [retryCount, setRetryCount] = useState(0)

  // Refs
  const svgRef = useRef<SVGSVGElement>(null)
  const loadTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Hooks
  const animationFactory = useMemo(() => SVGAnimationFactory.getInstance(), [])
  const { reportError } = useErrorReporting()
  const {
    metrics,
    startMonitoring,
    stopMonitoring,
    measureLoadTime
  } = usePerformanceMonitor(performanceConfig)
  const {
    startRender,
    endRender,
    trackEvent
  } = useComponentPerformance('AnimatedSVGLogo')

  // Get colors based on scheme
  const colors = useMemo(() => {
    const baseColors = colorSchemes[colorScheme] || colorSchemes.default
    return customColors ? { ...baseColors, ...customColors } : baseColors
  }, [colorScheme, customColors])

  // Get animation timing
  const timing = useMemo(() => {
    const defaultTiming = animationFactory.getDefaultTiming(speed)
    return {
      ...defaultTiming,
      duration: customDuration || defaultTiming.duration,
      delay
    }
  }, [animationFactory, speed, customDuration, delay])

  // Calculate path lengths for stroke animations
  const [pathLengths, setPathLengths] = useState<number[]>([])

  // Initialize component
  useEffect(() => {
    let isMounted = true

    const initializeComponent = async () => {
      if (!isMounted) return

      startRender()
      startMonitoring()
      setLoadingState('loading')

      try {
        // Simulate path length calculation
        await new Promise(resolve => setTimeout(resolve, 100))

        if (!isMounted) return

        setPathLengths([2000, 2200])
        setLoadingState('loaded')

        const loadTime = measureLoadTime()
        trackEvent('load_complete', loadTime)

        onLoad?.()
        endRender()
      } catch (error) {
        if (isMounted) {
          handleError(error instanceof Error ? error : new Error('Failed to calculate path lengths'))
        }
      }
    }

    // Set loading timeout
    if (loadingConfig.timeout) {
      loadTimeoutRef.current = setTimeout(() => {
        if (isMounted && loadingState === 'loading') {
          handleError(new Error('Loading timeout'))
        }
      }, loadingConfig.timeout)
    }

    initializeComponent()

    return () => {
      isMounted = false
      stopMonitoring()
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current)
      }
    }
  }, []) // Empty dependency array to run only once

  // Error handling
  const handleError = useCallback((error: Error) => {
    setLoadingState('error')
    reportError(error, { variant, colorScheme, speed })
    onError?.(error)
    trackEvent('error', 1)
  }, [reportError, variant, colorScheme, speed, onError, trackEvent])

  // Retry logic
  const handleRetry = useCallback(() => {
    const maxRetries = loadingConfig.retryCount || 3
    if (retryCount < maxRetries) {
      setRetryCount(prev => prev + 1)
      setLoadingState('loading')
      trackEvent('retry', retryCount + 1)

      // Retry after a delay
      setTimeout(() => {
        setLoadingState('loaded')
        onLoad?.()
      }, 1000)
    }
  }, [retryCount, loadingConfig.retryCount, onLoad, trackEvent])

  // Create animation variants using the factory
  const createPathVariants = useCallback((pathIndex: number) => {
    const activeVariant = showControls ? currentVariant : variant
    return animationFactory.createVariants({
      variant: activeVariant,
      timing,
      pathIndex,
      pathLength: pathLengths[pathIndex],
      loop,
      autoPlay: isPlaying
    })
  }, [animationFactory, showControls, currentVariant, variant, timing, pathLengths, loop, isPlaying])

  // Handle loading states
  if (loadingState === 'loading') {
    return (
      <div className={cn("relative inline-block", className)} style={{ width, height }}>
        <LoadingIndicator
          state="loading"
          size="md"
          showText={loadingConfig.showSpinner !== false}
          text={{ loading: "Loading logo..." }}
        />
      </div>
    )
  }

  if (loadingState === 'error') {
    return (
      <div className={cn("relative inline-block", className)} style={{ width, height }}>
        <LoadingIndicator
          state="error"
          size="md"
          showText={true}
          text={{ error: "Failed to load logo" }}
          onRetry={retryCount < (loadingConfig.retryCount || 3) ? handleRetry : undefined}
        />
      </div>
    )
  }

  return (
    <SVGLogoErrorBoundary
      onError={handleError}
      maxRetries={loadingConfig.retryCount || 3}
      fallback={
        <LogoSkeleton
          width={typeof width === 'number' ? width : 200}
          height={typeof height === 'number' ? height : 125}
          className={className}
        />
      }
    >
      <div
        className={cn("relative inline-block", className)}
        style={{
          // Fixed dimensions to prevent layout shifts
          width: typeof width === 'number' ? `${width}px` : width,
          height: typeof height === 'number' ? `${height}px` : height,
          // Ensure container maintains space during animations
          minWidth: typeof width === 'number' ? `${width}px` : width,
          minHeight: typeof height === 'number' ? `${height}px` : height
        }}
      >
        <motion.svg
          ref={svgRef}
          width={width}
          height={height}
          viewBox="0 0 161.93771 101.03214"
          className="overflow-visible absolute inset-0"
          initial="hidden"
          animate={isPlaying ? "visible" : "hidden"}
          onAnimationStart={() => {
            trackEvent('animation_start')
            onAnimationStart?.()
          }}
          onAnimationComplete={() => {
            trackEvent('animation_complete')
            onAnimationComplete?.()
          }}
          onHoverStart={() => playOnHover && setIsPlaying(true)}
          onHoverEnd={() => playOnHover && setIsPlaying(false)}
          role="img"
          aria-label={ariaLabel}
          style={{
            // Prevent SVG from affecting layout during animations
            position: 'absolute',
            top: 0,
            left: 0
          }}
        >
          {/* First path - main logo shape */}
          <motion.path
            d={logoPath1}
            fill={colors.primary}
            stroke={colors.primary}
            strokeWidth="0.6"
            transform="translate(-23.381566,-98.576284)"
            variants={createPathVariants(0)}
            custom={pathLengths[0]}
          />

          {/* Second path - accent element */}
          <motion.path
            d={logoPath2}
            fill={colors.secondary}
            stroke={colors.secondary}
            strokeWidth="0.5"
            transform="translate(-23.381566,-98.576284)"
            variants={createPathVariants(1)}
            custom={pathLengths[1]}
          />
        </motion.svg>

        {/* Debug controls */}
        {showControls && (
          <div className="absolute top-full left-0 mt-2 p-3 bg-background border rounded-md shadow-md z-10">
            <div className="flex flex-col gap-2 text-xs min-w-[200px]">
              <div className="flex gap-2">
                <button
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="px-3 py-1 bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
                >
                  {isPlaying ? 'Pause' : 'Play'}
                </button>
                <button
                  onClick={() => {
                    setIsPlaying(false)
                    setTimeout(() => setIsPlaying(true), 100)
                  }}
                  className="px-3 py-1 bg-secondary text-secondary-foreground rounded hover:bg-secondary/90 transition-colors"
                >
                  Restart
                </button>
              </div>
              <div className="flex flex-col gap-1">
                <label className="text-xs font-medium">Animation:</label>
                <select
                  value={currentVariant}
                  onChange={(e) => {
                    setCurrentVariant(e.target.value as AnimationVariant)
                    setIsPlaying(false)
                    setTimeout(() => setIsPlaying(true), 100)
                  }}
                  className="px-2 py-1 border rounded text-xs"
                >
                  <option value="pathDraw">Path Draw</option>
                  <option value="staggeredReveal">Staggered Reveal</option>
                  <option value="colorWave">Color Wave</option>
                  <option value="morphing">Morphing</option>
                  <option value="glow">Glow Effect</option>
                  <option value="rotate">Rotation</option>
                  <option value="breathe">Breathing</option>
                  <option value="combined">Combined</option>
                  <option value="loading">Loading</option>
                  <option value="error">Error</option>
                  <option value="success">Success</option>
                </select>
              </div>

              {/* Performance metrics in dev mode */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-2 pt-2 border-t text-xs text-muted-foreground">
                  <div>Load: {metrics.loadTime.toFixed(0)}ms</div>
                  <div>FPS: {metrics.fps || 0}</div>
                  <div>State: {loadingState}</div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </SVGLogoErrorBoundary>
  )
}

// Export types for external use
export type { AnimationVariant, AnimationSpeed }

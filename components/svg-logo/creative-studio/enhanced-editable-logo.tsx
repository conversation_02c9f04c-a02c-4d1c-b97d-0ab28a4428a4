"use client"

import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON>, 
  Palette, 
  Gallery, 
  Settings, 
  Download, 
  Share2,
  Wand2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { AnimatedSVGLogo, AnimatedSVGLogoProps } from '../animated-svg-logo'
import { LogoContextMenu, useLogoContextMenu } from '../index'
import { CreativeStudio } from './creative-studio'
import { VariationGallery } from './variation-gallery'
import { 
  LogoCustomization, 
  LogoVariation, 
  DEFAULT_LOGO_CUSTOMIZATION 
} from './types'
import { AnimationVariant, ColorScheme, AnimationSpeed } from '../animations'

interface EnhancedEditableLogoProps extends Omit<AnimatedSVGLogoProps, 'customColors'> {
  // Enhanced editing features
  enableCreativeStudio?: boolean
  enableVariationGallery?: boolean
  enableAdvancedControls?: boolean
  
  // Customization
  initialCustomization?: LogoCustomization
  onCustomizationChange?: (customization: LogoCustomization) => void
  
  // Variation management
  savedVariations?: LogoVariation[]
  onVariationSave?: (variation: LogoVariation) => void
  onVariationLoad?: (customization: LogoCustomization) => void
  
  // Export capabilities
  onExport?: (customization: LogoCustomization, format: 'svg' | 'png' | 'json') => void
  
  // Creative features
  showCreativeHints?: boolean
  enableQuickActions?: boolean
  
  // Standard props
  enableEditing?: boolean
  className?: string
}

export function EnhancedEditableLogo({
  width = 200,
  height = 125,
  initialVariant = 'pathDraw',
  initialColorScheme = 'default',
  initialSpeed = 'normal',
  enableCreativeStudio = true,
  enableVariationGallery = true,
  enableAdvancedControls = true,
  enableEditing = true,
  showCreativeHints = true,
  enableQuickActions = true,
  initialCustomization = DEFAULT_LOGO_CUSTOMIZATION,
  onCustomizationChange,
  savedVariations = [],
  onVariationSave,
  onVariationLoad,
  onExport,
  className,
  ...logoProps
}: EnhancedEditableLogoProps) {
  // State management
  const [currentVariant, setCurrentVariant] = useState<AnimationVariant>(initialVariant)
  const [currentColorScheme, setCurrentColorScheme] = useState<ColorScheme>(initialColorScheme)
  const [currentSpeed, setCurrentSpeed] = useState<AnimationSpeed>(initialSpeed)
  const [isPlaying, setIsPlaying] = useState(true)
  
  // Creative studio state
  const [showCreativeStudio, setShowCreativeStudio] = useState(false)
  const [showVariationGallery, setShowVariationGallery] = useState(false)
  const [currentCustomization, setCurrentCustomization] = useState<LogoCustomization>(initialCustomization)
  
  // Enhanced context menu
  const logoContextMenu = useLogoContextMenu({
    initialVariant: currentVariant,
    initialColorScheme: currentColorScheme,
    initialSpeed: currentSpeed,
    initialPlaying: isPlaying,
    onVariantChange: useCallback((variant: AnimationVariant) => {
      setCurrentVariant(variant)
      setIsPlaying(false)
      setTimeout(() => setIsPlaying(true), 100)
    }, []),
    onColorSchemeChange: useCallback((scheme: ColorScheme) => {
      setCurrentColorScheme(scheme)
    }, []),
    onSpeedChange: useCallback((speed: AnimationSpeed) => {
      setCurrentSpeed(speed)
    }, []),
    onPlayToggle: useCallback((playing: boolean) => {
      setIsPlaying(playing)
    }, [])
  })

  // Handle customization changes
  const handleCustomizationChange = useCallback((customization: LogoCustomization) => {
    setCurrentCustomization(customization)
    onCustomizationChange?.(customization)
    
    // Apply color changes to current state
    setCurrentColorScheme('custom')
  }, [onCustomizationChange])

  // Handle variation management
  const handleVariationSave = useCallback((variation: LogoVariation) => {
    onVariationSave?.(variation)
    setShowCreativeStudio(false)
  }, [onVariationSave])

  const handleVariationLoad = useCallback((customization: LogoCustomization) => {
    handleCustomizationChange(customization)
    onVariationLoad?.(customization)
    setShowVariationGallery(false)
  }, [handleCustomizationChange, onVariationLoad])

  // Export functionality
  const handleExport = useCallback((format: 'svg' | 'png' | 'json' = 'svg') => {
    onExport?.(currentCustomization, format)
  }, [currentCustomization, onExport])

  // Quick actions
  const quickActions = [
    {
      icon: Sparkles,
      label: 'Creative Studio',
      action: () => setShowCreativeStudio(true),
      enabled: enableCreativeStudio,
      color: 'text-purple-500'
    },
    {
      icon: Gallery,
      label: 'Variations',
      action: () => setShowVariationGallery(true),
      enabled: enableVariationGallery,
      color: 'text-blue-500'
    },
    {
      icon: Palette,
      label: 'Quick Colors',
      action: () => logoContextMenu.handleContextMenu,
      enabled: enableAdvancedControls,
      color: 'text-green-500'
    },
    {
      icon: Download,
      label: 'Export',
      action: () => handleExport(),
      enabled: true,
      color: 'text-orange-500'
    }
  ]

  // Apply customization to logo colors
  const customColors = currentColorScheme === 'custom' ? {
    primary: currentCustomization.colors.primary,
    secondary: currentCustomization.colors.secondary,
    accent: currentCustomization.colors.accent
  } : undefined

  return (
    <TooltipProvider>
      <div className={cn("relative group", className)}>
        {/* Main Logo */}
        <div
          className={cn(
            "relative inline-block",
            enableEditing && "cursor-context-menu",
            "transition-transform hover:scale-105"
          )}
          onContextMenu={enableEditing ? logoContextMenu.handleContextMenu : undefined}
          title={enableEditing ? "Right-click for advanced editing" : undefined}
        >
          <AnimatedSVGLogo
            width={width}
            height={height}
            variant={currentVariant}
            colorScheme={currentColorScheme}
            speed={currentSpeed}
            autoPlay={isPlaying}
            customColors={customColors}
            {...logoProps}
          />

          {/* Creative Enhancement Indicator */}
          {enableEditing && (
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              className="absolute -top-2 -right-2"
            >
              <Badge 
                variant="secondary" 
                className="text-xs px-1 py-0 h-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white border-0"
              >
                <Wand2 className="w-2 h-2" />
              </Badge>
            </motion.div>
          )}
        </div>

        {/* Quick Actions Toolbar */}
        {enableQuickActions && enableEditing && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <div className="flex gap-1 bg-background border rounded-lg shadow-lg p-1">
              {quickActions.filter(action => action.enabled).map((action, index) => (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={action.action}
                      className={cn("h-8 w-8 p-0", action.color)}
                    >
                      <action.icon className="w-3 h-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">{action.label}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          </motion.div>
        )}

        {/* Creative Hints */}
        {showCreativeHints && enableEditing && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute -bottom-6 left-0 right-0 text-center opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <p className="text-xs text-muted-foreground">
              Right-click for context menu • Hover for quick actions
            </p>
          </motion.div>
        )}

        {/* Enhanced Context Menu */}
        {enableEditing && (
          <LogoContextMenu 
            {...logoContextMenu.contextMenuProps}
            // Add enhanced features to context menu
            onExportSVG={() => handleExport('svg')}
            onShowMetrics={() => console.log('Show metrics')}
          />
        )}

        {/* Creative Studio Modal */}
        <CreativeStudio
          isOpen={showCreativeStudio}
          onClose={() => setShowCreativeStudio(false)}
          initialCustomization={currentCustomization}
          onSave={handleVariationSave}
          onExport={(customization) => {
            handleCustomizationChange(customization)
            handleExport('svg')
          }}
        />

        {/* Variation Gallery Modal */}
        {showVariationGallery && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
            onClick={() => setShowVariationGallery(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-6xl max-h-[90vh] overflow-hidden"
            >
              <VariationGallery
                variations={savedVariations}
                onApply={handleVariationLoad}
                onEdit={(variation) => {
                  handleVariationLoad(variation.customization)
                  setShowCreativeStudio(true)
                }}
                onExport={(variation) => {
                  handleCustomizationChange(variation.customization)
                  handleExport('svg')
                }}
                onDuplicate={(variation) => {
                  handleVariationLoad(variation.customization)
                  setShowCreativeStudio(true)
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </div>
    </TooltipProvider>
  )
}

// Export enhanced variants
export function EnhancedEditableNavbarLogo(props: Omit<EnhancedEditableLogoProps, 'width' | 'height'>) {
  return (
    <EnhancedEditableLogo
      width={40}
      height={28}
      initialVariant="fadeIn"
      initialSpeed="fast"
      enableQuickActions={false}
      showCreativeHints={false}
      {...props}
    />
  )
}

export function EnhancedEditableHeroLogo(props: Omit<EnhancedEditableLogoProps, 'width' | 'height'>) {
  return (
    <EnhancedEditableLogo
      width={400}
      height={250}
      initialVariant="combined"
      initialSpeed="normal"
      enableQuickActions={true}
      showCreativeHints={true}
      {...props}
    />
  )
}

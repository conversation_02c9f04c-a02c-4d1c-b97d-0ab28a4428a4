// Creative Studio exports
export { CreativeStudio } from './creative-studio'
export { ColorStudio } from './color-studio'
export { PathEditor } from './path-editor'
export { VariationGallery } from './variation-gallery'
export { 
  EnhancedEditableLogo,
  EnhancedEditableNavbarLogo,
  EnhancedEditableHeroLogo
} from './enhanced-editable-logo'

// Types
export type {
  LogoVariation,
  LogoCustomization,
  ColorCustomization,
  PathCustomization,
  GradientDefinition,
  PatternDefinition,
  TextCustomization,
  TextElement,
  EffectsCustomization,
  LayoutCustomization,
  AnimationCustomization,
  AnimationStep
} from './types'

// Constants
export {
  DEFAULT_LOGO_CUSTOMIZATION
} from './types'

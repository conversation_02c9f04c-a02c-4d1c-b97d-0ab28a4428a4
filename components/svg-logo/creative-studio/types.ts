// Creative Studio Types and Interfaces

export interface LogoVariation {
  id: string
  name: string
  description?: string
  createdAt: Date
  customization: LogoCustomization
  thumbnail?: string
  tags?: string[]
  isPublic?: boolean
  author?: string
}

export interface LogoCustomization {
  // Path modifications
  paths: {
    path1: PathCustomization
    path2: PathCustomization
  }
  
  // Color system
  colors: ColorCustomization
  
  // Typography
  text?: TextCustomization
  
  // Effects
  effects: EffectsCustomization
  
  // Layout
  layout: LayoutCustomization
  
  // Animation overrides
  animation?: AnimationCustomization
}

export interface PathCustomization {
  // Original path data
  originalPath: string
  
  // Modified path data
  customPath?: string
  
  // Transform properties
  transform: {
    scale: { x: number; y: number }
    rotate: number
    translate: { x: number; y: number }
    skew: { x: number; y: number }
  }
  
  // Stroke properties
  stroke: {
    width: number
    color: string
    dashArray?: string
    lineCap: 'butt' | 'round' | 'square'
    lineJoin: 'miter' | 'round' | 'bevel'
  }
  
  // Fill properties
  fill: {
    type: 'solid' | 'gradient' | 'pattern'
    color: string
    gradient?: GradientDefinition
    pattern?: PatternDefinition
  }
  
  // Visibility and opacity
  visible: boolean
  opacity: number
  
  // Morphing
  morphTargets?: string[]
  morphProgress?: number
}

export interface ColorCustomization {
  // Base colors
  primary: string
  secondary: string
  accent: string
  
  // Advanced color options
  gradients: {
    primary: GradientDefinition
    secondary: GradientDefinition
    accent: GradientDefinition
  }
  
  // Color effects
  saturation: number
  brightness: number
  contrast: number
  hue: number
  
  // Color harmony
  harmony: 'monochromatic' | 'analogous' | 'complementary' | 'triadic' | 'custom'
  
  // Dynamic colors
  dynamicColors?: {
    enabled: boolean
    speed: number
    range: number
  }
}

export interface GradientDefinition {
  type: 'linear' | 'radial' | 'conic'
  stops: Array<{
    offset: number
    color: string
    opacity?: number
  }>
  angle?: number // for linear
  center?: { x: number; y: number } // for radial/conic
  radius?: number // for radial
}

export interface PatternDefinition {
  type: 'dots' | 'lines' | 'grid' | 'custom'
  size: number
  spacing: number
  color: string
  opacity: number
  rotation?: number
}

export interface TextCustomization {
  elements: TextElement[]
  enabled: boolean
}

export interface TextElement {
  id: string
  text: string
  position: { x: number; y: number }
  font: {
    family: string
    size: number
    weight: number
    style: 'normal' | 'italic'
  }
  color: string
  opacity: number
  rotation: number
  effects: {
    shadow: boolean
    outline: boolean
    glow: boolean
  }
  animation?: {
    type: 'fade' | 'slide' | 'typewriter' | 'bounce'
    duration: number
    delay: number
  }
}

export interface EffectsCustomization {
  // Shadow effects
  dropShadow: {
    enabled: boolean
    x: number
    y: number
    blur: number
    color: string
    opacity: number
  }
  
  // Glow effects
  glow: {
    enabled: boolean
    color: string
    intensity: number
    size: number
  }
  
  // Blur effects
  blur: {
    enabled: boolean
    amount: number
  }
  
  // 3D effects
  perspective: {
    enabled: boolean
    rotateX: number
    rotateY: number
    rotateZ: number
    depth: number
  }
  
  // Particle effects
  particles: {
    enabled: boolean
    count: number
    size: number
    color: string
    speed: number
    direction: number
  }
}

export interface LayoutCustomization {
  // Canvas properties
  canvas: {
    width: number
    height: number
    background: string
    padding: number
  }
  
  // Logo positioning
  position: {
    x: number
    y: number
    anchor: 'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  }
  
  // Responsive behavior
  responsive: {
    enabled: boolean
    breakpoints: {
      mobile: LogoCustomization
      tablet: LogoCustomization
      desktop: LogoCustomization
    }
  }
}

export interface AnimationCustomization {
  // Override default animations
  customSequence?: AnimationStep[]
  
  // Interactive triggers
  triggers: {
    hover: boolean
    click: boolean
    scroll: boolean
    time: boolean
  }
  
  // Performance settings
  performance: {
    reducedMotion: boolean
    frameRate: number
    quality: 'low' | 'medium' | 'high'
  }
}

export interface AnimationStep {
  target: 'path1' | 'path2' | 'text' | 'all'
  property: string
  from: any
  to: any
  duration: number
  delay: number
  easing: string
}

// Default customization values
export const DEFAULT_LOGO_CUSTOMIZATION: LogoCustomization = {
  paths: {
    path1: {
      originalPath: '',
      transform: {
        scale: { x: 1, y: 1 },
        rotate: 0,
        translate: { x: 0, y: 0 },
        skew: { x: 0, y: 0 }
      },
      stroke: {
        width: 0.6,
        color: '#000000',
        lineCap: 'round',
        lineJoin: 'round'
      },
      fill: {
        type: 'solid',
        color: '#000000'
      },
      visible: true,
      opacity: 1
    },
    path2: {
      originalPath: '',
      transform: {
        scale: { x: 1, y: 1 },
        rotate: 0,
        translate: { x: 0, y: 0 },
        skew: { x: 0, y: 0 }
      },
      stroke: {
        width: 0.5,
        color: '#666666',
        lineCap: 'round',
        lineJoin: 'round'
      },
      fill: {
        type: 'solid',
        color: '#666666'
      },
      visible: true,
      opacity: 1
    }
  },
  colors: {
    primary: '#000000',
    secondary: '#666666',
    accent: '#0066cc',
    gradients: {
      primary: {
        type: 'linear',
        stops: [
          { offset: 0, color: '#000000' },
          { offset: 1, color: '#333333' }
        ],
        angle: 45
      },
      secondary: {
        type: 'linear',
        stops: [
          { offset: 0, color: '#666666' },
          { offset: 1, color: '#999999' }
        ],
        angle: 45
      },
      accent: {
        type: 'linear',
        stops: [
          { offset: 0, color: '#0066cc' },
          { offset: 1, color: '#0099ff' }
        ],
        angle: 45
      }
    },
    saturation: 100,
    brightness: 100,
    contrast: 100,
    hue: 0,
    harmony: 'custom'
  },
  effects: {
    dropShadow: {
      enabled: false,
      x: 2,
      y: 2,
      blur: 4,
      color: '#000000',
      opacity: 0.3
    },
    glow: {
      enabled: false,
      color: '#0066cc',
      intensity: 50,
      size: 10
    },
    blur: {
      enabled: false,
      amount: 0
    },
    perspective: {
      enabled: false,
      rotateX: 0,
      rotateY: 0,
      rotateZ: 0,
      depth: 1000
    },
    particles: {
      enabled: false,
      count: 50,
      size: 2,
      color: '#0066cc',
      speed: 1,
      direction: 0
    }
  },
  layout: {
    canvas: {
      width: 200,
      height: 125,
      background: 'transparent',
      padding: 10
    },
    position: {
      x: 0,
      y: 0,
      anchor: 'center'
    },
    responsive: {
      enabled: false,
      breakpoints: {
        mobile: {} as LogoCustomization,
        tablet: {} as LogoCustomization,
        desktop: {} as LogoCustomization
      }
    }
  }
}

"use client"

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Gallery, 
  Search, 
  Filter, 
  Heart, 
  Download, 
  Edit3, 
  Trash2, 
  <PERSON>hare2,
  <PERSON><PERSON>,
  <PERSON>,
  Clock,
  User,
  Tag
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'
import { AnimatedSVGLogo } from '../animated-svg-logo'
import { LogoVariation, LogoCustomization } from './types'

interface VariationGalleryProps {
  variations: LogoVariation[]
  onEdit?: (variation: LogoVariation) => void
  onDelete?: (variationId: string) => void
  onDuplicate?: (variation: LogoVariation) => void
  onExport?: (variation: LogoVariation) => void
  onApply?: (customization: LogoCustomization) => void
  className?: string
}

// Sample variations for demonstration
const SAMPLE_VARIATIONS: LogoVariation[] = [
  {
    id: '1',
    name: 'Ocean Breeze',
    description: 'Cool blue tones with flowing animations',
    createdAt: new Date('2024-01-15'),
    customization: {} as LogoCustomization, // Would contain actual customization
    tags: ['blue', 'ocean', 'calm'],
    isPublic: true,
    author: 'Designer Pro'
  },
  {
    id: '2',
    name: 'Sunset Glow',
    description: 'Warm orange and yellow gradient',
    createdAt: new Date('2024-01-14'),
    customization: {} as LogoCustomization,
    tags: ['orange', 'warm', 'gradient'],
    isPublic: true,
    author: 'Creative Studio'
  },
  {
    id: '3',
    name: 'Midnight Purple',
    description: 'Deep purple with subtle glow effects',
    createdAt: new Date('2024-01-13'),
    customization: {} as LogoCustomization,
    tags: ['purple', 'dark', 'glow'],
    isPublic: false,
    author: 'You'
  },
  {
    id: '4',
    name: 'Forest Green',
    description: 'Natural green tones with organic feel',
    createdAt: new Date('2024-01-12'),
    customization: {} as LogoCustomization,
    tags: ['green', 'nature', 'organic'],
    isPublic: true,
    author: 'Nature Lover'
  },
  {
    id: '5',
    name: 'Coral Reef',
    description: 'Vibrant coral colors with wave animations',
    createdAt: new Date('2024-01-11'),
    customization: {} as LogoCustomization,
    tags: ['coral', 'vibrant', 'waves'],
    isPublic: true,
    author: 'Ocean Designer'
  },
  {
    id: '6',
    name: 'Minimalist Black',
    description: 'Clean black and white design',
    createdAt: new Date('2024-01-10'),
    customization: {} as LogoCustomization,
    tags: ['minimal', 'black', 'clean'],
    isPublic: true,
    author: 'Minimal Studio'
  }
]

export function VariationGallery({
  variations = SAMPLE_VARIATIONS,
  onEdit,
  onDelete,
  onDuplicate,
  onExport,
  onApply,
  className
}: VariationGalleryProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTag, setSelectedTag] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<'recent' | 'name' | 'author'>('recent')
  const [activeTab, setActiveTab] = useState('all')

  // Get all unique tags
  const allTags = Array.from(
    new Set(variations.flatMap(v => v.tags || []))
  ).sort()

  // Filter and sort variations
  const filteredVariations = variations
    .filter(variation => {
      const matchesSearch = variation.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           variation.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           variation.author?.toLowerCase().includes(searchQuery.toLowerCase())
      
      const matchesTag = !selectedTag || variation.tags?.includes(selectedTag)
      
      const matchesTab = activeTab === 'all' || 
                        (activeTab === 'mine' && variation.author === 'You') ||
                        (activeTab === 'public' && variation.isPublic)
      
      return matchesSearch && matchesTag && matchesTab
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'author':
          return (a.author || '').localeCompare(b.author || '')
        case 'recent':
        default:
          return b.createdAt.getTime() - a.createdAt.getTime()
      }
    })

  const handleApplyVariation = (variation: LogoVariation) => {
    onApply?.(variation.customization)
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Gallery className="w-5 h-5" />
          Variation Gallery
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Controls */}
        <div className="space-y-4 mb-6">
          {/* Search and Filter */}
          <div className="flex gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search variations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
              className="px-3 py-2 border rounded-md text-sm"
            >
              <option value="recent">Recent</option>
              <option value="name">Name</option>
              <option value="author">Author</option>
            </select>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="all">All ({variations.length})</TabsTrigger>
              <TabsTrigger value="mine">
                Mine ({variations.filter(v => v.author === 'You').length})
              </TabsTrigger>
              <TabsTrigger value="public">
                Public ({variations.filter(v => v.isPublic).length})
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedTag === null ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedTag(null)}
              className="h-6 text-xs"
            >
              All Tags
            </Button>
            {allTags.map(tag => (
              <Button
                key={tag}
                variant={selectedTag === tag ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedTag(selectedTag === tag ? null : tag)}
                className="h-6 text-xs"
              >
                <Tag className="w-3 h-3 mr-1" />
                {tag}
              </Button>
            ))}
          </div>
        </div>

        {/* Variations Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <AnimatePresence>
            {filteredVariations.map((variation) => (
              <motion.div
                key={variation.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="group hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    {/* Preview */}
                    <div className="relative mb-3 bg-muted/50 rounded-lg p-4 aspect-video flex items-center justify-center">
                      <AnimatedSVGLogo
                        width={120}
                        height={80}
                        variant="fadeIn"
                        colorScheme="default"
                        speed="fast"
                        autoPlay={false}
                        loop={false}
                      />
                      
                      {/* Overlay Actions */}
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleApplyVariation(variation)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                        {onEdit && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onEdit(variation)}
                            className="text-xs"
                          >
                            <Edit3 className="w-3 h-3" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Info */}
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h4 className="font-medium text-sm truncate">{variation.name}</h4>
                        {variation.isPublic && (
                          <Star className="w-3 h-3 text-yellow-500 flex-shrink-0 ml-1" />
                        )}
                      </div>
                      
                      {variation.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {variation.description}
                        </p>
                      )}

                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <User className="w-3 h-3" />
                        <span>{variation.author}</span>
                        <Clock className="w-3 h-3 ml-auto" />
                        <span>{variation.createdAt.toLocaleDateString()}</span>
                      </div>

                      {/* Tags */}
                      {variation.tags && variation.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {variation.tags.slice(0, 3).map(tag => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {variation.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{variation.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex gap-1 pt-2">
                        {onDuplicate && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDuplicate(variation)}
                            className="h-6 px-2 text-xs"
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                        )}
                        {onExport && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onExport(variation)}
                            className="h-6 px-2 text-xs"
                          >
                            <Download className="w-3 h-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-2 text-xs"
                        >
                          <Share2 className="w-3 h-3" />
                        </Button>
                        {variation.author === 'You' && onDelete && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDelete(variation.id)}
                            className="h-6 px-2 text-xs text-destructive hover:text-destructive"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Empty State */}
        {filteredVariations.length === 0 && (
          <div className="text-center py-12">
            <Gallery className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No variations found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || selectedTag 
                ? "Try adjusting your search or filters"
                : "Create your first logo variation to get started"
              }
            </p>
            {(searchQuery || selectedTag) && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery('')
                  setSelectedTag(null)
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Edit3, 
  Move, 
  RotateCw, 
  Scale, 
  Eye, 
  EyeOff, 
  <PERSON><PERSON>, 
  Trash2,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ap
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { cn } from '@/lib/utils'
import { PathCustomization } from './types'

interface PathEditorProps {
  path1: PathCustomization
  path2: PathCustomization
  onPath1Change: (path: PathCustomization) => void
  onPath2Change: (path: PathCustomization) => void
  className?: string
}

export function PathEditor({ 
  path1, 
  path2, 
  onPath1Change, 
  onPath2Change, 
  className 
}: PathEditorProps) {
  const [selectedPath, setSelectedPath] = useState<'path1' | 'path2'>('path1')
  const [activeTab, setActiveTab] = useState('transform')

  const currentPath = selectedPath === 'path1' ? path1 : path2
  const updateCurrentPath = selectedPath === 'path1' ? onPath1Change : onPath2Change

  const updatePathProperty = (property: keyof PathCustomization, value: any) => {
    updateCurrentPath({ ...currentPath, [property]: value })
  }

  const updateTransform = (property: keyof PathCustomization['transform'], value: any) => {
    updatePathProperty('transform', {
      ...currentPath.transform,
      [property]: value
    })
  }

  const updateStroke = (property: keyof PathCustomization['stroke'], value: any) => {
    updatePathProperty('stroke', {
      ...currentPath.stroke,
      [property]: value
    })
  }

  const updateFill = (property: keyof PathCustomization['fill'], value: any) => {
    updatePathProperty('fill', {
      ...currentPath.fill,
      [property]: value
    })
  }

  const resetPath = () => {
    const defaultPath: PathCustomization = {
      originalPath: currentPath.originalPath,
      transform: {
        scale: { x: 1, y: 1 },
        rotate: 0,
        translate: { x: 0, y: 0 },
        skew: { x: 0, y: 0 }
      },
      stroke: {
        width: selectedPath === 'path1' ? 0.6 : 0.5,
        color: selectedPath === 'path1' ? '#000000' : '#666666',
        lineCap: 'round',
        lineJoin: 'round'
      },
      fill: {
        type: 'solid',
        color: selectedPath === 'path1' ? '#000000' : '#666666'
      },
      visible: true,
      opacity: 1
    }
    updateCurrentPath(defaultPath)
  }

  const duplicatePath = () => {
    if (selectedPath === 'path1') {
      onPath2Change({ ...path1 })
    } else {
      onPath1Change({ ...path2 })
    }
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Edit3 className="w-5 h-5" />
          Path Editor
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Path Selection */}
        <div className="flex gap-2 mb-4">
          <Button
            variant={selectedPath === 'path1' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedPath('path1')}
            className="flex-1"
          >
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-current" />
              Path 1 (Main)
            </div>
          </Button>
          <Button
            variant={selectedPath === 'path2' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedPath('path2')}
            className="flex-1"
          >
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-current opacity-70" />
              Path 2 (Accent)
            </div>
          </Button>
        </div>

        {/* Path Controls */}
        <div className="space-y-4">
          {/* Visibility and Opacity */}
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-2">
              {currentPath.visible ? (
                <Eye className="w-4 h-4" />
              ) : (
                <EyeOff className="w-4 h-4" />
              )}
              <Label className="text-sm">Visible</Label>
            </div>
            <div className="flex items-center gap-3">
              <Switch
                checked={currentPath.visible}
                onCheckedChange={(checked) => updatePathProperty('visible', checked)}
              />
              <div className="flex items-center gap-2">
                <Label className="text-xs">Opacity:</Label>
                <span className="text-xs w-8">{Math.round(currentPath.opacity * 100)}%</span>
              </div>
              <Slider
                value={[currentPath.opacity * 100]}
                onValueChange={([value]) => updatePathProperty('opacity', value / 100)}
                min={0}
                max={100}
                step={5}
                className="w-20"
              />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={duplicatePath}
              className="flex-1"
            >
              <Copy className="w-3 h-3 mr-1" />
              Duplicate
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetPath}
              className="flex-1"
            >
              <Undo className="w-3 h-3 mr-1" />
              Reset
            </Button>
          </div>

          {/* Detailed Controls */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="transform">Transform</TabsTrigger>
              <TabsTrigger value="stroke">Stroke</TabsTrigger>
              <TabsTrigger value="fill">Fill</TabsTrigger>
            </TabsList>

            {/* Transform Controls */}
            <TabsContent value="transform" className="space-y-4">
              <div className="space-y-4">
                {/* Scale */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Scale className="w-4 h-4" />
                    Scale
                  </Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-xs">X: {currentPath.transform.scale.x.toFixed(2)}</Label>
                      <Slider
                        value={[currentPath.transform.scale.x]}
                        onValueChange={([value]) => updateTransform('scale', { 
                          ...currentPath.transform.scale, 
                          x: value 
                        })}
                        min={0.1}
                        max={3}
                        step={0.05}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs">Y: {currentPath.transform.scale.y.toFixed(2)}</Label>
                      <Slider
                        value={[currentPath.transform.scale.y]}
                        onValueChange={([value]) => updateTransform('scale', { 
                          ...currentPath.transform.scale, 
                          y: value 
                        })}
                        min={0.1}
                        max={3}
                        step={0.05}
                      />
                    </div>
                  </div>
                </div>

                {/* Rotation */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <RotateCw className="w-4 h-4" />
                    Rotation: {currentPath.transform.rotate}°
                  </Label>
                  <Slider
                    value={[currentPath.transform.rotate]}
                    onValueChange={([value]) => updateTransform('rotate', value)}
                    min={-180}
                    max={180}
                    step={5}
                  />
                </div>

                {/* Translation */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium flex items-center gap-2">
                    <Move className="w-4 h-4" />
                    Position
                  </Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-xs">X: {currentPath.transform.translate.x}</Label>
                      <Slider
                        value={[currentPath.transform.translate.x]}
                        onValueChange={([value]) => updateTransform('translate', { 
                          ...currentPath.transform.translate, 
                          x: value 
                        })}
                        min={-50}
                        max={50}
                        step={1}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs">Y: {currentPath.transform.translate.y}</Label>
                      <Slider
                        value={[currentPath.transform.translate.y]}
                        onValueChange={([value]) => updateTransform('translate', { 
                          ...currentPath.transform.translate, 
                          y: value 
                        })}
                        min={-50}
                        max={50}
                        step={1}
                      />
                    </div>
                  </div>
                </div>

                {/* Skew */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Skew</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label className="text-xs">X: {currentPath.transform.skew.x}°</Label>
                      <Slider
                        value={[currentPath.transform.skew.x]}
                        onValueChange={([value]) => updateTransform('skew', { 
                          ...currentPath.transform.skew, 
                          x: value 
                        })}
                        min={-45}
                        max={45}
                        step={1}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs">Y: {currentPath.transform.skew.y}°</Label>
                      <Slider
                        value={[currentPath.transform.skew.y]}
                        onValueChange={([value]) => updateTransform('skew', { 
                          ...currentPath.transform.skew, 
                          y: value 
                        })}
                        min={-45}
                        max={45}
                        step={1}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Stroke Controls */}
            <TabsContent value="stroke" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm">Width: {currentPath.stroke.width}</Label>
                  <Slider
                    value={[currentPath.stroke.width]}
                    onValueChange={([value]) => updateStroke('width', value)}
                    min={0}
                    max={5}
                    step={0.1}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">Color</Label>
                  <div className="flex gap-2">
                    <Input
                      type="color"
                      value={currentPath.stroke.color}
                      onChange={(e) => updateStroke('color', e.target.value)}
                      className="w-12 h-8 p-1"
                    />
                    <Input
                      type="text"
                      value={currentPath.stroke.color}
                      onChange={(e) => updateStroke('color', e.target.value)}
                      className="flex-1 text-xs"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label className="text-xs">Line Cap</Label>
                    <select
                      value={currentPath.stroke.lineCap}
                      onChange={(e) => updateStroke('lineCap', e.target.value)}
                      className="w-full text-xs border rounded px-2 py-1"
                    >
                      <option value="butt">Butt</option>
                      <option value="round">Round</option>
                      <option value="square">Square</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-xs">Line Join</Label>
                    <select
                      value={currentPath.stroke.lineJoin}
                      onChange={(e) => updateStroke('lineJoin', e.target.value)}
                      className="w-full text-xs border rounded px-2 py-1"
                    >
                      <option value="miter">Miter</option>
                      <option value="round">Round</option>
                      <option value="bevel">Bevel</option>
                    </select>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Fill Controls */}
            <TabsContent value="fill" className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm">Fill Type</Label>
                  <select
                    value={currentPath.fill.type}
                    onChange={(e) => updateFill('type', e.target.value)}
                    className="w-full text-xs border rounded px-2 py-1"
                  >
                    <option value="solid">Solid Color</option>
                    <option value="gradient">Gradient</option>
                    <option value="pattern">Pattern</option>
                  </select>
                </div>

                {currentPath.fill.type === 'solid' && (
                  <div className="space-y-2">
                    <Label className="text-sm">Color</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={currentPath.fill.color}
                        onChange={(e) => updateFill('color', e.target.value)}
                        className="w-12 h-8 p-1"
                      />
                      <Input
                        type="text"
                        value={currentPath.fill.color}
                        onChange={(e) => updateFill('color', e.target.value)}
                        className="flex-1 text-xs"
                      />
                    </div>
                  </div>
                )}

                {currentPath.fill.type === 'gradient' && (
                  <div className="text-center text-sm text-muted-foreground py-4">
                    <Zap className="w-6 h-6 mx-auto mb-2 opacity-50" />
                    Gradient fills coming soon...
                  </div>
                )}

                {currentPath.fill.type === 'pattern' && (
                  <div className="text-center text-sm text-muted-foreground py-4">
                    <Zap className="w-6 h-6 mx-auto mb-2 opacity-50" />
                    Pattern fills coming soon...
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  )
}

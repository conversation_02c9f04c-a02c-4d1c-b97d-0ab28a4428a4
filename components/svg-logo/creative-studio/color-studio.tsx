"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import { cn } from '@/lib/utils'
import { ColorCustomization } from './types'

interface ColorStudioProps {
  colors: ColorCustomization
  onChange: (colors: ColorCustomization) => void
  className?: string
}

// Predefined color harmonies
const COLOR_HARMONIES = {
  monochromatic: {
    name: 'Monochromatic',
    description: 'Variations of a single hue',
    generate: (baseColor: string) => generateMonochromatic(baseColor)
  },
  analogous: {
    name: 'Analogous',
    description: 'Adjacent colors on the color wheel',
    generate: (baseColor: string) => generateAnalogous(baseColor)
  },
  complementary: {
    name: 'Complementary',
    description: 'Opposite colors on the color wheel',
    generate: (baseColor: string) => generateComplementary(baseColor)
  },
  triadic: {
    name: 'Triadic',
    description: 'Three evenly spaced colors',
    generate: (baseColor: string) => generateTriadic(baseColor)
  },
  custom: {
    name: 'Custom',
    description: 'Manual color selection',
    generate: (baseColor: string) => ({ primary: baseColor })
  }
}

// Trending color palettes
const TRENDING_PALETTES = [
  {
    name: 'Ocean Breeze',
    colors: { primary: '#0077be', secondary: '#00a8cc', accent: '#40e0d0' }
  },
  {
    name: 'Sunset Glow',
    colors: { primary: '#ff6b35', secondary: '#f7931e', accent: '#ffcd3c' }
  },
  {
    name: 'Forest Deep',
    colors: { primary: '#2d5016', secondary: '#4a7c59', accent: '#7fb069' }
  },
  {
    name: 'Royal Purple',
    colors: { primary: '#6a0572', secondary: '#a663cc', accent: '#d4adfc' }
  },
  {
    name: 'Midnight Blue',
    colors: { primary: '#0f3460', secondary: '#16537e', accent: '#1e88e5' }
  },
  {
    name: 'Coral Reef',
    colors: { primary: '#ff7f7f', secondary: '#ffb3ba', accent: '#ffd1dc' }
  }
]

export function ColorStudio({ colors, onChange, className }: ColorStudioProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [selectedHarmony, setSelectedHarmony] = useState<keyof typeof COLOR_HARMONIES>('custom')

  const updateColors = (updates: Partial<ColorCustomization>) => {
    onChange({ ...colors, ...updates })
  }

  const updateColorProperty = (property: keyof ColorCustomization, value: any) => {
    updateColors({ [property]: value })
  }

  const applyPalette = (palette: typeof TRENDING_PALETTES[0]) => {
    updateColors({
      primary: palette.colors.primary,
      secondary: palette.colors.secondary,
      accent: palette.colors.accent
    })
  }

  const generateHarmony = (harmony: keyof typeof COLOR_HARMONIES) => {
    if (harmony === 'custom') return
    
    const generator = COLOR_HARMONIES[harmony].generate
    const newColors = generator(colors.primary)
    updateColors(newColors)
    setSelectedHarmony(harmony)
  }

  const randomizeColors = () => {
    const hues = [
      Math.random() * 360,
      Math.random() * 360,
      Math.random() * 360
    ]
    
    updateColors({
      primary: `hsl(${hues[0]}, 70%, 50%)`,
      secondary: `hsl(${hues[1]}, 70%, 60%)`,
      accent: `hsl(${hues[2]}, 80%, 55%)`
    })
  }

  const resetColors = () => {
    updateColors({
      primary: '#000000',
      secondary: '#666666',
      accent: '#0066cc',
      saturation: 100,
      brightness: 100,
      contrast: 100,
      hue: 0
    })
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="w-5 h-5" />
          Color Studio
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="gradients">Gradients</TabsTrigger>
            <TabsTrigger value="harmony">Harmony</TabsTrigger>
            <TabsTrigger value="effects">Effects</TabsTrigger>
          </TabsList>

          {/* Basic Colors */}
          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-xs">Primary</Label>
                <div className="flex gap-2">
                  <Input
                    type="color"
                    value={colors.primary}
                    onChange={(e) => updateColorProperty('primary', e.target.value)}
                    className="w-12 h-8 p-1 border rounded"
                  />
                  <Input
                    type="text"
                    value={colors.primary}
                    onChange={(e) => updateColorProperty('primary', e.target.value)}
                    className="flex-1 text-xs"
                    placeholder="#000000"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-xs">Secondary</Label>
                <div className="flex gap-2">
                  <Input
                    type="color"
                    value={colors.secondary}
                    onChange={(e) => updateColorProperty('secondary', e.target.value)}
                    className="w-12 h-8 p-1 border rounded"
                  />
                  <Input
                    type="text"
                    value={colors.secondary}
                    onChange={(e) => updateColorProperty('secondary', e.target.value)}
                    className="flex-1 text-xs"
                    placeholder="#666666"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-xs">Accent</Label>
                <div className="flex gap-2">
                  <Input
                    type="color"
                    value={colors.accent}
                    onChange={(e) => updateColorProperty('accent', e.target.value)}
                    className="w-12 h-8 p-1 border rounded"
                  />
                  <Input
                    type="text"
                    value={colors.accent}
                    onChange={(e) => updateColorProperty('accent', e.target.value)}
                    className="flex-1 text-xs"
                    placeholder="#0066cc"
                  />
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={randomizeColors}
                className="flex-1"
              >
                <Shuffle className="w-3 h-3 mr-1" />
                Random
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={resetColors}
                className="flex-1"
              >
                <RotateCcw className="w-3 h-3 mr-1" />
                Reset
              </Button>
            </div>

            {/* Trending Palettes */}
            <div className="space-y-2">
              <Label className="text-xs font-medium">Trending Palettes</Label>
              <div className="grid grid-cols-2 gap-2">
                {TRENDING_PALETTES.map((palette) => (
                  <Button
                    key={palette.name}
                    variant="outline"
                    size="sm"
                    onClick={() => applyPalette(palette)}
                    className="h-auto p-2 flex flex-col items-start"
                  >
                    <div className="flex gap-1 mb-1">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: palette.colors.primary }}
                      />
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: palette.colors.secondary }}
                      />
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: palette.colors.accent }}
                      />
                    </div>
                    <span className="text-xs">{palette.name}</span>
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Gradients */}
          <TabsContent value="gradients" className="space-y-4">
            <div className="space-y-4">
              <div className="text-center text-sm text-muted-foreground py-4">
                <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                Advanced gradient controls coming soon...
              </div>
            </div>
          </TabsContent>

          {/* Color Harmony */}
          <TabsContent value="harmony" className="space-y-4">
            <div className="space-y-3">
              <Label className="text-xs font-medium">Color Harmony</Label>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(COLOR_HARMONIES).map(([key, harmony]) => (
                  <Button
                    key={key}
                    variant={selectedHarmony === key ? "default" : "outline"}
                    size="sm"
                    onClick={() => generateHarmony(key as keyof typeof COLOR_HARMONIES)}
                    className="h-auto p-3 flex flex-col items-start"
                  >
                    <span className="text-xs font-medium">{harmony.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {harmony.description}
                    </span>
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Color Effects */}
          <TabsContent value="effects" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-xs">Saturation: {colors.saturation}%</Label>
                <Slider
                  value={[colors.saturation]}
                  onValueChange={([value]) => updateColorProperty('saturation', value)}
                  min={0}
                  max={200}
                  step={5}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-xs">Brightness: {colors.brightness}%</Label>
                <Slider
                  value={[colors.brightness]}
                  onValueChange={([value]) => updateColorProperty('brightness', value)}
                  min={0}
                  max={200}
                  step={5}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-xs">Contrast: {colors.contrast}%</Label>
                <Slider
                  value={[colors.contrast]}
                  onValueChange={([value]) => updateColorProperty('contrast', value)}
                  min={0}
                  max={200}
                  step={5}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-xs">Hue Shift: {colors.hue}°</Label>
                <Slider
                  value={[colors.hue]}
                  onValueChange={([value]) => updateColorProperty('hue', value)}
                  min={-180}
                  max={180}
                  step={5}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

// Color harmony generators
function generateMonochromatic(baseColor: string): Partial<ColorCustomization> {
  // Implementation for monochromatic colors
  return {
    primary: baseColor,
    secondary: adjustBrightness(baseColor, 0.3),
    accent: adjustBrightness(baseColor, -0.2)
  }
}

function generateAnalogous(baseColor: string): Partial<ColorCustomization> {
  // Implementation for analogous colors
  return {
    primary: baseColor,
    secondary: adjustHue(baseColor, 30),
    accent: adjustHue(baseColor, -30)
  }
}

function generateComplementary(baseColor: string): Partial<ColorCustomization> {
  // Implementation for complementary colors
  return {
    primary: baseColor,
    secondary: adjustHue(baseColor, 180),
    accent: adjustHue(baseColor, 90)
  }
}

function generateTriadic(baseColor: string): Partial<ColorCustomization> {
  // Implementation for triadic colors
  return {
    primary: baseColor,
    secondary: adjustHue(baseColor, 120),
    accent: adjustHue(baseColor, 240)
  }
}

// Helper functions for color manipulation
function adjustBrightness(color: string, amount: number): string {
  // Simple brightness adjustment - in real implementation, use proper color library
  // For now, return the original color
  console.log('Adjusting brightness:', { color, amount })
  return color
}

function adjustHue(color: string, degrees: number): string {
  // Simple hue adjustment - in real implementation, use proper color library
  // For now, return the original color
  console.log('Adjusting hue:', { color, degrees })
  return color
}

"use client"

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Palette, 
  Edit3, 
  <PERSON>rkles, 
  Download, 
  Save, 
  Share2, 
  Undo, 
  Redo,
  Eye,
  Settings,
  X,
  Maximize2,
  Minimize2
} from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { AnimatedSVGLogo } from '../animated-svg-logo'
import { ColorStudio } from './color-studio'
import { PathEditor } from './path-editor'
import { 
  LogoCustomization, 
  LogoVariation, 
  DEFAULT_LOGO_CUSTOMIZATION,
  ColorCustomization,
  PathCustomization 
} from './types'

interface CreativeStudioProps {
  isOpen: boolean
  onClose: () => void
  initialCustomization?: LogoCustomization
  onSave?: (variation: LogoVariation) => void
  onExport?: (customization: LogoCustomization) => void
  className?: string
}

export function CreativeStudio({
  isOpen,
  onClose,
  initialCustomization = DEFAULT_LOGO_CUSTOMIZATION,
  onSave,
  onExport,
  className
}: CreativeStudioProps) {
  const [customization, setCustomization] = useState<LogoCustomization>(initialCustomization)
  const [activeTab, setActiveTab] = useState('colors')
  const [variationName, setVariationName] = useState('')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [undoStack, setUndoStack] = useState<LogoCustomization[]>([])
  const [redoStack, setRedoStack] = useState<LogoCustomization[]>([])

  // Update customization with undo/redo support
  const updateCustomization = useCallback((newCustomization: LogoCustomization) => {
    setUndoStack(prev => [...prev, customization])
    setRedoStack([]) // Clear redo stack when making new changes
    setCustomization(newCustomization)
  }, [customization])

  const handleUndo = () => {
    if (undoStack.length > 0) {
      const previous = undoStack[undoStack.length - 1]
      setRedoStack(prev => [...prev, customization])
      setCustomization(previous)
      setUndoStack(prev => prev.slice(0, -1))
    }
  }

  const handleRedo = () => {
    if (redoStack.length > 0) {
      const next = redoStack[redoStack.length - 1]
      setUndoStack(prev => [...prev, customization])
      setCustomization(next)
      setRedoStack(prev => prev.slice(0, -1))
    }
  }

  const handleColorChange = (colors: ColorCustomization) => {
    updateCustomization({
      ...customization,
      colors
    })
  }

  const handlePath1Change = (path1: PathCustomization) => {
    updateCustomization({
      ...customization,
      paths: {
        ...customization.paths,
        path1
      }
    })
  }

  const handlePath2Change = (path2: PathCustomization) => {
    updateCustomization({
      ...customization,
      paths: {
        ...customization.paths,
        path2
      }
    })
  }

  const handleSave = () => {
    if (!variationName.trim()) {
      alert('Please enter a name for your variation')
      return
    }

    const variation: LogoVariation = {
      id: Date.now().toString(),
      name: variationName,
      description: `Custom logo variation created on ${new Date().toLocaleDateString()}`,
      createdAt: new Date(),
      customization,
      tags: ['custom', 'user-created'],
      isPublic: false,
      author: 'User'
    }

    onSave?.(variation)
    setVariationName('')
  }

  const handleExport = () => {
    onExport?.(customization)
  }

  const handleReset = () => {
    if (confirm('Are you sure you want to reset all changes?')) {
      updateCustomization(DEFAULT_LOGO_CUSTOMIZATION)
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50"
            onClick={onClose}
          />

          {/* Creative Studio Panel */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "fixed z-50 bg-background border rounded-lg shadow-xl",
              isFullscreen 
                ? "inset-4" 
                : "top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] h-[85vh] max-w-7xl",
              className
            )}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Sparkles className="w-6 h-6 text-primary" />
                  <h2 className="text-xl font-bold">Creative Studio</h2>
                </div>
                <Badge variant="secondary">Beta</Badge>
              </div>

              <div className="flex items-center gap-2">
                {/* Undo/Redo */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleUndo}
                  disabled={undoStack.length === 0}
                  title="Undo"
                >
                  <Undo className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRedo}
                  disabled={redoStack.length === 0}
                  title="Redo"
                >
                  <Redo className="w-4 h-4" />
                </Button>

                <Separator orientation="vertical" className="h-6" />

                {/* Fullscreen Toggle */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
                >
                  {isFullscreen ? (
                    <Minimize2 className="w-4 h-4" />
                  ) : (
                    <Maximize2 className="w-4 h-4" />
                  )}
                </Button>

                {/* Close */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="flex h-full overflow-hidden">
              {/* Left Panel - Controls */}
              <div className="w-1/3 border-r overflow-y-auto">
                <div className="p-4">
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="colors" className="text-xs">
                        <Palette className="w-3 h-3 mr-1" />
                        Colors
                      </TabsTrigger>
                      <TabsTrigger value="paths" className="text-xs">
                        <Edit3 className="w-3 h-3 mr-1" />
                        Paths
                      </TabsTrigger>
                      <TabsTrigger value="effects" className="text-xs">
                        <Sparkles className="w-3 h-3 mr-1" />
                        Effects
                      </TabsTrigger>
                    </TabsList>

                    <div className="mt-4">
                      <TabsContent value="colors" className="space-y-4">
                        <ColorStudio
                          colors={customization.colors}
                          onChange={handleColorChange}
                        />
                      </TabsContent>

                      <TabsContent value="paths" className="space-y-4">
                        <PathEditor
                          path1={customization.paths.path1}
                          path2={customization.paths.path2}
                          onPath1Change={handlePath1Change}
                          onPath2Change={handlePath2Change}
                        />
                      </TabsContent>

                      <TabsContent value="effects" className="space-y-4">
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Sparkles className="w-5 h-5" />
                              Effects Studio
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-center text-sm text-muted-foreground py-8">
                              <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                              Advanced effects coming soon...
                              <br />
                              <span className="text-xs">Shadows, glows, 3D transforms, and more!</span>
                            </div>
                          </CardContent>
                        </Card>
                      </TabsContent>
                    </div>
                  </Tabs>
                </div>
              </div>

              {/* Right Panel - Preview and Actions */}
              <div className="flex-1 flex flex-col">
                {/* Preview Area */}
                <div className="flex-1 p-8 flex items-center justify-center bg-muted/20">
                  <div className="text-center space-y-6">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg blur-xl" />
                      <div className="relative bg-background p-8 rounded-lg border shadow-lg">
                        <AnimatedSVGLogo
                          width={300}
                          height={200}
                          variant="pathDraw"
                          colorScheme="default"
                          speed="normal"
                          autoPlay={true}
                          loop={true}
                          // Apply customizations here
                          customColors={{
                            primary: customization.colors.primary,
                            secondary: customization.colors.secondary,
                            accent: customization.colors.accent
                          }}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold">Live Preview</h3>
                      <p className="text-sm text-muted-foreground">
                        Your custom logo variation updates in real-time
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Bar */}
                <div className="p-4 border-t bg-muted/50">
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <Label className="text-xs">Variation Name</Label>
                      <Input
                        value={variationName}
                        onChange={(e) => setVariationName(e.target.value)}
                        placeholder="My Custom Logo"
                        className="mt-1"
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={handleReset}
                        className="text-xs"
                      >
                        Reset
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleExport}
                        className="text-xs"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        Export
                      </Button>
                      <Button
                        onClick={handleSave}
                        disabled={!variationName.trim()}
                        className="text-xs"
                      >
                        <Save className="w-3 h-3 mr-1" />
                        Save Variation
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

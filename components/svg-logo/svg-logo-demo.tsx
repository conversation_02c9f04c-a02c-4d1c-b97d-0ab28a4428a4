"use client"

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  AnimatedSVGLogo, 
  AnimationVariant, 
  ColorScheme, 
  AnimationSpeed 
} from './animated-svg-logo'
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Palette, 
  Zap, 
  Settings,
  Eye,
  Download
} from 'lucide-react'

export function SVGLogoDemo() {
  const [selectedVariant, setSelectedVariant] = useState<AnimationVariant>('pathDraw')
  const [selectedColorScheme, setSelectedColorScheme] = useState<ColorScheme>('default')
  const [selectedSpeed, setSelectedSpeed] = useState<AnimationSpeed>('normal')
  const [isPlaying, setIsPlaying] = useState(true)
  const [showControls, setShowControls] = useState(false)

  const variants: { value: AnimationVariant; label: string; description: string }[] = [
    { value: 'pathDraw', label: 'Path Draw', description: 'Draws paths progressively' },
    { value: 'staggeredReveal', label: 'Staggered Reveal', description: 'Elements appear with stagger' },
    { value: 'colorWave', label: 'Color Wave', description: 'Animated color transitions' },
    { value: 'morphing', label: 'Morphing', description: 'Scale and rotation morphing' },
    { value: 'glow', label: 'Glow Effect', description: 'Pulsing glow animation' },
    { value: 'rotate', label: 'Rotation', description: 'Spinning rotation effect' },
    { value: 'breathe', label: 'Breathing', description: 'Gentle breathing motion' },
    { value: 'combined', label: 'Combined', description: 'Multiple effects combined' }
  ]

  const colorSchemes: { value: ColorScheme; label: string }[] = [
    { value: 'default', label: 'Default' },
    { value: 'brand', label: 'Brand Colors' },
    { value: 'monochrome', label: 'Monochrome' },
    { value: 'rainbow', label: 'Rainbow' }
  ]

  const speeds: { value: AnimationSpeed; label: string }[] = [
    { value: 'slow', label: 'Slow' },
    { value: 'normal', label: 'Normal' },
    { value: 'fast', label: 'Fast' }
  ]

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-4"
      >
        <h1 className="text-4xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 bg-clip-text text-transparent">
          Animated SVG Logo Showcase
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Explore amazing Framer Motion animations for the Soimagine logo. 
          Choose from different animation variants, color schemes, and speeds.
        </p>
      </motion.div>

      {/* Main Demo Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Logo Display */}
        <div className="lg:col-span-2">
          <Card className="h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="w-5 h-5" />
                    Live Preview
                  </CardTitle>
                  <CardDescription>
                    Current: {variants.find(v => v.value === selectedVariant)?.label} • {selectedColorScheme} • {selectedSpeed}
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsPlaying(!isPlaying)}
                  >
                    {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsPlaying(false)
                      setTimeout(() => setIsPlaying(true), 100)
                    }}
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowControls(!showControls)}
                  >
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="flex items-center justify-center min-h-[400px] bg-gradient-to-br from-background to-muted/20">
              <AnimatedSVGLogo
                width={300}
                height={200}
                variant={selectedVariant}
                colorScheme={selectedColorScheme}
                speed={selectedSpeed}
                autoPlay={isPlaying}
                loop={true}
                showControls={showControls}
                className="drop-shadow-lg"
              />
            </CardContent>
          </Card>
        </div>

        {/* Controls */}
        <div className="space-y-4">
          {/* Animation Variants */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Zap className="w-5 h-5" />
                Animation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {variants.map((variant) => (
                <motion.div
                  key={variant.value}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant={selectedVariant === variant.value ? "default" : "outline"}
                    className="w-full justify-start text-left h-auto p-3"
                    onClick={() => setSelectedVariant(variant.value)}
                  >
                    <div>
                      <div className="font-medium">{variant.label}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {variant.description}
                      </div>
                    </div>
                  </Button>
                </motion.div>
              ))}
            </CardContent>
          </Card>

          {/* Color Schemes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Palette className="w-5 h-5" />
                Colors
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-2">
              {colorSchemes.map((scheme) => (
                <Button
                  key={scheme.value}
                  variant={selectedColorScheme === scheme.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedColorScheme(scheme.value)}
                >
                  {scheme.label}
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Speed Control */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Speed</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-3 gap-2">
              {speeds.map((speed) => (
                <Button
                  key={speed.value}
                  variant={selectedSpeed === speed.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedSpeed(speed.value)}
                >
                  {speed.label}
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Usage Example */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-3 rounded-md text-sm font-mono">
                <div className="text-muted-foreground">{'<AnimatedSVGLogo'}</div>
                <div className="ml-2 text-blue-600">variant="{selectedVariant}"</div>
                <div className="ml-2 text-green-600">colorScheme="{selectedColorScheme}"</div>
                <div className="ml-2 text-purple-600">speed="{selectedSpeed}"</div>
                <div className="ml-2 text-orange-600">width={300}</div>
                <div className="ml-2 text-red-600">height={200}</div>
                <div className="text-muted-foreground">{'/>'}</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[
          { icon: Zap, title: "8 Animations", desc: "Multiple animation variants" },
          { icon: Palette, title: "4 Color Schemes", desc: "Built-in color themes" },
          { icon: Settings, title: "Fully Configurable", desc: "Extensive customization" },
          { icon: Download, title: "Production Ready", desc: "Optimized performance" }
        ].map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <Card className="text-center">
              <CardContent className="pt-6">
                <feature.icon className="w-8 h-8 mx-auto mb-2 text-primary" />
                <h3 className="font-semibold mb-1">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">{feature.desc}</p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

"use client"

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { AnimatedSV<PERSON><PERSON>ogo, AnimatedSVGLogoProps } from './animated-svg-logo'
import { NavbarSVGLogo } from './navbar-svg-logo'
import { LogoContextMenu, useLogoContextMenu } from './index'
import { AnimationVariant, ColorScheme, AnimationSpeed } from './animations'

interface EditableLogoProps extends Omit<AnimatedSVGLogoProps, 'variant' | 'colorScheme' | 'speed'> {
  /** Initial animation variant */
  initialVariant?: AnimationVariant
  /** Initial color scheme */
  initialColorScheme?: ColorScheme
  /** Initial animation speed */
  initialSpeed?: AnimationSpeed
  /** Whether to use navbar-optimized version */
  navbarOptimized?: boolean
  /** Whether to enable context menu editing (development only) */
  enableEditing?: boolean
  /** Whether to prevent layout shifts */
  preventLayoutShifts?: boolean
  /** Callback when variant changes */
  onVariantChange?: (variant: AnimationVariant) => void
  /** Callback when color scheme changes */
  onColorSchemeChange?: (scheme: ColorScheme) => void
  /** Callback when speed changes */
  onSpeedChange?: (speed: AnimationSpeed) => void
  /** Click handler for the logo */
  onClick?: () => void
  /** Additional CSS classes */
  className?: string
}

/**
 * Editable logo component with right-click context menu for development
 * Combines layout-stable logo with interactive editing capabilities
 */
export function EditableLogo({
  width = 40,
  height = 28,
  initialVariant = 'fadeIn',
  initialColorScheme = 'default',
  initialSpeed = 'fast',
  navbarOptimized = false,
  enableEditing = process.env.NODE_ENV === 'development',
  preventLayoutShifts = true,
  onVariantChange,
  onColorSchemeChange,
  onSpeedChange,
  onClick,
  className = "",
  autoPlay = true,
  loop = true,
  ...props
}: EditableLogoProps) {
  // State for current animation settings
  const [currentVariant, setCurrentVariant] = useState<AnimationVariant>(initialVariant)
  const [currentColorScheme, setCurrentColorScheme] = useState<ColorScheme>(initialColorScheme)
  const [currentSpeed, setCurrentSpeed] = useState<AnimationSpeed>(initialSpeed)
  const [isPlaying, setIsPlaying] = useState(autoPlay)

  // Context menu hook
  const logoContextMenu = useLogoContextMenu({
    initialVariant: currentVariant,
    initialColorScheme: currentColorScheme,
    initialSpeed: currentSpeed,
    initialPlaying: isPlaying,
    onVariantChange: useCallback((variant: AnimationVariant) => {
      setCurrentVariant(variant)
      onVariantChange?.(variant)
      // Restart animation when variant changes
      setIsPlaying(false)
      setTimeout(() => setIsPlaying(true), 100)
    }, [onVariantChange]),
    onColorSchemeChange: useCallback((scheme: ColorScheme) => {
      setCurrentColorScheme(scheme)
      onColorSchemeChange?.(scheme)
    }, [onColorSchemeChange]),
    onSpeedChange: useCallback((speed: AnimationSpeed) => {
      setCurrentSpeed(speed)
      onSpeedChange?.(speed)
    }, [onSpeedChange]),
    onPlayToggle: useCallback((playing: boolean) => {
      setIsPlaying(playing)
    }, []),
    onRestart: useCallback(() => {
      setIsPlaying(false)
      setTimeout(() => setIsPlaying(true), 100)
    }, []),
    onExportSVG: useCallback(() => {
      // Export functionality
      console.log('Exporting SVG with settings:', {
        variant: currentVariant,
        colorScheme: currentColorScheme,
        speed: currentSpeed
      })
    }, [currentVariant, currentColorScheme, currentSpeed]),
    onCopyCode: useCallback(() => {
      const codeSnippet = `<AnimatedSVGLogo
  variant="${currentVariant}"
  colorScheme="${currentColorScheme}"
  speed="${currentSpeed}"
  width={${width}}
  height={${height}}
  autoPlay={${isPlaying}}
  loop={${loop}}
/>`
      
      navigator.clipboard.writeText(codeSnippet).then(() => {
        console.log('Code copied to clipboard!')
      }).catch(err => {
        console.error('Failed to copy code:', err)
      })
    }, [currentVariant, currentColorScheme, currentSpeed, width, height, isPlaying, loop]),
    onShowMetrics: useCallback(() => {
      console.log('Logo metrics:', {
        variant: currentVariant,
        colorScheme: currentColorScheme,
        speed: currentSpeed,
        isPlaying,
        dimensions: { width, height }
      })
    }, [currentVariant, currentColorScheme, currentSpeed, isPlaying, width, height])
  })

  // Calculate fixed dimensions for layout stability
  const fixedWidth = typeof width === 'number' ? width : 40
  const fixedHeight = typeof height === 'number' ? height : 28

  const containerStyle = preventLayoutShifts ? {
    width: `${fixedWidth}px`,
    height: `${fixedHeight}px`,
    minWidth: `${fixedWidth}px`,
    minHeight: `${fixedHeight}px`,
    overflow: 'hidden' as const,
    boxSizing: 'border-box' as const
  } : {}



  return (
    <>
      <div
        className={cn(
          "relative inline-block",
          preventLayoutShifts && "transform-gpu",
          enableEditing && "cursor-context-menu",
          onClick && "cursor-pointer",
          className
        )}
        style={containerStyle}
        onClick={onClick}
        onContextMenu={enableEditing ? logoContextMenu.handleContextMenu : undefined}
        title={enableEditing ? "Right-click to edit logo" : undefined}
      >
        {/* Layout-stable logo container */}
        <div 
          className="absolute inset-0 flex items-center justify-center"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%'
          }}
        >
          <AnimatedSVGLogo
            width={width}
            height={height}
            variant={currentVariant}
            colorScheme={currentColorScheme}
            speed={currentSpeed}
            autoPlay={isPlaying}
            loop={loop}
            className="max-w-full max-h-full"
            {...props}
          />
        </div>

        {/* Development indicator */}
        {enableEditing && process.env.NODE_ENV === 'development' && (
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full opacity-50" />
        )}
      </div>

      {/* Context Menu (Development Only) */}
      {enableEditing && (
        <LogoContextMenu {...logoContextMenu.contextMenuProps} />
      )}
    </>
  )
}

/**
 * Navbar-specific editable logo
 * Optimized for navigation bar use with context menu editing
 */
export function EditableNavbarLogo({
  className = "",
  ...props
}: Omit<EditableLogoProps, 'navbarOptimized' | 'preventLayoutShifts'>) {
  return (
    <EditableLogo
      navbarOptimized={true}
      preventLayoutShifts={true}
      initialVariant="fadeIn"
      initialSpeed="fast"
      className={cn("navbar-logo", className)}
      {...props}
    />
  )
}

/**
 * Compact editable logo for mobile navbar
 */
export function EditableCompactLogo({
  className = "",
  ...props
}: Omit<EditableLogoProps, 'width' | 'height' | 'navbarOptimized'>) {
  return (
    <EditableLogo
      width={32}
      height={22}
      navbarOptimized={true}
      preventLayoutShifts={true}
      initialVariant="fadeIn"
      initialSpeed="fast"
      className={cn("compact-logo md:hidden", className)}
      {...props}
    />
  )
}

/**
 * Hero section editable logo
 * Allows dramatic animations with editing capabilities
 */
export function EditableHeroLogo({
  className = "",
  initialVariant = 'combined',
  initialSpeed = 'normal',
  ...props
}: EditableLogoProps) {
  return (
    <EditableLogo
      navbarOptimized={false}
      preventLayoutShifts={false} // Allow layout shifts for hero sections
      initialVariant={initialVariant}
      initialSpeed={initialSpeed}
      className={cn("hero-logo", className)}
      {...props}
    />
  )
}

/**
 * Simple logo with context menu but no layout stability constraints
 * For use in areas where layout shifts are acceptable
 */
export function EditableSimpleLogo({
  className = "",
  ...props
}: EditableLogoProps) {
  return (
    <EditableLogo
      navbarOptimized={false}
      preventLayoutShifts={false}
      className={cn("simple-logo", className)}
      {...props}
    />
  )
}

/**
 * Hook to create an editable logo with custom settings
 */
export function useEditableLogo(
  initialSettings: {
    variant?: AnimationVariant
    colorScheme?: ColorScheme
    speed?: AnimationSpeed
  } = {}
) {
  const [settings, setSettings] = useState({
    variant: initialSettings.variant || 'fadeIn' as AnimationVariant,
    colorScheme: initialSettings.colorScheme || 'default' as ColorScheme,
    speed: initialSettings.speed || 'fast' as AnimationSpeed
  })

  const updateSettings = useCallback((newSettings: Partial<typeof settings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }))
  }, [])

  return {
    settings,
    updateSettings,
    EditableLogo: useCallback((props: Omit<EditableLogoProps, 'initialVariant' | 'initialColorScheme' | 'initialSpeed'>) => (
      <EditableLogo
        initialVariant={settings.variant}
        initialColorScheme={settings.colorScheme}
        initialSpeed={settings.speed}
        onVariantChange={(variant) => updateSettings({ variant })}
        onColorSchemeChange={(colorScheme) => updateSettings({ colorScheme })}
        onSpeedChange={(speed) => updateSettings({ speed })}
        {...props}
      />
    ), [settings, updateSettings])
  }
}

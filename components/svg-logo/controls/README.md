# Fine-Grained Logo Animation Controls

A modular system for advanced logo animation controls with sliders, inputs, and real-time preview.

## Overview

The fine-grained controls system provides detailed control over logo animations through a clean, modular interface. It includes:

- **Timing Controls**: Duration, stagger, and animation offset
- **Transform Controls**: Rotation, scale X/Y
- **Appearance Controls**: Opacity and color intensity
- **Easing Controls**: Custom cubic bezier curves with presets

## Components

### FineGrainedPanel
Main side panel component that contains all control sections.

```tsx
import { FineGrainedPanel, DEFAULT_FINE_GRAINED_CONTROLS } from '@/components/svg-logo/controls'

const [controls, setControls] = useState(DEFAULT_FINE_GRAINED_CONTROLS)
const [showPanel, setShowPanel] = useState(false)

<FineGrainedPanel
  isOpen={showPanel}
  onClose={() => setShowPanel(false)}
  controls={controls}
  onControlChange={(key, value) => setControls(prev => ({ ...prev, [key]: value }))}
  onReset={() => setControls(DEFAULT_FINE_GRAINED_CONTROLS)}
  position={{ x: 100, y: 100 }}
/>
```

### Individual Control Sections
Use individual sections for custom layouts:

```tsx
import { TimingControls, TransformControls } from '@/components/svg-logo/controls'

<TimingControls 
  controls={controls}
  onControlChange={handleControlChange}
/>
<TransformControls 
  controls={controls}
  onControlChange={handleControlChange}
/>
```

### SliderControl
Reusable slider component:

```tsx
import { SliderControl } from '@/components/svg-logo/controls'

<SliderControl
  label="Duration"
  value={2.5}
  onChange={(value) => console.log(value)}
  min={0.5}
  max={10}
  step={0.1}
  unit="s"
/>
```

## Control Types

### FineGrainedControls Interface
```typescript
interface FineGrainedControls {
  // Timing
  duration: number          // 0.5 - 10s
  stagger: number          // 0 - 2s
  animationOffset: number  // -100% - 100%
  
  // Transform
  rotationAngle: number    // 0° - 360°
  scaleX: number          // 0.1x - 3x
  scaleY: number          // 0.1x - 3x
  
  // Appearance
  opacity: number         // 0% - 100%
  colorIntensity: number  // 0% - 200%
  
  // Easing
  customEasing: [number, number, number, number]  // Cubic bezier
}
```

### Default Values
```typescript
const DEFAULT_FINE_GRAINED_CONTROLS = {
  duration: 2.5,
  stagger: 0.1,
  animationOffset: 0,
  rotationAngle: 0,
  scaleX: 1,
  scaleY: 1,
  opacity: 100,
  colorIntensity: 100,
  customEasing: [0.25, 0.1, 0.25, 1]
}
```

## Integration with Context Menu

The fine-grained controls integrate seamlessly with the existing logo context menu:

```tsx
import { LogoContextMenu, FineGrainedControls } from '@/components/svg-logo'

<LogoContextMenu
  // ... other props
  fineGrainedControls={controls}
  onFineGrainedControlChange={handleControlChange}
  onResetControls={handleReset}
/>
```

## Styling and Customization

All components accept a `className` prop for custom styling:

```tsx
<TimingControls 
  controls={controls}
  onControlChange={handleControlChange}
  className="custom-timing-controls"
/>
```

## Easing Presets

The easing controls include common presets:

- **ease**: [0.25, 0.1, 0.25, 1]
- **ease-in**: [0.42, 0, 1, 1]
- **ease-out**: [0, 0, 0.58, 1]
- **ease-in-out**: [0.42, 0, 0.58, 1]
- **bounce**: [0.68, -0.55, 0.265, 1.55]
- **elastic**: [0.175, 0.885, 0.32, 1.275]

## Demo

See `FineGrainedControlsDemo` for a complete implementation example:

```tsx
import { FineGrainedControlsDemo } from '@/components/svg-logo'

<FineGrainedControlsDemo />
```

## File Structure

```
components/svg-logo/controls/
├── index.ts                    # Main exports
├── types.ts                    # TypeScript interfaces
├── fine-grained-panel.tsx      # Main panel component
├── timing-controls.tsx         # Timing section
├── transform-controls.tsx      # Transform section
├── appearance-controls.tsx     # Appearance section
├── easing-controls.tsx         # Easing section
├── slider-control.tsx          # Reusable slider
└── README.md                   # This file
```

## Best Practices

1. **Use default values**: Start with `DEFAULT_FINE_GRAINED_CONTROLS`
2. **Modular approach**: Import only the components you need
3. **Consistent styling**: Use the provided `className` props
4. **Real-time updates**: Controls update immediately for live preview
5. **Reset functionality**: Always provide a way to reset to defaults

"use client"

import React from 'react'
import { Slider } from '@/components/ui/slider'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { SliderControlProps } from './types'

export function SliderControl({
  label,
  value,
  onChange,
  min,
  max,
  step,
  unit = '',
  className
}: SliderControlProps) {
  const formatValue = (val: number) => {
    if (unit === '%' || unit === '°') {
      return `${val.toFixed(0)}${unit}`
    }
    if (unit === 's') {
      return `${val.toFixed(1)}${unit}`
    }
    if (unit === 'x') {
      return `${val.toFixed(2)}${unit}`
    }
    return val.toString()
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <Label className="text-xs font-medium">{label}</Label>
        <span className="text-xs text-muted-foreground font-mono">
          {formatValue(value)}
        </span>
      </div>
      <Slider
        value={[value]}
        onValueChange={([newValue]) => onChange(newValue)}
        min={min}
        max={max}
        step={step}
        className="w-full"
      />
    </div>
  )
}

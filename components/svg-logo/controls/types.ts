// Fine-grained control types and interfaces

export interface FineGrainedControls {
  // Timing controls
  duration: number
  stagger: number
  animationOffset: number
  
  // Transform controls
  rotationAngle: number
  scaleX: number
  scaleY: number
  
  // Appearance controls
  opacity: number
  colorIntensity: number
  
  // Custom easing (cubic bezier)
  customEasing: [number, number, number, number]
}

export interface ControlSectionProps {
  controls: FineGrainedControls
  onControlChange: (key: keyof FineGrainedControls, value: number | [number, number, number, number]) => void
  className?: string
}

export interface SliderControlProps {
  label: string
  value: number
  onChange: (value: number) => void
  min: number
  max: number
  step: number
  unit?: string
  className?: string
}

export interface NumberInputProps {
  label: string
  value: number
  onChange: (value: number) => void
  min?: number
  max?: number
  step?: number
  className?: string
}

// Default values for fine-grained controls
export const DEFAULT_FINE_GRAINED_CONTROLS: FineGrainedControls = {
  duration: 2.5,
  stagger: 0.1,
  animationOffset: 0,
  rotationAngle: 0,
  scaleX: 1,
  scaleY: 1,
  opacity: 100,
  colorIntensity: 100,
  customEasing: [0.25, 0.1, 0.25, 1]
}

// Control ranges and configurations
export const CONTROL_CONFIGS = {
  duration: { min: 0.5, max: 10, step: 0.1, unit: 's' },
  stagger: { min: 0, max: 2, step: 0.05, unit: 's' },
  animationOffset: { min: -100, max: 100, step: 5, unit: '%' },
  rotationAngle: { min: 0, max: 360, step: 5, unit: '°' },
  scaleX: { min: 0.1, max: 3, step: 0.05, unit: 'x' },
  scaleY: { min: 0.1, max: 3, step: 0.05, unit: 'x' },
  opacity: { min: 0, max: 100, step: 5, unit: '%' },
  colorIntensity: { min: 0, max: 200, step: 10, unit: '%' }
} as const

// Control descriptions for tooltips
export const CONTROL_DESCRIPTIONS = {
  duration: 'Total animation duration',
  stagger: 'Delay between element animations',
  animationOffset: 'Animation start position offset',
  rotationAngle: 'Rotation angle for transform animations',
  scaleX: 'Horizontal scale factor',
  scaleY: 'Vertical scale factor',
  opacity: 'Element opacity level',
  colorIntensity: 'Color saturation and brightness',
  customEasing: 'Custom cubic bezier easing curve'
} as const

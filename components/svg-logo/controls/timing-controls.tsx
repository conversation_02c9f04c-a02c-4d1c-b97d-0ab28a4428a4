"use client"

import React from 'react'
import { Timer } from 'lucide-react'
import { cn } from '@/lib/utils'
import { SliderControl } from './slider-control'
import { ControlSectionProps, CONTROL_CONFIGS } from './types'

export function TimingControls({ 
  controls, 
  onControlChange, 
  className 
}: ControlSectionProps) {
  return (
    <div className={cn("p-3 border-b", className)}>
      <div className="flex items-center gap-2 mb-3">
        <Timer className="w-4 h-4" />
        <span className="text-xs font-medium text-muted-foreground">Timing</span>
      </div>
      
      <div className="space-y-4">
        <SliderControl
          label="Duration"
          value={controls.duration}
          onChange={(value) => onControlChange('duration', value)}
          min={CONTROL_CONFIGS.duration.min}
          max={CONTROL_CONFIGS.duration.max}
          step={CONTROL_CONFIGS.duration.step}
          unit={CONTROL_CONFIGS.duration.unit}
        />

        <SliderControl
          label="Stagger"
          value={controls.stagger}
          onChange={(value) => onControlChange('stagger', value)}
          min={CONTROL_CONFIGS.stagger.min}
          max={CONTROL_CONFIGS.stagger.max}
          step={CONTROL_CONFIGS.stagger.step}
          unit={CONTROL_CONFIGS.stagger.unit}
        />

        <SliderControl
          label="Offset"
          value={controls.animationOffset}
          onChange={(value) => onControlChange('animationOffset', value)}
          min={CONTROL_CONFIGS.animationOffset.min}
          max={CONTROL_CONFIGS.animationOffset.max}
          step={CONTROL_CONFIGS.animationOffset.step}
          unit={CONTROL_CONFIGS.animationOffset.unit}
        />
      </div>
    </div>
  )
}

"use client"

import React from 'react'
import { Paintbrush } from 'lucide-react'
import { cn } from '@/lib/utils'
import { SliderControl } from './slider-control'
import { ControlSectionProps, CONTROL_CONFIGS } from './types'

export function AppearanceControls({ 
  controls, 
  onControlChange, 
  className 
}: ControlSectionProps) {
  return (
    <div className={cn("p-3 border-b", className)}>
      <div className="flex items-center gap-2 mb-3">
        <Paintbrush className="w-4 h-4" />
        <span className="text-xs font-medium text-muted-foreground">Appearance</span>
      </div>
      
      <div className="space-y-4">
        <SliderControl
          label="Opacity"
          value={controls.opacity}
          onChange={(value) => onControlChange('opacity', value)}
          min={CONTROL_CONFIGS.opacity.min}
          max={CONTROL_CONFIGS.opacity.max}
          step={CONTROL_CONFIGS.opacity.step}
          unit={CONTROL_CONFIGS.opacity.unit}
        />

        <SliderControl
          label="Color Intensity"
          value={controls.colorIntensity}
          onChange={(value) => onControlChange('colorIntensity', value)}
          min={CONTROL_CONFIGS.colorIntensity.min}
          max={CONTROL_CONFIGS.colorIntensity.max}
          step={CONTROL_CONFIGS.colorIntensity.step}
          unit={CONTROL_CONFIGS.colorIntensity.unit}
        />
      </div>
    </div>
  )
}

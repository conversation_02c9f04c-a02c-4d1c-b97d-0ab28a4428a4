"use client"

import React from 'react'
import { Move } from 'lucide-react'
import { cn } from '@/lib/utils'
import { SliderControl } from './slider-control'
import { ControlSectionProps, CONTROL_CONFIGS } from './types'

export function TransformControls({ 
  controls, 
  onControlChange, 
  className 
}: ControlSectionProps) {
  return (
    <div className={cn("p-3 border-b", className)}>
      <div className="flex items-center gap-2 mb-3">
        <Move className="w-4 h-4" />
        <span className="text-xs font-medium text-muted-foreground">Transform</span>
      </div>
      
      <div className="space-y-4">
        <SliderControl
          label="Rotation"
          value={controls.rotationAngle}
          onChange={(value) => onControlChange('rotationAngle', value)}
          min={CONTROL_CONFIGS.rotationAngle.min}
          max={CONTROL_CONFIGS.rotationAngle.max}
          step={CONTROL_CONFIGS.rotationAngle.step}
          unit={CONTROL_CONFIGS.rotationAngle.unit}
        />

        <SliderControl
          label="Scale X"
          value={controls.scaleX}
          onChange={(value) => onControlChange('scaleX', value)}
          min={CONTROL_CONFIGS.scaleX.min}
          max={CONTROL_CONFIGS.scaleX.max}
          step={CONTROL_CONFIGS.scaleX.step}
          unit={CONTROL_CONFIGS.scaleX.unit}
        />

        <SliderControl
          label="Scale Y"
          value={controls.scaleY}
          onChange={(value) => onControlChange('scaleY', value)}
          min={CONTROL_CONFIGS.scaleY.min}
          max={CONTROL_CONFIGS.scaleY.max}
          step={CONTROL_CONFIGS.scaleY.step}
          unit={CONTROL_CONFIGS.scaleY.unit}
        />
      </div>
    </div>
  )
}

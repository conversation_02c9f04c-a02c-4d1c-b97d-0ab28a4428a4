"use client"

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, RefreshCw, Gauge } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { TimingControls } from './timing-controls'
import { TransformControls } from './transform-controls'
import { AppearanceControls } from './appearance-controls'
import { EasingControls } from './easing-controls'
import { FineGrainedControls } from './types'

interface FineGrainedPanelProps {
  isOpen: boolean
  onClose: () => void
  controls: FineGrainedControls
  onControlChange: (key: keyof FineGrainedControls, value: number | [number, number, number, number]) => void
  onReset: () => void
  position: { x: number; y: number }
  className?: string
}

export function FineGrainedPanel({
  isOpen,
  onClose,
  controls,
  onControlChange,
  onReset,
  position,
  className
}: FineGrainedPanelProps) {
  // Adjust position to keep panel in viewport
  const getAdjustedPosition = () => {
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const panelWidth = 400
    const panelHeight = 600

    let { x, y } = position

    // Position to the right of the main menu
    x = x + 340

    // Adjust if panel would go off screen
    if (x + panelWidth > viewportWidth) {
      x = position.x - panelWidth - 10 // Position to the left instead
    }

    // Adjust vertical position
    if (y + panelHeight > viewportHeight) {
      y = viewportHeight - panelHeight - 10
    }

    return { 
      x: Math.max(10, x), 
      y: Math.max(10, y) 
    }
  }

  const adjustedPosition = getAdjustedPosition()

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, x: 20, scale: 0.95 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: 20, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className={cn(
            "fixed z-950 bg-background border rounded-lg shadow-lg",
            "min-w-[320px] max-w-[400px]",
            className
          )}
          style={{
            left: adjustedPosition.x,
            top: adjustedPosition.y,
            maxHeight: '80vh',
            overflowY: 'auto'
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b bg-muted/50 sticky top-0 z-10">
            <div className="flex items-center gap-2">
              <Gauge className="w-4 h-4 text-primary" />
              <span className="font-semibold text-sm">Fine Controls</span>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={onReset}
                className="h-6 w-6 p-0"
                title="Reset to defaults"
              >
                <RefreshCw className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-6 w-6 p-0"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>

          {/* Control Sections */}
          <div className="divide-y">
            <TimingControls 
              controls={controls}
              onControlChange={onControlChange}
            />
            
            <TransformControls 
              controls={controls}
              onControlChange={onControlChange}
            />
            
            <AppearanceControls 
              controls={controls}
              onControlChange={onControlChange}
            />
            
            <EasingControls 
              controls={controls}
              onControlChange={onControlChange}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

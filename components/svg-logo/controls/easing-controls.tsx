"use client"

import React from 'react'
import { RotateCw } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { ControlSectionProps } from './types'

// Common easing presets
const EASING_PRESETS = {
  'ease': [0.25, 0.1, 0.25, 1],
  'ease-in': [0.42, 0, 1, 1],
  'ease-out': [0, 0, 0.58, 1],
  'ease-in-out': [0.42, 0, 0.58, 1],
  'bounce': [0.68, -0.55, 0.265, 1.55],
  'elastic': [0.175, 0.885, 0.32, 1.275]
} as const

export function EasingControls({ 
  controls, 
  onControlChange, 
  className 
}: ControlSectionProps) {
  const handleEasingChange = (index: number, value: string) => {
    const newEasing = [...controls.customEasing] as [number, number, number, number]
    newEasing[index] = parseFloat(value) || 0
    onControlChange('customEasing', newEasing)
  }

  const applyPreset = (preset: keyof typeof EASING_PRESETS) => {
    onControlChange('customEasing', [...EASING_PRESETS[preset]] as [number, number, number, number])
  }

  return (
    <div className={cn("p-3", className)}>
      <div className="flex items-center gap-2 mb-3">
        <RotateCw className="w-4 h-4" />
        <span className="text-xs font-medium text-muted-foreground">Custom Easing</span>
      </div>
      
      <div className="space-y-3">
        {/* Easing Presets */}
        <div>
          <Label className="text-xs mb-2 block">Presets</Label>
          <div className="grid grid-cols-2 gap-1">
            {Object.keys(EASING_PRESETS).map((preset) => (
              <Button
                key={preset}
                variant="outline"
                size="sm"
                onClick={() => applyPreset(preset as keyof typeof EASING_PRESETS)}
                className="text-xs h-6 capitalize"
              >
                {preset}
              </Button>
            ))}
          </div>
        </div>

        {/* Custom Values */}
        <div>
          <Label className="text-xs mb-2 block">Custom Values</Label>
          <div className="grid grid-cols-2 gap-2">
            {controls.customEasing.map((value, index) => (
              <div key={index} className="space-y-1">
                <Label className="text-xs">P{index + 1}</Label>
                <Input
                  type="number"
                  value={value.toFixed(2)}
                  onChange={(e) => handleEasingChange(index, e.target.value)}
                  min={-2}
                  max={2}
                  step={0.01}
                  className="h-7 text-xs"
                />
              </div>
            ))}
          </div>
        </div>
        
        {/* Preview */}
        <div className="mt-2 p-2 bg-muted/50 rounded text-xs text-muted-foreground">
          <div className="font-mono">
            cubic-bezier({controls.customEasing.map(v => v.toFixed(2)).join(', ')})
          </div>
        </div>
      </div>
    </div>
  )
}

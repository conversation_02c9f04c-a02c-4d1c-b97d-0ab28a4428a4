"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Play, Pause, RotateCcw, Zap, Palette, Settings } from 'lucide-react'
import { AnimatedSVGLogo } from './animated-svg-logo'
import { AnimationVariant, ColorScheme, AnimationSpeed } from './animations'

interface AnimationDemo {
  variant: AnimationVariant
  name: string
  description: string
  category: 'basic' | 'advanced' | 'special'
  layoutStable: boolean
  recommended: string[]
}

const ANIMATION_DEMOS: AnimationDemo[] = [
  {
    variant: 'fadeIn',
    name: 'Fade In',
    description: 'Simple opacity fade-in animation. Perfect for navbar use.',
    category: 'basic',
    layoutStable: true,
    recommended: ['navbar', 'cards', 'modals']
  },
  {
    variant: 'pathDraw',
    name: 'Path Draw',
    description: 'Progressive SVG path drawing animation. Great for hero sections.',
    category: 'basic',
    layoutStable: true,
    recommended: ['hero', 'landing', 'showcase']
  },
  {
    variant: 'staggeredReveal',
    name: 'Staggered Reveal',
    description: 'Elements appear with staggered timing. Layout-stable version.',
    category: 'basic',
    layoutStable: true,
    recommended: ['lists', 'grids', 'navigation']
  },
  {
    variant: 'colorWave',
    name: 'Color Wave',
    description: 'Animated color transitions with hue rotation. Infinite loop.',
    category: 'advanced',
    layoutStable: true,
    recommended: ['hero', 'accent', 'branding']
  },
  {
    variant: 'glow',
    name: 'Glow Effect',
    description: 'Pulsing glow effect with drop shadows. Infinite animation.',
    category: 'advanced',
    layoutStable: true,
    recommended: ['hero', 'cta', 'highlights']
  },
  {
    variant: 'breathe',
    name: 'Breathe',
    description: 'Gentle breathing motion using brightness. Layout-stable.',
    category: 'advanced',
    layoutStable: true,
    recommended: ['ambient', 'background', 'subtle']
  },
  {
    variant: 'morphing',
    name: 'Morphing',
    description: 'Scale and rotation transformations. May cause layout shifts.',
    category: 'advanced',
    layoutStable: false,
    recommended: ['hero', 'showcase', 'transitions']
  },
  {
    variant: 'rotate',
    name: 'Rotate',
    description: 'Spinning rotation animation with scale changes.',
    category: 'advanced',
    layoutStable: false,
    recommended: ['loading', 'transitions', 'effects']
  },
  {
    variant: 'combined',
    name: 'Combined',
    description: 'Multiple effects: path drawing + scaling + rotation + glow.',
    category: 'advanced',
    layoutStable: false,
    recommended: ['hero', 'showcase', 'premium']
  },
  {
    variant: 'loading',
    name: 'Loading',
    description: 'Continuous rotation with opacity/scale changes for loading states.',
    category: 'special',
    layoutStable: false,
    recommended: ['loading', 'spinners', 'progress']
  },
  {
    variant: 'error',
    name: 'Error',
    description: 'Horizontal shake motion for error states.',
    category: 'special',
    layoutStable: true,
    recommended: ['errors', 'validation', 'alerts']
  },
  {
    variant: 'success',
    name: 'Success',
    description: 'Scale bounce with green glow for success states.',
    category: 'special',
    layoutStable: false,
    recommended: ['success', 'completion', 'celebration']
  }
]

export function AllAnimationsDemo() {
  const [isPlaying, setIsPlaying] = useState(true)
  const [colorScheme, setColorScheme] = useState<ColorScheme>('default')
  const [speed, setSpeed] = useState<AnimationSpeed>('normal')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const filteredAnimations = selectedCategory === 'all' 
    ? ANIMATION_DEMOS 
    : ANIMATION_DEMOS.filter(demo => demo.category === selectedCategory)

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'advanced': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'special': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const restartAnimations = () => {
    setIsPlaying(false)
    setTimeout(() => setIsPlaying(true), 100)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            All Logo Animations Demo
          </CardTitle>
          <CardDescription>
            Comprehensive showcase of all available logo animation variants with controls and recommendations.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Global Controls */}
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
              >
                {isPlaying ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
                {isPlaying ? 'Pause All' : 'Play All'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={restartAnimations}
              >
                <RotateCcw className="w-4 h-4 mr-1" />
                Restart All
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              <span className="text-sm font-medium">Color:</span>
              {(['default', 'brand', 'monochrome', 'rainbow'] as ColorScheme[]).map((scheme) => (
                <Button
                  key={scheme}
                  variant={colorScheme === scheme ? "default" : "outline"}
                  size="sm"
                  onClick={() => setColorScheme(scheme)}
                  className="text-xs"
                >
                  {scheme}
                </Button>
              ))}
            </div>

            <div className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              <span className="text-sm font-medium">Speed:</span>
              {(['slow', 'normal', 'fast'] as AnimationSpeed[]).map((spd) => (
                <Button
                  key={spd}
                  variant={speed === spd ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSpeed(spd)}
                  className="text-xs"
                >
                  {spd}
                </Button>
              ))}
            </div>
          </div>

          {/* Category Tabs */}
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All ({ANIMATION_DEMOS.length})</TabsTrigger>
              <TabsTrigger value="basic">Basic ({ANIMATION_DEMOS.filter(d => d.category === 'basic').length})</TabsTrigger>
              <TabsTrigger value="advanced">Advanced ({ANIMATION_DEMOS.filter(d => d.category === 'advanced').length})</TabsTrigger>
              <TabsTrigger value="special">Special ({ANIMATION_DEMOS.filter(d => d.category === 'special').length})</TabsTrigger>
            </TabsList>

            <TabsContent value={selectedCategory} className="mt-6">
              {/* Animations Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredAnimations.map((demo) => (
                  <Card key={demo.variant} className="relative overflow-hidden">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{demo.name}</CardTitle>
                        <div className="flex gap-1">
                          <Badge className={getCategoryColor(demo.category)}>
                            {demo.category}
                          </Badge>
                          {demo.layoutStable ? (
                            <Badge variant="secondary" className="text-green-600">
                              Stable
                            </Badge>
                          ) : (
                            <Badge variant="secondary" className="text-orange-600">
                              Shifts
                            </Badge>
                          )}
                        </div>
                      </div>
                      <CardDescription className="text-sm">
                        {demo.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Logo Display */}
                      <div className="flex justify-center p-6 border-2 border-dashed border-muted rounded-lg bg-muted/20">
                        <AnimatedSVGLogo
                          width={60}
                          height={42}
                          variant={demo.variant}
                          colorScheme={colorScheme}
                          speed={speed}
                          autoPlay={isPlaying}
                          loop={true}
                          key={`${demo.variant}-${isPlaying}-${colorScheme}-${speed}`}
                        />
                      </div>

                      {/* Recommended Use Cases */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">Recommended for:</h4>
                        <div className="flex flex-wrap gap-1">
                          {demo.recommended.map((use) => (
                            <Badge key={use} variant="outline" className="text-xs">
                              {use}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {/* Code Example */}
                      <div className="p-3 bg-muted rounded-lg">
                        <code className="text-xs">
                          {`<AnimatedSVGLogo variant="${demo.variant}" />`}
                        </code>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          {/* Usage Guidelines */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Usage Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-green-600">✅ Layout Stable (Recommended for Navbar)</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• fadeIn - Simple and fast</li>
                    <li>• pathDraw - Elegant drawing effect</li>
                    <li>• staggeredReveal - Smooth reveal</li>
                    <li>• colorWave - Dynamic colors</li>
                    <li>• glow - Subtle emphasis</li>
                    <li>• breathe - Ambient animation</li>
                    <li>• error - Shake feedback</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-orange-600">⚠️ May Cause Layout Shifts</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• morphing - Scale/rotate effects</li>
                    <li>• rotate - Spinning animation</li>
                    <li>• combined - Multiple transforms</li>
                    <li>• loading - Continuous motion</li>
                    <li>• success - Bounce effect</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-600">💡 Best Practices</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Use 'fast' speed for navbar</li>
                    <li>• Use 'normal' for hero sections</li>
                    <li>• Use 'slow' for emphasis</li>
                    <li>• Test on mobile devices</li>
                    <li>• Consider reduced motion preferences</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight, Code, Database, Globe, Lightbulb, Smartphone, Wand2 } from "lucide-react"

export function Services() {
  const services = [
    {
      icon: <Globe className="h-12 w-12 text-primary" />,
      title: "Web Development",
      description: "Custom web applications that streamline processes and enhance efficiency for your business.",
      link: "/services/web-development",
    },
    {
      icon: <Smartphone className="h-12 w-12 text-primary" />,
      title: "Mobile Applications",
      description: "Native and cross-platform mobile apps that provide seamless experiences across devices.",
      link: "/services/mobile-applications",
    },
    {
      icon: <Database className="h-12 w-12 text-primary" />,
      title: "Data Analytics",
      description: "Sophisticated data tools that empower organizations with meaningful reports and forecasting.",
      link: "/services/data-analytics",
    },
    {
      icon: <Lightbulb className="h-12 w-12 text-primary" />,
      title: "AI Solutions",
      description: "Artificial intelligence integration to automate processes and provide intelligent insights.",
      link: "/services/ai-solutions",
    },
    {
      icon: <Code className="h-12 w-12 text-primary" />,
      title: "Custom Software",
      description: "Tailored software solutions designed to address your specific business challenges.",
      link: "/services/custom-software",
    },
    {
      icon: <Wand2 className="h-12 w-12 text-primary" />,
      title: "UI/UX Design",
      description: "Intuitive and engaging user interfaces that enhance user experience and satisfaction.",
      link: "/services/ui-ux-design",
    },
  ]

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our <span className="gradient-text">Services</span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We specialize in creating custom solutions that drive success while fostering a culture of creativity,
            collaboration, and excellence.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true, margin: "-100px" }}
              className="group"
            >
              <div className="bg-muted/30 p-8 rounded-lg border border-border/50 h-full flex flex-col transition-all duration-300 group-hover:border-primary/50 group-hover:shadow-lg">
                <div className="mb-6">{service.icon}</div>
                <h3 className="text-xl font-bold mb-3">{service.title}</h3>
                <p className="text-muted-foreground mb-6 flex-grow">{service.description}</p>
                <Button asChild variant="ghost" className="gap-2 justify-start p-0 hover:bg-transparent">
                  <Link href={service.link}>
                    Learn more <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

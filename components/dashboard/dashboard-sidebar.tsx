"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  BarChart3,
  Code,
  Cog,
  FileText,
  Home,
  LayoutDashboard,
  LogOut,
  MessageSquare,
  PanelLeft,
  Sparkles,
} from "lucide-react"
import { cn } from "@/lib/utils"

interface DashboardSidebarProps {
  closeSidebar: () => void
}

export function DashboardSidebar({ closeSidebar }: DashboardSidebarProps) {
  const pathname = usePathname()

  const routes = [
    {
      label: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      href: "/dashboard",
      active: pathname === "/dashboard",
    },
    {
      label: "Projects",
      icon: <PanelLeft className="h-5 w-5" />,
      href: "/dashboard/projects",
      active: pathname === "/dashboard/projects",
    },
    {
      label: "AI Tools",
      icon: <Sparkles className="h-5 w-5" />,
      href: "/dashboard/ai-tools",
      active: pathname === "/dashboard/ai-tools",
    },
    {
      label: "Analytics",
      icon: <BarChart3 className="h-5 w-5" />,
      href: "/dashboard/analytics",
      active: pathname === "/dashboard/analytics",
    },
    {
      label: "Chat",
      icon: <MessageSquare className="h-5 w-5" />,
      href: "/dashboard/chat",
      active: pathname === "/dashboard/chat",
    },
    {
      label: "Code Generator",
      icon: <Code className="h-5 w-5" />,
      href: "/dashboard/code",
      active: pathname === "/dashboard/code",
    },
    {
      label: "Forms",
      icon: <FileText className="h-5 w-5" />,
      href: "/dashboard/forms",
      active: pathname === "/dashboard/forms",
    },
    {
      label: "Settings",
      icon: <Cog className="h-5 w-5" />,
      href: "/dashboard/settings",
      active: pathname === "/dashboard/settings",
    },
  ]

  return (
    <div className="flex flex-col h-full">
      <div className="px-4 py-6">
        <Link href="/" className="flex items-center gap-2">
          <div className="relative h-8 w-8 rounded-full overflow-hidden">
            <div className="absolute inset-0 animated-gradient rounded-full"></div>
            <div className="absolute inset-1 bg-background rounded-full flex items-center justify-center">
              <span className="text-primary font-bold">S</span>
            </div>
          </div>
          <span className="font-bold text-xl">Soimagine</span>
        </Link>
      </div>
      <ScrollArea className="flex-1 px-2">
        <div className="space-y-1 py-2">
          {routes.map((route) => (
            <Button
              key={route.href}
              variant={route.active ? "secondary" : "ghost"}
              className={cn("w-full justify-start", route.active && "bg-secondary")}
              asChild
              onClick={closeSidebar}
            >
              <Link href={route.href}>
                {route.icon}
                <span className="ml-2">{route.label}</span>
              </Link>
            </Button>
          ))}
        </div>
      </ScrollArea>
      <div className="px-2 py-4 border-t border-border/50">
        <Button variant="ghost" className="w-full justify-start text-muted-foreground" asChild>
          <Link href="/">
            <Home className="h-5 w-5 mr-2" />
            Back to Home
          </Link>
        </Button>
        <Button variant="ghost" className="w-full justify-start text-muted-foreground">
          <LogOut className="h-5 w-5 mr-2" />
          Logout
        </Button>
      </div>
    </div>
  )
}

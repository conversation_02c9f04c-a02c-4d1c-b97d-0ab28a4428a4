'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Trophy, 
  Target, 
  Zap, 
  TrendingUp, 
  Coins, 
  Star,
  Users,
  Calendar
} from 'lucide-react'
import { useUserProgress } from '@/hooks/use-user-progress'
import { useCredits } from '@/hooks/use-credits'
import { cn } from '@/lib/utils'

interface StatCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  trend?: {
    value: number
    label: string
    positive: boolean
  }
  color?: string
  delay?: number
}

function StatCard({ title, value, subtitle, icon, trend, color = "primary", delay = 0 }: StatCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
    >
      <Card className="relative overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {title}
              </p>
              <div className="flex items-baseline gap-2">
                <p className="text-2xl font-bold">
                  {value}
                </p>
                {subtitle && (
                  <p className="text-sm text-muted-foreground">
                    {subtitle}
                  </p>
                )}
              </div>
              {trend && (
                <div className="flex items-center gap-1">
                  <TrendingUp 
                    className={cn(
                      "h-3 w-3",
                      trend.positive ? "text-green-600" : "text-red-600"
                    )} 
                  />
                  <span className={cn(
                    "text-xs font-medium",
                    trend.positive ? "text-green-600" : "text-red-600"
                  )}>
                    {trend.positive ? '+' : ''}{trend.value}% {trend.label}
                  </span>
                </div>
              )}
            </div>
            <div className={cn(
              "p-3 rounded-full",
              color === "primary" && "bg-primary/10 text-primary",
              color === "green" && "bg-green-100 text-green-600",
              color === "blue" && "bg-blue-100 text-blue-600",
              color === "purple" && "bg-purple-100 text-purple-600",
              color === "orange" && "bg-orange-100 text-orange-600"
            )}>
              {icon}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export function StatsOverview() {
  const { progress, loading: progressLoading } = useUserProgress()
  const { balance, loading: creditsLoading } = useCredits()

  if (progressLoading || creditsLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-muted rounded w-1/2"></div>
                <div className="h-8 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const stats = [
    {
      title: "Total Credits",
      value: balance?.totalCredits || 0,
      subtitle: "available",
      icon: <Coins className="h-5 w-5" />,
      color: "green" as const,
      trend: {
        value: 12,
        label: "this week",
        positive: true
      }
    },
    {
      title: "Experience Points",
      value: progress?.totalXp || 0,
      subtitle: `Level ${progress?.currentLevel || 1}`,
      icon: <Star className="h-5 w-5" />,
      color: "purple" as const,
      trend: {
        value: 8,
        label: "this week",
        positive: true
      }
    },
    {
      title: "Challenges Completed",
      value: progress?.challengesCompleted || 0,
      subtitle: "total",
      icon: <Trophy className="h-5 w-5" />,
      color: "blue" as const,
      trend: {
        value: 25,
        label: "this month",
        positive: true
      }
    },
    {
      title: "Current Streak",
      value: progress?.currentStreak || 0,
      subtitle: "days",
      icon: <Zap className="h-5 w-5" />,
      color: "orange" as const,
      trend: progress?.currentStreak && progress.currentStreak > 0 ? {
        value: progress.currentStreak,
        label: "active",
        positive: true
      } : undefined
    }
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Overview</h2>
        <Badge variant="outline" className="gap-1">
          <Calendar className="h-3 w-3" />
          Last 30 days
        </Badge>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard
            key={stat.title}
            {...stat}
            delay={index * 0.1}
          />
        ))}
      </div>
    </div>
  )
}

"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { motion } from "framer-motion"
import { BarChart3, Code, FileText, Users } from "lucide-react"

export function StatsCards() {
  const stats = [
    {
      title: "Total Projects",
      value: "12",
      description: "3 active",
      icon: <BarChart3 className="h-5 w-5 text-muted-foreground" />,
      change: "+2 from last month",
    },
    {
      title: "AI Interactions",
      value: "487",
      description: "24 today",
      icon: <Code className="h-5 w-5 text-muted-foreground" />,
      change: "+32% from last week",
    },
    {
      title: "Forms Created",
      value: "9",
      description: "2 this week",
      icon: <FileText className="h-5 w-5 text-muted-foreground" />,
      change: "+1 from last week",
    },
    {
      title: "Team Members",
      value: "5",
      description: "2 admins",
      icon: <Users className="h-5 w-5 text-muted-foreground" />,
      change: "No change",
    },
  ]

  return (
    <>
      {stats.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card className="border-border/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              {stat.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
              <div className="mt-2 text-xs text-muted-foreground">{stat.change}</div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </>
  )
}

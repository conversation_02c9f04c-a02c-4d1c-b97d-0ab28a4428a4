"use client"

import { <PERSON>, Card<PERSON>es<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { ArrowRight, Bot, Braces, FileText, ImageIcon, MessageSquare, Sparkles, Video } from "lucide-react"
import Link from "next/link"

export function AIToolsGrid() {
  const tools = [
    {
      icon: <MessageSquare className="h-8 w-8 text-primary" />,
      title: "AI Chat Assistant",
      description: "Chat with our AI assistant to get help with your projects and tasks.",
      href: "/dashboard/chat",
    },
    {
      icon: <Braces className="h-8 w-8 text-primary" />,
      title: "Code Generator",
      description: "Generate code snippets and templates for your development projects.",
      href: "/dashboard/code",
    },
    {
      icon: <FileText className="h-8 w-8 text-primary" />,
      title: "Form Builder",
      description: "Create custom forms for data collection and user feedback.",
      href: "/dashboard/forms",
    },
    {
      icon: <ImageIcon className="h-8 w-8 text-primary" />,
      title: "Image Generator",
      description: "Create custom images and graphics for your projects.",
      href: "/dashboard/image-generator",
    },
    {
      icon: <Bot className="h-8 w-8 text-primary" />,
      title: "Chatbot Builder",
      description: "Create custom chatbots for your website or application.",
      href: "/dashboard/chatbot-builder",
    },
    {
      icon: <Video className="h-8 w-8 text-primary" />,
      title: "Video Generator",
      description: "Create promotional videos and animations for your brand.",
      href: "/dashboard/video-generator",
    },
    {
      icon: <Sparkles className="h-8 w-8 text-primary" />,
      title: "Content Writer",
      description: "Generate marketing copy, blog posts, and other content.",
      href: "/dashboard/content-writer",
    },
  ]

  return (
    <div className="grid gap-6 mt-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {tools.map((tool, index) => (
        <motion.div
          key={tool.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
          whileHover={{ y: -5 }}
        >
          <Card className="h-full border-border/50 hover:border-primary/50 transition-colors">
            <CardHeader>
              <div className="mb-2">{tool.icon}</div>
              <CardTitle>{tool.title}</CardTitle>
              <CardDescription>{tool.description}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button asChild variant="ghost" className="gap-2 hover:bg-transparent hover:text-primary">
                <Link href={tool.href}>
                  Try it now <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      ))}
    </div>
  )
}

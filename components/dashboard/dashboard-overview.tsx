"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ProjectCard } from "@/components/dashboard/project-card"
import { ActivityFeed } from "@/components/dashboard/activity-feed"
import { StatsCards } from "@/components/dashboard/stats-cards"
import { motion } from "framer-motion"
import { Plus } from "lucide-react"
import { ThreeDScene } from "@/components/three-d-scene"

export function DashboardOverview() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="grid gap-6 mt-6"
    >
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCards />
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4 border-border/50">
              <CardHeader>
                <CardTitle>Recent Projects</CardTitle>
                <CardDescription>You have 3 ongoing projects and 2 completed projects.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <ProjectCard
                    title="E-commerce Website"
                    description="Online store with product catalog and payment integration"
                    progress={75}
                    daysLeft={14}
                  />
                  <ProjectCard
                    title="Mobile App"
                    description="Cross-platform mobile application for service booking"
                    progress={40}
                    daysLeft={30}
                  />
                  <ProjectCard
                    title="Dashboard UI"
                    description="Admin dashboard with analytics and user management"
                    progress={90}
                    daysLeft={5}
                  />
                  <div className="flex items-center justify-center h-full">
                    <Button variant="outline" className="h-[160px] w-full border-dashed">
                      <Plus className="mr-2 h-4 w-4" />
                      New Project
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="col-span-3 border-border/50">
              <CardHeader>
                <CardTitle>Activity</CardTitle>
                <CardDescription>Your recent activity and notifications.</CardDescription>
              </CardHeader>
              <CardContent>
                <ActivityFeed />
              </CardContent>
            </Card>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-3 border-border/50">
              <CardHeader>
                <CardTitle>AI Assistant Usage</CardTitle>
                <CardDescription>Your interaction with Soimagine AI tools.</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px] flex items-center justify-center">
                <div className="w-full h-full">
                  <ThreeDScene />
                </div>
              </CardContent>
            </Card>
            <Card className="col-span-4 border-border/50">
              <CardHeader>
                <CardTitle>Project Timeline</CardTitle>
                <CardDescription>Upcoming deadlines and milestones.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <div className="w-full bg-muted h-3 rounded-full overflow-hidden">
                      <div className="bg-primary h-full w-[75%]"></div>
                    </div>
                    <span className="ml-2 text-sm">75%</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">May 15, 2023</span>
                      <span className="text-muted-foreground">June 30, 2023</span>
                    </div>
                    <div className="relative h-20 border-l border-border/50">
                      <div className="absolute left-0 top-0 w-3 h-3 -ml-1.5 rounded-full bg-primary"></div>
                      <div className="absolute left-0 top-1/3 w-3 h-3 -ml-1.5 rounded-full bg-primary"></div>
                      <div className="absolute left-0 top-2/3 w-3 h-3 -ml-1.5 rounded-full bg-muted"></div>
                      <div className="absolute left-0 bottom-0 w-3 h-3 -ml-1.5 rounded-full bg-muted"></div>

                      <div className="absolute left-6 top-0 -mt-2">
                        <p className="font-medium">Project Start</p>
                        <p className="text-sm text-muted-foreground">Initial planning and setup</p>
                      </div>
                      <div className="absolute left-6 top-1/3 -mt-2">
                        <p className="font-medium">Design Phase Complete</p>
                        <p className="text-sm text-muted-foreground">UI/UX designs approved</p>
                      </div>
                      <div className="absolute left-6 top-2/3 -mt-2">
                        <p className="font-medium">Development Milestone</p>
                        <p className="text-sm text-muted-foreground">Core functionality complete</p>
                      </div>
                      <div className="absolute left-6 bottom-0 -mb-2">
                        <p className="font-medium">Project Delivery</p>
                        <p className="text-sm text-muted-foreground">Final testing and handover</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>Your project and usage analytics.</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <p className="text-muted-foreground">Analytics dashboard coming soon.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="reports" className="space-y-4">
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>Reports</CardTitle>
              <CardDescription>View and download project reports.</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <p className="text-muted-foreground">Reports dashboard coming soon.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="notifications" className="space-y-4">
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>Notifications</CardTitle>
              <CardDescription>Manage your notification preferences.</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex items-center justify-center">
              <p className="text-muted-foreground">Notifications dashboard coming soon.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </motion.div>
  )
}

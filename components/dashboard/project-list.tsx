"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { motion } from "framer-motion"
import { Clock, Edit, MoreHorizontal, Plus, Search, Trash } from "lucide-react"

interface Project {
  id: string
  title: string
  description: string
  status: "active" | "completed" | "on-hold"
  progress: number
  daysLeft?: number
  client: string
  team: string[]
}

export function ProjectList() {
  const [searchQuery, setSearchQuery] = useState("")

  const projects: Project[] = [
    {
      id: "1",
      title: "E-commerce Website",
      description: "Online store with product catalog and payment integration",
      status: "active",
      progress: 75,
      daysLeft: 14,
      client: "Fashion Boutique Inc.",
      team: ["John <PERSON>.", "Sarah L.", "Mike T."],
    },
    {
      id: "2",
      title: "Mobile App",
      description: "Cross-platform mobile application for service booking",
      status: "active",
      progress: 40,
      daysLeft: 30,
      client: "ServiceNow Ltd.",
      team: ["Emma R.", "David K."],
    },
    {
      id: "3",
      title: "Dashboard UI",
      description: "Admin dashboard with analytics and user management",
      status: "active",
      progress: 90,
      daysLeft: 5,
      client: "DataViz Corp",
      team: ["John D.", "Lisa M."],
    },
    {
      id: "4",
      title: "Corporate Website",
      description: "Company website with about, services, and contact pages",
      status: "completed",
      progress: 100,
      client: "Global Industries",
      team: ["Sarah L.", "Mike T.", "Emma R."],
    },
    {
      id: "5",
      title: "Marketing Landing Page",
      description: "Product launch landing page with lead generation form",
      status: "completed",
      progress: 100,
      client: "TechStart Inc.",
      team: ["David K.", "Lisa M."],
    },
    {
      id: "6",
      title: "CRM Integration",
      description: "Customer relationship management system integration",
      status: "on-hold",
      progress: 30,
      client: "Sales Solutions Co.",
      team: ["John D.", "Emma R."],
    },
  ]

  const filteredProjects = projects.filter((project) => project.title.toLowerCase().includes(searchQuery.toLowerCase()))

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500/20 text-green-500 hover:bg-green-500/30"
      case "completed":
        return "bg-blue-500/20 text-blue-500 hover:bg-blue-500/30"
      case "on-hold":
        return "bg-amber-500/20 text-amber-500 hover:bg-amber-500/30"
      default:
        return "bg-gray-500/20 text-gray-500 hover:bg-gray-500/30"
    }
  }

  return (
    <div className="space-y-6 mt-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="relative w-full sm:w-96">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search projects..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New Project
        </Button>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Projects</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="on-hold">On Hold</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="mt-4">
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>All Projects</CardTitle>
              <CardDescription>View and manage all your projects.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredProjects.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">No projects found</div>
                ) : (
                  filteredProjects.map((project, index) => (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <Card className="border-border/50">
                        <CardContent className="p-4">
                          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold">{project.title}</h3>
                                <Badge className={getStatusColor(project.status)}>
                                  {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{project.description}</p>
                              <p className="text-xs text-muted-foreground">Client: {project.client}</p>
                            </div>
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                              <div className="flex flex-col items-end gap-1">
                                <div className="flex items-center gap-2">
                                  <div className="w-full bg-muted h-2 rounded-full overflow-hidden w-24">
                                    <div
                                      className="bg-primary h-full transition-all duration-500 ease-in-out"
                                      style={{ width: `${project.progress}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs">{project.progress}%</span>
                                </div>
                                {project.daysLeft && (
                                  <div className="flex items-center text-xs text-muted-foreground">
                                    <Clock className="h-3 w-3 mr-1" />
                                    {project.daysLeft} days left
                                  </div>
                                )}
                              </div>
                              <div className="flex -space-x-2">
                                {project.team.map((member, i) => (
                                  <div
                                    key={i}
                                    className="h-8 w-8 rounded-full bg-muted flex items-center justify-center border-2 border-background text-xs font-medium"
                                    title={member}
                                  >
                                    {member.split(" ")[0][0]}
                                    {member.split(" ")[1]?.[0]}
                                  </div>
                                ))}
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Trash className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">Showing {filteredProjects.length} projects</div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled>
                  Previous
                </Button>
                <Button variant="outline" size="sm" disabled>
                  Next
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="active" className="mt-4">
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>Active Projects</CardTitle>
              <CardDescription>View and manage your active projects.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredProjects
                  .filter((project) => project.status === "active")
                  .map((project, index) => (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <Card className="border-border/50">
                        <CardContent className="p-4">
                          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold">{project.title}</h3>
                                <Badge className={getStatusColor(project.status)}>
                                  {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{project.description}</p>
                              <p className="text-xs text-muted-foreground">Client: {project.client}</p>
                            </div>
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                              <div className="flex flex-col items-end gap-1">
                                <div className="flex items-center gap-2">
                                  <div className="w-full bg-muted h-2 rounded-full overflow-hidden w-24">
                                    <div
                                      className="bg-primary h-full transition-all duration-500 ease-in-out"
                                      style={{ width: `${project.progress}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs">{project.progress}%</span>
                                </div>
                                {project.daysLeft && (
                                  <div className="flex items-center text-xs text-muted-foreground">
                                    <Clock className="h-3 w-3 mr-1" />
                                    {project.daysLeft} days left
                                  </div>
                                )}
                              </div>
                              <div className="flex -space-x-2">
                                {project.team.map((member, i) => (
                                  <div
                                    key={i}
                                    className="h-8 w-8 rounded-full bg-muted flex items-center justify-center border-2 border-background text-xs font-medium"
                                    title={member}
                                  >
                                    {member.split(" ")[0][0]}
                                    {member.split(" ")[1]?.[0]}
                                  </div>
                                ))}
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Trash className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="completed" className="mt-4">
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>Completed Projects</CardTitle>
              <CardDescription>View your completed projects.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredProjects
                  .filter((project) => project.status === "completed")
                  .map((project, index) => (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <Card className="border-border/50">
                        <CardContent className="p-4">
                          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold">{project.title}</h3>
                                <Badge className={getStatusColor(project.status)}>
                                  {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{project.description}</p>
                              <p className="text-xs text-muted-foreground">Client: {project.client}</p>
                            </div>
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                              <div className="flex flex-col items-end gap-1">
                                <div className="flex items-center gap-2">
                                  <div className="w-full bg-muted h-2 rounded-full overflow-hidden w-24">
                                    <div
                                      className="bg-primary h-full transition-all duration-500 ease-in-out"
                                      style={{ width: `${project.progress}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs">{project.progress}%</span>
                                </div>
                              </div>
                              <div className="flex -space-x-2">
                                {project.team.map((member, i) => (
                                  <div
                                    key={i}
                                    className="h-8 w-8 rounded-full bg-muted flex items-center justify-center border-2 border-background text-xs font-medium"
                                    title={member}
                                  >
                                    {member.split(" ")[0][0]}
                                    {member.split(" ")[1]?.[0]}
                                  </div>
                                ))}
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Trash className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="on-hold" className="mt-4">
          <Card className="border-border/50">
            <CardHeader>
              <CardTitle>On Hold Projects</CardTitle>
              <CardDescription>View projects that are currently on hold.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredProjects
                  .filter((project) => project.status === "on-hold")
                  .map((project, index) => (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <Card className="border-border/50">
                        <CardContent className="p-4">
                          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold">{project.title}</h3>
                                <Badge className={getStatusColor(project.status)}>
                                  {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{project.description}</p>
                              <p className="text-xs text-muted-foreground">Client: {project.client}</p>
                            </div>
                            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                              <div className="flex flex-col items-end gap-1">
                                <div className="flex items-center gap-2">
                                  <div className="w-full bg-muted h-2 rounded-full overflow-hidden w-24">
                                    <div
                                      className="bg-primary h-full transition-all duration-500 ease-in-out"
                                      style={{ width: `${project.progress}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs">{project.progress}%</span>
                                </div>
                              </div>
                              <div className="flex -space-x-2">
                                {project.team.map((member, i) => (
                                  <div
                                    key={i}
                                    className="h-8 w-8 rounded-full bg-muted flex items-center justify-center border-2 border-background text-xs font-medium"
                                    title={member}
                                  >
                                    {member.split(" ")[0][0]}
                                    {member.split(" ")[1]?.[0]}
                                  </div>
                                ))}
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Trash className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

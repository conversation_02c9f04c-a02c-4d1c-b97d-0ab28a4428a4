'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Trophy, 
  Medal, 
  Award, 
  Crown,
  Star,
  TrendingUp,
  Users,
  Zap
} from 'lucide-react'
import { LeaderboardEntry } from '@/lib/types/marketing-platform'
import { cn } from '@/lib/utils'

interface LeaderboardCardProps {
  className?: string
  showFullLeaderboard?: boolean
}

export function LeaderboardCard({ className, showFullLeaderboard = false }: LeaderboardCardProps) {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([])
  const [userRank, setUserRank] = useState<number | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [type, setType] = useState('xp')
  const [period, setPeriod] = useState('all')

  const fetchLeaderboard = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        type,
        period,
        limit: showFullLeaderboard ? '50' : '10'
      })

      const response = await fetch(`/api/leaderboard?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch leaderboard')
      }

      const data = await response.json()
      setLeaderboard(data.leaderboard || [])
      setUserRank(data.userRank)
    } catch (err) {
      console.error('Error fetching leaderboard:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch leaderboard')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLeaderboard()
  }, [type, period, showFullLeaderboard])

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="h-4 w-4 text-yellow-500" />
      case 2:
        return <Medal className="h-4 w-4 text-gray-400" />
      case 3:
        return <Award className="h-4 w-4 text-amber-600" />
      default:
        return <span className="text-sm font-medium text-muted-foreground">#{rank}</span>
    }
  }

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 2: return 'bg-gray-100 text-gray-800 border-gray-200'
      case 3: return 'bg-amber-100 text-amber-800 border-amber-200'
      default: return 'bg-muted text-muted-foreground'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'xp': return <Star className="h-4 w-4" />
      case 'challenges': return <Trophy className="h-4 w-4" />
      case 'streak': return <Zap className="h-4 w-4" />
      case 'level': return <TrendingUp className="h-4 w-4" />
      default: return <Users className="h-4 w-4" />
    }
  }

  const formatValue = (value: number, type: string) => {
    switch (type) {
      case 'xp':
        return `${value.toLocaleString()} XP`
      case 'challenges':
        return `${value} completed`
      case 'streak':
        return `${value} days`
      case 'level':
        return `Level ${value}`
      default:
        return value.toString()
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="h-6 bg-muted rounded w-1/2 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center gap-3 animate-pulse">
                <div className="w-8 h-8 bg-muted rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-muted rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
                <div className="h-4 bg-muted rounded w-16"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground mb-4">Failed to load leaderboard</p>
          <Button onClick={fetchLeaderboard} variant="outline" size="sm">
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className={className}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Leaderboard
          </CardTitle>
          
          {/* Filters */}
          <div className="flex gap-2">
            <Select value={type} onValueChange={setType}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="xp">Experience</SelectItem>
                <SelectItem value="challenges">Challenges</SelectItem>
                <SelectItem value="streak">Streak</SelectItem>
                <SelectItem value="level">Level</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent>
          {/* User's Rank */}
          {userRank && (
            <div className="mb-4 p-3 bg-primary/5 border border-primary/20 rounded-lg">
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="gap-1">
                  {getRankIcon(userRank)}
                  Your Rank: #{userRank}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {getTypeIcon(type)}
                </span>
              </div>
            </div>
          )}

          {/* Leaderboard List */}
          <div className="space-y-3">
            {leaderboard.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No data available</p>
              </div>
            ) : (
              leaderboard.map((entry, index) => (
                <motion.div
                  key={entry.user.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className={cn(
                    "flex items-center gap-3 p-3 rounded-lg transition-colors",
                    entry.rank <= 3 ? "bg-muted/50" : "hover:bg-muted/30"
                  )}
                >
                  {/* Rank */}
                  <div className="flex items-center justify-center w-8">
                    {getRankIcon(entry.rank)}
                  </div>

                  {/* Avatar */}
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={entry.user.image || undefined} />
                    <AvatarFallback>
                      {entry.user.name?.charAt(0) || 'U'}
                    </AvatarFallback>
                  </Avatar>

                  {/* User Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <p className="font-medium text-sm truncate">
                        {entry.user.name || 'Anonymous'}
                      </p>
                      {entry.rank <= 3 && (
                        <Badge 
                          variant="outline" 
                          className={cn("text-xs", getRankBadgeColor(entry.rank))}
                        >
                          Top {entry.rank}
                        </Badge>
                      )}
                    </div>
                    {entry.user.company && (
                      <p className="text-xs text-muted-foreground truncate">
                        {entry.user.company}
                      </p>
                    )}
                  </div>

                  {/* Value */}
                  <div className="text-right">
                    <p className="font-semibold text-sm">
                      {formatValue(entry.primaryValue, type)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Level {entry.stats.currentLevel}
                    </p>
                  </div>
                </motion.div>
              ))
            )}
          </div>

          {/* View More Button */}
          {!showFullLeaderboard && leaderboard.length >= 10 && (
            <div className="mt-4 text-center">
              <Button variant="outline" size="sm" className="w-full">
                View Full Leaderboard
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { Loader2 } from "lucide-react"

export function SettingsForm() {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      toast({
        title: "Settings updated",
        description: "Your account settings have been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Tabs defaultValue="general" className="w-full">
      <TabsList className="grid w-full grid-cols-3 mb-6">
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="notifications">Notifications</TabsTrigger>
        <TabsTrigger value="appearance">Appearance</TabsTrigger>
      </TabsList>
      <TabsContent value="general">
        <Card className="border-border/50">
          <CardHeader>
            <CardTitle>General Settings</CardTitle>
            <CardDescription>Manage your account information and preferences.</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" defaultValue="John Doe" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" defaultValue="<EMAIL>" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company">Company</Label>
                <Input id="company" defaultValue="Acme Inc." />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  defaultValue="I'm a product designer based in New York City. I specialize in UX/UI design, brand strategy, and web development."
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </TabsContent>
      <TabsContent value="notifications">
        <Card className="border-border/50">
          <CardHeader>
            <CardTitle>Notification Settings</CardTitle>
            <CardDescription>Configure how you receive notifications.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-muted-foreground">Receive email notifications for important updates.</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Project Updates</p>
                <p className="text-sm text-muted-foreground">Get notified when there are updates to your projects.</p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">AI Assistant Suggestions</p>
                <p className="text-sm text-muted-foreground">
                  Receive suggestions from the AI assistant based on your activity.
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Marketing Communications</p>
                <p className="text-sm text-muted-foreground">
                  Receive updates about new features and promotional offers.
                </p>
              </div>
              <Switch />
            </div>
          </CardContent>
          <CardFooter>
            <Button>Save Preferences</Button>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="appearance">
        <Card className="border-border/50">
          <CardHeader>
            <CardTitle>Appearance Settings</CardTitle>
            <CardDescription>Customize the look and feel of your dashboard.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Theme</Label>
              <div className="grid grid-cols-3 gap-2">
                <Button variant="outline" className="justify-start">
                  <span className="h-4 w-4 rounded-full bg-background mr-2 border border-border"></span>
                  Light
                </Button>
                <Button variant="outline" className="justify-start">
                  <span className="h-4 w-4 rounded-full bg-black mr-2"></span>
                  Dark
                </Button>
                <Button variant="outline" className="justify-start">
                  <span className="h-4 w-4 rounded-full bg-gradient-to-r from-background to-black mr-2"></span>
                  System
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Accent Color</Label>
              <div className="grid grid-cols-5 gap-2">
                <Button variant="outline" className="justify-center h-10 w-10 p-0">
                  <span className="h-6 w-6 rounded-full bg-red-500"></span>
                  <span className="sr-only">Red</span>
                </Button>
                <Button variant="outline" className="justify-center h-10 w-10 p-0">
                  <span className="h-6 w-6 rounded-full bg-blue-500"></span>
                  <span className="sr-only">Blue</span>
                </Button>
                <Button variant="outline" className="justify-center h-10 w-10 p-0">
                  <span className="h-6 w-6 rounded-full bg-green-500"></span>
                  <span className="sr-only">Green</span>
                </Button>
                <Button variant="outline" className="justify-center h-10 w-10 p-0">
                  <span className="h-6 w-6 rounded-full bg-purple-500"></span>
                  <span className="sr-only">Purple</span>
                </Button>
                <Button variant="outline" className="justify-center h-10 w-10 p-0">
                  <span className="h-6 w-6 rounded-full bg-orange-500"></span>
                  <span className="sr-only">Orange</span>
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Reduce Animations</p>
                <p className="text-sm text-muted-foreground">Minimize animations for improved performance.</p>
              </div>
              <Switch />
            </div>
          </CardContent>
          <CardFooter>
            <Button>Save Preferences</Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  )
}

'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Award, 
  Star, 
  Trophy, 
  Crown, 
  Shield, 
  Zap,
  Target,
  Users,
  Share2,
  PenTool,
  BookOpen,
  Lock
} from 'lucide-react'
import { UserAchievement, Achievement } from '@/lib/types/marketing-platform'
import { cn } from '@/lib/utils'

interface AchievementShowcaseProps {
  className?: string
}

export function AchievementShowcase({ className }: AchievementShowcaseProps) {
  const [achievements, setAchievements] = useState<UserAchievement[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<'all' | 'completed' | 'in_progress'>('all')

  // Mock data for demonstration
  useEffect(() => {
    const mockAchievements: UserAchievement[] = [
      {
        id: '1',
        userId: 'user1',
        achievementId: 'first-challenge',
        progress: 1.0,
        isCompleted: true,
        creditsAwarded: 25,
        xpAwarded: 50,
        unlockedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
        createdAt: new Date(),
        updatedAt: new Date(),
        achievement: {
          id: 'first-challenge',
          name: 'First Steps',
          description: 'Complete your first challenge',
          icon: '🎯',
          type: 'MILESTONE',
          category: 'PROGRESS',
          rarity: 'COMMON',
          requirements: { challengesCompleted: 1 },
          creditReward: 25,
          xpReward: 50,
          isActive: true,
          isSecret: false,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      },
      {
        id: '2',
        userId: 'user1',
        achievementId: 'social-butterfly',
        progress: 1.0,
        isCompleted: true,
        creditsAwarded: 50,
        xpAwarded: 100,
        unlockedAt: new Date(Date.now() - 1000 * 60 * 60 * 24),
        createdAt: new Date(),
        updatedAt: new Date(),
        achievement: {
          id: 'social-butterfly',
          name: 'Social Butterfly',
          description: 'Share content on 5 different social platforms',
          icon: '🦋',
          type: 'SOCIAL',
          category: 'SOCIAL',
          rarity: 'UNCOMMON',
          requirements: { socialShares: 5, platforms: 5 },
          creditReward: 50,
          xpReward: 100,
          isActive: true,
          isSecret: false,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      },
      {
        id: '3',
        userId: 'user1',
        achievementId: 'streak-master',
        progress: 0.7,
        isCompleted: false,
        creditsAwarded: 0,
        xpAwarded: 0,
        unlockedAt: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        achievement: {
          id: 'streak-master',
          name: 'Streak Master',
          description: 'Maintain a 10-day activity streak',
          icon: '🔥',
          type: 'STREAK',
          category: 'PROGRESS',
          rarity: 'RARE',
          requirements: { streak: 10 },
          creditReward: 100,
          xpReward: 250,
          isActive: true,
          isSecret: false,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      },
      {
        id: '4',
        userId: 'user1',
        achievementId: 'content-creator',
        progress: 0.4,
        isCompleted: false,
        creditsAwarded: 0,
        xpAwarded: 0,
        unlockedAt: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        achievement: {
          id: 'content-creator',
          name: 'Content Creator',
          description: 'Create 10 pieces of marketing content',
          icon: '✍️',
          type: 'MILESTONE',
          category: 'CONTENT',
          rarity: 'UNCOMMON',
          requirements: { contentCreated: 10 },
          creditReward: 75,
          xpReward: 150,
          isActive: true,
          isSecret: false,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      },
      {
        id: '5',
        userId: 'user1',
        achievementId: 'legendary-marketer',
        progress: 0.0,
        isCompleted: false,
        creditsAwarded: 0,
        xpAwarded: 0,
        unlockedAt: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        achievement: {
          id: 'legendary-marketer',
          name: 'Legendary Marketer',
          description: 'Reach Level 20 and complete 100 challenges',
          icon: '👑',
          type: 'SPECIAL',
          category: 'SPECIAL',
          rarity: 'LEGENDARY',
          requirements: { level: 20, challengesCompleted: 100 },
          creditReward: 500,
          xpReward: 1000,
          isActive: true,
          isSecret: false,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      }
    ]

    setTimeout(() => {
      setAchievements(mockAchievements)
      setLoading(false)
    }, 1000)
  }, [])

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'COMMON': return 'text-gray-600 border-gray-200 bg-gray-50'
      case 'UNCOMMON': return 'text-green-600 border-green-200 bg-green-50'
      case 'RARE': return 'text-blue-600 border-blue-200 bg-blue-50'
      case 'EPIC': return 'text-purple-600 border-purple-200 bg-purple-50'
      case 'LEGENDARY': return 'text-yellow-600 border-yellow-200 bg-yellow-50'
      default: return 'text-gray-600 border-gray-200 bg-gray-50'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'PROGRESS': return <Target className="h-4 w-4" />
      case 'SOCIAL': return <Share2 className="h-4 w-4" />
      case 'CONTENT': return <PenTool className="h-4 w-4" />
      case 'REFERRALS': return <Users className="h-4 w-4" />
      case 'LEARNING': return <BookOpen className="h-4 w-4" />
      case 'SPECIAL': return <Crown className="h-4 w-4" />
      default: return <Award className="h-4 w-4" />
    }
  }

  const filteredAchievements = achievements.filter(achievement => {
    switch (filter) {
      case 'completed': return achievement.isCompleted
      case 'in_progress': return !achievement.isCompleted && achievement.progress > 0
      default: return true
    }
  })

  const completedCount = achievements.filter(a => a.isCompleted).length
  const totalCount = achievements.length

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="h-6 bg-muted rounded w-1/2 animate-pulse"></div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="h-12 w-12 bg-muted rounded-full"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-full"></div>
                    <div className="h-2 bg-muted rounded w-full"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground mb-4">Failed to load achievements</p>
          <Button variant="outline" size="sm">
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={className}
    >
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Achievements
            </CardTitle>
            <Badge variant="outline">
              {completedCount}/{totalCount} Unlocked
            </Badge>
          </div>
          
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Overall Progress</span>
              <span className="font-medium">
                {totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0}%
              </span>
            </div>
            <Progress 
              value={totalCount > 0 ? (completedCount / totalCount) * 100 : 0} 
              className="h-2" 
            />
          </div>

          {/* Filters */}
          <div className="flex gap-2">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
            >
              All
            </Button>
            <Button
              variant={filter === 'completed' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('completed')}
            >
              Completed
            </Button>
            <Button
              variant={filter === 'in_progress' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('in_progress')}
            >
              In Progress
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredAchievements.map((userAchievement, index) => (
              <motion.div
                key={userAchievement.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className={cn(
                  "relative overflow-hidden transition-all duration-200 hover:shadow-md",
                  userAchievement.isCompleted 
                    ? "border-primary/20 bg-primary/5" 
                    : "hover:border-muted-foreground/20"
                )}>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="text-2xl">
                          {userAchievement.achievement.icon}
                        </div>
                        <div className="flex items-center gap-1">
                          <Badge 
                            variant="outline" 
                            className={cn("text-xs", getRarityColor(userAchievement.achievement.rarity))}
                          >
                            {userAchievement.achievement.rarity}
                          </Badge>
                          {userAchievement.isCompleted && (
                            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                              <Trophy className="h-3 w-3 text-white" />
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Content */}
                      <div>
                        <h3 className="font-semibold text-sm mb-1">
                          {userAchievement.achievement.name}
                        </h3>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {userAchievement.achievement.description}
                        </p>
                      </div>

                      {/* Progress */}
                      {!userAchievement.isCompleted && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-muted-foreground">Progress</span>
                            <span className="font-medium">
                              {Math.round(userAchievement.progress * 100)}%
                            </span>
                          </div>
                          <Progress 
                            value={userAchievement.progress * 100} 
                            className="h-2" 
                          />
                        </div>
                      )}

                      {/* Rewards */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getCategoryIcon(userAchievement.achievement.category)}
                          <span className="text-xs text-muted-foreground">
                            {userAchievement.achievement.category}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-xs">
                          <span className="flex items-center gap-1 text-green-600">
                            <Zap className="h-3 w-3" />
                            {userAchievement.achievement.creditReward}
                          </span>
                          <span className="flex items-center gap-1 text-purple-600">
                            <Star className="h-3 w-3" />
                            {userAchievement.achievement.xpReward}
                          </span>
                        </div>
                      </div>

                      {/* Completion Date */}
                      {userAchievement.isCompleted && userAchievement.unlockedAt && (
                        <div className="text-xs text-muted-foreground">
                          Unlocked {new Date(userAchievement.unlockedAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {filteredAchievements.length === 0 && (
            <div className="text-center py-8">
              <Lock className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">
                {filter === 'completed' 
                  ? 'No achievements unlocked yet'
                  : filter === 'in_progress'
                  ? 'No achievements in progress'
                  : 'No achievements available'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

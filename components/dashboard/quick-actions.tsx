'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Zap, 
  Share2, 
  PenTool, 
  Users, 
  BookOpen, 
  Gift,
  Sparkles,
  Target,
  MessageSquare,
  TrendingUp
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'

interface QuickAction {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  color: string
  credits: number
  xp: number
  action: () => void
  badge?: string
}

export function QuickActions() {
  const router = useRouter()

  const quickActions: QuickAction[] = [
    {
      id: 'social-share',
      title: 'Share on Social',
      description: 'Share Soimagine content on your social media',
      icon: <Share2 className="h-5 w-5" />,
      color: 'bg-blue-500',
      credits: 10,
      xp: 25,
      action: () => {
        // Navigate to social sharing challenge
        router.push('/challenges/social-share')
      },
      badge: 'Quick'
    },
    {
      id: 'create-content',
      title: 'Create Content',
      description: 'Write a blog post or create marketing content',
      icon: <PenTool className="h-5 w-5" />,
      color: 'bg-green-500',
      credits: 25,
      xp: 50,
      action: () => {
        router.push('/challenges/content-creation')
      }
    },
    {
      id: 'refer-friend',
      title: 'Refer a Friend',
      description: 'Invite someone to join the platform',
      icon: <Users className="h-5 w-5" />,
      color: 'bg-purple-500',
      credits: 50,
      xp: 100,
      action: () => {
        router.push('/referrals')
      },
      badge: 'High Reward'
    },
    {
      id: 'ai-chat',
      title: 'Chat with AI',
      description: 'Get marketing advice from our AI consultant',
      icon: <MessageSquare className="h-5 w-5" />,
      color: 'bg-orange-500',
      credits: -5, // Costs credits
      xp: 10,
      action: () => {
        router.push('/assistant')
      },
      badge: 'Costs Credits'
    },
    {
      id: 'learn-marketing',
      title: 'Learn Marketing',
      description: 'Complete a marketing education module',
      icon: <BookOpen className="h-5 w-5" />,
      color: 'bg-indigo-500',
      credits: 15,
      xp: 75,
      action: () => {
        router.push('/challenges/learning')
      }
    },
    {
      id: 'daily-bonus',
      title: 'Daily Check-in',
      description: 'Claim your daily bonus credits',
      icon: <Gift className="h-5 w-5" />,
      color: 'bg-pink-500',
      credits: 20,
      xp: 10,
      action: () => {
        // Handle daily check-in
        console.log('Daily check-in clicked')
      },
      badge: 'Daily'
    }
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Quick Actions</h2>
          <p className="text-sm text-muted-foreground">
            Fast ways to earn credits and XP
          </p>
        </div>
        <Button variant="outline" size="sm" className="gap-2">
          <Sparkles className="h-4 w-4" />
          View All
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {quickActions.map((action, index) => (
          <motion.div
            key={action.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="group hover:shadow-md transition-all duration-200 cursor-pointer">
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className={cn(
                      "p-2 rounded-lg text-white",
                      action.color
                    )}>
                      {action.icon}
                    </div>
                    {action.badge && (
                      <Badge 
                        variant="secondary" 
                        className="text-xs"
                      >
                        {action.badge}
                      </Badge>
                    )}
                  </div>

                  {/* Content */}
                  <div>
                    <h3 className="font-semibold text-sm mb-1">
                      {action.title}
                    </h3>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {action.description}
                    </p>
                  </div>

                  {/* Rewards */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs">
                      {action.credits !== 0 && (
                        <span className={cn(
                          "flex items-center gap-1",
                          action.credits > 0 ? "text-green-600" : "text-red-600"
                        )}>
                          <Zap className="h-3 w-3" />
                          {action.credits > 0 ? '+' : ''}{action.credits}
                        </span>
                      )}
                      {action.xp > 0 && (
                        <span className="flex items-center gap-1 text-purple-600">
                          <Target className="h-3 w-3" />
                          +{action.xp} XP
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Action Button */}
                  <Button
                    onClick={action.action}
                    size="sm"
                    className="w-full group-hover:bg-primary/90 transition-colors"
                  >
                    Start
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Featured Challenge */}
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-primary/20 rounded-full">
              <TrendingUp className="h-6 w-6 text-primary" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-primary">
                Featured Challenge: Social Media Blitz
              </h3>
              <p className="text-sm text-muted-foreground">
                Share 3 posts across different platforms this week
              </p>
              <div className="flex items-center gap-3 mt-2">
                <Badge variant="outline" className="gap-1">
                  <Zap className="h-3 w-3" />
                  +100 Credits
                </Badge>
                <Badge variant="outline" className="gap-1">
                  <Target className="h-3 w-3" />
                  +250 XP
                </Badge>
                <Badge variant="secondary">
                  Limited Time
                </Badge>
              </div>
            </div>
            <Button className="shrink-0">
              Accept Challenge
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

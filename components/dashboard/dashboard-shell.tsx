"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar"
import { useMediaQuery } from "@/hooks/use-media-query"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface DashboardShellProps {
  children: React.ReactNode
}

export function DashboardShell({ children }: DashboardShellProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const isDesktop = useMediaQuery("(min-width: 1024px)")

  useEffect(() => {
    if (isDesktop) {
      setSidebarOpen(true)
    } else {
      setSidebarOpen(false)
    }
  }, [isDesktop])

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/90">
      <Navbar />
      <div className="container mx-auto px-0 lg:px-4 pt-20">
        <div className="flex relative">
          {!isDesktop && (
            <Button
              variant="outline"
              size="icon"
              className="fixed bottom-6 left-6 z-40 shadow-md"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle sidebar</span>
            </Button>
          )}

          <AnimatePresence>
            {sidebarOpen && (
              <motion.div
                initial={{ x: -300, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -300, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className={`${
                  isDesktop ? "relative" : "fixed inset-y-0 left-0 z-30"
                } w-64 bg-card/80 backdrop-blur-md border-r border-border/50 h-[calc(100vh-5rem)]`}
              >
                <DashboardSidebar closeSidebar={() => !isDesktop && setSidebarOpen(false)} />
              </motion.div>
            )}
          </AnimatePresence>

          <main className="flex-1 p-4 lg:p-6">
            <div className={`${isDesktop && sidebarOpen ? "ml-0" : "ml-0"}`}>{children}</div>
          </main>
        </div>
      </div>
    </div>
  )
}

"use client"

import { <PERSON><PERSON>Area } from "@/components/ui/scroll-area"
import { motion } from "framer-motion"
import { Code, FileText, MessageSquare, User, Zap } from "lucide-react"

export function ActivityFeed() {
  const activities = [
    {
      id: 1,
      type: "message",
      icon: <MessageSquare className="h-4 w-4" />,
      content: "New message from Client A regarding project timeline",
      time: "10 minutes ago",
    },
    {
      id: 2,
      type: "code",
      icon: <Code className="h-4 w-4" />,
      content: "Generated landing page code for E-commerce project",
      time: "1 hour ago",
    },
    {
      id: 3,
      type: "form",
      icon: <FileText className="h-4 w-4" />,
      content: "Created feedback form for Mobile App project",
      time: "3 hours ago",
    },
    {
      id: 4,
      type: "user",
      icon: <User className="h-4 w-4" />,
      content: "Team member David joined the Dashboard UI project",
      time: "5 hours ago",
    },
    {
      id: 5,
      type: "ai",
      icon: <Zap className="h-4 w-4" />,
      content: "AI Assistant generated project proposal for Client B",
      time: "Yesterday",
    },
    {
      id: 6,
      type: "message",
      icon: <MessageSquare className="h-4 w-4" />,
      content: "Client C approved the design mockups",
      time: "Yesterday",
    },
    {
      id: 7,
      type: "code",
      icon: <Code className="h-4 w-4" />,
      content: "Updated API integration for E-commerce project",
      time: "2 days ago",
    },
  ]

  const getIconBackground = (type: string) => {
    switch (type) {
      case "message":
        return "bg-blue-500"
      case "code":
        return "bg-purple-500"
      case "form":
        return "bg-green-500"
      case "user":
        return "bg-yellow-500"
      case "ai":
        return "bg-primary"
      default:
        return "bg-gray-500"
    }
  }

  return (
    <ScrollArea className="h-[300px] pr-4">
      <div className="space-y-4">
        {activities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
            className="flex gap-3"
          >
            <div className={`${getIconBackground(activity.type)} p-1.5 rounded-full text-white flex-shrink-0`}>
              {activity.icon}
            </div>
            <div>
              <p className="text-sm">{activity.content}</p>
              <p className="text-xs text-muted-foreground">{activity.time}</p>
            </div>
          </motion.div>
        ))}
      </div>
    </ScrollArea>
  )
}

'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Star, 
  Trophy, 
  Target, 
  Zap, 
  TrendingUp,
  Award,
  Calendar,
  BarChart3
} from 'lucide-react'
import { useUserProgress } from '@/hooks/use-user-progress'
import { cn } from '@/lib/utils'

interface ProgressSectionProps {
  title: string
  icon: React.ReactNode
  children: React.ReactNode
  className?: string
}

function ProgressSection({ title, icon, children, className }: ProgressSectionProps) {
  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex items-center gap-2">
        {icon}
        <h3 className="font-semibold text-sm">{title}</h3>
      </div>
      {children}
    </div>
  )
}

interface StatItemProps {
  label: string
  value: string | number
  color?: string
  trend?: {
    value: number
    positive: boolean
  }
}

function StatItem({ label, value, color = "primary", trend }: StatItemProps) {
  return (
    <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
      <span className="text-sm text-muted-foreground">{label}</span>
      <div className="flex items-center gap-2">
        <span className={cn(
          "font-semibold",
          color === "primary" && "text-primary",
          color === "green" && "text-green-600",
          color === "blue" && "text-blue-600",
          color === "purple" && "text-purple-600",
          color === "orange" && "text-orange-600"
        )}>
          {value}
        </span>
        {trend && (
          <Badge 
            variant={trend.positive ? "default" : "destructive"}
            className="text-xs"
          >
            {trend.positive ? '+' : ''}{trend.value}
          </Badge>
        )}
      </div>
    </div>
  )
}

export function UserProgressCard() {
  const { progress, loading, error, getLevelProgress, getXpForNextLevel } = useUserProgress()

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <div className="h-6 bg-muted rounded w-1/3 animate-pulse"></div>
        </CardHeader>
        <CardContent className="space-y-6">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <div className="h-4 bg-muted rounded w-1/4 animate-pulse"></div>
              <div className="h-8 bg-muted rounded animate-pulse"></div>
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  if (error || !progress) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">Failed to load progress data</p>
        </CardContent>
      </Card>
    )
  }

  const levelProgress = getLevelProgress(progress.totalXp, progress.currentLevel)
  const xpForNextLevel = getXpForNextLevel(progress.currentLevel)
  const xpToNextLevel = xpForNextLevel - progress.totalXp

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Your Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Level Progress */}
          <ProgressSection
            title="Level & Experience"
            icon={<Star className="h-4 w-4 text-purple-600" />}
          >
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-lg px-3 py-1">
                    Level {progress.currentLevel}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {progress.totalXp.toLocaleString()} XP
                  </span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {xpToNextLevel.toLocaleString()} XP to next level
                </span>
              </div>
              <Progress value={levelProgress} className="h-3" />
            </div>
          </ProgressSection>

          {/* Challenge Stats */}
          <ProgressSection
            title="Challenge Statistics"
            icon={<Trophy className="h-4 w-4 text-yellow-600" />}
          >
            <div className="grid grid-cols-2 gap-3">
              <StatItem
                label="Completed"
                value={progress.challengesCompleted}
                color="green"
              />
              <StatItem
                label="Attempted"
                value={progress.challengesAttempted}
                color="blue"
              />
            </div>
            {progress.challengesAttempted > 0 && (
              <div className="mt-3">
                <div className="flex items-center justify-between text-sm mb-2">
                  <span className="text-muted-foreground">Success Rate</span>
                  <span className="font-medium">
                    {Math.round((progress.challengesCompleted / progress.challengesAttempted) * 100)}%
                  </span>
                </div>
                <Progress 
                  value={(progress.challengesCompleted / progress.challengesAttempted) * 100} 
                  className="h-2" 
                />
              </div>
            )}
          </ProgressSection>

          {/* Category Progress */}
          <ProgressSection
            title="Category Experience"
            icon={<Target className="h-4 w-4 text-blue-600" />}
          >
            <div className="space-y-2">
              {[
                { label: 'Marketing', value: progress.marketingXp, color: 'bg-red-500' },
                { label: 'Social Media', value: progress.socialXp, color: 'bg-blue-500' },
                { label: 'Content', value: progress.contentXp, color: 'bg-green-500' },
                { label: 'Referrals', value: progress.referralXp, color: 'bg-purple-500' }
              ].map((category) => (
                <div key={category.label} className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">{category.label}</span>
                    <span className="font-medium">{category.value} XP</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className={cn("h-2 rounded-full transition-all", category.color)}
                      style={{
                        width: `${Math.min((category.value / Math.max(progress.totalXp, 1)) * 100, 100)}%`
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </ProgressSection>

          {/* Streak */}
          <ProgressSection
            title="Activity Streak"
            icon={<Zap className="h-4 w-4 text-orange-600" />}
          >
            <div className="grid grid-cols-2 gap-3">
              <StatItem
                label="Current"
                value={`${progress.currentStreak} days`}
                color="orange"
              />
              <StatItem
                label="Longest"
                value={`${progress.longestStreak} days`}
                color="purple"
              />
            </div>
            {progress.currentStreak > 0 && (
              <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center gap-2 text-orange-800">
                  <Zap className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    You're on fire! Keep the streak going!
                  </span>
                </div>
              </div>
            )}
          </ProgressSection>

          {/* Recent Activity */}
          {progress.lastActivityDate && (
            <div className="pt-3 border-t">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                Last activity: {new Date(progress.lastActivityDate).toLocaleDateString()}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

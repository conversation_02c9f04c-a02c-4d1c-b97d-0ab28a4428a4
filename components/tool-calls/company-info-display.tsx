"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Building, Users, Target, Workflow, Mail, Phone, Globe } from "lucide-react"

interface CompanyInfoDisplayProps {
  result: Record<string, {
    title: string
    description: string
    values?: Array<{
      name: string
      description: string
    }>
    phases?: Array<{
      phase: string
      description: string
      duration: string
    }>
    members?: Array<{
      role: string
      expertise: string
    }>
    email?: string
    phone?: string
    website?: string
    consultation?: string
    response_time?: string
    availability?: string
    founded?: string
    location?: string
    specialties?: string[]
    mission?: string
    size?: string
    approach?: string
    methodology?: string
  }>
}

export function CompanyInfoDisplay({ result }: CompanyInfoDisplayProps) {
  const getIconForSection = (key: string) => {
    switch (key) {
      case 'about': return Building
      case 'team': return Users
      case 'values': return Target
      case 'process': return Workflow
      case 'contact': return Mail
      default: return Building
    }
  }

  const getColorForSection = (key: string) => {
    switch (key) {
      case 'about': return 'text-blue-500'
      case 'team': return 'text-green-500'
      case 'values': return 'text-purple-500'
      case 'process': return 'text-orange-500'
      case 'contact': return 'text-pink-500'
      default: return 'text-gray-500'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 space-y-8"
    >
      {Object.entries(result).map(([key, info], sectionIndex) => {
        const Icon = getIconForSection(key)
        const colorClass = getColorForSection(key)
        
        return (
          <motion.div
            key={key}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: sectionIndex * 0.2 }}
            className="border-b pb-6 last:border-b-0"
          >
            {/* Section Header */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: sectionIndex * 0.2 + 0.1 }}
              className="flex items-center gap-3 mb-4"
            >
              <div className={`p-2 rounded-lg bg-muted/50 ${colorClass}`}>
                <Icon className="h-5 w-5" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-primary">{info.title}</h2>
                <p className="text-muted-foreground text-sm">{info.description}</p>
              </div>
            </motion.div>

            {/* Basic Info */}
            {(info.founded || info.location || info.mission) && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: sectionIndex * 0.2 + 0.2 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"
              >
                {info.founded && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Founded: {info.founded}</Badge>
                  </div>
                )}
                {info.location && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Location: {info.location}</Badge>
                  </div>
                )}
                {info.size && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Team Size: {info.size}</Badge>
                  </div>
                )}
              </motion.div>
            )}

            {/* Mission */}
            {info.mission && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: sectionIndex * 0.2 + 0.3 }}
                className="bg-primary/5 rounded-lg p-4 mb-4 border border-primary/20"
              >
                <h4 className="font-semibold text-primary mb-2">Our Mission</h4>
                <p className="text-sm text-muted-foreground">{info.mission}</p>
              </motion.div>
            )}

            {/* Specialties */}
            {info.specialties && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: sectionIndex * 0.2 + 0.4 }}
                className="mb-4"
              >
                <h4 className="font-semibold mb-2">Specialties</h4>
                <div className="flex flex-wrap gap-2">
                  {info.specialties.map((specialty, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: sectionIndex * 0.2 + 0.5 + index * 0.05 }}
                    >
                      <Badge variant="secondary">{specialty}</Badge>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Values */}
            {info.values && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: sectionIndex * 0.2 + 0.5 }}
                className="space-y-3"
              >
                {info.values.map((value, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: sectionIndex * 0.2 + 0.6 + index * 0.1 }}
                    className="border-l-4 border-primary pl-4 py-2 bg-gradient-to-r from-primary/5 to-transparent rounded-r-lg"
                  >
                    <h4 className="font-semibold text-primary">{value.name}</h4>
                    <p className="text-sm text-muted-foreground">{value.description}</p>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {/* Process Phases */}
            {info.phases && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: sectionIndex * 0.2 + 0.5 }}
                className="space-y-4"
              >
                {info.methodology && (
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                    <h4 className="font-semibold text-orange-800 mb-1">Methodology</h4>
                    <p className="text-sm text-orange-700">{info.methodology}</p>
                  </div>
                )}
                {info.phases.map((phase, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: sectionIndex * 0.2 + 0.6 + index * 0.1 }}
                    className="flex gap-4 p-3 rounded-lg bg-gradient-to-r from-muted/30 to-transparent hover:from-muted/50 transition-colors"
                  >
                    <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-primary">{phase.phase}</h4>
                      <p className="text-sm text-muted-foreground mb-1">{phase.description}</p>
                      <Badge variant="outline" className="text-xs">{phase.duration}</Badge>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {/* Team Members */}
            {info.members && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: sectionIndex * 0.2 + 0.5 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-3"
              >
                {info.approach && (
                  <div className="md:col-span-2 bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                    <h4 className="font-semibold text-green-800 mb-1">Our Approach</h4>
                    <p className="text-sm text-green-700">{info.approach}</p>
                  </div>
                )}
                {info.members.map((member, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: sectionIndex * 0.2 + 0.6 + index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    className="border rounded-lg p-3 bg-gradient-to-br from-background to-muted/20 hover:shadow-md transition-all"
                  >
                    <h4 className="font-semibold text-primary">{member.role}</h4>
                    <p className="text-sm text-muted-foreground">{member.expertise}</p>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {/* Contact Information */}
            {(info.email || info.phone || info.website) && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: sectionIndex * 0.2 + 0.5 }}
                className="bg-gradient-to-r from-pink-50 to-purple-50 border border-pink-200 rounded-lg p-4"
              >
                <h4 className="font-semibold text-pink-800 mb-3">Contact Details</h4>
                <div className="space-y-2">
                  {info.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-pink-600" />
                      <span className="text-sm text-pink-700">{info.email}</span>
                    </div>
                  )}
                  {info.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-pink-600" />
                      <span className="text-sm text-pink-700">{info.phone}</span>
                    </div>
                  )}
                  {info.website && (
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-pink-600" />
                      <span className="text-sm text-pink-700">{info.website}</span>
                    </div>
                  )}
                  {info.consultation && (
                    <div className="mt-3 p-2 bg-pink-100 rounded border border-pink-300">
                      <p className="text-sm text-pink-800 font-medium">{info.consultation}</p>
                    </div>
                  )}
                  {info.response_time && (
                    <p className="text-xs text-pink-600">Response time: {info.response_time}</p>
                  )}
                  {info.availability && (
                    <p className="text-xs text-pink-600">Available: {info.availability}</p>
                  )}
                </div>
              </motion.div>
            )}
          </motion.div>
        )
      })}
    </motion.div>
  )
}

"use client"

import { motion } from "framer-motion"
import { ServiceInfoDisplay } from "./service-info-display"
import { ProjectProposalDisplay } from "./project-proposal-display"
import { CompanyInfoDisplay } from "./company-info-display"
import { CodeGenerationDisplay } from "./code-generation-display"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Code, Copy, Check, AlertCircle } from "lucide-react"
import { useState } from "react"

interface ToolCallRendererProps {
  part: {
    type: 'tool-invocation'
    toolInvocation: any // Using any to handle different AI SDK types
  }
}

export function ToolCallRenderer({ part }: ToolCallRendererProps) {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const toolName = part.toolInvocation.toolName
  const result = (part.toolInvocation as any).result

  // Loading state
  if (part.toolInvocation.state === 'call' || part.toolInvocation.state === 'partial-call') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <Card className="mt-4 border-primary/20 bg-gradient-to-r from-primary/5 via-blue-500/5 to-purple-500/5">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Code className="h-4 w-4 text-primary" />
              </motion.div>
              <div>
                <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                  {toolName}
                </Badge>
                <p className="text-sm text-muted-foreground mt-1">
                  Generating {toolName.replace(/([A-Z])/g, ' $1').toLowerCase()}...
                </p>
              </div>
            </div>
            
            {/* Loading animation */}
            <motion.div
              className="mt-3 flex items-center gap-1"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-primary rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: i * 0.2
                  }}
                />
              ))}
              <span className="text-xs text-muted-foreground ml-2">Processing...</span>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  // Error state
  if (!result) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mt-4"
      >
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <Badge variant="destructive">{toolName}</Badge>
            </div>
            <p className="text-sm text-red-600 mt-2">
              Tool execution failed or returned no result.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  // Success state with tool-specific rendering
  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ delay: 0.3, duration: 0.5, ease: "easeOut" }}
    >
      <Card className="mt-4 border-primary/20 bg-gradient-to-r from-primary/5 via-blue-500/5 to-purple-500/5 overflow-hidden">
        {/* Tool Header */}
        <div className="border-b border-primary/10 p-4 bg-gradient-to-r from-primary/10 to-blue-500/10">
          <div className="flex items-center justify-between">
            <motion.div
              initial={{ x: -10, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="flex items-center gap-2"
            >
              <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                <Code className="h-3 w-3 mr-1" />
                {toolName}
              </Badge>
              <motion.div
                className="w-2 h-2 bg-green-500 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <span className="text-xs text-green-600 font-medium">Completed</span>
            </motion.div>

            {/* Copy button for code results */}
            {result.code && (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(result.code)}
                  className="hover:bg-primary/10"
                >
                  <motion.div
                    animate={copied ? { scale: [1, 1.2, 1] } : {}}
                    transition={{ duration: 0.3 }}
                  >
                    {copied ? (
                      <Check className="h-3 w-3 text-green-500" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </motion.div>
                </Button>
              </motion.div>
            )}
          </div>
        </div>

        {/* Tool-specific content */}
        <div className="bg-white">
          {(() => {
            switch (toolName) {
              case 'getServiceInfo':
                if (result.service) {
                  return <ServiceInfoDisplay result={result} />
                }
                break
              
              case 'generateProposal':
                if (result.proposal) {
                  return <ProjectProposalDisplay result={result} />
                }
                break
              
              case 'getCompanyInfo':
                return <CompanyInfoDisplay result={result} />
              
              case 'generateComponent':
              case 'generateLandingPage':
              case 'generateForm':
                if (result.code) {
                  return <CodeGenerationDisplay result={result} toolName={toolName} />
                }
                break
              
              default:
                // Fallback for unknown tools
                return (
                  <div className="p-6">
                    <h3 className="font-semibold mb-4">Tool Result</h3>
                    <pre className="bg-muted p-4 rounded-lg text-xs overflow-auto max-h-96">
                      <code>{JSON.stringify(result, null, 2)}</code>
                    </pre>
                  </div>
                )
            }

            // Fallback if tool is recognized but result format is unexpected
            return (
              <div className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <AlertCircle className="h-4 w-4 text-amber-500" />
                  <h3 className="font-semibold">Unexpected Result Format</h3>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  The tool executed successfully but returned an unexpected format.
                </p>
                <pre className="bg-muted p-4 rounded-lg text-xs overflow-auto max-h-96">
                  <code>{JSON.stringify(result, null, 2)}</code>
                </pre>
              </div>
            )
          })()}
        </div>
      </Card>
    </motion.div>
  )
}

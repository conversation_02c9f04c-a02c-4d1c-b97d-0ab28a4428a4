"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON><PERSON>, Clock, DollarSign, Zap } from "lucide-react"

interface ProjectProposalDisplayProps {
  result: {
    proposal: {
      title: string
      overview: string
      budget_range: string
      timeline_estimate: string
      budget_recommendation: string
      deliverables: string[]
      technologies: string[]
      next_steps: string[]
      included_services: string[]
    }
    call_to_action: string
  }
}

export function ProjectProposalDisplay({ result }: ProjectProposalDisplayProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 space-y-6"
    >
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="border-b pb-4"
      >
        <h2 className="text-2xl font-bold text-primary mb-2">
          {result.proposal.title}
        </h2>
        <p className="text-muted-foreground">
          {result.proposal.overview}
        </p>
      </motion.div>

      {/* Project Details */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50 border border-green-200">
          <DollarSign className="h-5 w-5 text-green-600" />
          <div>
            <p className="font-medium text-green-800">Budget Range</p>
            <p className="text-sm text-green-600">{result.proposal.budget_range}</p>
          </div>
        </div>
        <div className="flex items-center gap-3 p-3 rounded-lg bg-blue-50 border border-blue-200">
          <Clock className="h-5 w-5 text-blue-600" />
          <div>
            <p className="font-medium text-blue-800">Timeline</p>
            <p className="text-sm text-blue-600">{result.proposal.timeline_estimate}</p>
          </div>
        </div>
      </motion.div>

      {/* Budget Recommendation */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-amber-50 border border-amber-200 rounded-lg p-4"
      >
        <div className="flex items-center gap-2 mb-2">
          <Zap className="h-4 w-4 text-amber-600" />
          <h3 className="font-semibold text-amber-800">Recommendation</h3>
        </div>
        <p className="text-sm text-amber-700">{result.proposal.budget_recommendation}</p>
      </motion.div>

      {/* Deliverables */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-500" />
          Deliverables
        </h3>
        <div className="space-y-2">
          {result.proposal.deliverables.map((item: string, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 + index * 0.05 }}
              className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0" />
              <span className="text-sm">{item}</span>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Technologies */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full" />
          Technologies
        </h3>
        <div className="flex flex-wrap gap-2">
          {result.proposal.technologies.map((tech: string, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.7 + index * 0.05 }}
              whileHover={{ scale: 1.05 }}
            >
              <Badge variant="outline" className="hover:bg-blue-50 transition-colors">
                {tech}
              </Badge>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Next Steps */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <div className="w-2 h-2 bg-purple-500 rounded-full" />
          Next Steps
        </h3>
        <div className="space-y-3">
          {result.proposal.next_steps.map((step: string, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.9 + index * 0.1 }}
              className="flex items-center gap-3 p-3 rounded-lg bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200"
            >
              <div className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0">
                {index + 1}
              </div>
              <span className="text-sm text-purple-800">{step}</span>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Included Services */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
        className="bg-muted/30 rounded-lg p-4"
      >
        <h3 className="font-semibold mb-3 text-muted-foreground">Included Services</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {result.proposal.included_services.map((service: string, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 1.1 + index * 0.05 }}
              className="flex items-center gap-2 text-sm text-muted-foreground"
            >
              <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
              <span>{service}</span>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2 }}
        className="bg-gradient-to-r from-primary/10 via-blue-500/10 to-purple-500/10 rounded-lg p-6 border border-primary/20 text-center"
      >
        <motion.p
          initial={{ scale: 0.95 }}
          animate={{ scale: 1 }}
          transition={{ delay: 1.3, type: "spring", stiffness: 200 }}
          className="font-medium text-primary text-lg"
        >
          {result.call_to_action}
        </motion.p>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.4 }}
          className="mt-2 text-sm text-muted-foreground"
        >
          Schedule your free consultation today!
        </motion.div>
      </motion.div>
    </motion.div>
  )
}

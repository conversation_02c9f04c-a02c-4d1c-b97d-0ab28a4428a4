// Tool Call Components
export { ServiceInfoDisplay } from './service-info-display'
export { ProjectProposalDisplay } from './project-proposal-display'
export { CompanyInfoDisplay } from './company-info-display'
export { CodeGenerationDisplay } from './code-generation-display'
export { Tool<PERSON>allRenderer } from './tool-call-renderer'

// Types
export interface ToolCallPart {
  type: 'tool-invocation'
  toolInvocation: {
    toolName: string
    state: 'call' | 'result'
    result?: any
  }
}

export interface ServiceInfo {
  service: {
    title: string
    description: string
    features: string[]
    technologies: string[]
    packages?: Record<string, {
      price: string
      timeline: string
      pages?: string
      features?: string
      deliverables?: string
    }>
  }
}

export interface ProjectProposal {
  proposal: {
    title: string
    overview: string
    budget_range: string
    timeline_estimate: string
    budget_recommendation: string
    deliverables: string[]
    technologies: string[]
    next_steps: string[]
    included_services: string[]
  }
  call_to_action: string
}

export interface CompanyInfo {
  [key: string]: {
    title: string
    description: string
    values?: Array<{
      name: string
      description: string
    }>
    phases?: Array<{
      phase: string
      description: string
      duration: string
    }>
    members?: Array<{
      role: string
      expertise: string
    }>
    email?: string
    phone?: string
    website?: string
    consultation?: string
    response_time?: string
    availability?: string
    founded?: string
    location?: string
    specialties?: string[]
    mission?: string
    size?: string
    approach?: string
    methodology?: string
  }
}

export interface CodeGeneration {
  code: string
  componentName?: string
  description?: string
  title?: string
  features?: string[]
  styling?: string
  technologies?: string[]
}

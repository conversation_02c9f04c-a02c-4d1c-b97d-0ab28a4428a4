"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"

interface ServiceInfoDisplayProps {
  result: {
    service: {
      title: string
      description: string
      features: string[]
      technologies: string[]
      packages?: Record<string, {
        price: string
        timeline: string
        pages?: string
        features?: string
        deliverables?: string
      }>
    }
  }
}

export function ServiceInfoDisplay({ result }: ServiceInfoDisplayProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 space-y-6"
    >
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="border-b pb-4"
      >
        <h2 className="text-2xl font-bold text-primary mb-2">
          {result.service.title}
        </h2>
        <p className="text-muted-foreground">
          {result.service.description}
        </p>
      </motion.div>

      {/* Key Features */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <div className="w-2 h-2 bg-primary rounded-full" />
          Key Features
        </h3>
        <div className="grid grid-cols-1 gap-2">
          {result.service.features.map((feature: string, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 + index * 0.05 }}
              className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
              <span className="text-sm">{feature}</span>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Technologies */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full" />
          Technologies
        </h3>
        <div className="flex flex-wrap gap-2">
          {result.service.technologies.map((tech: string, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 + index * 0.05 }}
              whileHover={{ scale: 1.05 }}
            >
              <Badge variant="secondary" className="hover:bg-primary/10 transition-colors">
                {tech}
              </Badge>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Pricing Packages */}
      {result.service.packages && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full" />
            Pricing Packages
          </h3>
          <div className="space-y-3">
            {Object.entries(result.service.packages).map(([name, pkg], index) => (
              <motion.div
                key={name}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                whileHover={{ scale: 1.02, y: -2 }}
                className="border rounded-lg p-4 hover:shadow-md transition-all duration-200 bg-gradient-to-r from-background to-muted/20"
              >
                <h4 className="font-medium capitalize mb-2 text-primary">
                  {name}
                </h4>
                <div className="text-sm space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-green-600">Price:</span>
                    <span>{pkg.price}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-blue-600">Timeline:</span>
                    <span>{pkg.timeline}</span>
                  </div>
                  {pkg.pages && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-purple-600">Pages:</span>
                      <span>{pkg.pages}</span>
                    </div>
                  )}
                  {pkg.features && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-orange-600">Features:</span>
                      <span>{pkg.features}</span>
                    </div>
                  )}
                  {pkg.deliverables && (
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-pink-600">Deliverables:</span>
                      <span>{pkg.deliverables}</span>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="bg-gradient-to-r from-primary/5 via-blue-500/5 to-purple-500/5 rounded-lg p-4 border border-primary/20"
      >
        <p className="text-center text-sm text-muted-foreground">
          Ready to get started? 
          <span className="text-primary font-medium ml-1">
            Contact us for a free consultation!
          </span>
        </p>
      </motion.div>
    </motion.div>
  )
}

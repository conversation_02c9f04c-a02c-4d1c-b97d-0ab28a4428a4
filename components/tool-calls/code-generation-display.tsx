"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Copy, Check, Download, Eye, Code2 } from "lucide-react"
import { useState } from "react"

interface CodeGenerationDisplayProps {
  result: {
    code: string
    componentName?: string
    description?: string
    title?: string
    features?: string[]
    styling?: string
    technologies?: string[]
  }
  toolName: string
}

export function CodeGenerationDisplay({ result, toolName }: CodeGenerationDisplayProps) {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const getToolIcon = () => {
    switch (toolName) {
      case 'generateComponent': return Code2
      case 'generateLandingPage': return Eye
      case 'generateForm': return Code2
      default: return Code2
    }
  }

  const getToolColor = () => {
    switch (toolName) {
      case 'generateComponent': return 'text-blue-500'
      case 'generateLandingPage': return 'text-purple-500'
      case 'generateForm': return 'text-green-500'
      default: return 'text-gray-500'
    }
  }

  const ToolIcon = getToolIcon()
  const toolColor = getToolColor()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 space-y-6"
    >
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="border-b pb-4"
      >
        <div className="flex items-center gap-3 mb-2">
          <div className={`p-2 rounded-lg bg-muted/50 ${toolColor}`}>
            <ToolIcon className="h-5 w-5" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-primary">
              {result.componentName || result.title || 'Generated Code'}
            </h2>
            <Badge variant="secondary" className="mt-1">
              {toolName.replace('generate', '').replace(/([A-Z])/g, ' $1').trim()}
            </Badge>
          </div>
        </div>
        {result.description && (
          <p className="text-muted-foreground">{result.description}</p>
        )}
      </motion.div>

      {/* Features */}
      {result.features && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
            Features
          </h3>
          <div className="grid grid-cols-1 gap-2">
            {result.features.map((feature: string, index: number) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + index * 0.05 }}
                className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0" />
                <span className="text-sm">{feature}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Technologies */}
      {result.technologies && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full" />
            Technologies
          </h3>
          <div className="flex flex-wrap gap-2">
            {result.technologies.map((tech: string, index: number) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 + index * 0.05 }}
                whileHover={{ scale: 1.05 }}
              >
                <Badge variant="outline" className="hover:bg-purple-50 transition-colors">
                  {tech}
                </Badge>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Styling Info */}
      {result.styling && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-muted/30 rounded-lg p-4"
        >
          <h3 className="font-semibold mb-2">Styling Approach</h3>
          <p className="text-sm text-muted-foreground">{result.styling}</p>
        </motion.div>
      )}

      {/* Code Display */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="space-y-3"
      >
        <div className="flex items-center justify-between">
          <h3 className="font-semibold flex items-center gap-2">
            <Code2 className="h-4 w-4 text-primary" />
            Generated Code
          </h3>
          <div className="flex items-center gap-2">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(result.code)}
                className="hover:bg-primary/10"
              >
                <motion.div
                  animate={copied ? { scale: [1, 1.2, 1] } : {}}
                  transition={{ duration: 0.3 }}
                >
                  {copied ? (
                    <Check className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <Copy className="h-3 w-3 mr-1" />
                  )}
                </motion.div>
                {copied ? 'Copied!' : 'Copy'}
              </Button>
            </motion.div>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button variant="outline" size="sm">
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>
            </motion.div>
          </div>
        </div>

        <motion.div
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="relative"
        >
          {/* Animated border */}
          <motion.div
            className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-primary via-blue-500 to-purple-500"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ delay: 0.9, duration: 0.8, ease: "easeOut" }}
          />
          
          <pre className="bg-muted/50 p-4 rounded-lg text-xs overflow-x-auto border border-border/50 relative max-h-96">
            <code className="text-foreground font-mono leading-relaxed">
              {result.code}
            </code>
          </pre>
        </motion.div>
      </motion.div>

      {/* Usage Instructions */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
        className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4"
      >
        <h4 className="font-semibold text-blue-800 mb-2">Usage Instructions</h4>
        <div className="space-y-1 text-sm text-blue-700">
          <p>• Copy the code above and save it as a .tsx file</p>
          <p>• Make sure you have the required dependencies installed</p>
          <p>• Import and use the component in your React application</p>
          <p>• Customize the styling and functionality as needed</p>
        </div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.1 }}
        className="bg-gradient-to-r from-primary/5 via-blue-500/5 to-purple-500/5 rounded-lg p-4 border border-primary/20 text-center"
      >
        <p className="text-sm text-muted-foreground">
          Need modifications or have questions? 
          <span className="text-primary font-medium ml-1">
            Ask me to adjust the code!
          </span>
        </p>
      </motion.div>
    </motion.div>
  )
}

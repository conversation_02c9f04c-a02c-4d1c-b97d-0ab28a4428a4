"use client";

import React, { useRef, useEffect, useState, useC<PERSON>back, useMemo } from "react";
import { motion } from "framer-motion";
import { useChat } from "@ai-sdk/react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

// Import modular components
import {
  FloatingElements,
  QuickActionsGrid,
  ChatMessage,
  EnhancedInputArea,
  PreviewPanel,
  useUIStore,
  useChatStore,
  useAnimationStore,
} from "./components";

// Import tool renderer
import { ToolCallRenderer } from "@/components/tool-calls";

// Import icons
import {
  Camera,
  FileImage,
  Upload,
  Layout,
  FileText,
  Code,
  MessageSquare,
  Wrench,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

// Import types
import type { QuickAction } from "./store/types";
import { Button } from "@/components/ui/button";

// Default quick actions
const QUICK_ACTIONS: QuickAction[] = [
  {
    icon: Camera,
    label: "Clone a Screenshot",
    description: "Upload an image to recreate",
  },
  {
    icon: FileImage,
    label: "Import from Figma",
    description: "Connect your Figma designs",
  },
  {
    icon: Upload,
    label: "Upload a Project",
    description: "Import existing code",
  },
  {
    icon: Layout,
    label: "Landing Page",
    description: "Create a landing page",
  },
  {
    icon: FileText,
    label: "Sign Up Form",
    description: "Build a registration form",
  },
  {
    icon: Code,
    label: "Component Library",
    description: "Generate reusable components",
  },
];

export interface AgentInterfaceProps {
  className?: string;
  children?: React.ReactNode;
}

export function AgentInterface({
  className = "",
  children,
}: AgentInterfaceProps = {}) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [activeToolInvocations, setActiveToolInvocations] = useState<any[]>([]);
  const [isMobile, setIsMobile] = useState(false);

  // Check for mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      // Auto-collapse sidebar on mobile
      if (window.innerWidth < 768) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Store hooks
  const { state: uiState, actions: uiActions } = useUIStore();
  const { actions: chatActions } = useChatStore();
  const { state: animationState } = useAnimationStore();

  // Memoize chat configuration to prevent unnecessary re-initializations
  const chatConfig = useMemo(() => ({
    maxSteps: 5,
    body: {
      assistantType: uiState.assistantType,
    },
  }), [uiState.assistantType]);

  // Memoize callbacks to prevent re-renders
  const onFinish = useCallback((message: any) => {
    // Check if the message contains tool calls that should show preview
    const hasToolCall = message.parts?.some(
      (part: any) =>
        part.type === "tool-invocation" &&
        [
          "generateComponent",
          "generateLandingPage",
          "generateForm",
          "getServiceInfo",
          "generateProposal",
          "getCompanyInfo",
        ].includes(part.toolInvocation.toolName)
    );

    if (hasToolCall) {
      uiActions.setShowPreview(true);
    }
  }, [uiActions]);

  const onError = useCallback((error: Error) => {
    console.error("Chat error:", error);
    chatActions.setError(error.message);
  }, [chatActions]);

  // AI SDK Chat hook
  const { messages, input, handleInputChange, handleSubmit, append, status } = useChat({
    ...chatConfig,
    onFinish,
    onError,
  });

  // Sync chat state with store (one-way sync to prevent loops)
  useEffect(() => {
    chatActions.setMessages(messages);
  }, [messages, chatActions]);

  useEffect(() => {
    chatActions.setInput(input);
  }, [input, chatActions]);

  useEffect(() => {
    chatActions.setStatus(status === "streaming" ? "streaming" : "idle");
  }, [status, chatActions]);

  // Track tool invocations for the main content area
  useEffect(() => {
    const toolInvocations: any[] = [];

    messages.forEach((message) => {
      if (message.parts) {
        message.parts.forEach((part) => {
          if (part.type === "tool-invocation") {
            toolInvocations.push({
              ...part,
              messageId: message.id,
              timestamp: new Date(),
            });
          }
        });
      }
    });

    setActiveToolInvocations(toolInvocations);
  }, [messages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Handle quick action clicks
  const handleQuickAction = useCallback((action: QuickAction) => {
    const prompt = `Help me ${action.label.toLowerCase()}: ${action.description}`;
    append({
      role: "user",
      content: prompt,
    });
  }, [append]);

  // Handle send button click
  const handleSend = useCallback(() => {
    if (input.trim()) {
      const syntheticEvent = {
        preventDefault: () => {},
        currentTarget: {
          elements: {
            message: { value: input },
          },
        },
      } as any;
      handleSubmit(syntheticEvent);
    }
  }, [input, handleSubmit]);

  // Handle input change with proper memoization
  const handleInputChangeWrapper = useCallback((value: string) => {
    handleInputChange({ target: { value } } as any);
  }, [handleInputChange]);

  // Render initial state with horizontal layout
  if (messages.length === 0) {
    return (
      <div className={cn("flex-1 flex relative z-10", className)}>
        {/* Floating Elements Background */}
        <FloatingElements
          elementCount={6}
          enableRandomMovement
          enablePulse
        />

        {/* Mobile backdrop overlay */}
        {isMobile && !sidebarCollapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setSidebarCollapsed(true)}
          />
        )}

        {/* Left Sidebar - Chat Interface */}
        <motion.div
          initial={{ x: -300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className={cn(
            "flex flex-col border-r border-border/50 bg-background/80 backdrop-blur-sm transition-all duration-300",
            sidebarCollapsed
              ? "w-16"
              : isMobile
                ? "w-full"
                : "w-[30%] min-w-[320px] max-w-[400px]",
            isMobile && !sidebarCollapsed && "absolute inset-y-0 left-0 z-50 shadow-xl"
          )}
        >
          {/* Sidebar Header */}
          <div className="p-4 border-b border-border/50 flex items-center justify-between">
            {!sidebarCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="flex items-center gap-2"
              >
                <MessageSquare className="h-5 w-5 text-primary" />
                <h2 className="font-semibold">AI Assistant</h2>
              </motion.div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="h-8 w-8 p-0"
            >
              {sidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
          </div>

          {/* Chat Content */}
          {!sidebarCollapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="flex-1 flex flex-col p-4"
            >
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold mb-4 bg-gradient-to-r from-primary via-blue-500 to-purple-500 bg-clip-text text-transparent">
                  What can I help you ship?
                </h1>
                <p className="text-muted-foreground text-sm mb-4">
                  You are running low on credits. Your limit will reset on June 20.
                  <button className="ml-2 text-primary hover:text-primary/80 transition-colors">
                    Upgrade Plan ✕
                  </button>
                </p>
              </div>

              <div className="mt-auto">
                <EnhancedInputArea
                  value={input}
                  onChange={handleInputChangeWrapper}
                  onSend={handleSend}
                  variant="initial"
                  assistantType={uiState.assistantType}
                  onAssistantTypeChange={uiActions.setAssistantType}
                  isStreaming={status === "streaming"}
                  enableGradientBorder
                  enableGlowEffect
                  size="md"
                />
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Right Main Area - Welcome Content */}
        <motion.div
          initial={{ x: 300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="flex-1 flex flex-col items-center justify-center p-8 relative"
        >
          <motion.div
            initial={{ opacity: 0, y: 30, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.5 }}
            className="text-center max-w-2xl mx-auto"
          >
            <div className="mb-8">
              <Wrench className="h-16 w-16 text-primary/60 mx-auto mb-4" />
              <h2 className="text-3xl font-bold mb-4 text-muted-foreground">
                Ready to Build
              </h2>
              <p className="text-muted-foreground">
                Start a conversation to see AI tools and outputs appear here
              </p>
            </div>

            <QuickActionsGrid
              actions={QUICK_ACTIONS}
              onActionClick={handleQuickAction}
              enableHoverEffects
              enableStaggeredAnimation
            />
          </motion.div>
        </motion.div>
      </div>
    );
  }

  // Render chat state with horizontal split layout
  return (
    <div className={cn("flex-1 flex relative z-10 h-screen", className)}>
      {/* Mobile backdrop overlay */}
      {isMobile && !sidebarCollapsed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-40"
          onClick={() => setSidebarCollapsed(true)}
        />
      )}

      {/* Left Sidebar - Chat Interface */}
      <motion.div
        initial={{ x: -300, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className={cn(
          "flex flex-col border-r border-border/50 bg-background/80 backdrop-blur-sm transition-all duration-300",
          sidebarCollapsed
            ? "w-16"
            : isMobile
              ? "w-full"
              : "w-[30%] min-w-[320px] max-w-[400px]",
          isMobile && !sidebarCollapsed && "absolute inset-y-0 left-0 z-50 shadow-xl"
        )}
      >
        {/* Sidebar Header */}
        <div className="p-4 border-b border-border/50 flex items-center justify-between">
          {!sidebarCollapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="flex items-center gap-2"
            >
              <MessageSquare className="h-5 w-5 text-primary" />
              <h2 className="font-semibold">AI Assistant</h2>
            </motion.div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="h-8 w-8 p-0"
          >
            {sidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>

        {/* Chat Messages */}
        {!sidebarCollapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="flex-1 flex flex-col"
          >
            <ScrollArea className="flex-1">
              <div className="p-4 space-y-4">
                {messages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    isStreaming={status === "streaming"}
                    isLatest={message.id === messages[messages.length - 1]?.id}
                    showActions
                    enableAnimations={animationState.enableAnimations}
                  />
                ))}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Input Area */}
            <div className="p-4 border-t border-border/50 bg-background/90">
              <EnhancedInputArea
                value={input}
                onChange={handleInputChangeWrapper}
                onSend={handleSend}
                variant="chat"
                assistantType={uiState.assistantType}
                onAssistantTypeChange={uiActions.setAssistantType}
                isStreaming={status === "streaming"}
                placeholder="Ask a follow up..."
                size="sm"
              />
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Right Main Area - Tool Renderer */}
      <motion.div
        initial={{ x: 300, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
        className="flex-1 flex flex-col bg-background/50"
      >
        {/* Main Content Header */}
        <div className="p-4 border-b border-border/50 bg-background/80 backdrop-blur-sm">
          <div className="flex items-center gap-2">
            <Wrench className="h-5 w-5 text-primary" />
            <h2 className="font-semibold">Tool Outputs</h2>
            {activeToolInvocations.length > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="ml-auto"
              >
                <div className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs font-medium">
                  {activeToolInvocations.length} active
                </div>
              </motion.div>
            )}
          </div>
        </div>

        {/* Tool Renderer Content */}
        <ScrollArea className="flex-1">
          <div className="p-6">
            {activeToolInvocations.length > 0 ? (
              <div className="space-y-6">
                {activeToolInvocations.map((toolInvocation, index) => (
                  <motion.div
                    key={`${toolInvocation.messageId}-${index}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <ToolCallRenderer part={toolInvocation} />
                  </motion.div>
                ))}
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex flex-col items-center justify-center h-full min-h-[400px] text-center"
              >
                <div className="mb-6">
                  <div className="w-16 h-16 rounded-full bg-muted/50 flex items-center justify-center mb-4">
                    <Wrench className="h-8 w-8 text-muted-foreground/50" />
                  </div>
                  <h3 className="text-lg font-semibold text-muted-foreground mb-2">
                    No Tool Outputs Yet
                  </h3>
                  <p className="text-muted-foreground/70 max-w-md">
                    When the AI uses tools to generate code, analyze data, or perform other tasks,
                    the results will appear here.
                  </p>
                </div>
              </motion.div>
            )}
          </div>
        </ScrollArea>
      </motion.div>

      {/* Preview Panel (if needed) */}
      <PreviewPanel
        messages={messages}
        isVisible={uiState.showPreview}
        onClose={() => uiActions.setShowPreview(false)}
        enableAnimations={animationState.enableAnimations}
      />

      {children}
    </div>
  );
}

// Export the main component as default
export default AgentInterface;

// Re-export everything from components for convenience
export * from "./components";
export * from "./store";

import { create } from "zustand";
import { devtools, persist, subscribeWithSelector } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { useMemo } from "react";
import type {
  V0InterfaceStore,
  V0InterfaceState,
  UIState,
  ChatState,
  AnimationState,
  SettingsState,
  AssistantType,
} from "./types";

// Initial States
const initialUIState: UIState = {
  selectedProject: "1",
  showPreview: false,
  isRecording: false,
  copied: false,
  assistantType: "consultant",
  isExpanded: false,
  showToolbar: false,
  isFocused: false,
  isTyping: false,
};

const initialChatState: ChatState = {
  messages: [],
  input: "",
  status: "idle",
  isLoading: false,
  error: null,
};

const initialAnimationState: AnimationState = {
  enableAnimations: true,
  animationSpeed: 1,
  particleCount: 50,
  gradientAnimation: true,
};

const initialSettingsState: SettingsState = {
  theme: "system",
  autoSave: true,
  showLineNumbers: false,
  enableCollaboration: false,
  maxHistorySize: 100,
};

// Create the store
export const useV0InterfaceStore = create<V0InterfaceStore>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          // State
          ui: initialUIState,
          chat: initialChatState,
          animation: initialAnimationState,
          settings: initialSettingsState,

          // UI Actions
          setSelectedProject: (projectId: string) =>
            set((state) => {
              state.ui.selectedProject = projectId;
            }),
          setShowPreview: (show: boolean) =>
            set((state) => {
              state.ui.showPreview = show;
            }),
          setIsRecording: (recording: boolean) =>
            set((state) => {
              state.ui.isRecording = recording;
            }),
          setCopied: (copied: boolean) =>
            set((state) => {
              state.ui.copied = copied;
            }),
          setAssistantType: (type: AssistantType) =>
            set((state) => {
              state.ui.assistantType = type;
            }),
          setIsExpanded: (expanded: boolean) =>
            set((state) => {
              state.ui.isExpanded = expanded;
            }),
          setShowToolbar: (show: boolean) =>
            set((state) => {
              state.ui.showToolbar = show;
            }),
          setIsFocused: (focused: boolean) =>
            set((state) => {
              state.ui.isFocused = focused;
            }),
          setIsTyping: (typing: boolean) =>
            set((state) => {
              state.ui.isTyping = typing;
            }),
          togglePreview: () =>
            set((state) => {
              state.ui.showPreview = !state.ui.showPreview;
            }),
          toggleRecording: () =>
            set((state) => {
              state.ui.isRecording = !state.ui.isRecording;
            }),
          toggleExpanded: () =>
            set((state) => {
              state.ui.isExpanded = !state.ui.isExpanded;
            }),
          toggleToolbar: () =>
            set((state) => {
              state.ui.showToolbar = !state.ui.showToolbar;
            }),

          // Chat Actions
          setMessages: (messages) =>
            set((state) => {
              state.chat.messages = [...messages];
            }),
          addMessage: (message) =>
            set((state) => {
              state.chat.messages.push(message);
            }),
          updateMessage: (id, updates) =>
            set((state) => {
              const index = state.chat.messages.findIndex((m) => m.id === id);
              if (index !== -1) {
                Object.assign(state.chat.messages[index], updates);
              }
            }),
          removeMessage: (id) =>
            set((state) => {
              state.chat.messages = state.chat.messages.filter((m) => m.id !== id);
            }),
          setInput: (input) =>
            set((state) => {
              state.chat.input = input;
            }),
          setStatus: (status) =>
            set((state) => {
              state.chat.status = status;
            }),
          setIsLoading: (loading) =>
            set((state) => {
              state.chat.isLoading = loading;
            }),
          setError: (error) =>
            set((state) => {
              state.chat.error = error;
            }),
          clearMessages: () =>
            set((state) => {
              state.chat.messages = [];
            }),
          clearInput: () =>
            set((state) => {
              state.chat.input = "";
            }),
          clearError: () =>
            set((state) => {
              state.chat.error = null;
            }),

          // Animation Actions
          setEnableAnimations: (enable) =>
            set((state) => {
              state.animation.enableAnimations = enable;
            }),
          setAnimationSpeed: (speed) =>
            set((state) => {
              state.animation.animationSpeed = speed;
            }),
          setParticleCount: (count) =>
            set((state) => {
              state.animation.particleCount = count;
            }),
          setGradientAnimation: (enable) =>
            set((state) => {
              state.animation.gradientAnimation = enable;
            }),
          resetAnimationSettings: () =>
            set((state) => {
              state.animation = initialAnimationState;
            }),

          // Settings Actions
          setTheme: (theme) =>
            set((state) => {
              state.settings.theme = theme;
            }),
          setAutoSave: (autoSave) =>
            set((state) => {
              state.settings.autoSave = autoSave;
            }),
          setShowLineNumbers: (show) =>
            set((state) => {
              state.settings.showLineNumbers = show;
            }),
          setEnableCollaboration: (enable) =>
            set((state) => {
              state.settings.enableCollaboration = enable;
            }),
          setMaxHistorySize: (size) =>
            set((state) => {
              state.settings.maxHistorySize = size;
            }),
          resetSettings: () =>
            set((state) => {
              state.settings = initialSettingsState;
            }),

          // Global Reset
          reset: () =>
            set((state) => {
              state.ui = initialUIState;
              state.chat = initialChatState;
              state.animation = initialAnimationState;
              state.settings = initialSettingsState;
            }),
        }))
      ),
      {
        name: "v0-interface-store",
        partialize: (state) => ({
          ui: {
            selectedProject: state.ui.selectedProject,
            assistantType: state.ui.assistantType,
          },
          animation: state.animation,
          settings: state.settings,
        }),
      }
    ),
    {
      name: "V0InterfaceStore",
    }
  )
);

// Selector hooks for better performance
export const useUIStore = () => {
  const state = useV0InterfaceStore((state) => state.ui);

  // Extract actions separately to avoid creating new objects on every render
  const setSelectedProject = useV0InterfaceStore((state) => state.setSelectedProject);
  const setShowPreview = useV0InterfaceStore((state) => state.setShowPreview);
  const setIsRecording = useV0InterfaceStore((state) => state.setIsRecording);
  const setCopied = useV0InterfaceStore((state) => state.setCopied);
  const setAssistantType = useV0InterfaceStore((state) => state.setAssistantType);
  const setIsExpanded = useV0InterfaceStore((state) => state.setIsExpanded);
  const setShowToolbar = useV0InterfaceStore((state) => state.setShowToolbar);
  const setIsFocused = useV0InterfaceStore((state) => state.setIsFocused);
  const setIsTyping = useV0InterfaceStore((state) => state.setIsTyping);
  const togglePreview = useV0InterfaceStore((state) => state.togglePreview);
  const toggleRecording = useV0InterfaceStore((state) => state.toggleRecording);
  const toggleExpanded = useV0InterfaceStore((state) => state.toggleExpanded);
  const toggleToolbar = useV0InterfaceStore((state) => state.toggleToolbar);

  // Memoize the actions object to prevent infinite re-renders
  const actions = useMemo(() => ({
    setSelectedProject,
    setShowPreview,
    setIsRecording,
    setCopied,
    setAssistantType,
    setIsExpanded,
    setShowToolbar,
    setIsFocused,
    setIsTyping,
    togglePreview,
    toggleRecording,
    toggleExpanded,
    toggleToolbar,
  }), [
    setSelectedProject,
    setShowPreview,
    setIsRecording,
    setCopied,
    setAssistantType,
    setIsExpanded,
    setShowToolbar,
    setIsFocused,
    setIsTyping,
    togglePreview,
    toggleRecording,
    toggleExpanded,
    toggleToolbar,
  ]);

  return { state, actions };
};

export const useChatStore = () => {
  const state = useV0InterfaceStore((state) => state.chat);

  // Extract actions separately to avoid creating new objects on every render
  const setMessages = useV0InterfaceStore((state) => state.setMessages);
  const addMessage = useV0InterfaceStore((state) => state.addMessage);
  const updateMessage = useV0InterfaceStore((state) => state.updateMessage);
  const removeMessage = useV0InterfaceStore((state) => state.removeMessage);
  const setInput = useV0InterfaceStore((state) => state.setInput);
  const setStatus = useV0InterfaceStore((state) => state.setStatus);
  const setIsLoading = useV0InterfaceStore((state) => state.setIsLoading);
  const setError = useV0InterfaceStore((state) => state.setError);
  const clearMessages = useV0InterfaceStore((state) => state.clearMessages);
  const clearInput = useV0InterfaceStore((state) => state.clearInput);
  const clearError = useV0InterfaceStore((state) => state.clearError);

  // Memoize the actions object to prevent infinite re-renders
  const actions = useMemo(() => ({
    setMessages,
    addMessage,
    updateMessage,
    removeMessage,
    setInput,
    setStatus,
    setIsLoading,
    setError,
    clearMessages,
    clearInput,
    clearError,
  }), [
    setMessages,
    addMessage,
    updateMessage,
    removeMessage,
    setInput,
    setStatus,
    setIsLoading,
    setError,
    clearMessages,
    clearInput,
    clearError,
  ]);

  return { state, actions };
};

export const useAnimationStore = () => {
  const state = useV0InterfaceStore((state) => state.animation);

  // Extract actions separately to avoid creating new objects on every render
  const setEnableAnimations = useV0InterfaceStore((state) => state.setEnableAnimations);
  const setAnimationSpeed = useV0InterfaceStore((state) => state.setAnimationSpeed);
  const setParticleCount = useV0InterfaceStore((state) => state.setParticleCount);
  const setGradientAnimation = useV0InterfaceStore((state) => state.setGradientAnimation);
  const resetAnimationSettings = useV0InterfaceStore((state) => state.resetAnimationSettings);

  // Memoize the actions object to prevent infinite re-renders
  const actions = useMemo(() => ({
    setEnableAnimations,
    setAnimationSpeed,
    setParticleCount,
    setGradientAnimation,
    resetAnimationSettings,
  }), [
    setEnableAnimations,
    setAnimationSpeed,
    setParticleCount,
    setGradientAnimation,
    resetAnimationSettings,
  ]);

  return { state, actions };
};

export const useSettingsStore = () => {
  const state = useV0InterfaceStore((state) => state.settings);

  // Extract actions separately to avoid creating new objects on every render
  const setTheme = useV0InterfaceStore((state) => state.setTheme);
  const setAutoSave = useV0InterfaceStore((state) => state.setAutoSave);
  const setShowLineNumbers = useV0InterfaceStore((state) => state.setShowLineNumbers);
  const setEnableCollaboration = useV0InterfaceStore((state) => state.setEnableCollaboration);
  const setMaxHistorySize = useV0InterfaceStore((state) => state.setMaxHistorySize);
  const resetSettings = useV0InterfaceStore((state) => state.resetSettings);

  // Memoize the actions object to prevent infinite re-renders
  const actions = useMemo(() => ({
    setTheme,
    setAutoSave,
    setShowLineNumbers,
    setEnableCollaboration,
    setMaxHistorySize,
    resetSettings,
  }), [
    setTheme,
    setAutoSave,
    setShowLineNumbers,
    setEnableCollaboration,
    setMaxHistorySize,
    resetSettings,
  ]);

  return { state, actions };
};

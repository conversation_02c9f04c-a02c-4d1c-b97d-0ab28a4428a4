// V0 Interface Store Types
import { Message } from "@ai-sdk/react";

// Quick Action Types
export interface QuickAction {
  icon: React.ComponentType<any>;
  label: string;
  description: string;
}

// Project Types
export interface Project {
  id: string;
  name: string;
  type: string;
}

// Assistant Types
export type AssistantType = "consultant" | "engineer" | "executive";

// UI State Types
export interface UIState {
  selectedProject: string;
  showPreview: boolean;
  isRecording: boolean;
  copied: boolean;
  assistantType: AssistantType;
  isExpanded: boolean;
  showToolbar: boolean;
  isFocused: boolean;
  isTyping: boolean;
}

// Chat State Types
export interface ChatState {
  messages: Message[];
  input: string;
  status: "idle" | "streaming" | "error";
  isLoading: boolean;
  error: string | null;
}

// Animation State Types
export interface AnimationState {
  enableAnimations: boolean;
  animationSpeed: number;
  particleCount: number;
  gradientAnimation: boolean;
}

// Settings State Types
export interface SettingsState {
  theme: "light" | "dark" | "system";
  autoSave: boolean;
  showLineNumbers: boolean;
  enableCollaboration: boolean;
  maxHistorySize: number;
}

// Combined Store State
export interface V0InterfaceState {
  ui: UIState;
  chat: ChatState;
  animation: AnimationState;
  settings: SettingsState;
}

// Store Actions (Flattened)
export interface V0InterfaceActions {
  // UI Actions
  setSelectedProject: (projectId: string) => void;
  setShowPreview: (show: boolean) => void;
  setIsRecording: (recording: boolean) => void;
  setCopied: (copied: boolean) => void;
  setAssistantType: (type: AssistantType) => void;
  setIsExpanded: (expanded: boolean) => void;
  setShowToolbar: (show: boolean) => void;
  setIsFocused: (focused: boolean) => void;
  setIsTyping: (typing: boolean) => void;
  togglePreview: () => void;
  toggleRecording: () => void;
  toggleExpanded: () => void;
  toggleToolbar: () => void;

  // Chat Actions
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  updateMessage: (id: string, updates: Partial<Message>) => void;
  removeMessage: (id: string) => void;
  setInput: (input: string) => void;
  setStatus: (status: ChatState["status"]) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearMessages: () => void;
  clearInput: () => void;
  clearError: () => void;

  // Animation Actions
  setEnableAnimations: (enable: boolean) => void;
  setAnimationSpeed: (speed: number) => void;
  setParticleCount: (count: number) => void;
  setGradientAnimation: (enable: boolean) => void;
  resetAnimationSettings: () => void;

  // Settings Actions
  setTheme: (theme: SettingsState["theme"]) => void;
  setAutoSave: (autoSave: boolean) => void;
  setShowLineNumbers: (show: boolean) => void;
  setEnableCollaboration: (enable: boolean) => void;
  setMaxHistorySize: (size: number) => void;
  resetSettings: () => void;

  // Global Actions
  reset: () => void;
}

// Store Type
export type V0InterfaceStore = V0InterfaceState & V0InterfaceActions;

// Selector Types
export type UISelector<T> = (state: V0InterfaceStore) => T;
export type ChatSelector<T> = (state: V0InterfaceStore) => T;
export type AnimationSelector<T> = (state: V0InterfaceStore) => T;
export type SettingsSelector<T> = (state: V0InterfaceStore) => T;

// Hook Return Types
export interface UseUIStore {
  state: UIState;
  actions: {
    setSelectedProject: (projectId: string) => void;
    setShowPreview: (show: boolean) => void;
    setIsRecording: (recording: boolean) => void;
    setCopied: (copied: boolean) => void;
    setAssistantType: (type: AssistantType) => void;
    setIsExpanded: (expanded: boolean) => void;
    setShowToolbar: (show: boolean) => void;
    setIsFocused: (focused: boolean) => void;
    setIsTyping: (typing: boolean) => void;
    togglePreview: () => void;
    toggleRecording: () => void;
    toggleExpanded: () => void;
    toggleToolbar: () => void;
  };
}

export interface UseChatStore {
  state: ChatState;
  actions: {
    setMessages: (messages: Message[]) => void;
    addMessage: (message: Message) => void;
    updateMessage: (id: string, updates: Partial<Message>) => void;
    removeMessage: (id: string) => void;
    setInput: (input: string) => void;
    setStatus: (status: ChatState["status"]) => void;
    setIsLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    clearMessages: () => void;
    clearInput: () => void;
    clearError: () => void;
  };
}

export interface UseAnimationStore {
  state: AnimationState;
  actions: {
    setEnableAnimations: (enable: boolean) => void;
    setAnimationSpeed: (speed: number) => void;
    setParticleCount: (count: number) => void;
    setGradientAnimation: (enable: boolean) => void;
    resetAnimationSettings: () => void;
  };
}

export interface UseSettingsStore {
  state: SettingsState;
  actions: {
    setTheme: (theme: SettingsState["theme"]) => void;
    setAutoSave: (autoSave: boolean) => void;
    setShowLineNumbers: (show: boolean) => void;
    setEnableCollaboration: (enable: boolean) => void;
    setMaxHistorySize: (size: number) => void;
    resetSettings: () => void;
  };
}

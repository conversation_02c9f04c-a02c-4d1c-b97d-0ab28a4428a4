"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationStore } from "../../store";

export interface FloatingElementsProps {
  className?: string;
  elementCount?: number;
  colors?: string[];
  sizes?: number[];
  animationDuration?: number[];
  enableRandomMovement?: boolean;
  enablePulse?: boolean;
  enableRotation?: boolean;
}

export interface FloatingElement {
  id: string;
  x: number;
  y: number;
  size: number;
  color: string;
  duration: number;
  delay: number;
  opacity: number;
}

const DEFAULT_COLORS = [
  "bg-primary/30",
  "bg-blue-400/40",
  "bg-purple-500/30",
  "bg-emerald-500/30",
  "bg-pink-500/30",
  "bg-yellow-500/30",
];

const DEFAULT_SIZES = [1, 2, 3, 4];
const DEFAULT_DURATIONS = [2, 3, 4, 5];

export function FloatingElements({
  className,
  elementCount = 6,
  colors = DEFAULT_COLORS,
  sizes = DEFAULT_SIZES,
  animationDuration = DEFAULT_DURATIONS,
  enableRandomMovement = true,
  enablePulse = true,
  enableRotation = false,
}: FloatingElementsProps) {
  const { state: animationState } = useAnimationStore();

  // Generate floating elements
  const elements = React.useMemo<FloatingElement[]>(() => {
    if (!animationState.enableAnimations) return [];

    return Array.from({ length: elementCount }, (_, i) => ({
      id: `floating-element-${i}`,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: sizes[Math.floor(Math.random() * sizes.length)],
      color: colors[Math.floor(Math.random() * colors.length)],
      duration: animationDuration[Math.floor(Math.random() * animationDuration.length)],
      delay: Math.random() * 2,
      opacity: 0.3 + Math.random() * 0.4,
    }));
  }, [elementCount, colors, sizes, animationDuration, animationState.enableAnimations]);

  if (!animationState.enableAnimations) {
    return null;
  }

  return (
    <div className={cn("absolute inset-0 pointer-events-none overflow-hidden", className)}>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className={cn(
            "absolute rounded-full",
            element.color
          )}
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: `${element.size * 4}px`,
            height: `${element.size * 4}px`,
            opacity: element.opacity,
          }}
          initial={{
            scale: 0,
            opacity: 0,
          }}
          animate={{
            scale: [0, 1, 1, 0],
            opacity: [0, element.opacity, element.opacity, 0],
            ...(enableRandomMovement && {
              x: [0, Math.random() * 40 - 20, Math.random() * 40 - 20, 0],
              y: [0, Math.random() * 40 - 20, Math.random() * 40 - 20, 0],
            }),
            ...(enablePulse && {
              scale: [1, 1.2, 1],
            }),
            ...(enableRotation && {
              rotate: [0, 360],
            }),
          }}
          transition={{
            duration: element.duration * animationState.animationSpeed,
            repeat: Infinity,
            ease: "easeInOut",
            delay: element.delay,
            times: [0, 0.2, 0.8, 1],
          }}
        />
      ))}
    </div>
  );
}

// Enhanced floating elements with more complex animations
export interface EnhancedFloatingElementsProps extends FloatingElementsProps {
  enableTrails?: boolean;
  enableGlow?: boolean;
  enableMorphing?: boolean;
  trailLength?: number;
  glowIntensity?: number;
}

export function EnhancedFloatingElements({
  enableTrails = false,
  enableGlow = false,
  enableMorphing = false,
  trailLength = 3,
  glowIntensity = 0.5,
  ...props
}: EnhancedFloatingElementsProps) {
  const { state: animationState } = useAnimationStore();

  if (!animationState.enableAnimations) {
    return null;
  }

  return (
    <div className="relative">
      <FloatingElements {...props} />
      
      {/* Glow effect layer */}
      {enableGlow && (
        <div className="absolute inset-0 pointer-events-none">
          <div 
            className="absolute inset-0 bg-gradient-radial from-primary/10 via-transparent to-transparent blur-xl"
            style={{
              opacity: glowIntensity,
            }}
          />
        </div>
      )}
      
      {/* Trail effect layer */}
      {enableTrails && (
        <div className="absolute inset-0 pointer-events-none">
          {Array.from({ length: trailLength }, (_, i) => (
            <FloatingElements
              key={`trail-${i}`}
              {...props}
              elementCount={Math.floor(props.elementCount || 6 / 2)}
              className="opacity-30"
            />
          ))}
        </div>
      )}
    </div>
  );
}

// Preset configurations
export const FloatingElementsPresets = {
  minimal: {
    elementCount: 3,
    colors: ["bg-primary/20", "bg-blue-400/20"] as string[],
    sizes: [1, 2] as number[],
    animationDuration: [4, 5] as number[],
    enableRandomMovement: false,
    enablePulse: true,
    enableRotation: false,
  },

  dynamic: {
    elementCount: 8,
    colors: [...DEFAULT_COLORS],
    sizes: [1, 2, 3, 4, 5] as number[],
    animationDuration: [2, 3, 4] as number[],
    enableRandomMovement: true,
    enablePulse: true,
    enableRotation: true,
  },

  elegant: {
    elementCount: 5,
    colors: ["bg-primary/30", "bg-purple-500/30", "bg-blue-400/30"] as string[],
    sizes: [2, 3] as number[],
    animationDuration: [5, 6, 7] as number[],
    enableRandomMovement: true,
    enablePulse: false,
    enableRotation: false,
  },

  energetic: {
    elementCount: 12,
    colors: [...DEFAULT_COLORS],
    sizes: [1, 2, 3] as number[],
    animationDuration: [1, 2, 3] as number[],
    enableRandomMovement: true,
    enablePulse: true,
    enableRotation: true,
  },
} as const;

export type FloatingElementsPreset = keyof typeof FloatingElementsPresets;

export interface PresetFloatingElementsProps {
  preset: FloatingElementsPreset;
  className?: string;
  overrides?: Partial<FloatingElementsProps>;
}

export function PresetFloatingElements({
  preset,
  className,
  overrides = {},
}: PresetFloatingElementsProps) {
  const presetConfig = FloatingElementsPresets[preset];
  
  return (
    <FloatingElements
      {...presetConfig}
      {...overrides}
      className={className}
    />
  );
}

// Export all components
export default FloatingElements;

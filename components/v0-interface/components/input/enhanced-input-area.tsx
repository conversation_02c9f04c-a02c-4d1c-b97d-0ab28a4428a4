"use client";

import React, { useRef, memo, useCallback } from "react";
import { motion } from "framer-motion";
import { ModernTextArea, ModernTextAreaRef } from "@/components/ui/modern-textarea";
import { cn } from "@/lib/utils";
import { useUIStore, useChatStore, useAnimationStore } from "../../store";
import type { AssistantType } from "../../store/types";

export interface EnhancedInputAreaProps {
  value: string;
  onChange: (value: string) => void;
  onSend?: () => void;
  placeholder?: string;
  disabled?: boolean;
  isStreaming?: boolean;
  className?: string;
  variant?: "initial" | "chat";
  size?: "sm" | "md" | "lg" | "xl";
  showAssistantSelector?: boolean;
  assistantType?: AssistantType;
  onAssistantTypeChange?: (type: AssistantType) => void;
  enableGradientBorder?: boolean;
  enableGlowEffect?: boolean;
  enablePulseAnimation?: boolean;
  showCharCount?: boolean;
  maxLength?: number;
  minRows?: number;
  maxRows?: number;
}

export const EnhancedInputArea = memo(function EnhancedInputArea({
  value,
  onChange,
  onSend,
  placeholder = "Ask v0 to build something amazing...",
  disabled = false,
  isStreaming = false,
  className,
  variant = "initial",
  size = "lg",
  showAssistantSelector = true,
  assistantType = "consultant",
  onAssistantTypeChange,
  enableGradientBorder = true,
  enableGlowEffect = true,
  enablePulseAnimation = true,
  showCharCount = true,
  maxLength,
  minRows,
  maxRows,
}: EnhancedInputAreaProps) {
  const { state: animationState } = useAnimationStore();
  const textareaRef = useRef<ModernTextAreaRef>(null);

  const variantConfig = {
    initial: {
      minRows: 2,
      maxRows: 8,
      size: "lg" as const,
      placeholder: "Ask v0 to build something amazing...",
    },
    chat: {
      minRows: 1,
      maxRows: 4,
      size: "md" as const,
      placeholder: "Ask a follow up...",
    },
  };

  const config = variantConfig[variant];

  const handleSend = useCallback(() => {
    if (value.trim() && !disabled && !isStreaming) {
      onSend?.();
    }
  }, [value, disabled, isStreaming, onSend]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  return (
    <motion.div
      initial={{ opacity: 0, y: variant === "initial" ? 20 : 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: variant === "initial" ? 0.6 : 0.5, 
        delay: variant === "initial" ? 0.7 : 0,
        ease: "easeOut" 
      }}
      className={cn("relative", className)}
    >
      <div className="relative">
        {/* Gradient border effect */}
        {enableGradientBorder && animationState.enableAnimations && (
          <motion.div
            className="absolute -inset-0.5 bg-gradient-to-r from-primary/20 via-blue-500/20 to-purple-500/20 rounded-lg blur opacity-0"
            animate={{
              opacity: value.length > 0 ? [0.3, 0.6, 0.3] : 0,
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        )}

        {/* Glow effect */}
        {enableGlowEffect && animationState.enableAnimations && value.length > 0 && (
          <motion.div
            className="absolute -inset-1 bg-gradient-to-r from-primary/10 via-blue-500/10 to-purple-500/10 rounded-lg blur-lg"
            animate={{
              opacity: [0.2, 0.4, 0.2],
              scale: [1, 1.02, 1],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        )}

        <div className="relative">
          <form onSubmit={(e) => { e.preventDefault(); handleSend(); }}>
            <ModernTextArea
              ref={textareaRef}
              value={value}
              onChange={onChange}
              placeholder={placeholder}
              variant="message"
              size={size}
              autoResize
              minRows={minRows || config.minRows}
              maxRows={maxRows || config.maxRows}
              showCharCount={showCharCount}
              maxLength={maxLength}
              enableMarkdown
              enableAutoComplete
              enableDragDrop
              showSendButton
              onSend={handleSend}
              sendButtonDisabled={!value.trim() || disabled || isStreaming}
              isStreaming={isStreaming}
              showAssistantSelector={showAssistantSelector}
              assistantType={assistantType}
              onAssistantTypeChange={onAssistantTypeChange as ((type: string) => void) | undefined}
              disabled={disabled}
              onKeyDown={handleKeyDown}
              className={cn(
                "border-border/50 bg-background/80 backdrop-blur-sm hover:bg-background/90 transition-colors",
                variant === "chat" && "bg-background/90 backdrop-blur-sm hover:bg-background transition-colors"
              )}
            />
          </form>
        </div>
      </div>

      {/* Pulse animation for active state */}
      {enablePulseAnimation && animationState.enableAnimations && isStreaming && (
        <motion.div
          className="absolute inset-0 border-2 border-primary/30 rounded-lg pointer-events-none"
          animate={{
            scale: [1, 1.02, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      )}
    </motion.div>
  );
});

// Wrapper component that integrates with the store
export interface ConnectedEnhancedInputAreaProps extends Omit<EnhancedInputAreaProps, 'value' | 'onChange' | 'assistantType' | 'onAssistantTypeChange' | 'isStreaming'> {
  onSubmit?: (value: string) => void;
}

export const ConnectedEnhancedInputArea = memo(function ConnectedEnhancedInputArea({
  onSubmit,
  ...props
}: ConnectedEnhancedInputAreaProps) {
  const { state: uiState, actions: uiActions } = useUIStore();
  const { state: chatState, actions: chatActions } = useChatStore();

  const handleSend = useCallback(() => {
    if (chatState.input.trim()) {
      onSubmit?.(chatState.input);
      chatActions.clearInput();
    }
  }, [chatState.input, onSubmit, chatActions]);

  return (
    <EnhancedInputArea
      {...props}
      value={chatState.input}
      onChange={chatActions.setInput}
      onSend={handleSend}
      assistantType={uiState.assistantType}
      onAssistantTypeChange={uiActions.setAssistantType}
      isStreaming={chatState.status === "streaming"}
    />
  );
});

// Preset configurations
export const InputAreaPresets = {
  minimal: {
    enableGradientBorder: false,
    enableGlowEffect: false,
    enablePulseAnimation: false,
    showCharCount: false,
    showAssistantSelector: false,
    size: "md" as const,
  },
  
  standard: {
    enableGradientBorder: true,
    enableGlowEffect: false,
    enablePulseAnimation: true,
    showCharCount: true,
    showAssistantSelector: true,
    size: "lg" as const,
  },
  
  enhanced: {
    enableGradientBorder: true,
    enableGlowEffect: true,
    enablePulseAnimation: true,
    showCharCount: true,
    showAssistantSelector: true,
    size: "lg" as const,
  },
  
  compact: {
    enableGradientBorder: false,
    enableGlowEffect: false,
    enablePulseAnimation: false,
    showCharCount: false,
    showAssistantSelector: true,
    size: "sm" as const,
    minRows: 1,
    maxRows: 3,
  },
} as const;

export type InputAreaPreset = keyof typeof InputAreaPresets;

export interface PresetEnhancedInputAreaProps extends Omit<EnhancedInputAreaProps, keyof typeof InputAreaPresets[InputAreaPreset]> {
  preset: InputAreaPreset;
  overrides?: Partial<EnhancedInputAreaProps>;
}

export const PresetEnhancedInputArea = memo(function PresetEnhancedInputArea({
  preset,
  overrides = {},
  ...props
}: PresetEnhancedInputAreaProps) {
  const presetConfig = InputAreaPresets[preset];

  return (
    <EnhancedInputArea
      {...presetConfig}
      {...overrides}
      {...props}
    />
  );
});

export default EnhancedInputArea;

"use client";

import React from "react";
import { motion } from "framer-motion";
import { Message } from "@ai-sdk/react";
import { <PERSON><PERSON>, User, <PERSON><PERSON>, ThumbsUp, ThumbsDown, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { ToolCallRenderer } from "@/components/tool-calls";
import { useAnimationStore, useChatStore } from "../../store";

export interface ChatMessageProps {
  message: Message;
  isStreaming?: boolean;
  isLatest?: boolean;
  showActions?: boolean;
  showTimestamp?: boolean;
  enableAnimations?: boolean;
  onCopy?: (content: string) => void;
  onRegenerate?: (messageId: string) => void;
  onFeedback?: (messageId: string, type: "positive" | "negative") => void;
  className?: string;
}

export interface StreamingIndicatorProps {
  className?: string;
  variant?: "dots" | "pulse" | "wave";
}

export function StreamingIndicator({ 
  className, 
  variant = "dots" 
}: StreamingIndicatorProps) {
  const { state: animationState } = useAnimationStore();

  if (!animationState.enableAnimations) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <span className="text-xs text-muted-foreground">AI is thinking...</span>
      </div>
    );
  }

  const variants = {
    dots: (
      <div className="flex items-center gap-1">
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className="w-2 h-2 bg-primary rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 0.8,
              repeat: Infinity,
              ease: "easeInOut",
              delay: index * 0.2,
            }}
          />
        ))}
        <span className="text-xs text-muted-foreground ml-2">
          AI is thinking...
        </span>
      </div>
    ),
    
    pulse: (
      <div className="flex items-center gap-2">
        <motion.div
          className="w-3 h-3 bg-primary rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <span className="text-xs text-muted-foreground">
          AI is thinking...
        </span>
      </div>
    ),
    
    wave: (
      <div className="flex items-center gap-1">
        {[0, 1, 2, 3].map((index) => (
          <motion.div
            key={index}
            className="w-1 bg-primary rounded-full"
            animate={{
              height: [8, 16, 8],
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "easeInOut",
              delay: index * 0.1,
            }}
          />
        ))}
        <span className="text-xs text-muted-foreground ml-2">
          AI is thinking...
        </span>
      </div>
    ),
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      className={cn("flex items-center", className)}
    >
      {variants[variant]}
    </motion.div>
  );
}

export function ChatMessage({
  message,
  isStreaming = false,
  isLatest = false,
  showActions = true,
  showTimestamp = false,
  enableAnimations = true,
  onCopy,
  onRegenerate,
  onFeedback,
  className,
}: ChatMessageProps) {
  const { state: animationState } = useAnimationStore();
  const [copied, setCopied] = React.useState(false);

  const handleCopy = async () => {
    const content = message.content || "";
    await navigator.clipboard.writeText(content);
    setCopied(true);
    onCopy?.(content);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleFeedback = (type: "positive" | "negative") => {
    onFeedback?.(message.id, type);
  };

  const isUser = message.role === "user";
  const isAssistant = message.role === "assistant";

  return (
    <motion.div
      initial={enableAnimations && animationState.enableAnimations ? { 
        opacity: 0, 
        y: 10 
      } : false}
      animate={{ opacity: 1, y: 0 }}
      className={cn("space-y-4", className)}
    >
      {isUser ? (
        // User Message
        <div className="flex justify-end">
          <motion.div
            initial={enableAnimations && animationState.enableAnimations ? { 
              opacity: 0, 
              x: 20 
            } : false}
            animate={{ opacity: 1, x: 0 }}
            className="bg-primary text-primary-foreground rounded-2xl px-4 py-3 max-w-[80%] shadow-sm relative group"
          >
            <p className="text-sm leading-relaxed">
              {message.content}
            </p>
            
            {showTimestamp && (
              <div className="text-xs opacity-70 mt-1">
                {new Date().toLocaleTimeString()}
              </div>
            )}

            {showActions && (
              <div className="absolute -left-8 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={handleCopy}
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            )}
          </motion.div>
        </div>
      ) : (
        // Assistant Message
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            {/* Enhanced Bot Avatar */}
            <motion.div
              initial={enableAnimations && animationState.enableAnimations ? { 
                scale: 0 
              } : false}
              animate={{ scale: 1 }}
              transition={{
                delay: 0.2,
                type: "spring",
                stiffness: 200,
              }}
              className="relative"
            >
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-blue-500 flex items-center justify-center flex-shrink-0 relative">
                <Bot className="h-4 w-4 text-white" />
                {isStreaming && isLatest && (
                  <motion.div
                    className="absolute inset-0 rounded-full bg-primary/30"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.5, 0.8, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                )}
              </div>
            </motion.div>

            <div className="flex-1 space-y-2">
              {/* Streaming Indicator */}
              {isStreaming && isLatest && (
                <StreamingIndicator variant="dots" />
              )}

              {/* Message Content */}
              <div className="space-y-4">
                {/* Render message parts */}
                {message.parts?.map((part, index) => {
                  switch (part.type) {
                    case "text":
                      return (
                        <motion.div
                          key={index}
                          initial={enableAnimations && animationState.enableAnimations ? { 
                            opacity: 0, 
                            y: 10 
                          } : false}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 }}
                          className="prose prose-sm max-w-none"
                        >
                          <p className="whitespace-pre-wrap text-foreground">
                            {part.text}
                          </p>
                        </motion.div>
                      );

                    case "tool-invocation":
                      return (
                        <ToolCallRenderer
                          key={index}
                          part={part}
                        />
                      );

                    default:
                      return null;
                  }
                }) || (
                  // Fallback for messages without parts
                  <motion.div
                    initial={enableAnimations && animationState.enableAnimations ? { 
                      opacity: 0, 
                      y: 10 
                    } : false}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                    className="prose prose-sm max-w-none"
                  >
                    <p className="whitespace-pre-wrap text-foreground">
                      {message.content}
                    </p>
                  </motion.div>
                )}
              </div>

              {/* Message Actions */}
              {showActions && !isStreaming && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="flex items-center gap-2 pt-2"
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={handleCopy}
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    {copied ? "Copied!" : "Copy"}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => handleFeedback("positive")}
                  >
                    <ThumbsUp className="h-3 w-3" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={() => handleFeedback("negative")}
                  >
                    <ThumbsDown className="h-3 w-3" />
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem onClick={() => onRegenerate?.(message.id)}>
                        Regenerate
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleCopy}>
                        Copy message
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </motion.div>
              )}

              {showTimestamp && (
                <div className="text-xs text-muted-foreground">
                  {new Date().toLocaleTimeString()}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
}

export default ChatMessage;

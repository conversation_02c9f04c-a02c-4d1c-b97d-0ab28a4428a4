// Animation Components
export {
  FloatingElements,
  EnhancedFloatingElements,
  PresetFloatingElements,
  FloatingElementsPresets,
  type FloatingElementsProps,
  type EnhancedFloatingElementsProps,
  type FloatingElementsPreset,
  type PresetFloatingElementsProps,
} from "./animations/floating-elements";

// Chat Components
export {
  ChatMessage,
  StreamingIndicator,
  type ChatMessageProps,
  type StreamingIndicatorProps,
} from "./chat/chat-message";

// Input Components
export {
  EnhancedInputArea,
  ConnectedEnhancedInputArea,
  PresetEnhancedInputArea,
  InputAreaPresets,
  type EnhancedInputAreaProps,
  type ConnectedEnhancedInputAreaProps,
  type InputAreaPreset,
  type PresetEnhancedInputAreaProps,
} from "./input/enhanced-input-area";

// Layout Components
export {
  MainLayout,
  LayoutSection,
  ChatLayout,
  InitialStateLayout,
  ResponsiveLayout,
  PresetMainLayout,
  LayoutPresets,
  type MainLayoutProps,
  type LayoutSectionProps,
  type ChatLayoutProps,
  type InitialStateLayoutProps,
  type ResponsiveLayoutProps,
  type LayoutPreset,
  type PresetMainLayoutProps,
} from "./layout/main-layout";

// UI Components
export {
  QuickActionsGrid,
  PresetQuickActionsGrid,
  CategorizedQuickActionsGrid,
  QuickActionsPresets,
  type QuickActionsGridProps,
  type QuickActionsPreset,
  type PresetQuickActionsGridProps,
  type CategorizedQuickActionsGridProps,
} from "./ui/quick-actions-grid";

export {
  PreviewPanel,
  EnhancedPreviewPanel,
  PresetPreviewPanel,
  ConnectedPreviewPanel,
  PreviewContent,
  PreviewPanelPresets,
  type PreviewPanelProps,
  type EnhancedPreviewPanelProps,
  type PreviewPanelPreset,
  type PresetPreviewPanelProps,
  type ConnectedPreviewPanelProps,
  type PreviewContentProps,
} from "./ui/preview-panel";

// Re-export store types for convenience
export type {
  QuickAction,
  Project,
  AssistantType,
  UIState,
  ChatState,
  AnimationState,
  SettingsState,
  V0InterfaceState,
  V0InterfaceActions,
  V0InterfaceStore,
  UseUIStore,
  UseChatStore,
  UseAnimationStore,
  UseSettingsStore,
} from "../store/types";

// Re-export store hooks
export {
  useV0InterfaceStore,
  useUIStore,
  useChatStore,
  useAnimationStore,
  useSettingsStore,
} from "../store";

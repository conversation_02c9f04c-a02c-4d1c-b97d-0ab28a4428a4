"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Eye, Download, Code, Layout, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { ToolCallRenderer } from "@/components/tool-calls";
import { useUIStore, useAnimationStore } from "../../store";
import type { Message } from "@ai-sdk/react";

export interface PreviewPanelProps {
  messages: Message[];
  isVisible?: boolean;
  onClose?: () => void;
  className?: string;
  width?: string | number;
  enableAnimations?: boolean;
  showTabs?: boolean;
  defaultTab?: "preview" | "code";
}

export interface PreviewContentProps {
  messages: Message[];
  activeTab?: "preview" | "code";
}

export function PreviewContent({ messages, activeTab = "preview" }: PreviewContentProps) {
  // Find the latest tool result to display
  const latestMessage = messages[messages.length - 1];
  const toolResult = latestMessage?.parts?.find(
    (part) =>
      part.type === "tool-invocation" &&
      (part.toolInvocation as any).state === "result" &&
      [
        "generateComponent",
        "generateLandingPage", 
        "generateForm",
        "getServiceInfo",
        "generateProposal",
        "getCompanyInfo",
      ].includes((part.toolInvocation as any).toolName)
  );

  if (!toolResult) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Layout className="h-8 w-8 text-primary" />
          </div>
          <h3 className="font-medium mb-2">Tool Results</h3>
          <p className="text-sm text-muted-foreground">
            Tool results will appear here when you use consultant tools
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full">
      <ToolCallRenderer part={toolResult as any} />
    </div>
  );
}

export function PreviewPanel({
  messages,
  isVisible = false,
  onClose,
  className,
  width = "75%",
  enableAnimations = true,
  showTabs = true,
  defaultTab = "preview",
}: PreviewPanelProps) {
  const { state: animationState } = useAnimationStore();
  const [activeTab, setActiveTab] = React.useState<"preview" | "code">(defaultTab);

  // Get the title for the preview based on the latest tool result
  const getPreviewTitle = () => {
    const latestMessage = messages[messages.length - 1];
    const toolResult = latestMessage?.parts?.find(
      (part) =>
        part.type === "tool-invocation" &&
        (part.toolInvocation as any).state === "result" &&
        [
          "generateComponent",
          "generateLandingPage",
          "generateForm",
          "getServiceInfo",
          "generateProposal",
          "getCompanyInfo",
        ].includes((part.toolInvocation as any).toolName)
    );

    if (!toolResult) return "Preview";

    const toolName = (toolResult as any).toolInvocation.toolName;
    switch (toolName) {
      case "getServiceInfo":
        return "Service Information";
      case "generateProposal":
        return "Project Proposal";
      case "getCompanyInfo":
        return "Company Information";
      case "generateComponent":
        return "Component Preview";
      case "generateLandingPage":
        return "Landing Page";
      case "generateForm":
        return "Form Component";
      default:
        return "Tool Result";
    }
  };

  // Check if we should show tabs (only for code generation tools)
  const shouldShowTabs = () => {
    if (!showTabs) return false;
    
    const latestMessage = messages[messages.length - 1];
    const toolResult = latestMessage?.parts?.find(
      (part) =>
        part.type === "tool-invocation" &&
        (part.toolInvocation as any).state === "result" &&
        [
          "generateComponent",
          "generateLandingPage",
          "generateForm",
        ].includes((part.toolInvocation as any).toolName)
    );

    return !!toolResult;
  };

  const animationProps = enableAnimations && animationState.enableAnimations ? {
    initial: { width: 0, opacity: 0 },
    animate: { width, opacity: 1 },
    exit: { width: 0, opacity: 0 },
    transition: { duration: 0.3, ease: "easeInOut" }
  } : {};

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          {...animationProps}
          className={cn(
            "border-l border-border/50 bg-muted/20 flex flex-col",
            className
          )}
        >
          {/* Header */}
          <div className="border-b border-border/50 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <span className="font-medium">
                  {getPreviewTitle()}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                {/* Tabs for code generation tools */}
                {shouldShowTabs() && (
                  <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "preview" | "code")}>
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="preview">Preview</TabsTrigger>
                      <TabsTrigger value="code">Code</TabsTrigger>
                    </TabsList>
                  </Tabs>
                )}
                
                {/* Action buttons */}
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-4">
            <div className="w-full h-full bg-white rounded-lg border border-border/50 overflow-auto">
              <PreviewContent messages={messages} activeTab={activeTab} />
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Enhanced preview panel with additional features
export interface EnhancedPreviewPanelProps extends PreviewPanelProps {
  enableFullscreen?: boolean;
  enableResize?: boolean;
  minWidth?: string | number;
  maxWidth?: string | number;
  onFullscreen?: () => void;
  onResize?: (width: string | number) => void;
}

export function EnhancedPreviewPanel({
  enableFullscreen = false,
  enableResize = false,
  minWidth = "300px",
  maxWidth = "80%",
  onFullscreen,
  onResize,
  ...props
}: EnhancedPreviewPanelProps) {
  const [isFullscreen, setIsFullscreen] = React.useState(false);
  const [currentWidth, setCurrentWidth] = React.useState(props.width || "75%");

  const handleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    onFullscreen?.();
  };

  const handleResize = (newWidth: string | number) => {
    setCurrentWidth(newWidth);
    onResize?.(newWidth);
  };

  return (
    <PreviewPanel
      {...props}
      width={isFullscreen ? "100%" : currentWidth}
      className={cn(
        props.className,
        isFullscreen && "fixed inset-0 z-50 bg-background"
      )}
    />
  );
}

// Preset configurations
export const PreviewPanelPresets = {
  default: {
    width: "75%",
    enableAnimations: true,
    showTabs: true,
    defaultTab: "preview" as const,
  },
  
  compact: {
    width: "50%",
    enableAnimations: true,
    showTabs: false,
    defaultTab: "preview" as const,
  },
  
  wide: {
    width: "85%",
    enableAnimations: true,
    showTabs: true,
    defaultTab: "preview" as const,
  },
  
  minimal: {
    width: "40%",
    enableAnimations: false,
    showTabs: false,
    defaultTab: "preview" as const,
  },
} as const;

export type PreviewPanelPreset = keyof typeof PreviewPanelPresets;

export interface PresetPreviewPanelProps extends Omit<PreviewPanelProps, keyof typeof PreviewPanelPresets[PreviewPanelPreset]> {
  preset: PreviewPanelPreset;
  overrides?: Partial<PreviewPanelProps>;
}

export function PresetPreviewPanel({
  preset,
  overrides = {},
  ...props
}: PresetPreviewPanelProps) {
  const presetConfig = PreviewPanelPresets[preset];
  
  return (
    <PreviewPanel
      {...presetConfig}
      {...overrides}
      {...props}
    />
  );
}

// Connected version that uses the store
export interface ConnectedPreviewPanelProps extends Omit<PreviewPanelProps, 'isVisible' | 'onClose' | 'messages'> {
  messages: Message[];
}

export function ConnectedPreviewPanel({
  messages,
  ...props
}: ConnectedPreviewPanelProps) {
  const { state: uiState, actions: uiActions } = useUIStore();

  return (
    <PreviewPanel
      {...props}
      messages={messages}
      isVisible={uiState.showPreview}
      onClose={() => uiActions.setShowPreview(false)}
    />
  );
}

export default PreviewPanel;

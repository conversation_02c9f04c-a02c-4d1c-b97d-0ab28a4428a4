"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Camera,
  FileImage,
  Upload,
  Layout,
  FileText,
  Code,
  Palette,
  Database,
  Globe,
  Smartphone,
  ShoppingCart,
  Users,
} from "lucide-react";
import { useAnimationStore } from "../../store";
import type { QuickAction } from "../../store/types";

export interface QuickActionsGridProps {
  actions?: QuickAction[];
  onActionClick?: (action: QuickAction) => void;
  className?: string;
  columns?: number;
  showDescriptions?: boolean;
  variant?: "default" | "compact" | "detailed";
  enableHoverEffects?: boolean;
  enableStaggeredAnimation?: boolean;
  animationDelay?: number;
}

// Default quick actions
const DEFAULT_QUICK_ACTIONS: QuickAction[] = [
  {
    icon: Camera,
    label: "Clone a Screenshot",
    description: "Upload an image to recreate",
  },
  {
    icon: FileImage,
    label: "Import from Figma",
    description: "Connect your Figma designs",
  },
  {
    icon: Upload,
    label: "Upload a Project",
    description: "Import existing code",
  },
  {
    icon: Layout,
    label: "Landing Page",
    description: "Create a landing page",
  },
  {
    icon: FileText,
    label: "Sign Up Form",
    description: "Build a registration form",
  },
  {
    icon: Code,
    label: "Component Library",
    description: "Generate reusable components",
  },
];

// Extended actions for more variety
const EXTENDED_QUICK_ACTIONS: QuickAction[] = [
  ...DEFAULT_QUICK_ACTIONS,
  {
    icon: Palette,
    label: "Design System",
    description: "Create a design system",
  },
  {
    icon: Database,
    label: "API Integration",
    description: "Connect to external APIs",
  },
  {
    icon: Globe,
    label: "Website Builder",
    description: "Build a complete website",
  },
  {
    icon: Smartphone,
    label: "Mobile App",
    description: "Create mobile interfaces",
  },
  {
    icon: ShoppingCart,
    label: "E-commerce Store",
    description: "Build an online store",
  },
  {
    icon: Users,
    label: "User Dashboard",
    description: "Create user management",
  },
];

export function QuickActionsGrid({
  actions = DEFAULT_QUICK_ACTIONS,
  onActionClick,
  className,
  columns = 3,
  showDescriptions = true,
  variant = "default",
  enableHoverEffects = true,
  enableStaggeredAnimation = true,
  animationDelay = 0.9,
}: QuickActionsGridProps) {
  const { state: animationState } = useAnimationStore();

  const gridCols = {
    1: "grid-cols-1",
    2: "grid-cols-2",
    3: "grid-cols-2 md:grid-cols-3",
    4: "grid-cols-2 md:grid-cols-4",
    5: "grid-cols-2 md:grid-cols-5",
    6: "grid-cols-2 md:grid-cols-6",
  }[columns] || "grid-cols-2 md:grid-cols-3";

  const variantStyles = {
    default: {
      button: "h-auto p-4 flex flex-col items-center gap-2 text-center",
      icon: "h-6 w-6",
      label: "font-medium text-sm",
      description: "text-xs text-muted-foreground",
    },
    compact: {
      button: "h-auto p-3 flex flex-col items-center gap-1 text-center",
      icon: "h-5 w-5",
      label: "font-medium text-xs",
      description: "text-xs text-muted-foreground",
    },
    detailed: {
      button: "h-auto p-6 flex flex-col items-center gap-3 text-center",
      icon: "h-8 w-8",
      label: "font-semibold text-base",
      description: "text-sm text-muted-foreground",
    },
  };

  const styles = variantStyles[variant];

  const handleActionClick = (action: QuickAction) => {
    onActionClick?.(action);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        delay: animationDelay, 
        duration: 0.6,
        ease: "easeOut"
      }}
      className={cn(
        "grid gap-3 max-w-3xl mx-auto",
        gridCols,
        className
      )}
    >
      {actions.map((action, index) => (
        <motion.div
          key={`${action.label}-${index}`}
          initial={{ opacity: 0, y: 20, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{
            delay: enableStaggeredAnimation 
              ? animationDelay + index * 0.1 
              : animationDelay,
            duration: 0.5,
            ease: "easeOut",
          }}
          whileHover={enableHoverEffects && animationState.enableAnimations ? {
            scale: 1.02,
            y: -2,
          } : undefined}
          whileTap={enableHoverEffects && animationState.enableAnimations ? { 
            scale: 0.98 
          } : undefined}
        >
          <Button
            variant="outline"
            className={cn(
              styles.button,
              "hover:bg-muted/50 hover:border-primary/30 transition-all duration-200 relative overflow-hidden group",
              !enableHoverEffects && "hover:scale-100"
            )}
            onClick={() => handleActionClick(action)}
          >
            {/* Background gradient effect */}
            {enableHoverEffects && (
              <motion.div 
                className="absolute inset-0 bg-gradient-to-r from-primary/5 via-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" 
              />
            )}
            
            {/* Icon with hover animation */}
            <motion.div
              whileHover={enableHoverEffects && animationState.enableAnimations ? { 
                rotate: 5, 
                scale: 1.1 
              } : undefined}
              transition={{ duration: 0.2 }}
              className="relative z-10"
            >
              <action.icon className={cn(styles.icon, "text-primary")} />
            </motion.div>
            
            {/* Content */}
            <div className="relative z-10">
              <div className={styles.label}>
                {action.label}
              </div>
              {showDescriptions && action.description && (
                <div className={styles.description}>
                  {action.description}
                </div>
              )}
            </div>
          </Button>
        </motion.div>
      ))}
    </motion.div>
  );
}

// Preset configurations
export const QuickActionsPresets = {
  default: {
    actions: DEFAULT_QUICK_ACTIONS,
    columns: 3,
    variant: "default" as const,
    showDescriptions: true,
  },
  
  compact: {
    actions: DEFAULT_QUICK_ACTIONS.slice(0, 4),
    columns: 2,
    variant: "compact" as const,
    showDescriptions: false,
  },
  
  extended: {
    actions: EXTENDED_QUICK_ACTIONS,
    columns: 4,
    variant: "default" as const,
    showDescriptions: true,
  },
  
  minimal: {
    actions: DEFAULT_QUICK_ACTIONS.slice(0, 3),
    columns: 3,
    variant: "compact" as const,
    showDescriptions: false,
  },
} as const;

export type QuickActionsPreset = keyof typeof QuickActionsPresets;

export interface PresetQuickActionsGridProps {
  preset: QuickActionsPreset;
  onActionClick?: (action: QuickAction) => void;
  className?: string;
  overrides?: Partial<QuickActionsGridProps>;
}

export function PresetQuickActionsGrid({
  preset,
  onActionClick,
  className,
  overrides = {},
}: PresetQuickActionsGridProps) {
  const presetConfig = QuickActionsPresets[preset];
  
  return (
    <QuickActionsGrid
      {...presetConfig}
      {...overrides}
      onActionClick={onActionClick}
      className={className}
    />
  );
}

// Enhanced version with categories
export interface CategorizedQuickActionsGridProps extends Omit<QuickActionsGridProps, 'actions'> {
  categories: {
    title: string;
    actions: QuickAction[];
  }[];
  showCategoryTitles?: boolean;
}

export function CategorizedQuickActionsGrid({
  categories,
  showCategoryTitles = true,
  onActionClick,
  className,
  ...props
}: CategorizedQuickActionsGridProps) {
  return (
    <div className={cn("space-y-6", className)}>
      {categories.map((category, categoryIndex) => (
        <motion.div
          key={category.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            delay: categoryIndex * 0.2,
            duration: 0.5 
          }}
        >
          {showCategoryTitles && (
            <h3 className="text-lg font-semibold mb-3 text-center">
              {category.title}
            </h3>
          )}
          <QuickActionsGrid
            {...props}
            actions={category.actions}
            onActionClick={onActionClick}
            animationDelay={categoryIndex * 0.3}
          />
        </motion.div>
      ))}
    </div>
  );
}

export default QuickActionsGrid;

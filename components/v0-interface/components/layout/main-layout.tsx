"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useAnimationStore } from "../../store";

export interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
  enableAnimations?: boolean;
  showBackground?: boolean;
  backgroundVariant?: "gradient" | "particles" | "minimal";
}

export interface LayoutSectionProps {
  children: React.ReactNode;
  className?: string;
  variant?: "header" | "main" | "footer" | "sidebar";
  enableAnimations?: boolean;
  animationDelay?: number;
}

export function LayoutSection({
  children,
  className,
  variant = "main",
  enableAnimations = true,
  animationDelay = 0,
}: LayoutSectionProps) {
  const { state: animationState } = useAnimationStore();

  const variantStyles = {
    header: "border-b border-border/50",
    main: "flex-1 flex flex-col relative",
    footer: "border-t border-border/50",
    sidebar: "border-r border-border/50",
  };

  const animationProps = enableAnimations && animationState.enableAnimations ? {
    initial: { opacity: 0, y: variant === "header" ? -20 : variant === "footer" ? 20 : 0 },
    animate: { opacity: 1, y: 0 },
    transition: { 
      duration: 0.6, 
      delay: animationDelay,
      ease: "easeOut" 
    }
  } : {};

  return (
    <motion.div
      {...animationProps}
      className={cn(variantStyles[variant], className)}
    >
      {children}
    </motion.div>
  );
}

export function MainLayout({
  children,
  className,
  enableAnimations = true,
  showBackground = true,
  backgroundVariant = "gradient",
}: MainLayoutProps) {
  const { state: animationState } = useAnimationStore();

  const backgroundStyles = {
    gradient: "bg-gradient-to-br from-background via-background to-muted/20",
    particles: "bg-background",
    minimal: "bg-background",
  };

  return (
    <motion.div
      initial={enableAnimations && animationState.enableAnimations ? { 
        opacity: 0 
      } : false}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className={cn(
        "flex-1 flex flex-col relative z-10 min-h-screen",
        showBackground && backgroundStyles[backgroundVariant],
        className
      )}
    >
      {children}
    </motion.div>
  );
}

// Chat Layout - specific layout for chat interface
export interface ChatLayoutProps {
  header?: React.ReactNode;
  chatArea: React.ReactNode;
  inputArea: React.ReactNode;
  sidebar?: React.ReactNode;
  preview?: React.ReactNode;
  className?: string;
  enableAnimations?: boolean;
}

export function ChatLayout({
  header,
  chatArea,
  inputArea,
  sidebar,
  preview,
  className,
  enableAnimations = true,
}: ChatLayoutProps) {
  return (
    <MainLayout 
      className={className} 
      enableAnimations={enableAnimations}
      backgroundVariant="minimal"
    >
      {/* Header */}
      {header && (
        <LayoutSection variant="header" enableAnimations={enableAnimations}>
          {header}
        </LayoutSection>
      )}

      {/* Main Content Area */}
      <LayoutSection 
        variant="main" 
        enableAnimations={enableAnimations}
        animationDelay={0.2}
        className="flex"
      >
        {/* Sidebar */}
        {sidebar && (
          <LayoutSection 
            variant="sidebar" 
            enableAnimations={enableAnimations}
            animationDelay={0.3}
            className="w-64 flex-shrink-0"
          >
            {sidebar}
          </LayoutSection>
        )}

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-hidden">
            {chatArea}
          </div>

          {/* Input Area */}
          <LayoutSection 
            variant="footer" 
            enableAnimations={enableAnimations}
            animationDelay={0.4}
            className="p-4 bg-background/80 backdrop-blur-sm"
          >
            {inputArea}
          </LayoutSection>
        </div>

        {/* Preview Panel */}
        {preview}
      </LayoutSection>
    </MainLayout>
  );
}

// Initial State Layout - for empty state
export interface InitialStateLayoutProps {
  title: React.ReactNode;
  subtitle?: React.ReactNode;
  inputArea: React.ReactNode;
  quickActions?: React.ReactNode;
  floatingElements?: React.ReactNode;
  className?: string;
  enableAnimations?: boolean;
}

export function InitialStateLayout({
  title,
  subtitle,
  inputArea,
  quickActions,
  floatingElements,
  className,
  enableAnimations = true,
}: InitialStateLayoutProps) {
  return (
    <MainLayout 
      className={className} 
      enableAnimations={enableAnimations}
      backgroundVariant="gradient"
    >
      {/* Floating Elements Background */}
      {floatingElements}

      {/* Main Content */}
      <LayoutSection 
        variant="main" 
        enableAnimations={enableAnimations}
        className="flex flex-col items-center justify-center p-8 relative"
      >
        <motion.div
          initial={enableAnimations ? { opacity: 0, y: 30, scale: 0.9 } : false}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center max-w-2xl mx-auto relative"
        >
          {/* Title Section */}
          <motion.div
            initial={enableAnimations ? { opacity: 0 } : false}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="relative mb-6"
          >
            {title}
          </motion.div>

          {/* Subtitle */}
          {subtitle && (
            <motion.div
              initial={enableAnimations ? { opacity: 0, y: 10 } : false}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="mb-8"
            >
              {subtitle}
            </motion.div>
          )}

          {/* Input Area */}
          <motion.div
            initial={enableAnimations ? { opacity: 0, y: 20 } : false}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.6 }}
            className="relative mb-8"
          >
            {inputArea}
          </motion.div>

          {/* Quick Actions */}
          {quickActions && (
            <motion.div
              initial={enableAnimations ? { opacity: 0, y: 30 } : false}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9, duration: 0.6 }}
            >
              {quickActions}
            </motion.div>
          )}
        </motion.div>
      </LayoutSection>
    </MainLayout>
  );
}

// Responsive Layout - adapts to different screen sizes
export interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
  enableAnimations?: boolean;
  breakpoint?: "sm" | "md" | "lg" | "xl";
  mobileLayout?: "stack" | "tabs" | "drawer";
}

export function ResponsiveLayout({
  children,
  className,
  enableAnimations = true,
  breakpoint = "md",
  mobileLayout = "stack",
}: ResponsiveLayoutProps) {
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    const checkMobile = () => {
      const breakpoints = {
        sm: 640,
        md: 768,
        lg: 1024,
        xl: 1280,
      };
      setIsMobile(window.innerWidth < breakpoints[breakpoint]);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, [breakpoint]);

  const mobileLayoutStyles = {
    stack: "flex flex-col",
    tabs: "flex flex-col",
    drawer: "relative",
  };

  return (
    <MainLayout 
      className={cn(
        className,
        isMobile && mobileLayoutStyles[mobileLayout]
      )} 
      enableAnimations={enableAnimations}
    >
      {children}
    </MainLayout>
  );
}

// Layout presets
export const LayoutPresets = {
  chat: {
    enableAnimations: true,
    backgroundVariant: "minimal" as const,
  },
  
  initial: {
    enableAnimations: true,
    backgroundVariant: "gradient" as const,
  },
  
  minimal: {
    enableAnimations: false,
    backgroundVariant: "minimal" as const,
  },
  
  enhanced: {
    enableAnimations: true,
    backgroundVariant: "particles" as const,
  },
} as const;

export type LayoutPreset = keyof typeof LayoutPresets;

export interface PresetMainLayoutProps extends Omit<MainLayoutProps, keyof typeof LayoutPresets[LayoutPreset]> {
  preset: LayoutPreset;
  overrides?: Partial<MainLayoutProps>;
}

export function PresetMainLayout({
  preset,
  overrides = {},
  ...props
}: PresetMainLayoutProps) {
  const presetConfig = LayoutPresets[preset];
  
  return (
    <MainLayout
      {...presetConfig}
      {...overrides}
      {...props}
    />
  );
}

export default MainLayout;

# V0 Interface - Modular Component System

A fully refactored, modular implementation of the v0-interface component with enhanced features, Zustand state management, and improved TypeScript support.

## 🏗️ Architecture

The v0-interface has been broken down into smaller, reusable modular components organized in a clean directory structure:

```
components/v0-interface/
├── store/                    # Zustand state management
│   ├── index.ts             # Main store implementation
│   └── types.ts             # TypeScript interfaces
├── components/              # Modular components
│   ├── animations/          # Animation components
│   │   └── floating-elements.tsx
│   ├── chat/               # Chat-related components
│   │   └── chat-message.tsx
│   ├── input/              # Input components
│   │   └── enhanced-input-area.tsx
│   ├── layout/             # Layout components
│   │   └── main-layout.tsx
│   ├── ui/                 # UI components
│   │   ├── quick-actions-grid.tsx
│   │   └── preview-panel.tsx
│   └── index.ts            # Component exports
├── index.tsx               # Main interface component
└── README.md               # This file
```

## 🚀 Features

### Enhanced State Management
- **Zustand Store**: Centralized state management with TypeScript support
- **State Persistence**: Automatic persistence of user preferences
- **Optimized Selectors**: Performance-optimized hooks for component updates
- **Modular State Slices**: Organized into UI, Chat, Animation, and Settings states

### Modular Components
- **Floating Elements**: Configurable particle animations with presets
- **Chat Messages**: Enhanced message display with streaming indicators
- **Input Areas**: Advanced textarea with assistant selector and animations
- **Quick Actions**: Configurable action grids with hover effects
- **Preview Panel**: Resizable preview with tool result rendering
- **Layouts**: Responsive layout components for different states

### Enhanced Features
- **Accessibility**: Improved ARIA labels and keyboard navigation
- **Error Handling**: Comprehensive error boundaries and fallbacks
- **Animation System**: Configurable animations with performance controls
- **Theme Support**: Full theme integration with CSS variables
- **TypeScript**: Strict typing throughout the component system

## 📦 Usage

### Basic Usage

```tsx
import { AgentInterface } from "@/components/v0-interface";

export function MyApp() {
  return (
    <AgentInterface 
      className="min-h-screen"
      onSubmit={(e) => console.log("Form submitted", e)}
    />
  );
}
```

### Using Individual Components

```tsx
import {
  FloatingElements,
  QuickActionsGrid,
  EnhancedInputArea,
  ChatMessage,
  PreviewPanel,
} from "@/components/v0-interface";

// Use components individually
export function CustomInterface() {
  return (
    <div>
      <FloatingElements preset="dynamic" />
      <QuickActionsGrid preset="default" onActionClick={handleAction} />
      <EnhancedInputArea preset="enhanced" />
    </div>
  );
}
```

### Store Integration

```tsx
import { 
  useUIStore, 
  useChatStore, 
  useAnimationStore 
} from "@/components/v0-interface";

export function CustomComponent() {
  const { state: uiState, actions: uiActions } = useUIStore();
  const { state: chatState, actions: chatActions } = useChatStore();
  const { state: animationState } = useAnimationStore();

  return (
    <div>
      <button onClick={() => uiActions.togglePreview()}>
        {uiState.showPreview ? "Hide" : "Show"} Preview
      </button>
      <p>Messages: {chatState.messages.length}</p>
      <p>Animations: {animationState.enableAnimations ? "On" : "Off"}</p>
    </div>
  );
}
```

## 🎨 Presets

Most components come with built-in presets for common use cases:

### Floating Elements
- `minimal`: Simple, subtle animations
- `dynamic`: Active, engaging animations
- `elegant`: Smooth, professional animations
- `energetic`: High-energy, fast animations

### Quick Actions
- `default`: Standard 6-action grid
- `compact`: Reduced 4-action grid
- `extended`: Full 12-action grid
- `minimal`: Simple 3-action grid

### Input Areas
- `initial`: Large, prominent input for empty state
- `chat`: Compact input for chat conversations
- `compact`: Minimal input for tight spaces
- `focused`: Extra-large input for detailed descriptions

### Layouts
- `chat`: Optimized for chat conversations
- `initial`: Perfect for empty/welcome states
- `minimal`: Clean, distraction-free layout
- `enhanced`: Full-featured with all animations

## 🔧 Configuration

### Animation Settings

```tsx
import { useAnimationStore } from "@/components/v0-interface";

const { actions } = useAnimationStore();

// Configure animations
actions.setEnableAnimations(true);
actions.setAnimationSpeed(1.2);
actions.setParticleCount(25);
actions.setGradientAnimation(true);
```

### UI Settings

```tsx
import { useUIStore } from "@/components/v0-interface";

const { actions } = useUIStore();

// Configure UI
actions.setAssistantType("engineer");
actions.setShowPreview(true);
actions.setShowToolbar(false);
```

## 🎯 Component Props

### AgentInterface
```tsx
interface AgentInterfaceProps {
  className?: string;
  onSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
  children?: React.ReactNode;
}
```

### FloatingElements
```tsx
interface FloatingElementsProps {
  className?: string;
  elementCount?: number;
  colors?: string[];
  sizes?: number[];
  animationDuration?: number[];
  enableRandomMovement?: boolean;
  enablePulse?: boolean;
  enableRotation?: boolean;
}
```

### QuickActionsGrid
```tsx
interface QuickActionsGridProps {
  actions?: QuickAction[];
  onActionClick?: (action: QuickAction) => void;
  className?: string;
  columns?: number;
  showDescriptions?: boolean;
  variant?: "default" | "compact" | "detailed";
  enableHoverEffects?: boolean;
  enableStaggeredAnimation?: boolean;
}
```

## 🔄 Migration Guide

The refactored v0-interface maintains backward compatibility. Existing usage will continue to work:

```tsx
// Old usage (still works)
import { AgentInterface } from "@/components/v0-interface";

// New modular usage (recommended)
import { 
  AgentInterface,
  FloatingElements,
  QuickActionsGrid 
} from "@/components/v0-interface";
```

## 🧪 Testing

Components include comprehensive TypeScript interfaces and are designed for easy testing:

```tsx
import { render, screen } from "@testing-library/react";
import { AgentInterface } from "@/components/v0-interface";

test("renders agent interface", () => {
  render(<AgentInterface />);
  expect(screen.getByText("What can I help you ship?")).toBeInTheDocument();
});
```

## 🚀 Performance

- **Lazy Loading**: Components load only when needed
- **Optimized Selectors**: Zustand selectors prevent unnecessary re-renders
- **Animation Controls**: Animations can be disabled for performance
- **Memoization**: Heavy computations are memoized
- **Bundle Splitting**: Modular architecture enables code splitting

## 🤝 Contributing

When adding new components:

1. Follow the existing directory structure
2. Include TypeScript interfaces
3. Add preset configurations
4. Integrate with the Zustand store
5. Include comprehensive props documentation
6. Add animation controls where appropriate

## 📝 License

This component system is part of the Soimagine Digital Agency project.

# ModernTextArea Component Documentation

## Overview

The ModernTextArea is a comprehensive, feature-rich text input component built for modern web applications. It provides advanced functionality including syntax highlighting, auto-complete, collaboration features, and seamless integration with AI-powered workflows.

## Features

### Core Features
- ✅ **Multi-line text input** with auto-resize functionality
- ✅ **Syntax highlighting** support for multiple languages
- ✅ **Line numbers** display option
- ✅ **Character/word count** indicators
- ✅ **Placeholder text** with fade-in animation
- ✅ **Undo/redo** functionality with history tracking

### Design Features
- ✅ **Tailwind CSS** styling with consistent design system
- ✅ **Gradient borders** and focus states
- ✅ **Smooth animations** using Framer Motion
- ✅ **Dark/light mode** compatibility
- ✅ **Responsive design** for mobile and desktop
- ✅ **Multiple size variants** (sm, md, lg, xl)

### Advanced Features
- ✅ **Auto-complete** suggestions with trigger characters
- ✅ **Markdown preview** toggle
- ✅ **Copy/paste handling** with formatting preservation
- ✅ **Drag and drop** file support
- ✅ **Real-time collaboration** indicators
- ✅ **Plugin system** for extensions

## Installation

```bash
# The component is already included in your project
# Make sure you have the required dependencies:
npm install framer-motion lucide-react
```

## Basic Usage

```tsx
import { ModernTextArea } from '@/components/ui/modern-textarea'
import { useModernTextArea } from '@/hooks/use-modern-textarea'

function MyComponent() {
  const textArea = useModernTextArea('Initial content')

  return (
    <ModernTextArea
      ref={textArea.textareaRef}
      value={textArea.value}
      onChange={textArea.setValue}
      placeholder="Start typing..."
      showCharCount
      showWordCount
      autoResize
    />
  )
}
```

## Props Reference

### ModernTextAreaProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `string` | `''` | Controlled value |
| `onChange` | `(value: string) => void` | - | Change handler |
| `placeholder` | `string` | `'Start typing...'` | Placeholder text |
| `disabled` | `boolean` | `false` | Disable input |
| `readOnly` | `boolean` | `false` | Read-only mode |
| `autoResize` | `boolean` | `true` | Auto-resize height |
| `showLineNumbers` | `boolean` | `false` | Show line numbers |
| `showCharCount` | `boolean` | `false` | Show character count |
| `showWordCount` | `boolean` | `false` | Show word count |
| `maxLength` | `number` | - | Maximum character limit |
| `minRows` | `number` | `3` | Minimum rows |
| `maxRows` | `number` | `20` | Maximum rows |
| `size` | `'sm' \| 'md' \| 'lg' \| 'xl'` | `'md'` | Size variant |
| `variant` | `'default' \| 'code' \| 'message' \| 'form'` | `'default'` | Style variant |
| `syntaxHighlight` | `boolean` | `false` | Enable syntax highlighting |
| `language` | `string` | `'javascript'` | Programming language |
| `showToolbar` | `boolean` | `false` | Show formatting toolbar |
| `enableMarkdown` | `boolean` | `false` | Enable markdown features |
| `enableAutoComplete` | `boolean` | `false` | Enable auto-complete |
| `enableDragDrop` | `boolean` | `false` | Enable file drag & drop |
| `enableCollaboration` | `boolean` | `false` | Enable collaboration features |

## Size Variants

```tsx
// Small - Compact input
<ModernTextArea size="sm" minRows={2} />

// Medium - Default size
<ModernTextArea size="md" minRows={3} />

// Large - Spacious input
<ModernTextArea size="lg" minRows={4} />

// Extra Large - Maximum space
<ModernTextArea size="xl" minRows={5} />
```

## Style Variants

```tsx
// Default - General purpose
<ModernTextArea variant="default" />

// Code - For code editing
<ModernTextArea variant="code" showLineNumbers syntaxHighlight />

// Message - For chat/messaging
<ModernTextArea variant="message" enableMarkdown />

// Form - For form inputs
<ModernTextArea variant="form" showCharCount />
```

## Advanced Usage

### With Syntax Highlighting

```tsx
<ModernTextArea
  variant="code"
  syntaxHighlight
  language="typescript"
  showLineNumbers
  showToolbar
  value={codeContent}
  onChange={setCodeContent}
/>
```

### With Auto-complete

```tsx
const suggestions = ['component', 'interface', 'function']

<ModernTextArea
  enableAutoComplete
  // Auto-complete triggers on @, #, / characters
  placeholder="Type @ for suggestions..."
/>
```

### With File Drop

```tsx
<ModernTextArea
  enableDragDrop
  onDrop={(files) => {
    // Handle dropped files
    console.log('Files dropped:', files)
  }}
/>
```

### With Collaboration

```tsx
<ModernTextArea
  enableCollaboration
  // Shows typing indicators and cursors
  // Requires real-time backend integration
/>
```

## Hooks

### useModernTextArea

```tsx
const {
  value,
  setValue,
  textareaRef,
  isTyping,
  history: { canUndo, canRedo, undo, redo },
  operations: { insertText, formatText, clear },
  stats: { characters, words, lines, paragraphs }
} = useModernTextArea('initial value')
```

### useAutoComplete

```tsx
const autoComplete = useAutoComplete(['suggestion1', 'suggestion2'])

// Use in onKeyDown handler
const handleKeyDown = (e) => {
  if (autoComplete.handleKeyDown(e)) {
    return // Auto-complete handled the event
  }
  // Handle other keys
}
```

### useFileHandler

```tsx
const fileHandler = useFileHandler()

// Handle file drops
const handleDrop = async (files) => {
  const contents = await fileHandler.handleFileDrop(files)
  // Process file contents
}
```

## Plugins

### Emoji Picker

```tsx
import { EmojiPicker } from '@/components/ui/modern-textarea-plugins'

<EmojiPicker
  onEmojiSelect={(emoji) => insertText(emoji)}
/>
```

### Markdown Toolbar

```tsx
import { MarkdownToolbar } from '@/components/ui/modern-textarea-plugins'

<MarkdownToolbar
  onInsert={insertText}
  onFormat={formatText}
/>
```

### File Drop Zone

```tsx
import { FileDropZone } from '@/components/ui/modern-textarea-plugins'

<FileDropZone onFileDrop={handleFileDrop}>
  <ModernTextArea />
</FileDropZone>
```

## Integration with AI SDK

The ModernTextArea integrates seamlessly with the AI SDK chat system:

```tsx
import { useChat } from '@ai-sdk/react'

function ChatInterface() {
  const { input, handleInputChange, handleSubmit } = useChat()

  return (
    <form onSubmit={handleSubmit}>
      <ModernTextArea
        value={input}
        onChange={(value) => handleInputChange({ target: { value } } as any)}
        variant="message"
        enableMarkdown
        enableAutoComplete
        placeholder="Ask AI anything..."
      />
    </form>
  )
}
```

## Accessibility

The component includes comprehensive accessibility features:

- ✅ **ARIA labels** and descriptions
- ✅ **Keyboard navigation** support
- ✅ **Screen reader** compatibility
- ✅ **Focus management**
- ✅ **High contrast** mode support

## Performance

- ✅ **Optimized rendering** with React.memo
- ✅ **Debounced updates** for real-time features
- ✅ **Lazy loading** of plugins
- ✅ **Memory efficient** history management

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## Examples

Visit `/modern-textarea-demo` to see all features in action with interactive examples.

## Contributing

The component is designed to be modular and extensible. To add new features:

1. Create plugins in `modern-textarea-plugins.tsx`
2. Add hooks in `use-modern-textarea.ts`
3. Update the main component props interface
4. Add documentation and examples

## License

Part of the Soimagine Digital Agency component library.

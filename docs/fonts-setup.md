# Custom Fonts Setup with Next.js

This document explains how custom fonts are implemented in the Soimagine Digital Agency project using Next.js's `next/font/local` feature.

## Font Files

The following custom fonts are available in `assets/fonts/`:

### Circular Std
- **CircularStd-Light.otf** (300 weight)
- **CircularStd-Book.otf** (400 weight - regular)
- **CircularStd-Bold.otf** (700 weight)

### Century Gothic
- **century_gothic.ttf** (400 weight - regular)
- **century_gothic_bold.ttf** (700 weight)
- **century_gothic_italic.ttf** (400 weight - italic)
- **century_gothic_bold_italic.ttf** (700 weight - italic)

## Implementation

### 1. Font Configuration (`lib/fonts.ts`)

```typescript
import localFont from 'next/font/local'

// Century Gothic Font Family
export const centuryGothic = localFont({
  src: [
    {
      path: '../assets/fonts/century_gothic.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../assets/fonts/century_gothic_bold.ttf',
      weight: '700',
      style: 'normal',
    },
    // ... more variants
  ],
  variable: '--font-century-gothic',
  display: 'swap',
  fallback: ['Arial', 'sans-serif'],
})

// Circular Std Font Family
export const circularStd = localFont({
  src: [
    {
      path: '../assets/fonts/CircularStd-Light.otf',
      weight: '300',
      style: 'normal',
    },
    // ... more variants
  ],
  variable: '--font-circular-std',
  display: 'swap',
  fallback: ['Helvetica Neue', 'Arial', 'sans-serif'],
})
```

### 2. Layout Integration (`app/layout.tsx`)

```typescript
import { circularStd, centuryGothic, fontVariables } from "@/lib/fonts"

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning className={fontVariables}>
      <body className={circularStd.className}>
        {children}
      </body>
    </html>
  )
}
```

### 3. Tailwind Configuration (`tailwind.config.ts`)

```typescript
theme: {
  extend: {
    fontFamily: {
      'circular': ['var(--font-circular-std)', 'Helvetica Neue', 'Arial', 'sans-serif'],
      'century-gothic': ['var(--font-century-gothic)', 'Arial', 'sans-serif'],
      'primary': ['var(--font-circular-std)', 'Helvetica Neue', 'Arial', 'sans-serif'],
      'secondary': ['var(--font-century-gothic)', 'Arial', 'sans-serif'],
    },
  }
}
```

### 4. Global CSS Utilities (`app/globals.css`)

```css
@layer base {
  body {
    @apply bg-background text-foreground font-primary;
  }
}

@layer utilities {
  .font-circular {
    font-family: var(--font-circular-std), 'Helvetica Neue', 'Arial', sans-serif;
  }
  
  .font-century-gothic {
    font-family: var(--font-century-gothic), 'Arial', sans-serif;
  }
  
  .font-primary {
    font-family: var(--font-circular-std), 'Helvetica Neue', 'Arial', sans-serif;
  }
  
  .font-secondary {
    font-family: var(--font-century-gothic), 'Arial', sans-serif;
  }
}
```

## Usage

### Tailwind CSS Classes

Use these classes in your components:

```jsx
// Primary font (Circular Std)
<p className="font-primary">Body text using Circular Std</p>
<p className="font-circular">Explicit Circular Std usage</p>

// Secondary font (Century Gothic)
<h1 className="font-secondary">Heading using Century Gothic</h1>
<h1 className="font-century-gothic">Explicit Century Gothic usage</h1>

// With font weights
<h1 className="font-secondary font-bold">Bold Century Gothic</h1>
<p className="font-primary font-light">Light Circular Std</p>
```

### Direct Font Object Usage

For components that need direct access to font objects:

```jsx
import { circularStd, centuryGothic } from "@/lib/fonts"

function MyComponent() {
  return (
    <div className={circularStd.className}>
      <h1 className={centuryGothic.className}>Mixed fonts</h1>
    </div>
  )
}
```

### CSS Variables

Use CSS variables for custom styling:

```css
.custom-element {
  font-family: var(--font-circular-std), sans-serif;
}
```

## Font Hierarchy

### Primary Font: Circular Std
- **Usage**: Body text, UI elements, navigation, buttons
- **Characteristics**: Modern, clean, highly readable
- **Weights**: Light (300), Regular (400), Bold (700)

### Secondary Font: Century Gothic
- **Usage**: Headings, branding, emphasis text
- **Characteristics**: Geometric, distinctive, strong personality
- **Weights**: Regular (400), Bold (700)
- **Styles**: Normal, Italic

## Performance Considerations

1. **Font Display**: Uses `swap` for better loading performance
2. **Fallbacks**: Proper fallback fonts defined for each family
3. **Preloading**: Next.js automatically optimizes font loading
4. **Variable Fonts**: CSS variables enable efficient font switching

## Browser Support

- **Modern Browsers**: Full support with Next.js font optimization
- **Fallbacks**: Graceful degradation to system fonts
- **Loading States**: Smooth transitions with `font-display: swap`

## Demo Page

Visit `/fonts-demo` to see:
- All font weights and styles
- Usage examples with code snippets
- Tailwind class reference
- Live typography demonstrations

## Troubleshooting

### Fonts Not Loading
1. Check file paths in `lib/fonts.ts`
2. Verify font files exist in `assets/fonts/`
3. Ensure proper import in `layout.tsx`

### Styling Issues
1. Confirm Tailwind classes are properly configured
2. Check CSS variable names match font configuration
3. Verify fallback fonts are appropriate

### Performance Issues
1. Consider reducing font variants if not needed
2. Check font file sizes (optimize if necessary)
3. Monitor loading performance in dev tools

## Best Practices

1. **Consistent Usage**: Use `font-primary` and `font-secondary` for consistency
2. **Semantic Hierarchy**: Century Gothic for headings, Circular Std for body
3. **Weight Variation**: Use appropriate font weights for visual hierarchy
4. **Fallback Planning**: Always include appropriate fallback fonts
5. **Performance**: Only load font weights you actually use

import type { DefaultSession } from 'next-auth'

declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      role?: "USER" | "ADMIN"
      emailVerified?: Date | null
      isVerified?: boolean
      lockedUntil?: Date | null
      failedLoginAttempts?: number
    }
  }

  interface User {
    id: string
    name?: string | null
    email?: string | null
    image?: string | null
    role?: "USER" | "ADMIN"
    emailVerified?: Date | null
    isVerified?: boolean
    lockedUntil?: Date | null
    failedLoginAttempts?: number
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id?: string
    role?: "USER" | "ADMIN"
    emailVerified?: Date | null
    isVerified?: boolean
    lockedUntil?: Date | null
    failedLoginAttempts?: number
  }
}
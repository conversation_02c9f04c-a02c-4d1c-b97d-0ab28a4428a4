# 🚀 Soimagine Marketing Platform - Setup Guide

This guide will help you set up and run the comprehensive marketing platform with all its features.

## 📋 Prerequisites

- Node.js 18+ installed
- pnpm package manager (recommended) or npm
- OpenAI API key
- Git

## 🛠 Installation Steps

### 1. Clone and Install Dependencies

```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd soimagine-digital-agency

# Install dependencies
pnpm install
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

**Required Environment Variables:**
```env
# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-here"

# OpenAI API (Required)
OPENAI_API_KEY="your-openai-api-key-here"
```

### 3. Database Setup

```bash
# Generate Prisma client
pnpm db:generate

# Create database and tables
pnpm db:push

# Seed database with initial data
pnpm db:seed
```

### 4. Start Development Server

```bash
# Start the development server
pnpm dev
```

The application will be available at `http://localhost:3000`

## 🎯 Quick Start Guide

### 1. Create Your First User

1. Navigate to `http://localhost:3000/signup`
2. Register with your email and password
3. You'll automatically receive 100 welcome bonus credits

### 2. Explore the Dashboard

1. Go to `http://localhost:3000/dashboard`
2. View your credit balance, progress, and available challenges
3. Complete your first challenge to earn credits and XP

### 3. Access Admin Panel (Optional)

1. The seeding script creates an admin user: `<EMAIL>`
2. Set a password for this user or create your own admin account
3. Access admin features at `http://localhost:3000/admin`

## 🎮 Platform Features

### ✅ Implemented Features

#### **Core System**
- ✅ Multi-agent chat assistant interface
- ✅ Credit-based usage system with daily/monthly limits
- ✅ Comprehensive gamification engine
- ✅ AI-powered challenge generation

#### **Credit Management**
- ✅ Real-time credit balance tracking
- ✅ Transaction history and audit trail
- ✅ Automatic daily/monthly reset
- ✅ Rate limiting and usage enforcement

#### **Gamification**
- ✅ Challenge system with 8+ predefined templates
- ✅ XP and level progression system
- ✅ Achievement system with 10+ achievements
- ✅ Community leaderboards
- ✅ Activity streaks and progress tracking

#### **Content Marketing**
- ✅ Social media sharing challenges
- ✅ Content creation tasks
- ✅ Referral system with tracking
- ✅ Engagement analytics

#### **AI Integration**
- ✅ Enhanced chat assistants with credit deduction
- ✅ AI challenge generation agent
- ✅ Performance learning and optimization

#### **Admin Dashboard**
- ✅ Comprehensive statistics and analytics
- ✅ User management
- ✅ Challenge and achievement management
- ✅ Credit system administration

## 📊 Database Schema

The platform includes 15+ database models:

- **User Management**: User, Account, Session, VerificationToken
- **Credit System**: CreditBalance, CreditTransaction
- **Gamification**: Challenge, ChallengeCompletion, UserProgress, Achievement, UserAchievement
- **Marketing**: SocialShare, Referral, ApiUsage

## 🔧 Available Scripts

```bash
# Development
pnpm dev                 # Start development server
pnpm build              # Build for production
pnpm start              # Start production server

# Database
pnpm db:generate        # Generate Prisma client
pnpm db:push           # Push schema to database
pnpm db:seed           # Seed database with initial data
pnpm db:studio         # Open Prisma Studio
pnpm db:reset          # Reset database and reseed

# Other
pnpm lint              # Run ESLint
```

## 🎯 Challenge Types Available

1. **Social Share** - Share content on social platforms
2. **Content Creation** - Create blog posts, infographics, etc.
3. **Referral** - Invite friends to join the platform
4. **Learning** - Complete educational modules
5. **Engagement** - Participate in community activities
6. **Community** - Help other members

## 🏆 Achievement System

The platform includes 10+ achievements:
- **First Steps** - Complete your first challenge
- **Social Butterfly** - Share on 5 platforms
- **Streak Master** - Maintain 10-day streak
- **Content Creator** - Create 10 pieces of content
- **Referral Champion** - Refer 5 new members
- **Legendary Marketer** - Reach Level 20 + 100 challenges

## 🔐 User Roles

- **USER** - Standard platform user
- **ADMIN** - Full administrative access
- **MODERATOR** - Limited administrative access

## 📈 Credit System

### Default Credit Allocation
- **Welcome Bonus**: 100 credits
- **Daily Limit**: 100 credits
- **Monthly Limit**: 1000 credits

### Credit Costs
- **Consultant AI**: 2 credits per interaction
- **Engineer AI**: 3 credits per interaction
- **Executive AI**: 5 credits per interaction
- **Tool Usage**: +2 credits per tool call

### Earning Credits
- Complete challenges (10-500 credits)
- Refer new users (100+ credits)
- Daily check-ins (20 credits)
- Achievement unlocks (25-500 credits)

## 🚀 Production Deployment

### Environment Variables for Production
```env
DATABASE_URL="postgresql://user:password@host:port/database"
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="secure-random-string"
OPENAI_API_KEY="your-openai-api-key"
```

### Recommended Infrastructure
- **Database**: PostgreSQL with connection pooling
- **Hosting**: Vercel, Netlify, or similar
- **File Storage**: AWS S3 for media uploads
- **Monitoring**: Application performance monitoring
- **CDN**: Content delivery network

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   pnpm db:push
   pnpm db:generate
   ```

2. **Missing Environment Variables**
   - Check `.env` file exists and has required variables
   - Restart development server after changes

3. **OpenAI API Errors**
   - Verify API key is correct
   - Check API usage limits

4. **Build Errors**
   ```bash
   rm -rf .next
   pnpm build
   ```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the comprehensive documentation in `MARKETING_PLATFORM_README.md`
3. Contact the development team

## 🎉 Next Steps

1. **Customize Challenges**: Add your own challenge templates
2. **Integrate Social APIs**: Connect real social media APIs
3. **Add Email Notifications**: Set up email service
4. **Enhance Analytics**: Add more detailed tracking
5. **Mobile App**: Consider React Native implementation

---

**🎯 You're all set! Start exploring the platform and building your marketing community!**

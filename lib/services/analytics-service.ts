import { prisma } from '../prisma'
import { PlatformFactory } from './social-media/base-platform'
import { SocialPlatform, AnalyticsPeriod } from '@prisma/client'

export interface AnalyticsData {
  impressions: number
  reach: number
  engagement: number
  clicks: number
  shares: number
  comments: number
  likes: number
  saves?: number
  engagementRate: number
  clickThroughRate: number
  platformMetrics?: Record<string, any>
}

export interface PerformanceReport {
  period: {
    startDate: Date
    endDate: Date
  }
  overview: {
    totalPosts: number
    totalImpressions: number
    totalEngagement: number
    averageEngagementRate: number
    topPerformingPost: any
    worstPerformingPost: any
  }
  platformBreakdown: Record<SocialPlatform, AnalyticsData>
  trends: {
    impressionsTrend: number
    engagementTrend: number
    followerGrowth: Record<SocialPlatform, number>
  }
  recommendations: string[]
}

export interface CompetitorAnalysis {
  competitor: string
  platform: SocialPlatform
  metrics: {
    followers: number
    avgEngagement: number
    postFrequency: number
    topHashtags: string[]
    contentTypes: Record<string, number>
  }
  insights: string[]
}

export class AnalyticsService {
  
  async collectPostAnalytics(userId: string, postId: string): Promise<boolean> {
    try {
      const post = await prisma.post.findFirst({
        where: { id: postId, userId },
        include: { socialAccount: true }
      })

      if (!post || !post.platformPostId || !post.socialAccount) {
        return false
      }

      const platform = await PlatformFactory.createPlatform(
        post.platform,
        post.socialAccount.accessToken!,
        post.socialAccount.refreshToken || undefined
      )

      const engagement = await platform.getPostEngagement(post.platformPostId)
      const analytics = await platform.getPostAnalytics(
        post.platformPostId,
        new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        new Date()
      )

      // Calculate engagement rate
      const totalEngagement = engagement.likes + engagement.comments + engagement.shares
      const engagementRate = engagement.views > 0 ? (totalEngagement / engagement.views) * 100 : 0
      const clickThroughRate = engagement.views > 0 ? (engagement.clicks / engagement.views) * 100 : 0

      // Store analytics data
      await prisma.analytics.upsert({
        where: {
          userId_socialAccountId_postId_campaignId_date_period: {
            userId,
            socialAccountId: post.socialAccountId!,
            postId: post.id,
            campaignId: post.campaignId,
            date: new Date(),
            period: AnalyticsPeriod.DAILY
          }
        },
        update: {
          impressions: engagement.views,
          reach: engagement.views, // Simplified - platforms may provide different reach data
          engagement: totalEngagement,
          clicks: engagement.clicks,
          shares: engagement.shares,
          comments: engagement.comments,
          likes: engagement.likes,
          saves: engagement.saves || 0,
          engagementRate,
          clickThroughRate,
          platformMetrics: analytics
        },
        create: {
          userId,
          socialAccountId: post.socialAccountId!,
          postId: post.id,
          campaignId: post.campaignId,
          date: new Date(),
          period: AnalyticsPeriod.DAILY,
          impressions: engagement.views,
          reach: engagement.views,
          engagement: totalEngagement,
          clicks: engagement.clicks,
          shares: engagement.shares,
          comments: engagement.comments,
          likes: engagement.likes,
          saves: engagement.saves || 0,
          engagementRate,
          clickThroughRate,
          platformMetrics: analytics
        }
      })

      // Update post engagement metrics
      await prisma.post.update({
        where: { id: postId },
        data: {
          likes: engagement.likes,
          comments: engagement.comments,
          shares: engagement.shares,
          views: engagement.views,
          clicks: engagement.clicks
        }
      })

      return true
    } catch (error) {
      console.error('Failed to collect post analytics:', error)
      return false
    }
  }

  async generatePerformanceReport(
    userId: string,
    startDate: Date,
    endDate: Date,
    platforms?: SocialPlatform[]
  ): Promise<PerformanceReport> {
    try {
      // Get analytics data for the period
      const analytics = await prisma.analytics.findMany({
        where: {
          userId,
          date: {
            gte: startDate,
            lte: endDate
          },
          ...(platforms && {
            socialAccount: {
              platform: { in: platforms }
            }
          })
        },
        include: {
          socialAccount: true,
          post: true,
          campaign: true
        },
        orderBy: { date: 'desc' }
      })

      // Get posts for the period
      const posts = await prisma.post.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          ...(platforms && { platform: { in: platforms } })
        },
        orderBy: { createdAt: 'desc' }
      })

      // Calculate overview metrics
      const totalImpressions = analytics.reduce((sum, a) => sum + a.impressions, 0)
      const totalEngagement = analytics.reduce((sum, a) => sum + a.engagement, 0)
      const averageEngagementRate = analytics.length > 0 
        ? analytics.reduce((sum, a) => sum + a.engagementRate, 0) / analytics.length 
        : 0

      // Find top and worst performing posts
      const topPerformingPost = posts.reduce((top, post) => 
        (post.likes + post.comments + post.shares) > (top.likes + top.comments + top.shares) ? post : top
      , posts[0])

      const worstPerformingPost = posts.reduce((worst, post) => 
        (post.likes + post.comments + post.shares) < (worst.likes + worst.comments + worst.shares) ? post : worst
      , posts[0])

      // Platform breakdown
      const platformBreakdown: Record<SocialPlatform, AnalyticsData> = {} as any
      
      for (const platform of Object.values(SocialPlatform)) {
        const platformAnalytics = analytics.filter(a => a.socialAccount?.platform === platform)
        
        if (platformAnalytics.length > 0) {
          const totalImpr = platformAnalytics.reduce((sum, a) => sum + a.impressions, 0)
          const totalEng = platformAnalytics.reduce((sum, a) => sum + a.engagement, 0)
          const avgEngRate = platformAnalytics.reduce((sum, a) => sum + a.engagementRate, 0) / platformAnalytics.length
          const avgCTR = platformAnalytics.reduce((sum, a) => sum + a.clickThroughRate, 0) / platformAnalytics.length

          platformBreakdown[platform] = {
            impressions: totalImpr,
            reach: platformAnalytics.reduce((sum, a) => sum + a.reach, 0),
            engagement: totalEng,
            clicks: platformAnalytics.reduce((sum, a) => sum + a.clicks, 0),
            shares: platformAnalytics.reduce((sum, a) => sum + a.shares, 0),
            comments: platformAnalytics.reduce((sum, a) => sum + a.comments, 0),
            likes: platformAnalytics.reduce((sum, a) => sum + a.likes, 0),
            saves: platformAnalytics.reduce((sum, a) => sum + (a.saves || 0), 0),
            engagementRate: avgEngRate,
            clickThroughRate: avgCTR
          }
        }
      }

      // Calculate trends (compare with previous period)
      const previousPeriod = {
        start: new Date(startDate.getTime() - (endDate.getTime() - startDate.getTime())),
        end: startDate
      }

      const previousAnalytics = await prisma.analytics.findMany({
        where: {
          userId,
          date: {
            gte: previousPeriod.start,
            lte: previousPeriod.end
          }
        },
        include: { socialAccount: true }
      })

      const previousImpressions = previousAnalytics.reduce((sum, a) => sum + a.impressions, 0)
      const previousEngagement = previousAnalytics.reduce((sum, a) => sum + a.engagement, 0)

      const impressionsTrend = previousImpressions > 0 
        ? ((totalImpressions - previousImpressions) / previousImpressions) * 100 
        : 0

      const engagementTrend = previousEngagement > 0 
        ? ((totalEngagement - previousEngagement) / previousEngagement) * 100 
        : 0

      // Follower growth by platform
      const followerGrowth: Record<SocialPlatform, number> = {} as any
      
      for (const platform of Object.values(SocialPlatform)) {
        const currentFollowers = await this.getCurrentFollowers(userId, platform)
        const previousFollowers = await this.getPreviousFollowers(userId, platform, previousPeriod.start)
        
        followerGrowth[platform] = currentFollowers - previousFollowers
      }

      // Generate recommendations
      const recommendations = this.generateRecommendations(analytics, platformBreakdown)

      return {
        period: { startDate, endDate },
        overview: {
          totalPosts: posts.length,
          totalImpressions,
          totalEngagement,
          averageEngagementRate,
          topPerformingPost,
          worstPerformingPost
        },
        platformBreakdown,
        trends: {
          impressionsTrend,
          engagementTrend,
          followerGrowth
        },
        recommendations
      }
    } catch (error) {
      console.error('Failed to generate performance report:', error)
      throw error
    }
  }

  async getContentInsights(userId: string, period: number = 30): Promise<{
    topHashtags: Array<{ hashtag: string; usage: number; avgEngagement: number }>
    bestPostingTimes: Array<{ hour: number; dayOfWeek: number; avgEngagement: number }>
    contentTypePerformance: Record<string, { count: number; avgEngagement: number }>
    audienceInsights: {
      mostEngagedPlatforms: SocialPlatform[]
      peakEngagementHours: number[]
      contentPreferences: string[]
    }
  }> {
    const startDate = new Date(Date.now() - period * 24 * 60 * 60 * 1000)
    const endDate = new Date()

    const posts = await prisma.post.findMany({
      where: {
        userId,
        createdAt: { gte: startDate, lte: endDate },
        status: 'PUBLISHED'
      },
      include: { analytics: true }
    })

    // Analyze hashtags
    const hashtagMap = new Map<string, { usage: number; totalEngagement: number }>()
    
    posts.forEach(post => {
      const engagement = post.likes + post.comments + post.shares
      post.hashtags.forEach(hashtag => {
        const current = hashtagMap.get(hashtag) || { usage: 0, totalEngagement: 0 }
        hashtagMap.set(hashtag, {
          usage: current.usage + 1,
          totalEngagement: current.totalEngagement + engagement
        })
      })
    })

    const topHashtags = Array.from(hashtagMap.entries())
      .map(([hashtag, data]) => ({
        hashtag,
        usage: data.usage,
        avgEngagement: data.usage > 0 ? data.totalEngagement / data.usage : 0
      }))
      .sort((a, b) => b.avgEngagement - a.avgEngagement)
      .slice(0, 20)

    // Analyze posting times
    const timeMap = new Map<string, { count: number; totalEngagement: number }>()
    
    posts.forEach(post => {
      const date = new Date(post.createdAt)
      const hour = date.getHours()
      const dayOfWeek = date.getDay()
      const key = `${hour}-${dayOfWeek}`
      const engagement = post.likes + post.comments + post.shares
      
      const current = timeMap.get(key) || { count: 0, totalEngagement: 0 }
      timeMap.set(key, {
        count: current.count + 1,
        totalEngagement: current.totalEngagement + engagement
      })
    })

    const bestPostingTimes = Array.from(timeMap.entries())
      .map(([key, data]) => {
        const [hour, dayOfWeek] = key.split('-').map(Number)
        return {
          hour,
          dayOfWeek,
          avgEngagement: data.count > 0 ? data.totalEngagement / data.count : 0
        }
      })
      .sort((a, b) => b.avgEngagement - a.avgEngagement)
      .slice(0, 10)

    // Content type performance (simplified)
    const contentTypePerformance = {
      'text': { count: 0, avgEngagement: 0 },
      'image': { count: 0, avgEngagement: 0 },
      'video': { count: 0, avgEngagement: 0 },
      'carousel': { count: 0, avgEngagement: 0 }
    }

    posts.forEach(post => {
      const engagement = post.likes + post.comments + post.shares
      let type = 'text'
      
      if (post.mediaUrls.length > 1) {
        type = 'carousel'
      } else if (post.mediaUrls.length === 1) {
        type = post.mediaTypes[0] === 'VIDEO' ? 'video' : 'image'
      }

      contentTypePerformance[type as keyof typeof contentTypePerformance].count++
      contentTypePerformance[type as keyof typeof contentTypePerformance].avgEngagement += engagement
    })

    // Calculate averages
    Object.keys(contentTypePerformance).forEach(type => {
      const data = contentTypePerformance[type as keyof typeof contentTypePerformance]
      if (data.count > 0) {
        data.avgEngagement = data.avgEngagement / data.count
      }
    })

    // Audience insights
    const platformEngagement = new Map<SocialPlatform, number>()
    posts.forEach(post => {
      const engagement = post.likes + post.comments + post.shares
      const current = platformEngagement.get(post.platform) || 0
      platformEngagement.set(post.platform, current + engagement)
    })

    const mostEngagedPlatforms = Array.from(platformEngagement.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([platform]) => platform)

    const peakEngagementHours = bestPostingTimes
      .slice(0, 5)
      .map(time => time.hour)

    return {
      topHashtags,
      bestPostingTimes,
      contentTypePerformance,
      audienceInsights: {
        mostEngagedPlatforms,
        peakEngagementHours,
        contentPreferences: ['visual content', 'engaging captions', 'trending hashtags'] // Simplified
      }
    }
  }

  private async getCurrentFollowers(userId: string, platform: SocialPlatform): Promise<number> {
    const account = await prisma.socialAccount.findFirst({
      where: { userId, platform, isActive: true }
    })
    return account?.followersCount || 0
  }

  private async getPreviousFollowers(userId: string, platform: SocialPlatform, date: Date): Promise<number> {
    // This would typically be stored in a historical metrics table
    // For now, return current followers (simplified)
    return this.getCurrentFollowers(userId, platform)
  }

  private generateRecommendations(analytics: any[], platformBreakdown: Record<SocialPlatform, AnalyticsData>): string[] {
    const recommendations: string[] = []

    // Analyze engagement rates
    const avgEngagementRate = analytics.length > 0 
      ? analytics.reduce((sum, a) => sum + a.engagementRate, 0) / analytics.length 
      : 0

    if (avgEngagementRate < 2) {
      recommendations.push('Your engagement rate is below average. Consider posting more interactive content like polls, questions, or behind-the-scenes content.')
    }

    // Platform-specific recommendations
    Object.entries(platformBreakdown).forEach(([platform, data]) => {
      if (data.engagementRate < 1) {
        recommendations.push(`${platform} engagement is low. Try posting at different times or using more platform-specific features.`)
      }
      
      if (data.clickThroughRate < 0.5) {
        recommendations.push(`Improve your ${platform} call-to-actions to increase click-through rates.`)
      }
    })

    // General recommendations
    if (recommendations.length === 0) {
      recommendations.push('Great job! Your content is performing well. Keep experimenting with new formats and topics.')
    }

    return recommendations
  }
}

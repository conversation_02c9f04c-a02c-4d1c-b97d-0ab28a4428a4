import { OpenAI } from 'openai'
import { Anthropic } from '@anthropic-ai/sdk'
import { SocialPlatform, MediaType, CampaignObjective } from '@prisma/client'

export interface ContentGenerationRequest {
  platform: SocialPlatform
  contentType: 'post' | 'story' | 'video_script' | 'caption' | 'hashtags'
  topic?: string
  brandVoice?: BrandVoiceProfile
  targetAudience?: string
  campaignObjective?: CampaignObjective
  keywords?: string[]
  tone?: 'professional' | 'casual' | 'friendly' | 'humorous' | 'inspirational' | 'urgent'
  length?: 'short' | 'medium' | 'long'
  includeHashtags?: boolean
  includeCTA?: boolean
  customPrompt?: string
}

export interface BrandVoiceProfile {
  name: string
  tone: string
  style: string
  personality: Record<string, any>
  dosList: string[]
  dontsList: string[]
  keywords: string[]
  sampleContent: string[]
}

export interface GeneratedContent {
  content: string
  hashtags?: string[]
  suggestions?: string[]
  variations?: string[]
  metadata?: {
    wordCount: number
    characterCount: number
    estimatedEngagement?: number
    seoScore?: number
  }
}

export class AIContentGenerator {
  private openai: OpenAI
  private anthropic: Anthropic
  private defaultProvider: 'openai' | 'anthropic'

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    })
    
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY
    })

    this.defaultProvider = process.env.DEFAULT_AI_PROVIDER as 'openai' | 'anthropic' || 'openai'
  }

  async generateContent(request: ContentGenerationRequest, provider?: 'openai' | 'anthropic'): Promise<GeneratedContent> {
    const selectedProvider = provider || this.defaultProvider

    try {
      const prompt = this.buildPrompt(request)
      
      let response: string
      if (selectedProvider === 'anthropic') {
        response = await this.generateWithAnthropic(prompt)
      } else {
        response = await this.generateWithOpenAI(prompt)
      }

      return this.parseResponse(response, request)
    } catch (error) {
      console.error('Content generation failed:', error)
      throw new Error(`Failed to generate content: ${error}`)
    }
  }

  async generateMultipleVariations(request: ContentGenerationRequest, count: number = 3): Promise<GeneratedContent[]> {
    const variations: GeneratedContent[] = []
    
    for (let i = 0; i < count; i++) {
      try {
        const variation = await this.generateContent({
          ...request,
          customPrompt: `${request.customPrompt || ''} Generate variation ${i + 1} with a different approach.`
        })
        variations.push(variation)
      } catch (error) {
        console.error(`Failed to generate variation ${i + 1}:`, error)
      }
    }

    return variations
  }

  async optimizeForPlatform(content: string, fromPlatform: SocialPlatform, toPlatform: SocialPlatform): Promise<string> {
    const prompt = `
      Adapt this social media content from ${fromPlatform} to ${toPlatform}:
      
      Original content: "${content}"
      
      Consider the following platform-specific requirements:
      ${this.getPlatformRequirements(toPlatform)}
      
      Return only the adapted content, maintaining the core message but optimizing for the target platform.
    `

    try {
      return await this.generateWithOpenAI(prompt)
    } catch (error) {
      console.error('Content optimization failed:', error)
      return content // Return original if optimization fails
    }
  }

  async generateHashtags(topic: string, platform: SocialPlatform, count: number = 10): Promise<string[]> {
    const prompt = `
      Generate ${count} relevant hashtags for ${platform} about: ${topic}
      
      Requirements:
      - Mix of popular and niche hashtags
      - Platform-appropriate format
      - No spaces in hashtags
      - Include trending and evergreen tags
      
      Return only the hashtags, one per line, without the # symbol.
    `

    try {
      const response = await this.generateWithOpenAI(prompt)
      return response.split('\n')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0)
        .slice(0, count)
    } catch (error) {
      console.error('Hashtag generation failed:', error)
      return []
    }
  }

  async analyzeContentPerformance(content: string, platform: SocialPlatform): Promise<{
    engagementScore: number
    suggestions: string[]
    issues: string[]
  }> {
    const prompt = `
      Analyze this ${platform} content for potential performance:
      
      Content: "${content}"
      
      Provide analysis in JSON format:
      {
        "engagementScore": 0-100,
        "suggestions": ["improvement suggestion 1", "suggestion 2"],
        "issues": ["potential issue 1", "issue 2"]
      }
    `

    try {
      const response = await this.generateWithOpenAI(prompt)
      return JSON.parse(response)
    } catch (error) {
      console.error('Content analysis failed:', error)
      return {
        engagementScore: 50,
        suggestions: [],
        issues: []
      }
    }
  }

  private buildPrompt(request: ContentGenerationRequest): string {
    let prompt = `Generate ${request.contentType} content for ${request.platform}.`

    if (request.topic) {
      prompt += `\n\nTopic: ${request.topic}`
    }

    if (request.brandVoice) {
      prompt += `\n\nBrand Voice Guidelines:
        - Tone: ${request.brandVoice.tone}
        - Style: ${request.brandVoice.style}
        - Do: ${request.brandVoice.dosList.join(', ')}
        - Don't: ${request.brandVoice.dontsList.join(', ')}
        - Keywords to include: ${request.brandVoice.keywords.join(', ')}`
    }

    if (request.targetAudience) {
      prompt += `\n\nTarget Audience: ${request.targetAudience}`
    }

    if (request.campaignObjective) {
      prompt += `\n\nCampaign Objective: ${request.campaignObjective}`
    }

    if (request.tone) {
      prompt += `\n\nTone: ${request.tone}`
    }

    if (request.keywords && request.keywords.length > 0) {
      prompt += `\n\nKeywords to include: ${request.keywords.join(', ')}`
    }

    // Platform-specific requirements
    prompt += `\n\n${this.getPlatformRequirements(request.platform)}`

    if (request.includeHashtags) {
      prompt += `\n\nInclude relevant hashtags.`
    }

    if (request.includeCTA) {
      prompt += `\n\nInclude a compelling call-to-action.`
    }

    if (request.customPrompt) {
      prompt += `\n\nAdditional instructions: ${request.customPrompt}`
    }

    prompt += `\n\nReturn the content in a structured format with the main content and any additional elements (hashtags, etc.) clearly separated.`

    return prompt
  }

  private getPlatformRequirements(platform: SocialPlatform): string {
    const requirements = {
      [SocialPlatform.TWITTER]: 'Keep under 280 characters. Use engaging hooks. Include relevant hashtags and mentions.',
      [SocialPlatform.FACEBOOK]: 'Can be longer form. Focus on storytelling. Encourage engagement through questions.',
      [SocialPlatform.INSTAGRAM]: 'Visual-first content. Use compelling captions. Include relevant hashtags (up to 30).',
      [SocialPlatform.LINKEDIN]: 'Professional tone. Industry insights. Thought leadership content. Use professional hashtags.',
      [SocialPlatform.TIKTOK]: 'Trendy, engaging, short-form content. Use popular hashtags and sounds. Appeal to younger audience.',
      [SocialPlatform.YOUTUBE]: 'Longer descriptions. SEO-optimized. Include timestamps and chapters for videos.',
      [SocialPlatform.REDDIT]: 'Community-focused. Authentic and helpful. Follow subreddit rules.',
      [SocialPlatform.DISCORD]: 'Conversational and community-oriented. Use appropriate emojis and formatting.',
      [SocialPlatform.OTHER]: 'General social media best practices.'
    }

    return `Platform Requirements for ${platform}: ${requirements[platform] || requirements[SocialPlatform.OTHER]}`
  }

  private async generateWithOpenAI(prompt: string): Promise<string> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert social media content creator and marketing strategist. Generate engaging, platform-optimized content that drives engagement and achieves marketing objectives.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.7
    })

    return response.choices[0]?.message?.content || ''
  }

  private async generateWithAnthropic(prompt: string): Promise<string> {
    const response = await this.anthropic.messages.create({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 1000,
      messages: [
        {
          role: 'user',
          content: `You are an expert social media content creator and marketing strategist. Generate engaging, platform-optimized content that drives engagement and achieves marketing objectives.\n\n${prompt}`
        }
      ]
    })

    return response.content[0]?.type === 'text' ? response.content[0].text : ''
  }

  private parseResponse(response: string, request: ContentGenerationRequest): GeneratedContent {
    // Basic parsing - in production, you might want more sophisticated parsing
    const lines = response.split('\n').filter(line => line.trim())
    
    let content = ''
    let hashtags: string[] = []
    
    for (const line of lines) {
      if (line.startsWith('#') || line.toLowerCase().includes('hashtag')) {
        // Extract hashtags
        const hashtagMatches = line.match(/#\w+/g)
        if (hashtagMatches) {
          hashtags.push(...hashtagMatches.map(tag => tag.substring(1)))
        }
      } else if (line.trim() && !line.toLowerCase().includes('content:')) {
        content += line + '\n'
      }
    }

    content = content.trim()
    
    return {
      content,
      hashtags: hashtags.length > 0 ? hashtags : undefined,
      metadata: {
        wordCount: content.split(' ').length,
        characterCount: content.length
      }
    }
  }
}

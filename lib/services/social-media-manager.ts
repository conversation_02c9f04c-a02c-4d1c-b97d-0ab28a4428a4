import { prisma } from '../prisma'
import { PlatformFactory } from './social-media/base-platform'
import { AIContentGenerator, ContentGenerationRequest } from './ai/content-generator'
import { SocialPlatform, PostStatus, ScheduleStatus, MediaType, CampaignObjective } from '@prisma/client'

export interface PostCreationData {
  content: string
  title?: string
  description?: string
  mediaUrls?: string[]
  mediaTypes?: MediaType[]
  platforms: SocialPlatform[]
  scheduledFor?: Date
  campaignId?: string
  brandVoiceId?: string
  hashtags?: string[]
  mentions?: string[]
  isAiGenerated?: boolean
  aiPrompt?: string
}

export interface BulkPostData {
  posts: PostCreationData[]
  userId: string
}

export interface AnalyticsQuery {
  userId: string
  platforms?: SocialPlatform[]
  startDate: Date
  endDate: Date
  campaignId?: string
  postId?: string
}

export class SocialMediaManager {
  private aiGenerator: AIContentGenerator

  constructor() {
    this.aiGenerator = new AIContentGenerator()
  }

  // Content Creation and Publishing
  async createPost(userId: string, postData: PostCreationData): Promise<{
    success: boolean
    posts: any[]
    errors: string[]
  }> {
    const results = {
      success: true,
      posts: [] as any[],
      errors: [] as string[]
    }

    for (const platform of postData.platforms) {
      try {
        // Create post record in database
        const post = await prisma.post.create({
          data: {
            userId,
            content: postData.content,
            title: postData.title,
            description: postData.description,
            mediaUrls: postData.mediaUrls || [],
            mediaTypes: postData.mediaTypes || [],
            platform,
            status: postData.scheduledFor ? PostStatus.SCHEDULED : PostStatus.DRAFT,
            scheduledFor: postData.scheduledFor,
            campaignId: postData.campaignId,
            brandVoiceId: postData.brandVoiceId,
            hashtags: postData.hashtags || [],
            mentions: postData.mentions || [],
            isAiGenerated: postData.isAiGenerated || false,
            aiPrompt: postData.aiPrompt
          }
        })

        // If scheduled, create scheduled post record
        if (postData.scheduledFor) {
          await this.schedulePost(userId, post.id, platform, postData.scheduledFor)
        } else {
          // Publish immediately
          await this.publishPost(userId, post.id)
        }

        results.posts.push(post)
      } catch (error) {
        results.success = false
        results.errors.push(`Failed to create post for ${platform}: ${error}`)
      }
    }

    return results
  }

  async publishPost(userId: string, postId: string): Promise<boolean> {
    try {
      const post = await prisma.post.findFirst({
        where: { id: postId, userId },
        include: { socialAccount: true }
      })

      if (!post) {
        throw new Error('Post not found')
      }

      // Get social account for the platform
      const socialAccount = await prisma.socialAccount.findFirst({
        where: {
          userId,
          platform: post.platform,
          isActive: true
        }
      })

      if (!socialAccount || !socialAccount.accessToken) {
        throw new Error(`No active ${post.platform} account found`)
      }

      // Create platform instance
      const platform = await PlatformFactory.createPlatform(
        post.platform,
        socialAccount.accessToken,
        socialAccount.refreshToken || undefined
      )

      // Publish post
      const result = await platform.publishPost({
        content: post.content,
        mediaUrls: post.mediaUrls,
        mediaTypes: post.mediaTypes,
        hashtags: post.hashtags,
        mentions: post.mentions
      })

      // Update post with result
      await prisma.post.update({
        where: { id: postId },
        data: {
          status: result.status,
          platformPostId: result.platformPostId,
          postUrl: result.postUrl,
          publishedAt: result.success ? new Date() : undefined
        }
      })

      return result.success
    } catch (error) {
      console.error('Failed to publish post:', error)
      
      // Update post status to failed
      await prisma.post.update({
        where: { id: postId },
        data: { status: PostStatus.FAILED }
      })

      return false
    }
  }

  async schedulePost(userId: string, postId: string, platform: SocialPlatform, scheduledFor: Date): Promise<boolean> {
    try {
      const post = await prisma.post.findFirst({
        where: { id: postId, userId }
      })

      if (!post) {
        throw new Error('Post not found')
      }

      const socialAccount = await prisma.socialAccount.findFirst({
        where: {
          userId,
          platform,
          isActive: true
        }
      })

      if (!socialAccount) {
        throw new Error(`No active ${platform} account found`)
      }

      // Create scheduled post record
      await prisma.scheduledPost.create({
        data: {
          userId,
          socialAccountId: socialAccount.id,
          content: post.content,
          mediaUrls: post.mediaUrls,
          mediaTypes: post.mediaTypes,
          scheduledFor,
          platform,
          status: ScheduleStatus.PENDING
        }
      })

      return true
    } catch (error) {
      console.error('Failed to schedule post:', error)
      return false
    }
  }

  // AI Content Generation
  async generateContent(userId: string, request: ContentGenerationRequest): Promise<any> {
    try {
      // Get brand voice if specified
      let brandVoice
      if (request.brandVoice) {
        brandVoice = await prisma.brandVoice.findFirst({
          where: {
            userId,
            name: request.brandVoice.name
          }
        })
      }

      // Generate content
      const generatedContent = await this.aiGenerator.generateContent({
        ...request,
        brandVoice: brandVoice ? {
          name: brandVoice.name,
          tone: brandVoice.tone,
          style: brandVoice.style,
          personality: brandVoice.personality as Record<string, any>,
          dosList: brandVoice.dosList,
          dontsList: brandVoice.dontsList,
          keywords: brandVoice.keywords,
          sampleContent: brandVoice.sampleContent
        } : undefined
      })

      return {
        success: true,
        content: generatedContent
      }
    } catch (error) {
      console.error('Content generation failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  async generateMultiplePlatformContent(userId: string, baseRequest: ContentGenerationRequest, platforms: SocialPlatform[]): Promise<{
    [key in SocialPlatform]?: any
  }> {
    const results: { [key in SocialPlatform]?: any } = {}

    for (const platform of platforms) {
      try {
        const content = await this.generateContent(userId, {
          ...baseRequest,
          platform
        })
        results[platform] = content
      } catch (error) {
        console.error(`Failed to generate content for ${platform}:`, error)
        results[platform] = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    return results
  }

  // Analytics and Reporting
  async getAnalytics(query: AnalyticsQuery): Promise<any> {
    try {
      const analytics = await prisma.analytics.findMany({
        where: {
          userId: query.userId,
          date: {
            gte: query.startDate,
            lte: query.endDate
          },
          ...(query.campaignId && { campaignId: query.campaignId }),
          ...(query.postId && { postId: query.postId }),
          ...(query.platforms && {
            socialAccount: {
              platform: { in: query.platforms }
            }
          })
        },
        include: {
          socialAccount: true,
          post: true,
          campaign: true
        },
        orderBy: { date: 'desc' }
      })

      // Aggregate data
      const aggregated = {
        totalImpressions: 0,
        totalReach: 0,
        totalEngagement: 0,
        totalClicks: 0,
        totalShares: 0,
        totalComments: 0,
        totalLikes: 0,
        averageEngagementRate: 0,
        averageClickThroughRate: 0,
        platformBreakdown: {} as Record<string, any>,
        dailyMetrics: [] as any[]
      }

      analytics.forEach(record => {
        aggregated.totalImpressions += record.impressions
        aggregated.totalReach += record.reach
        aggregated.totalEngagement += record.engagement
        aggregated.totalClicks += record.clicks
        aggregated.totalShares += record.shares
        aggregated.totalComments += record.comments
        aggregated.totalLikes += record.likes

        // Platform breakdown
        const platform = record.socialAccount?.platform
        if (platform) {
          if (!aggregated.platformBreakdown[platform]) {
            aggregated.platformBreakdown[platform] = {
              impressions: 0,
              reach: 0,
              engagement: 0,
              clicks: 0
            }
          }
          aggregated.platformBreakdown[platform].impressions += record.impressions
          aggregated.platformBreakdown[platform].reach += record.reach
          aggregated.platformBreakdown[platform].engagement += record.engagement
          aggregated.platformBreakdown[platform].clicks += record.clicks
        }
      })

      // Calculate averages
      if (analytics.length > 0) {
        const totalEngagementRate = analytics.reduce((sum, record) => sum + record.engagementRate, 0)
        const totalClickThroughRate = analytics.reduce((sum, record) => sum + record.clickThroughRate, 0)
        
        aggregated.averageEngagementRate = totalEngagementRate / analytics.length
        aggregated.averageClickThroughRate = totalClickThroughRate / analytics.length
      }

      return {
        success: true,
        data: aggregated,
        rawData: analytics
      }
    } catch (error) {
      console.error('Failed to get analytics:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Account Management
  async connectSocialAccount(userId: string, platform: SocialPlatform, accessToken: string, refreshToken?: string): Promise<boolean> {
    try {
      // Validate token and get profile
      const platformInstance = await PlatformFactory.createPlatform(platform, accessToken, refreshToken)
      const isValid = await platformInstance.validateToken()
      
      if (!isValid) {
        throw new Error('Invalid access token')
      }

      const profile = await platformInstance.getUserProfile()
      const metrics = await platformInstance.getMetrics()

      // Create or update social account
      await prisma.socialAccount.upsert({
        where: {
          userId_platform_platformUserId: {
            userId,
            platform,
            platformUserId: profile.id
          }
        },
        update: {
          accessToken,
          refreshToken,
          username: profile.username || profile.name,
          displayName: profile.displayName || profile.name,
          profileUrl: profile.profileUrl,
          profileImage: profile.profileImage || profile.picture,
          isActive: true,
          followersCount: metrics.followers,
          followingCount: metrics.following,
          postsCount: metrics.posts,
          lastSyncAt: new Date()
        },
        create: {
          userId,
          platform,
          platformUserId: profile.id,
          accessToken,
          refreshToken,
          username: profile.username || profile.name,
          displayName: profile.displayName || profile.name,
          profileUrl: profile.profileUrl,
          profileImage: profile.profileImage || profile.picture,
          followersCount: metrics.followers,
          followingCount: metrics.following,
          postsCount: metrics.posts,
          lastSyncAt: new Date()
        }
      })

      return true
    } catch (error) {
      console.error('Failed to connect social account:', error)
      return false
    }
  }

  async syncAccountMetrics(userId: string, platform?: SocialPlatform): Promise<void> {
    try {
      const accounts = await prisma.socialAccount.findMany({
        where: {
          userId,
          isActive: true,
          ...(platform && { platform })
        }
      })

      for (const account of accounts) {
        try {
          if (!account.accessToken) continue

          const platformInstance = await PlatformFactory.createPlatform(
            account.platform,
            account.accessToken,
            account.refreshToken || undefined
          )

          const metrics = await platformInstance.getMetrics()

          await prisma.socialAccount.update({
            where: { id: account.id },
            data: {
              followersCount: metrics.followers,
              followingCount: metrics.following,
              postsCount: metrics.posts,
              lastSyncAt: new Date()
            }
          })
        } catch (error) {
          console.error(`Failed to sync metrics for ${account.platform}:`, error)
        }
      }
    } catch (error) {
      console.error('Failed to sync account metrics:', error)
    }
  }
}

import { SocialPlatform, MediaType, PostStatus } from '@prisma/client'

export interface PostData {
  content: string
  mediaUrls?: string[]
  mediaTypes?: MediaType[]
  scheduledFor?: Date
  hashtags?: string[]
  mentions?: string[]
}

export interface PostResult {
  success: boolean
  platformPostId?: string
  postUrl?: string
  error?: string
  status: PostStatus
}

export interface PlatformMetrics {
  followers: number
  following: number
  posts: number
  engagement?: number
  reach?: number
}

export interface EngagementData {
  likes: number
  comments: number
  shares: number
  views: number
  clicks: number
  saves?: number
}

export abstract class BasePlatform {
  protected accessToken: string
  protected refreshToken?: string
  protected platform: SocialPlatform

  constructor(accessToken: string, refreshToken?: string) {
    this.accessToken = accessToken
    this.refreshToken = refreshToken
  }

  abstract validateToken(): Promise<boolean>
  abstract refreshAccessToken(): Promise<string>
  abstract getUserProfile(): Promise<any>
  abstract getMetrics(): Promise<PlatformMetrics>
  abstract publishPost(postData: PostData): Promise<PostResult>
  abstract schedulePost(postData: PostData): Promise<PostResult>
  abstract deletePost(platformPostId: string): Promise<boolean>
  abstract getPostEngagement(platformPostId: string): Promise<EngagementData>
  abstract getPostAnalytics(platformPostId: string, startDate: Date, endDate: Date): Promise<any>

  // Common utility methods
  protected formatHashtags(hashtags: string[]): string {
    return hashtags.map(tag => tag.startsWith('#') ? tag : `#${tag}`).join(' ')
  }

  protected formatMentions(mentions: string[]): string {
    return mentions.map(mention => mention.startsWith('@') ? mention : `@${mention}`).join(' ')
  }

  protected validateMediaTypes(mediaUrls: string[], mediaTypes: MediaType[]): boolean {
    return mediaUrls.length === mediaTypes.length
  }

  protected async uploadMedia(mediaUrl: string, mediaType: MediaType): Promise<string> {
    // Base implementation - should be overridden by specific platforms
    throw new Error('uploadMedia must be implemented by platform-specific class')
  }

  protected handleApiError(error: any): PostResult {
    console.error(`${this.platform} API Error:`, error)
    return {
      success: false,
      error: error.message || 'Unknown API error',
      status: PostStatus.FAILED
    }
  }
}

export interface PlatformConfig {
  clientId: string
  clientSecret: string
  redirectUri: string
  scopes: string[]
}

export class PlatformFactory {
  static async createPlatform(
    platform: SocialPlatform,
    accessToken: string,
    refreshToken?: string,
    config?: PlatformConfig
  ): Promise<BasePlatform> {
    switch (platform) {
      case SocialPlatform.FACEBOOK:
        const { FacebookPlatform } = await import('./facebook-platform')
        return new FacebookPlatform(accessToken, refreshToken, config)
      
      case SocialPlatform.INSTAGRAM:
        const { InstagramPlatform } = await import('./instagram-platform')
        return new InstagramPlatform(accessToken, refreshToken, config)
      
      case SocialPlatform.TWITTER:
        const { TwitterPlatform } = await import('./twitter-platform')
        return new TwitterPlatform(accessToken, refreshToken, config)
      
      case SocialPlatform.LINKEDIN:
        const { LinkedInPlatform } = await import('./linkedin-platform')
        return new LinkedInPlatform(accessToken, refreshToken, config)
      
      case SocialPlatform.TIKTOK:
        const { TikTokPlatform } = await import('./tiktok-platform')
        return new TikTokPlatform(accessToken, refreshToken, config)
      
      case SocialPlatform.YOUTUBE:
        const { YouTubePlatform } = await import('./youtube-platform')
        return new YouTubePlatform(accessToken, refreshToken, config)
      
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }
  }
}

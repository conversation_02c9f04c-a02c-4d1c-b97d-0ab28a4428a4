import { randomBytes } from 'crypto'
import { prisma } from './prisma'

/**
 * Generate a verification token for email verification
 */
export async function generateVerificationToken(email: string) {
  const token = randomBytes(32).toString('hex')
  const expires = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

  await prisma.verificationToken.create({
    data: {
      identifier: email,
      token,
      expires,
    },
  })

  return token
}

/**
 * Generate a token for password reset
 */
export async function generatePasswordResetToken(email: string) {
  const token = randomBytes(32).toString('hex')
  const expires = new Date(Date.now() + 60 * 60 * 1000) // 1 hour

  await prisma.verificationToken.create({
    data: {
      identifier: email,
      token,
      expires,
    },
  })

  return token
}

/**
 * Generate a random token string
 */
export function generateToken(length: number = 32) {
  return randomBytes(length).toString('hex')
}

/**
 * Generate a temporary auth token for API requests
 */
export async function generateApiToken(userId: string, expiresIn: number = 3600) {
  const token = generateToken()
  const expires = new Date(Date.now() + expiresIn * 1000)

  await prisma.verificationToken.create({
    data: {
      identifier: userId,
      token,
      expires,
    },
  })

  return token
}

// TypeScript interfaces for Soimagine Marketing Platform

export interface User {
  id: string
  name?: string
  email: string
  emailVerified?: Date
  image?: string
  role: UserRole
  firstName?: string
  lastName?: string
  company?: string
  phone?: string
  timezone?: string
  isActive: boolean
  isVerified: boolean
  lastLoginAt?: Date
  createdAt: Date
  updatedAt: Date
}

export type UserRole = 'USER' | 'ADMIN' | 'MODERATOR'

// Credit Management Types
export interface CreditBalance {
  id: string
  userId: string
  totalCredits: number
  dailyCredits: number
  monthlyCredits: number
  dailyLimit: number
  monthlyLimit: number
  lastDailyReset: Date
  lastMonthlyReset: Date
  createdAt: Date
  updatedAt: Date
}

export interface CreditTransaction {
  id: string
  userId: string
  type: CreditTransactionType
  amount: number
  description: string
  source?: string
  sourceId?: string
  metadata?: any
  balanceAfter: number
  createdAt: Date
}

export type CreditTransactionType = 
  | 'EARNED' 
  | 'SPENT' 
  | 'BONUS' 
  | 'PENALTY' 
  | 'REFUND' 
  | 'ADMIN_ADJUSTMENT'

// Gamification Types
export interface Challenge {
  id: string
  title: string
  description: string
  instructions?: string
  type: ChallengeType
  category: ChallengeCategory
  difficulty: ChallengeDifficulty
  creditReward: number
  xpReward: number
  requirements?: any
  timeLimit?: number
  isActive: boolean
  isPublic: boolean
  startDate?: Date
  endDate?: Date
  generatedBy?: string
  learningData?: any
  completionRate: number
  averageRating: number
  totalAttempts: number
  createdAt: Date
  updatedAt: Date
  userCompletion?: ChallengeCompletion
}

export type ChallengeType = 
  | 'SOCIAL_SHARE' 
  | 'CONTENT_CREATION' 
  | 'REFERRAL' 
  | 'ENGAGEMENT' 
  | 'LEARNING' 
  | 'COMMUNITY' 
  | 'CUSTOM'

export type ChallengeCategory = 
  | 'MARKETING' 
  | 'SOCIAL_MEDIA' 
  | 'CONTENT' 
  | 'REFERRALS' 
  | 'COMMUNITY' 
  | 'LEARNING' 
  | 'SPECIAL_EVENT'

export type ChallengeDifficulty = 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT'

export interface ChallengeCompletion {
  id: string
  userId: string
  challengeId: string
  status: CompletionStatus
  score?: number
  timeSpent?: number
  submissionData?: any
  feedback?: string
  isVerified: boolean
  verifiedBy?: string
  verifiedAt?: Date
  creditsAwarded: number
  xpAwarded: number
  startedAt: Date
  completedAt?: Date
}

export type CompletionStatus = 
  | 'STARTED' 
  | 'IN_PROGRESS' 
  | 'COMPLETED' 
  | 'FAILED' 
  | 'ABANDONED'

export interface UserProgress {
  id: string
  userId: string
  totalXp: number
  currentLevel: number
  challengesCompleted: number
  challengesAttempted: number
  marketingXp: number
  socialXp: number
  contentXp: number
  referralXp: number
  currentStreak: number
  longestStreak: number
  lastActivityDate?: Date
  createdAt: Date
  updatedAt: Date
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon?: string
  type: AchievementType
  category: AchievementCategory
  rarity: AchievementRarity
  requirements: any
  creditReward: number
  xpReward: number
  isActive: boolean
  isSecret: boolean
  createdAt: Date
  updatedAt: Date
}

export type AchievementType = 
  | 'MILESTONE' 
  | 'STREAK' 
  | 'SOCIAL' 
  | 'REFERRAL' 
  | 'ENGAGEMENT' 
  | 'LEARNING' 
  | 'SPECIAL'

export type AchievementCategory = 
  | 'PROGRESS' 
  | 'SOCIAL' 
  | 'CONTENT' 
  | 'REFERRALS' 
  | 'COMMUNITY' 
  | 'LEARNING' 
  | 'SPECIAL'

export type AchievementRarity = 'COMMON' | 'UNCOMMON' | 'RARE' | 'EPIC' | 'LEGENDARY'

export interface UserAchievement {
  id: string
  userId: string
  achievementId: string
  progress: number
  isCompleted: boolean
  creditsAwarded: number
  xpAwarded: number
  unlockedAt?: Date
  createdAt: Date
  updatedAt: Date
  achievement: Achievement
}

// Content Marketing Types
export interface SocialShare {
  id: string
  userId: string
  platform: SocialPlatform
  contentType: ContentType
  contentUrl?: string
  referralLink: string
  title?: string
  description?: string
  imageUrl?: string
  hashtags?: string
  clicks: number
  conversions: number
  engagement?: any
  isVerified: boolean
  verificationData?: any
  creditsEarned: number
  xpEarned: number
  sharedAt: Date
  lastTrackedAt?: Date
}

export type SocialPlatform = 
  | 'TWITTER' 
  | 'FACEBOOK' 
  | 'INSTAGRAM' 
  | 'LINKEDIN' 
  | 'TIKTOK' 
  | 'YOUTUBE' 
  | 'REDDIT' 
  | 'DISCORD' 
  | 'OTHER'

export type ContentType = 
  | 'POST' 
  | 'STORY' 
  | 'VIDEO' 
  | 'IMAGE' 
  | 'ARTICLE' 
  | 'LINK' 
  | 'POLL' 
  | 'LIVE_STREAM' 
  | 'OTHER'

export interface Referral {
  id: string
  referrerId: string
  referralCode: string
  email?: string
  name?: string
  status: ReferralStatus
  convertedUserId?: string
  creditsAwarded: number
  xpAwarded: number
  clickCount: number
  lastClickAt?: Date
  convertedAt?: Date
  createdAt: Date
  updatedAt: Date
}

export type ReferralStatus = 'PENDING' | 'CLICKED' | 'CONVERTED' | 'EXPIRED' | 'CANCELLED'

export interface ApiUsage {
  id: string
  userId: string
  endpoint: string
  method: string
  assistantType?: string
  requestSize?: number
  responseSize?: number
  processingTime?: number
  creditsUsed: number
  status: ApiUsageStatus
  errorMessage?: string
  userAgent?: string
  ipAddress?: string
  metadata?: any
  createdAt: Date
}

export type ApiUsageStatus = 
  | 'SUCCESS' 
  | 'ERROR' 
  | 'TIMEOUT' 
  | 'RATE_LIMITED' 
  | 'UNAUTHORIZED'

// API Response Types
export interface ApiResponse<T = any> {
  success?: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T = any> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface LeaderboardEntry {
  rank: number
  user: {
    id: string
    name: string
    image?: string
    company?: string
  }
  stats: {
    totalXp: number
    currentLevel: number
    challengesCompleted: number
    currentStreak: number
    longestStreak: number
  }
  primaryValue: number
}

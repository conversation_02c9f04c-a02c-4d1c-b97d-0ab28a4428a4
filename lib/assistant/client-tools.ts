// Client-side tools configuration
import { ToolDefinition } from './types/tools';
import { createToolDefinition } from './utils/tool-utils';

// Navigation Tools
export const navigationTools: ToolDefinition[] = [
  createToolDefinition(
    'navigateTo',
    'Navigate to a specific page or route in the application',
    'navigation',
    {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'The path to navigate to (e.g., /dashboard, /settings)',
        },
        newTab: {
          type: 'boolean',
          description: 'Whether to open in a new tab',
          default: false,
        },
      },
      required: ['path'],
    },
    { executionMode: 'client' }
  ),
];

// Document Tools
export const documentTools: ToolDefinition[] = [
  createToolDefinition(
    'createDocument',
    'Create a new document',
    'document',
    {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'The title of the document',
        },
        content: {
          type: 'string',
          description: 'The content of the document',
        },
        tags: {
          type: 'array',
          description: 'Tags to associate with the document',
        },
        folderId: {
          type: 'string',
          description: 'ID of the folder to create the document in',
        },
      },
      required: ['title', 'content'],
    },
    { requiresConfirmation: true }
  ),
  createToolDefinition(
    'updateDocument',
    'Update an existing document',
    'document',
    {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'The ID of the document to update',
        },
        title: {
          type: 'string',
          description: 'The new title of the document',
        },
        content: {
          type: 'string',
          description: 'The new content of the document',
        },
        tags: {
          type: 'array',
          description: 'New tags for the document',
        },
      },
      required: ['id'],
    },
    { requiresConfirmation: true }
  ),
  createToolDefinition(
    'deleteDocument',
    'Delete a document',
    'document',
    {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'The ID of the document to delete',
        },
      },
      required: ['id'],
    },
    { requiresConfirmation: true, riskLevel: 'high' }
  ),
];

// Meeting Tools
export const meetingTools: ToolDefinition[] = [
  createToolDefinition(
    'createMeeting',
    'Create a new meeting',
    'meeting',
    {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'The title of the meeting',
        },
        description: {
          type: 'string',
          description: 'Description of the meeting',
        },
        startTime: {
          type: 'string',
          description: 'Start time in ISO format',
        },
        endTime: {
          type: 'string',
          description: 'End time in ISO format',
        },
        attendees: {
          type: 'array',
          description: 'List of attendee email addresses',
        },
      },
      required: ['title', 'startTime', 'endTime'],
    },
    { requiresConfirmation: true }
  ),
];

// Task Tools
export const taskTools: ToolDefinition[] = [
  createToolDefinition(
    'createTask',
    'Create a new task',
    'task',
    {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'The title of the task',
        },
        description: {
          type: 'string',
          description: 'Description of the task',
        },
        priority: {
          type: 'string',
          description: 'Priority level of the task',
          enum: ['low', 'medium', 'high'],
          default: 'medium',
        },
        dueDate: {
          type: 'string',
          description: 'Due date in ISO format',
        },
        assigneeId: {
          type: 'string',
          description: 'ID of the user to assign the task to',
        },
      },
      required: ['title'],
    }
  ),
];

// Web Research Tools
export const webResearchTools: ToolDefinition[] = [
  createToolDefinition(
    'fetchWebPage',
    'Fetch content from a web page',
    'webResearch',
    {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'The URL to fetch content from',
        },
      },
      required: ['url'],
    },
    { executionMode: 'client' }
  ),
  createToolDefinition(
    'searchWeb',
    'Search the web for information',
    'webResearch',
    {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'The search query',
        },
        maxResults: {
          type: 'number',
          description: 'Maximum number of results to return',
          default: 5,
          minimum: 1,
          maximum: 20,
        },
      },
      required: ['query'],
    }
  ),
];

// User Tools
export const userTools: ToolDefinition[] = [
  createToolDefinition(
    'updateUserProfile',
    'Update user profile information',
    'user',
    {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: 'User\'s display name',
        },
        email: {
          type: 'string',
          description: 'User\'s email address',
        },
        preferences: {
          type: 'object',
          description: 'User preferences object',
        },
      },
      required: [],
    },
    { requiresConfirmation: true, riskLevel: 'medium' }
  ),
];

// System Tools
export const systemTools: ToolDefinition[] = [
  createToolDefinition(
    'showNotification',
    'Show a notification to the user',
    'system',
    {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Notification title',
        },
        message: {
          type: 'string',
          description: 'Notification message',
        },
        type: {
          type: 'string',
          description: 'Notification type',
          enum: ['info', 'success', 'warning', 'error'],
          default: 'info',
        },
        duration: {
          type: 'number',
          description: 'Duration in milliseconds (0 for persistent)',
          default: 5000,
        },
      },
      required: ['title', 'message'],
    },
    { executionMode: 'client' }
  ),
];

// Combine all tools
export const tools: ToolDefinition[] = [
  ...navigationTools,
  ...documentTools,
  ...meetingTools,
  ...taskTools,
  ...webResearchTools,
  ...userTools,
  ...systemTools,
];

// Export tools by category
export const toolsByCategory = {
  navigation: navigationTools,
  document: documentTools,
  meeting: meetingTools,
  task: taskTools,
  webResearch: webResearchTools,
  user: userTools,
  system: systemTools,
};

// Export tool names for easy reference
export const toolNames = tools.map(tool => tool.name);

// Export high-risk tools
export const highRiskTools = tools
  .filter(tool => tool.riskLevel === 'high')
  .map(tool => tool.name);

// Export tools requiring confirmation
export const toolsRequiringConfirmation = tools
  .filter(tool => tool.requiresConfirmation)
  .map(tool => tool.name);

// Export client-side tools
export const clientSideTools = tools
  .filter(tool => tool.executionMode === 'client' || tool.executionMode === 'hybrid')
  .map(tool => tool.name);

// Default export
export default tools;

// Chat-specific utility functions
import { Message } from '@ai-sdk/react';
import { ChatSession, ChatMetrics } from '../types/chat';

export function createChatSession(assistantType: string): ChatSession {
  return {
    id: `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    title: `New Chat - ${new Date().toLocaleDateString()}`,
    messages: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    assistantType: assistantType as any,
  };
}

export function updateChatSession(session: ChatSession, updates: Partial<ChatSession>): ChatSession {
  return {
    ...session,
    ...updates,
    updatedAt: new Date(),
  };
}

export function addMessageToSession(session: ChatSession, message: Message): ChatSession {
  return {
    ...session,
    messages: [...session.messages, message],
    updatedAt: new Date(),
  };
}

export function generateChatTitle(messages: Message[]): string {
  if (messages.length === 0) return 'New Chat';
  
  const firstUserMessage = messages.find(m => m.role === 'user');
  if (!firstUserMessage) return 'New Chat';
  
  const content = firstUserMessage.content;
  if (typeof content === 'string') {
    // Truncate and clean up the content for title
    const title = content
      .replace(/[^\w\s]/g, '') // Remove special characters
      .trim()
      .substring(0, 50); // Limit length
    
    return title || 'New Chat';
  }
  
  return 'New Chat';
}

export function calculateChatMetrics(sessions: ChatSession[]): ChatMetrics {
  const totalMessages = sessions.reduce((sum, session) => sum + session.messages.length, 0);
  const totalSessions = sessions.length;
  
  // Calculate average response time (mock implementation)
  const averageResponseTime = 1500; // ms
  
  // Calculate user satisfaction (mock implementation)
  const userSatisfaction = 4.2; // out of 5
  
  // Calculate tool usage count
  const toolUsageCount: Record<string, number> = {};
  sessions.forEach(session => {
    session.messages.forEach(message => {
      if (message.parts) {
        message.parts.forEach(part => {
          if (part.type === 'tool-invocation' && 'toolInvocation' in part) {
            const toolName = part.toolInvocation.toolName;
            toolUsageCount[toolName] = (toolUsageCount[toolName] || 0) + 1;
          }
        });
      }
    });
  });
  
  return {
    totalMessages,
    totalSessions,
    averageResponseTime,
    userSatisfaction,
    toolUsageCount,
  };
}

export function filterMessagesByRole(messages: Message[], role: 'user' | 'assistant' | 'system'): Message[] {
  return messages.filter(message => message.role === role);
}

export function getLastUserMessage(messages: Message[]): Message | null {
  const userMessages = filterMessagesByRole(messages, 'user');
  return userMessages.length > 0 ? userMessages[userMessages.length - 1] : null;
}

export function getLastAssistantMessage(messages: Message[]): Message | null {
  const assistantMessages = filterMessagesByRole(messages, 'assistant');
  return assistantMessages.length > 0 ? assistantMessages[assistantMessages.length - 1] : null;
}

export function hasToolCalls(message: Message): boolean {
  if (!message.parts) return false;
  
  return message.parts.some(part => 
    part.type === 'tool-invocation' && 'toolInvocation' in part
  );
}

export function extractToolCalls(message: Message): Array<{ toolName: string; args: any }> {
  if (!message.parts) return [];
  
  return message.parts
    .filter(part => part.type === 'tool-invocation' && 'toolInvocation' in part)
    .map(part => {
      if ('toolInvocation' in part) {
        return {
          toolName: part.toolInvocation.toolName,
          args: part.toolInvocation.args,
        };
      }
      return null;
    })
    .filter(Boolean) as Array<{ toolName: string; args: any }>;
}

export function isMessageComplete(message: Message): boolean {
  if (!message.parts) return true;
  
  return message.parts.every(part => {
    if (part.type === 'tool-invocation' && 'toolInvocation' in part) {
      return part.toolInvocation.state === 'result';
    }
    return true;
  });
}

export function getMessageWordCount(message: Message): number {
  const content = message.content;
  if (typeof content === 'string') {
    return content.trim().split(/\s+/).length;
  }
  return 0;
}

export function getMessageCharacterCount(message: Message): number {
  const content = message.content;
  if (typeof content === 'string') {
    return content.length;
  }
  return 0;
}

export function searchMessages(messages: Message[], query: string): Message[] {
  const lowercaseQuery = query.toLowerCase();
  
  return messages.filter(message => {
    const content = message.content;
    if (typeof content === 'string') {
      return content.toLowerCase().includes(lowercaseQuery);
    }
    return false;
  });
}

export function exportChatSession(session: ChatSession): string {
  const exportData = {
    title: session.title,
    createdAt: session.createdAt,
    assistantType: session.assistantType,
    messages: session.messages.map(message => ({
      role: message.role,
      content: message.content,
      timestamp: message.createdAt || new Date(),
    })),
  };
  
  return JSON.stringify(exportData, null, 2);
}

export function importChatSession(data: string): ChatSession | null {
  try {
    const parsed = JSON.parse(data);
    
    return {
      id: `imported-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      title: parsed.title || 'Imported Chat',
      messages: parsed.messages || [],
      createdAt: new Date(parsed.createdAt) || new Date(),
      updatedAt: new Date(),
      assistantType: parsed.assistantType || 'consultant',
    };
  } catch (error) {
    console.error('Failed to import chat session:', error);
    return null;
  }
}

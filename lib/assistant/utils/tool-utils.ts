// Tool-specific utility functions
import { ToolDefinition, ToolResult, ToolExecution, ToolCategory } from '../types/tools';

export function createToolDefinition(
  name: string,
  description: string,
  category: ToolCategory,
  parameters: any,
  options: {
    requiresConfirmation?: boolean;
    riskLevel?: 'low' | 'medium' | 'high';
    executionMode?: 'client' | 'server' | 'hybrid';
  } = {}
): ToolDefinition {
  return {
    name,
    description,
    category,
    parameters,
    requiresConfirmation: options.requiresConfirmation ?? false,
    riskLevel: options.riskLevel ?? 'low',
    executionMode: options.executionMode ?? 'server',
  };
}

export function validateToolParameters(
  parameters: Record<string, any>,
  definition: ToolDefinition
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const { properties, required } = definition.parameters;
  
  // Check required parameters
  for (const requiredParam of required) {
    if (!(requiredParam in parameters)) {
      errors.push(`Missing required parameter: ${requiredParam}`);
    }
  }
  
  // Validate parameter types and constraints
  for (const [paramName, paramValue] of Object.entries(parameters)) {
    const paramDef = properties[paramName];
    if (!paramDef) {
      errors.push(`Unknown parameter: ${paramName}`);
      continue;
    }
    
    // Type validation
    const expectedType = paramDef.type;
    const actualType = Array.isArray(paramValue) ? 'array' : typeof paramValue;
    
    if (expectedType !== actualType) {
      errors.push(`Parameter ${paramName} should be of type ${expectedType}, got ${actualType}`);
      continue;
    }
    
    // Enum validation
    if (paramDef.enum && !paramDef.enum.includes(paramValue)) {
      errors.push(`Parameter ${paramName} must be one of: ${paramDef.enum.join(', ')}`);
    }
    
    // Number constraints
    if (expectedType === 'number') {
      if (paramDef.minimum !== undefined && paramValue < paramDef.minimum) {
        errors.push(`Parameter ${paramName} must be at least ${paramDef.minimum}`);
      }
      if (paramDef.maximum !== undefined && paramValue > paramDef.maximum) {
        errors.push(`Parameter ${paramName} must be at most ${paramDef.maximum}`);
      }
    }
    
    // String pattern validation
    if (expectedType === 'string' && paramDef.pattern) {
      const regex = new RegExp(paramDef.pattern);
      if (!regex.test(paramValue)) {
        errors.push(`Parameter ${paramName} does not match required pattern`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function createToolExecution(
  toolName: string,
  parameters: Record<string, any>,
  userId: string,
  sessionId: string
): ToolExecution {
  return {
    id: `exec-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    toolName,
    parameters,
    status: 'pending',
    startTime: new Date(),
    userId,
    sessionId,
  };
}

export function updateToolExecution(
  execution: ToolExecution,
  updates: Partial<ToolExecution>
): ToolExecution {
  return {
    ...execution,
    ...updates,
    endTime: updates.status === 'completed' || updates.status === 'failed' 
      ? new Date() 
      : execution.endTime,
  };
}

export function createSuccessResult(data?: any, message?: string): ToolResult {
  return {
    success: true,
    data,
    message,
  };
}

export function createErrorResult(error: string, data?: any): ToolResult {
  return {
    success: false,
    error,
    data,
  };
}

export function getToolsByCategory(tools: ToolDefinition[]): Record<ToolCategory, ToolDefinition[]> {
  const categorized: Record<string, ToolDefinition[]> = {};
  
  for (const tool of tools) {
    if (!categorized[tool.category]) {
      categorized[tool.category] = [];
    }
    categorized[tool.category].push(tool);
  }
  
  return categorized as Record<ToolCategory, ToolDefinition[]>;
}

export function getHighRiskTools(tools: ToolDefinition[]): ToolDefinition[] {
  return tools.filter(tool => tool.riskLevel === 'high');
}

export function getToolsRequiringConfirmation(tools: ToolDefinition[]): ToolDefinition[] {
  return tools.filter(tool => tool.requiresConfirmation);
}

export function getClientSideTools(tools: ToolDefinition[]): ToolDefinition[] {
  return tools.filter(tool => tool.executionMode === 'client' || tool.executionMode === 'hybrid');
}

export function getServerSideTools(tools: ToolDefinition[]): ToolDefinition[] {
  return tools.filter(tool => tool.executionMode === 'server' || tool.executionMode === 'hybrid');
}

export function calculateToolExecutionTime(execution: ToolExecution): number {
  if (!execution.endTime) return 0;
  return execution.endTime.getTime() - execution.startTime.getTime();
}

export function getToolExecutionStatus(execution: ToolExecution): string {
  switch (execution.status) {
    case 'pending':
      return 'Waiting to execute...';
    case 'running':
      return 'Executing...';
    case 'completed':
      return 'Completed successfully';
    case 'failed':
      return `Failed: ${execution.error || 'Unknown error'}`;
    case 'cancelled':
      return 'Cancelled by user';
    default:
      return 'Unknown status';
  }
}

export function formatToolResult(result: ToolResult): string {
  if (result.success) {
    return result.message || 'Tool executed successfully';
  } else {
    return result.error || 'Tool execution failed';
  }
}

export function isToolExecutionComplete(execution: ToolExecution): boolean {
  return ['completed', 'failed', 'cancelled'].includes(execution.status);
}

export function canRetryToolExecution(execution: ToolExecution): boolean {
  return execution.status === 'failed' && !execution.error?.includes('permission');
}

export function getToolDisplayName(toolName: string): string {
  // Convert camelCase to Title Case
  return toolName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
}

export function getToolCategoryDisplayName(category: ToolCategory): string {
  const categoryNames: Record<ToolCategory, string> = {
    navigation: 'Navigation',
    document: 'Documents',
    meeting: 'Meetings',
    task: 'Tasks',
    user: 'User Management',
    feedback: 'Feedback',
    plugin: 'Plugins',
    transcript: 'Transcripts',
    ai: 'AI Tools',
    folder: 'Folders',
    tag: 'Tags',
    version: 'Version Control',
    webResearch: 'Web Research',
    system: 'System',
  };
  
  return categoryNames[category] || category;
}

export function sortToolsByUsage(tools: ToolDefinition[], usageStats: Record<string, number>): ToolDefinition[] {
  return [...tools].sort((a, b) => {
    const usageA = usageStats[a.name] || 0;
    const usageB = usageStats[b.name] || 0;
    return usageB - usageA;
  });
}

export function filterToolsByRiskLevel(tools: ToolDefinition[], maxRiskLevel: 'low' | 'medium' | 'high'): ToolDefinition[] {
  const riskLevels = { low: 1, medium: 2, high: 3 };
  const maxLevel = riskLevels[maxRiskLevel];
  
  return tools.filter(tool => riskLevels[tool.riskLevel] <= maxLevel);
}

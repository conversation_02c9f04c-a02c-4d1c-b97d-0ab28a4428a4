// Assistant utility functions
import { Tool, ToolCategory } from '../types';

// Constants
export const APPROVAL = {
  APPROVED: 'approved',
  REJECTED: 'rejected',
  PENDING: 'pending',
} as const;

// User utility functions
export function getInitials(name: string): string {
  if (!name) return 'U';
  
  const words = name.trim().split(' ');
  if (words.length === 1) {
    return words[0].charAt(0).toUpperCase();
  }
  
  return words
    .slice(0, 2)
    .map(word => word.charAt(0).toUpperCase())
    .join('');
}

// Tool utility functions
export function getToolsRequiringConfirmation(tools: Tool[]): string[] {
  return tools
    .filter(tool => tool.requiresConfirmation)
    .map(tool => tool.name);
}

export function categorizeTools(tools: Tool[]): Record<string, Tool[]> {
  return tools.reduce((acc, tool) => {
    const category = tool.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(tool);
    return acc;
  }, {} as Record<string, Tool[]>);
}

export function getHighRiskTools(): string[] {
  return [
    'deleteDocument',
    'deleteMeeting',
    'deleteTask',
    'updateUserProfile',
    'deleteFolder',
    'removeDocumentTag',
    'clearAllData',
    'resetConfiguration',
    'deleteAccount',
  ];
}

export function isHighRiskTool(toolName: string): boolean {
  return getHighRiskTools().includes(toolName);
}

// Configuration utility functions
export function validateConfig(config: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config.name || config.name.trim().length === 0) {
    errors.push('Assistant name is required');
  }
  
  if (!config.greeting || config.greeting.trim().length === 0) {
    errors.push('Greeting message is required');
  }
  
  if (!config.model || config.model.trim().length === 0) {
    errors.push('Model is required');
  }
  
  if (config.maxSteps && (config.maxSteps < 1 || config.maxSteps > 20)) {
    errors.push('Max steps must be between 1 and 20');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

export function getDefaultConfig() {
  return {
    name: 'AI Assistant',
    greeting: 'Hello! I\'m your AI assistant. How can I help you today?',
    model: 'gpt-4o',
    maxSteps: 10,
    enableMemory: true,
    enableHumanInTheLoop: true,
    enableWebResearch: true,
    enabledTools: {},
    toolsRequiringConfirmation: {},
    theme: 'system' as const,
    avatarStyle: 'default' as const,
    customAvatarUrl: '',
    bubbleStyle: 'modern' as const,
  };
}

// Message utility functions
export function formatTimestamp(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  
  return date.toLocaleDateString();
}

export function truncateMessage(message: string, maxLength: number = 100): string {
  if (message.length <= maxLength) return message;
  return message.substring(0, maxLength - 3) + '...';
}

export function extractCodeBlocks(content: string): Array<{ language: string; code: string }> {
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
  const blocks: Array<{ language: string; code: string }> = [];
  let match;
  
  while ((match = codeBlockRegex.exec(content)) !== null) {
    blocks.push({
      language: match[1] || 'text',
      code: match[2].trim(),
    });
  }
  
  return blocks;
}

// Performance utility functions
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Storage utility functions
export function saveToLocalStorage(key: string, data: any): boolean {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Failed to save to localStorage:', error);
    return false;
  }
}

export function loadFromLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('Failed to load from localStorage:', error);
    return defaultValue;
  }
}

export function removeFromLocalStorage(key: string): boolean {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('Failed to remove from localStorage:', error);
    return false;
  }
}

// Error handling utility functions
export function createErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
}

export function isNetworkError(error: unknown): boolean {
  if (error instanceof Error) {
    return error.message.includes('network') || 
           error.message.includes('fetch') ||
           error.message.includes('connection');
  }
  return false;
}

// ID generation utility
export function generateId(prefix: string = 'id'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

// Export all utilities
export * from './chat-utils';
export * from './tool-utils';
export * from './config-utils';

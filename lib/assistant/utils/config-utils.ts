// Configuration utility functions
import { AssistantConfig, ConfigValidation, ConfigPreset } from '../types';

export function validateAssistantConfig(config: Partial<AssistantConfig>): ConfigValidation {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Required fields validation
  if (!config.name || config.name.trim().length === 0) {
    errors.push('Assistant name is required');
  } else if (config.name.length > 100) {
    warnings.push('Assistant name is quite long');
  }
  
  if (!config.greeting || config.greeting.trim().length === 0) {
    errors.push('Greeting message is required');
  } else if (config.greeting.length > 500) {
    warnings.push('Greeting message is quite long');
  }
  
  if (!config.model || config.model.trim().length === 0) {
    errors.push('Model is required');
  }
  
  // Numeric validations
  if (config.maxSteps !== undefined) {
    if (config.maxSteps < 1) {
      errors.push('Max steps must be at least 1');
    } else if (config.maxSteps > 20) {
      errors.push('Max steps cannot exceed 20');
    } else if (config.maxSteps > 15) {
      warnings.push('High max steps may impact performance');
    }
  }
  
  // URL validation for custom avatar
  if (config.customAvatarUrl && config.customAvatarUrl.trim().length > 0) {
    try {
      new URL(config.customAvatarUrl);
    } catch {
      errors.push('Custom avatar URL is not valid');
    }
  }
  
  // Theme validation
  if (config.theme && !['system', 'light', 'dark'].includes(config.theme)) {
    errors.push('Theme must be one of: system, light, dark');
  }
  
  // Avatar style validation
  if (config.avatarStyle && !['default', 'minimal', 'custom'].includes(config.avatarStyle)) {
    errors.push('Avatar style must be one of: default, minimal, custom');
  }
  
  // Bubble style validation
  if (config.bubbleStyle && !['modern', 'classic', 'minimal'].includes(config.bubbleStyle)) {
    errors.push('Bubble style must be one of: modern, classic, minimal');
  }
  
  // Custom avatar URL required when avatar style is custom
  if (config.avatarStyle === 'custom' && (!config.customAvatarUrl || config.customAvatarUrl.trim().length === 0)) {
    errors.push('Custom avatar URL is required when avatar style is set to custom');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

export function mergeConfigs(baseConfig: AssistantConfig, overrides: Partial<AssistantConfig>): AssistantConfig {
  return {
    ...baseConfig,
    ...overrides,
    enabledTools: {
      ...baseConfig.enabledTools,
      ...overrides.enabledTools,
    },
    toolsRequiringConfirmation: {
      ...baseConfig.toolsRequiringConfirmation,
      ...overrides.toolsRequiringConfirmation,
    },
  };
}

export function getDefaultAssistantConfig(): AssistantConfig {
  return {
    name: 'AI Assistant',
    greeting: 'Hello! I\'m your AI assistant. How can I help you today?',
    model: 'gpt-4o',
    maxSteps: 10,
    enableMemory: true,
    enableHumanInTheLoop: true,
    enableWebResearch: true,
    enabledTools: {},
    toolsRequiringConfirmation: {},
    theme: 'system',
    avatarStyle: 'default',
    customAvatarUrl: '',
    bubbleStyle: 'modern',
  };
}

export function createConfigPreset(
  id: string,
  name: string,
  description: string,
  config: Partial<AssistantConfig>,
  isDefault: boolean = false
): ConfigPreset {
  return {
    id,
    name,
    description,
    config,
    isDefault,
  };
}

export function getBuiltInPresets(): ConfigPreset[] {
  return [
    createConfigPreset(
      'consultant',
      'Consultant Assistant',
      'Optimized for business consulting and client interactions',
      {
        name: 'Business Consultant',
        greeting: 'Hello! I\'m your business consultant AI. I can help you with strategy, analysis, and client solutions.',
        maxSteps: 15,
        enableWebResearch: true,
        bubbleStyle: 'modern',
        theme: 'system',
      }
    ),
    createConfigPreset(
      'engineer',
      'Engineering Assistant',
      'Focused on technical development and problem-solving',
      {
        name: 'Engineering Assistant',
        greeting: 'Hi! I\'m your engineering AI assistant. I can help with code, architecture, and technical solutions.',
        maxSteps: 20,
        enableMemory: true,
        bubbleStyle: 'minimal',
        theme: 'dark',
      }
    ),
    createConfigPreset(
      'executive',
      'Executive Assistant',
      'Designed for high-level strategic and executive support',
      {
        name: 'Executive Assistant',
        greeting: 'Good day! I\'m your executive AI assistant, ready to support your strategic initiatives.',
        maxSteps: 12,
        enableHumanInTheLoop: true,
        bubbleStyle: 'classic',
        theme: 'light',
      }
    ),
    createConfigPreset(
      'minimal',
      'Minimal Assistant',
      'Simple, lightweight configuration with basic features',
      {
        name: 'Simple Assistant',
        greeting: 'Hello! How can I help?',
        maxSteps: 5,
        enableMemory: false,
        enableWebResearch: false,
        bubbleStyle: 'minimal',
        avatarStyle: 'minimal',
      }
    ),
  ];
}

export function exportConfig(config: AssistantConfig): string {
  const exportData = {
    version: '1.0',
    timestamp: new Date().toISOString(),
    config,
  };
  
  return JSON.stringify(exportData, null, 2);
}

export function importConfig(data: string): AssistantConfig | null {
  try {
    const parsed = JSON.parse(data);
    
    // Validate the imported data structure
    if (!parsed.config) {
      throw new Error('Invalid config format: missing config object');
    }
    
    const validation = validateAssistantConfig(parsed.config);
    if (!validation.isValid) {
      throw new Error(`Invalid config: ${validation.errors.join(', ')}`);
    }
    
    // Merge with defaults to ensure all required fields are present
    return mergeConfigs(getDefaultAssistantConfig(), parsed.config);
  } catch (error) {
    console.error('Failed to import config:', error);
    return null;
  }
}

export function compareConfigs(config1: AssistantConfig, config2: AssistantConfig): {
  identical: boolean;
  differences: Array<{ field: string; value1: any; value2: any }>;
} {
  const differences: Array<{ field: string; value1: any; value2: any }> = [];
  
  // Compare all fields
  const allKeys = new Set([...Object.keys(config1), ...Object.keys(config2)]);
  
  for (const key of allKeys) {
    const value1 = (config1 as any)[key];
    const value2 = (config2 as any)[key];
    
    if (typeof value1 === 'object' && typeof value2 === 'object') {
      // Deep comparison for objects
      if (JSON.stringify(value1) !== JSON.stringify(value2)) {
        differences.push({ field: key, value1, value2 });
      }
    } else if (value1 !== value2) {
      differences.push({ field: key, value1, value2 });
    }
  }
  
  return {
    identical: differences.length === 0,
    differences,
  };
}

export function sanitizeConfig(config: Partial<AssistantConfig>): Partial<AssistantConfig> {
  const sanitized = { ...config };
  
  // Trim string values
  if (sanitized.name) {
    sanitized.name = sanitized.name.trim();
  }
  if (sanitized.greeting) {
    sanitized.greeting = sanitized.greeting.trim();
  }
  if (sanitized.model) {
    sanitized.model = sanitized.model.trim();
  }
  if (sanitized.customAvatarUrl) {
    sanitized.customAvatarUrl = sanitized.customAvatarUrl.trim();
  }
  
  // Ensure numeric values are within bounds
  if (sanitized.maxSteps !== undefined) {
    sanitized.maxSteps = Math.max(1, Math.min(20, sanitized.maxSteps));
  }
  
  return sanitized;
}

export function getConfigSummary(config: AssistantConfig): string {
  const features: string[] = [];
  
  if (config.enableMemory) features.push('Memory');
  if (config.enableHumanInTheLoop) features.push('Human-in-the-loop');
  if (config.enableWebResearch) features.push('Web Research');
  
  const toolCount = Object.values(config.enabledTools).filter(Boolean).length;
  if (toolCount > 0) features.push(`${toolCount} Tools`);
  
  return `${config.name} • ${config.model} • ${features.join(', ')}`;
}

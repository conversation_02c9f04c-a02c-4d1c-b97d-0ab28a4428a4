// Assistant store exports and combined hooks
import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { AssistantStore, ChatStore, UIStore, AssistantConfig } from '../types';
import { getDefaultAssistantConfig } from '../utils/config-utils';

// Initial states
const initialAssistantState = {
  config: null,
  isLoading: false,
  error: null,
};

const initialChatState = {
  messages: [],
  input: '',
  isTyping: false,
  status: 'idle' as const,
};

const initialUIState = {
  isExpanded: false,
  showPreview: false,
  showSettings: false,
  theme: 'system' as const,
};

// Assistant Store
export const useAssistantStore = create<AssistantStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialAssistantState,
        
        setConfig: (config: AssistantConfig) => {
          set((state) => {
            state.config = config;
            state.error = null;
          });
        },
        
        updateConfig: (updates: Partial<AssistantConfig>) => {
          set((state) => {
            if (state.config) {
              state.config = { ...state.config, ...updates };
            }
          });
        },
        
        setLoading: (loading: boolean) => {
          set((state) => {
            state.isLoading = loading;
          });
        },
        
        setError: (error: string | null) => {
          set((state) => {
            state.error = error;
          });
        },
        
        reset: () => {
          set((state) => {
            state.config = getDefaultAssistantConfig();
            state.isLoading = false;
            state.error = null;
          });
        },
      })),
      {
        name: 'assistant-config',
        partialize: (state) => ({ config: state.config }),
      }
    ),
    { name: 'AssistantStore' }
  )
);

// Chat Store
export const useChatStore = create<ChatStore>()(
  devtools(
    immer((set, get) => ({
      ...initialChatState,
      
      setMessages: (messages) => {
        set((state) => {
          state.messages = messages;
        });
      },
      
      addMessage: (message) => {
        set((state) => {
          state.messages.push(message);
        });
      },
      
      setInput: (input) => {
        set((state) => {
          state.input = input;
        });
      },
      
      setTyping: (typing) => {
        set((state) => {
          state.isTyping = typing;
        });
      },
      
      setStatus: (status) => {
        set((state) => {
          state.status = status;
        });
      },
      
      clearMessages: () => {
        set((state) => {
          state.messages = [];
        });
      },
    })),
    { name: 'ChatStore' }
  )
);

// UI Store
export const useUIStore = create<UIStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialUIState,
        
        setExpanded: (expanded) => {
          set((state) => {
            state.isExpanded = expanded;
          });
        },
        
        setShowPreview: (show) => {
          set((state) => {
            state.showPreview = show;
          });
        },
        
        setShowSettings: (show) => {
          set((state) => {
            state.showSettings = show;
          });
        },
        
        setTheme: (theme) => {
          set((state) => {
            state.theme = theme;
          });
        },
        
        toggleExpanded: () => {
          set((state) => {
            state.isExpanded = !state.isExpanded;
          });
        },
        
        togglePreview: () => {
          set((state) => {
            state.showPreview = !state.showPreview;
          });
        },
        
        toggleSettings: () => {
          set((state) => {
            state.showSettings = !state.showSettings;
          });
        },
      })),
      {
        name: 'assistant-ui',
        partialize: (state) => ({ 
          theme: state.theme,
          isExpanded: state.isExpanded,
        }),
      }
    ),
    { name: 'UIStore' }
  )
);

// Combined hooks for convenience
export function useAssistantState() {
  const config = useAssistantStore((state) => state.config);
  const isLoading = useAssistantStore((state) => state.isLoading);
  const error = useAssistantStore((state) => state.error);
  
  return { config, isLoading, error };
}

export function useAssistantActions() {
  const setConfig = useAssistantStore((state) => state.setConfig);
  const updateConfig = useAssistantStore((state) => state.updateConfig);
  const setLoading = useAssistantStore((state) => state.setLoading);
  const setError = useAssistantStore((state) => state.setError);
  const reset = useAssistantStore((state) => state.reset);
  
  return { setConfig, updateConfig, setLoading, setError, reset };
}

export function useChatState() {
  const messages = useChatStore((state) => state.messages);
  const input = useChatStore((state) => state.input);
  const isTyping = useChatStore((state) => state.isTyping);
  const status = useChatStore((state) => state.status);
  
  return { messages, input, isTyping, status };
}

export function useChatActions() {
  const setMessages = useChatStore((state) => state.setMessages);
  const addMessage = useChatStore((state) => state.addMessage);
  const setInput = useChatStore((state) => state.setInput);
  const setTyping = useChatStore((state) => state.setTyping);
  const setStatus = useChatStore((state) => state.setStatus);
  const clearMessages = useChatStore((state) => state.clearMessages);
  
  return { setMessages, addMessage, setInput, setTyping, setStatus, clearMessages };
}

export function useUIState() {
  const isExpanded = useUIStore((state) => state.isExpanded);
  const showPreview = useUIStore((state) => state.showPreview);
  const showSettings = useUIStore((state) => state.showSettings);
  const theme = useUIStore((state) => state.theme);
  
  return { isExpanded, showPreview, showSettings, theme };
}

export function useUIActions() {
  const setExpanded = useUIStore((state) => state.setExpanded);
  const setShowPreview = useUIStore((state) => state.setShowPreview);
  const setShowSettings = useUIStore((state) => state.setShowSettings);
  const setTheme = useUIStore((state) => state.setTheme);
  const toggleExpanded = useUIStore((state) => state.toggleExpanded);
  const togglePreview = useUIStore((state) => state.togglePreview);
  const toggleSettings = useUIStore((state) => state.toggleSettings);
  
  return { 
    setExpanded, 
    setShowPreview, 
    setShowSettings, 
    setTheme, 
    toggleExpanded, 
    togglePreview, 
    toggleSettings 
  };
}

// Performance monitoring hook
export function useAssistantPerformance() {
  const messageCount = useChatStore((state) => state.messages.length);
  const isLoading = useAssistantStore((state) => state.isLoading);
  const hasConfig = useAssistantStore((state) => !!state.config);
  
  return {
    messageCount,
    isLoading,
    hasConfig,
    memoryUsage: {
      messages: messageCount * 512, // Rough estimate
      config: hasConfig ? 1024 : 0,
    },
  };
}

// Reset all stores
export function resetAllStores() {
  useAssistantStore.getState().reset();
  useChatStore.getState().clearMessages();
  useUIStore.setState(initialUIState);
}

// Export individual stores
export { useAssistantStore, useChatStore, useUIStore };

// Export store types
export type { AssistantStore, ChatStore, UIStore };

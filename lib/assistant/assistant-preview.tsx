'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON><PERSON>, User, SendHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { SimpleMarkdown } from './simple-markdown';
import { useSession } from 'next-auth/react';
import { getInitials } from '@/lib/assistant/utils';

interface AssistantPreviewProps {
  name: string;
  greeting: string;
  avatarStyle: string;
  customAvatarUrl?: string;
  bubbleStyle: string;
  theme: string;
}

export function AssistantPreview({
  name,
  greeting,
  avatarStyle,
  customAvatarUrl,
  bubbleStyle,
  theme,
}: AssistantPreviewProps) {
  const { data: session } = useSession();
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState([
    {
      role: 'assistant',
      content: greeting,
    },
  ]);

  // Function to handle sending a message in the preview
  const handleSendMessage = () => {
    if (!input.trim()) return;
    
    // Add user message
    setMessages([
      ...messages,
      {
        role: 'user',
        content: input,
      },
    ]);
    
    // Simulate assistant response
    setTimeout(() => {
      setMessages(prev => [
        ...prev,
        {
          role: 'assistant',
          content: `This is a preview of how ${name} will respond to your messages. The actual responses will use the AI model you've selected.`,
        },
      ]);
    }, 1000);
    
    setInput('');
  };

  // Get bubble style class
  const getBubbleStyle = () => {
    switch (bubbleStyle) {
      case 'classic':
        return 'rounded-lg';
      case 'minimal':
        return 'rounded-sm';
      case 'modern':
      default:
        return 'rounded-2xl';
    }
  };

  return (
    <Card className={cn(
      "flex flex-col h-[400px] overflow-hidden border shadow-md",
      theme === 'dark' ? 'bg-slate-900 text-slate-100' : ''
    )}>
      <CardContent className="flex-1 p-4 overflow-auto flex flex-col gap-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={cn(
              'flex w-full items-start gap-3',
              message.role === 'user' ? 'justify-end' : 'justify-start'
            )}
          >
            {message.role !== 'user' && (
              <Avatar className={cn("h-8 w-8", avatarStyle === 'minimal' ? 'h-6 w-6' : '')}>
                {customAvatarUrl && avatarStyle === 'custom' ? (
                  <AvatarImage src={customAvatarUrl} alt="Assistant" />
                ) : null}
                <AvatarFallback className={cn(
                  "bg-primary text-primary-foreground",
                  avatarStyle === 'minimal' ? 'text-xs' : ''
                )}>
                  <Bot size={avatarStyle === 'minimal' ? 12 : 16} />
                </AvatarFallback>
              </Avatar>
            )}

            <div
              className={cn(
                'max-w-[80%] px-4 py-3 shadow-sm',
                getBubbleStyle(),
                message.role === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : theme === 'dark' ? 'bg-slate-800' : 'bg-muted'
              )}
            >
              <SimpleMarkdown className="text-sm">
                {message.content}
              </SimpleMarkdown>
            </div>

            {message.role === 'user' && (
              <Avatar className={cn("h-8 w-8", avatarStyle === 'minimal' ? 'h-6 w-6' : '')}>
                <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || 'User'} />
                <AvatarFallback className={cn(
                  "bg-secondary",
                  avatarStyle === 'minimal' ? 'text-xs' : ''
                )}>
                  {session?.user?.name ? getInitials(session.user.name) : <User size={avatarStyle === 'minimal' ? 12 : 16} />}
                </AvatarFallback>
              </Avatar>
            )}
          </div>
        ))}
      </CardContent>
      
      <div className="p-4 border-t flex gap-2 items-center">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Type a message..."
          className="flex-1"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleSendMessage();
            }
          }}
        />
        <Button 
          size="icon" 
          className="rounded-full"
          onClick={handleSendMessage}
          disabled={!input.trim()}
        >
          <SendHorizontal className="h-4 w-4" />
        </Button>
      </div>
    </Card>
  );
}

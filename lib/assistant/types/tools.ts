// Tool-specific types

export interface ToolDefinition {
  name: string;
  description: string;
  category: ToolCategory;
  parameters: ToolParameters;
  requiresConfirmation: boolean;
  riskLevel: 'low' | 'medium' | 'high';
  executionMode: 'client' | 'server' | 'hybrid';
}

export interface ToolParameters {
  type: 'object';
  properties: Record<string, ToolParameter>;
  required: string[];
}

export interface ToolParameter {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  enum?: string[];
  default?: any;
  minimum?: number;
  maximum?: number;
  pattern?: string;
}

export type ToolCategory = 
  | 'navigation'
  | 'document'
  | 'meeting'
  | 'task'
  | 'user'
  | 'feedback'
  | 'plugin'
  | 'transcript'
  | 'ai'
  | 'folder'
  | 'tag'
  | 'version'
  | 'webResearch'
  | 'system';

export interface ToolExecution {
  id: string;
  toolName: string;
  parameters: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  result?: ToolResult;
  error?: string;
  userId: string;
  sessionId: string;
}

export interface ToolResult {
  success: boolean;
  data?: any;
  message?: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface ToolPermission {
  toolName: string;
  userId: string;
  granted: boolean;
  grantedAt: Date;
  grantedBy: string;
  expiresAt?: Date;
  conditions?: string[];
}

export interface ToolUsageStats {
  toolName: string;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastUsed: Date;
  popularityScore: number;
}

export interface ToolConfiguration {
  enabled: boolean;
  requiresConfirmation: boolean;
  maxExecutionsPerMinute: number;
  allowedUsers: string[];
  blockedUsers: string[];
  customSettings: Record<string, any>;
}

export interface ClientTool {
  name: string;
  execute: (parameters: Record<string, any>) => Promise<ToolResult>;
  validate?: (parameters: Record<string, any>) => boolean;
  requiresConfirmation?: boolean;
}

export interface ServerTool {
  name: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  requiresAuth: boolean;
}

export interface ToolRegistry {
  clientTools: Map<string, ClientTool>;
  serverTools: Map<string, ServerTool>;
  toolDefinitions: Map<string, ToolDefinition>;
  categories: Map<string, ToolCategory>;
}

export interface ToolEvent {
  type: 'execution_started' | 'execution_completed' | 'execution_failed' | 'permission_granted' | 'permission_denied';
  toolName: string;
  userId: string;
  sessionId: string;
  timestamp: Date;
  data?: any;
}

export interface ToolValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Navigation Tools
export interface NavigationToolArgs {
  path: string;
  newTab?: boolean;
}

// Document Tools
export interface DocumentToolArgs {
  id?: string;
  title?: string;
  content?: string;
  tags?: string[];
  folderId?: string;
}

// Meeting Tools
export interface MeetingToolArgs {
  id?: string;
  title?: string;
  description?: string;
  startTime?: string;
  endTime?: string;
  attendees?: string[];
}

// Task Tools
export interface TaskToolArgs {
  id?: string;
  title?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high';
  dueDate?: string;
  assigneeId?: string;
}

// Web Research Tools
export interface WebResearchToolArgs {
  query?: string;
  url?: string;
  maxResults?: number;
  includeImages?: boolean;
}

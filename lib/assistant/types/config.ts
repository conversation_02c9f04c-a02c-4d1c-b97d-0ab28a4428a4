// Configuration-specific types

export interface ModelConfig {
  name: string;
  provider: 'openai' | 'anthropic' | 'xai' | 'custom';
  version: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
}

export interface SecurityConfig {
  enableContentFilter: boolean;
  allowedDomains: string[];
  blockedKeywords: string[];
  maxRequestsPerMinute: number;
  requireAuthentication: boolean;
}

export interface PerformanceConfig {
  cacheEnabled: boolean;
  cacheTTL: number;
  maxConcurrentRequests: number;
  requestTimeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface IntegrationConfig {
  webhookUrl?: string;
  apiKeys: Record<string, string>;
  externalServices: {
    enabled: boolean;
    services: string[];
  };
}

export interface AnalyticsConfig {
  enabled: boolean;
  trackUserInteractions: boolean;
  trackPerformance: boolean;
  dataRetentionDays: number;
  anonymizeData: boolean;
}

export interface NotificationConfig {
  email: {
    enabled: boolean;
    templates: Record<string, string>;
  };
  push: {
    enabled: boolean;
    vapidKey?: string;
  };
  inApp: {
    enabled: boolean;
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  };
}

export interface AccessibilityConfig {
  highContrast: boolean;
  fontSize: 'small' | 'medium' | 'large';
  screenReaderSupport: boolean;
  keyboardNavigation: boolean;
  reducedMotion: boolean;
}

export interface ExperimentalConfig {
  features: Record<string, boolean>;
  betaAccess: boolean;
  debugMode: boolean;
  verboseLogging: boolean;
}

export interface ConfigValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ConfigPreset {
  id: string;
  name: string;
  description: string;
  config: Partial<AssistantConfig>;
  isDefault: boolean;
}

export interface ConfigHistory {
  id: string;
  config: AssistantConfig;
  timestamp: Date;
  userId: string;
  description?: string;
}

// Extended AssistantConfig with additional settings
export interface ExtendedAssistantConfig extends AssistantConfig {
  model: ModelConfig;
  security: SecurityConfig;
  performance: PerformanceConfig;
  integrations: IntegrationConfig;
  analytics: AnalyticsConfig;
  notifications: NotificationConfig;
  accessibility: AccessibilityConfig;
  experimental: ExperimentalConfig;
}

// Re-export base config
export type { AssistantConfig } from './index';

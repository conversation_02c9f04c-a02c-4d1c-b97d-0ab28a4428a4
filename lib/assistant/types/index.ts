// Assistant Chat Interface Types
import { Message } from '@ai-sdk/react';

// Core Assistant Types
export interface AssistantProps {
  initialMessages?: Message[];
  id?: string;
}

export interface AssistantConfig {
  // General settings
  name: string;
  greeting: string;
  model: string;
  
  // Behavior settings
  maxSteps: number;
  enableMemory: boolean;
  enableHumanInTheLoop: boolean;
  enableWebResearch: boolean;
  
  // Tool settings
  enabledTools: Record<string, boolean>;
  toolsRequiringConfirmation: Record<string, boolean>;
  
  // UI settings
  theme: 'system' | 'light' | 'dark';
  avatarStyle: 'default' | 'minimal' | 'custom';
  customAvatarUrl?: string;
  bubbleStyle: 'modern' | 'classic' | 'minimal';
}

export interface AssistantState {
  config: AssistantConfig | null;
  isLoading: boolean;
  error: string | null;
  isConfigured: boolean;
}

// Chat Message Types
export interface ChatMessageProps {
  message: Message;
  addToolResult: (toolCallId: string, result: any) => void;
  toolsRequiringConfirmation: string[];
  bubbleStyle?: string;
  avatarStyle?: string;
  customAvatarUrl?: string;
}

export interface ChatInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
  disabled?: boolean;
}

// Tool Types
export interface Tool {
  name: string;
  description: string;
  category: string;
  requiresConfirmation: boolean;
  parameters: Record<string, any>;
}

export interface ToolCategory {
  id: string;
  name: string;
  description: string;
  tools: string[];
}

export interface ToolResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
}

// Assistant Store Types
export interface AssistantStore {
  // State
  config: AssistantConfig | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setConfig: (config: AssistantConfig) => void;
  updateConfig: (updates: Partial<AssistantConfig>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

// Chat Store Types
export interface ChatStore {
  // State
  messages: Message[];
  input: string;
  isTyping: boolean;
  status: 'idle' | 'loading' | 'streaming' | 'error';
  
  // Actions
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  setInput: (input: string) => void;
  setTyping: (typing: boolean) => void;
  setStatus: (status: ChatStore['status']) => void;
  clearMessages: () => void;
}

// UI Store Types
export interface UIStore {
  // State
  isExpanded: boolean;
  showPreview: boolean;
  showSettings: boolean;
  theme: 'light' | 'dark' | 'system';
  
  // Actions
  setExpanded: (expanded: boolean) => void;
  setShowPreview: (show: boolean) => void;
  setShowSettings: (show: boolean) => void;
  setTheme: (theme: UIStore['theme']) => void;
  toggleExpanded: () => void;
  togglePreview: () => void;
  toggleSettings: () => void;
}

// Combined Store Type
export interface AssistantChatStore {
  assistant: AssistantStore;
  chat: ChatStore;
  ui: UIStore;
}

// Hook Return Types
export interface UseAssistantConfigReturn {
  config: AssistantConfig | null;
  isLoading: boolean;
  error: string | null;
  updateConfig: (updates: Partial<AssistantConfig>) => Promise<void>;
  resetConfig: () => Promise<void>;
}

export interface UseChatReturn {
  messages: Message[];
  input: string;
  isLoading: boolean;
  error: string | null;
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;
  setInput: (input: string) => void;
}

// Event Types
export interface AssistantEvent {
  type: 'message' | 'tool_call' | 'error' | 'config_change';
  data: any;
  timestamp: Date;
}

// Utility Types
export type AssistantType = 'consultant' | 'engineer' | 'executive';

export interface AssistantCapability {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  category: string;
}

// Export all types
export * from './chat';
export * from './config';
export * from './tools';

// Chat-specific types
import { Message } from '@ai-sdk/react';

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  assistantType: 'consultant' | 'engineer' | 'executive';
}

export interface ChatHistory {
  sessions: ChatSession[];
  currentSessionId: string | null;
  maxSessions: number;
}

export interface ChatMetrics {
  totalMessages: number;
  totalSessions: number;
  averageResponseTime: number;
  userSatisfaction: number;
  toolUsageCount: Record<string, number>;
}

export interface ChatSettings {
  autoSave: boolean;
  maxHistorySize: number;
  enableNotifications: boolean;
  soundEnabled: boolean;
  typingIndicator: boolean;
  messageTimestamps: boolean;
}

export interface ChatContext {
  userId: string;
  sessionId: string;
  assistantType: string;
  metadata: Record<string, any>;
}

export interface StreamingState {
  isStreaming: boolean;
  currentChunk: string;
  totalChunks: number;
  processedChunks: number;
}

export interface MessageReaction {
  type: 'like' | 'dislike' | 'helpful' | 'not_helpful';
  messageId: string;
  userId: string;
  timestamp: Date;
}

export interface ChatError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  recoverable: boolean;
}

export interface ChatNotification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

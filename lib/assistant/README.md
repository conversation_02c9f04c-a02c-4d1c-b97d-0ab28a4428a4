# Assistant Chat Interface Library

A comprehensive, modular assistant chat interface library built following the vscode-panels architecture pattern. This library provides a complete solution for integrating AI assistant functionality into React applications.

## 🏗️ Architecture Overview

The library follows a modular architecture inspired by the vscode-panels library structure:

```
lib/assistant/
├── README.md                     # This documentation
├── index.ts                      # Main exports
├── types/                        # TypeScript type definitions
│   ├── index.ts                  # Common types and re-exports
│   ├── chat.ts                   # Chat-related types
│   ├── config.ts                 # Configuration types
│   └── tools.ts                  # Tool-related types
├── store/                        # Zustand state management
│   └── index.ts                  # Store exports and combined hooks
├── hooks/                        # Custom React hooks
│   ├── index.ts                  # Hook exports
│   ├── use-assistant-config.ts   # Configuration management
│   ├── use-assistant-tools.ts    # Tool management
│   └── use-assistant-chat.ts     # Chat functionality
├── utils/                        # Utility functions
│   ├── index.ts                  # Utility exports
│   ├── chat-utils.ts             # Chat-specific utilities
│   ├── tool-utils.ts             # Tool-specific utilities
│   └── config-utils.ts           # Configuration utilities
├── components/                   # React components
│   ├── assistant-chat.tsx        # Main chat component
│   ├── assistant-config.tsx      # Configuration component
│   ├── assistant-preview.tsx     # Preview component
│   ├── chat-message.tsx          # Message component
│   ├── chat-input.tsx            # Input component
│   ├── floating-assistant-button.tsx # Floating button
│   ├── sidebar-assistant.tsx     # Sidebar component
│   ├── simple-markdown.tsx       # Markdown renderer
│   └── tool-result-card.tsx      # Tool result display
└── client-tools.ts              # Client-side tools configuration
```

## 🚀 Quick Start

### Basic Usage

```tsx
import { AssistantChat } from '@/lib/assistant';

function App() {
  return (
    <div className="h-screen">
      <AssistantChat id="main-chat" />
    </div>
  );
}
```

### With Configuration

```tsx
import { AssistantChat, useAssistantConfig } from '@/lib/assistant';

function ConfiguredAssistant() {
  const { config, updateConfig } = useAssistantConfig();
  
  return (
    <AssistantChat 
      id="configured-chat"
      initialMessages={[{
        id: 'welcome',
        role: 'assistant',
        content: config?.greeting || 'Hello! How can I help you?'
      }]}
    />
  );
}
```

## 🎯 Core Features

### ✅ Implemented Features

- **Modular Architecture** - Clean separation of concerns with dedicated directories
- **TypeScript Support** - Full type safety throughout the library
- **Zustand State Management** - Efficient state management with selectors
- **Configuration Management** - Comprehensive assistant configuration system
- **Tool System** - Client/server tool execution with validation
- **Chat History** - Session management and persistence
- **Theme Support** - Light/dark/system theme compatibility
- **Import/Export** - Configuration and chat session import/export
- **Performance Monitoring** - Built-in performance metrics
- **Accessibility** - Screen reader support and keyboard navigation
- **Error Handling** - Graceful error handling with user feedback

### 🔧 Configuration

The assistant supports comprehensive configuration:

```tsx
import { useAssistantConfig } from '@/lib/assistant';

function ConfigExample() {
  const { config, updateConfig } = useAssistantConfig();
  
  const handleConfigUpdate = async () => {
    await updateConfig({
      name: 'My Custom Assistant',
      greeting: 'Welcome! I\'m here to help.',
      theme: 'dark',
      bubbleStyle: 'modern',
      maxSteps: 15,
      enableWebResearch: true,
    });
  };
  
  return (
    <button onClick={handleConfigUpdate}>
      Update Configuration
    </button>
  );
}
```

### 🛠️ Tool System

The library includes a comprehensive tool system:

```tsx
import { useAssistantTools } from '@/lib/assistant';

function ToolExample() {
  const { 
    availableTools, 
    enabledTools, 
    executeTool 
  } = useAssistantTools();
  
  const handleToolExecution = async () => {
    try {
      const result = await executeTool('navigateTo', {
        path: '/dashboard',
        newTab: false
      });
      console.log('Tool result:', result);
    } catch (error) {
      console.error('Tool execution failed:', error);
    }
  };
  
  return (
    <div>
      <h3>Available Tools: {availableTools.length}</h3>
      <button onClick={handleToolExecution}>
        Navigate to Dashboard
      </button>
    </div>
  );
}
```

### 💬 Chat Management

Advanced chat functionality with history and sessions:

```tsx
import { useAssistantChat, useChatHistory } from '@/lib/assistant';

function ChatExample() {
  const { 
    messages, 
    sendMessage, 
    clearChat, 
    exportChat 
  } = useAssistantChat('session-1');
  
  const { sessions, saveSession } = useChatHistory();
  
  return (
    <div>
      <div>Messages: {messages.length}</div>
      <div>Sessions: {sessions.length}</div>
      <button onClick={() => sendMessage('Hello!')}>
        Send Message
      </button>
      <button onClick={clearChat}>
        Clear Chat
      </button>
      <button onClick={exportChat}>
        Export Chat
      </button>
    </div>
  );
}
```

## 📊 State Management

The library uses Zustand for state management with three main stores:

- **AssistantStore** - Configuration and assistant state
- **ChatStore** - Messages and chat state  
- **UIStore** - UI preferences and state

```tsx
import { 
  useAssistantStore, 
  useChatStore, 
  useUIStore 
} from '@/lib/assistant';

function StateExample() {
  const config = useAssistantStore(state => state.config);
  const messages = useChatStore(state => state.messages);
  const theme = useUIStore(state => state.theme);
  
  return (
    <div>
      <div>Assistant: {config?.name}</div>
      <div>Messages: {messages.length}</div>
      <div>Theme: {theme}</div>
    </div>
  );
}
```

## 🎨 Theming

The library supports comprehensive theming:

```tsx
import { useAssistantTheme } from '@/lib/assistant';

function ThemeExample() {
  const { theme, configuredTheme, systemTheme } = useAssistantTheme();
  
  return (
    <div className={`assistant-theme-${theme}`}>
      <div>Current Theme: {theme}</div>
      <div>Configured: {configuredTheme}</div>
      <div>System: {systemTheme}</div>
    </div>
  );
}
```

## 🔌 Tool Development

Create custom tools for your assistant:

```tsx
import { createToolDefinition } from '@/lib/assistant';

const customTool = createToolDefinition(
  'customAction',
  'Perform a custom action',
  'system',
  {
    type: 'object',
    properties: {
      action: {
        type: 'string',
        description: 'The action to perform'
      }
    },
    required: ['action']
  },
  {
    requiresConfirmation: true,
    riskLevel: 'medium',
    executionMode: 'client'
  }
);
```

## 📈 Performance

The library includes built-in performance monitoring:

```tsx
import { useAssistantPerformance } from '@/lib/assistant';

function PerformanceExample() {
  const { metrics, recordResponse } = useAssistantPerformance();
  
  return (
    <div>
      <div>Uptime: {metrics.uptime}ms</div>
      <div>Messages: {metrics.messageCount}</div>
      <div>Errors: {metrics.errorCount}</div>
      <div>Avg Response: {metrics.responseTime}ms</div>
    </div>
  );
}
```

## 🌐 Accessibility

Built-in accessibility features:

```tsx
import { useAssistantAccessibility } from '@/lib/assistant';

function AccessibilityExample() {
  const { announceToScreenReader, focusElement } = useAssistantAccessibility();
  
  const handleAnnouncement = () => {
    announceToScreenReader('Message sent successfully');
  };
  
  return (
    <button onClick={handleAnnouncement}>
      Send with Announcement
    </button>
  );
}
```

## 📄 License

Part of the Soimagine Digital Agency project.

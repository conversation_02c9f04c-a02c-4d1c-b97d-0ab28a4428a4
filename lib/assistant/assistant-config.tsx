'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Loader2, Bot, Sparkles, Save, RefreshCw, Check, RotateCcw, Download, Upload } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { AssistantPreview } from './assistant-preview';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

// Define the form schema
const formSchema = z.object({
  // General settings
  name: z.string().min(1, 'Assistant name is required'),
  greeting: z.string().min(1, 'Greeting message is required'),
  model: z.string().min(1, 'Model is required'),

  // Behavior settings
  maxSteps: z.number().min(1).max(20),
  enableMemory: z.boolean(),
  enableHumanInTheLoop: z.boolean(),
  enableWebResearch: z.boolean(),

  // Tool settings
  enabledTools: z.record(z.boolean()),
  toolsRequiringConfirmation: z.record(z.boolean()),

  // UI settings
  theme: z.enum(['system', 'light', 'dark']),
  avatarStyle: z.enum(['default', 'minimal', 'custom']),
  customAvatarUrl: z.string().optional(),
  bubbleStyle: z.enum(['modern', 'classic', 'minimal']),
});

type FormValues = z.infer<typeof formSchema>;

// Tool categories for the configuration
const toolCategories = [
  { id: 'document', name: 'Document Tools', description: 'Tools for managing documents' },
  { id: 'meeting', name: 'Meeting Tools', description: 'Tools for managing meetings' },
  { id: 'task', name: 'Task Tools', description: 'Tools for managing tasks' },
  { id: 'user', name: 'User Tools', description: 'Tools for managing user settings' },
  { id: 'feedback', name: 'Feedback Tools', description: 'Tools for providing feedback' },
  { id: 'plugin', name: 'Plugin Tools', description: 'Tools for managing plugins' },
  { id: 'transcript', name: 'Transcript Tools', description: 'Tools for managing transcripts' },
  { id: 'ai', name: 'AI Tools', description: 'Advanced AI-powered tools' },
  { id: 'folder', name: 'Folder Tools', description: 'Tools for managing folders' },
  { id: 'tag', name: 'Tag Tools', description: 'Tools for managing tags' },
  { id: 'version', name: 'Version Tools', description: 'Tools for managing versions' },
  { id: 'webResearch', name: 'Web Research Tools', description: 'Tools for web research' },
];

// High-risk tools that should require confirmation by default
const highRiskTools = [
  'deleteDocument',
  'deleteMeeting',
  'deleteTask',
  'updateUserProfile',
  'deleteFolder',
  'removeDocumentTag',
];

export function AssistantConfig() {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [availableTools, setAvailableTools] = useState<Record<string, string[]>>({});

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: 'AI Assistant',
      greeting: 'Hello! I\'m your AI assistant. How can I help you today?',
      model: 'gpt-4o',
      maxSteps: 10,
      enableMemory: true,
      enableHumanInTheLoop: true,
      enableWebResearch: true,
      enabledTools: {},
      toolsRequiringConfirmation: {},
      theme: 'system',
      avatarStyle: 'default',
      customAvatarUrl: '',
      bubbleStyle: 'modern',
    },
  });

  // Fetch current configuration on component mount
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        // Fetch available tools
        const toolsResponse = await fetch('/api/assistant/tools');
        if (toolsResponse.ok) {
          const toolsData = await toolsResponse.json();
          setAvailableTools(toolsData.tools);

          // Initialize enabled tools
          const enabledTools: Record<string, boolean> = {};
          const toolsRequiringConfirmation: Record<string, boolean> = {};

          Object.entries(toolsData.tools).forEach(([category, tools]) => {
            enabledTools[category] = true;

            (tools as string[]).forEach(tool => {
              // Set high-risk tools to require confirmation by default
              toolsRequiringConfirmation[tool] = highRiskTools.includes(tool);
            });
          });

          // Fetch user's saved configuration
          const configResponse = await fetch('/api/assistant/config');
          if (configResponse.ok) {
            const configData = await configResponse.json();

            // Update form with saved configuration
            form.reset({
              ...configData,
              enabledTools,
              toolsRequiringConfirmation,
            });
          } else {
            // Set default values if no saved configuration
            form.reset({
              ...form.getValues(),
              enabledTools,
              toolsRequiringConfirmation,
            });
          }
        }
      } catch (error) {
        console.error('Error fetching assistant configuration:', error);
        toast.error('Failed to load assistant configuration');
      } finally {
        setIsLoading(false);
      }
    };

    if (session?.user) {
      fetchConfig();
    }
  }, [session, form]);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSaving(true);

    try {
      const response = await fetch('/api/assistant/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        toast.success('Assistant configuration saved successfully');
        router.refresh();
      } else {
        throw new Error('Failed to save configuration');
      }
    } catch (error) {
      console.error('Error saving assistant configuration:', error);
      toast.error('Failed to save assistant configuration');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[60vh]">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading configuration...</p>
        </div>
      </div>
    );
  }

  // Default configuration values
  const defaultConfig = {
    name: 'AI Assistant',
    greeting: 'Hello! I\'m your AI assistant. How can I help you today?',
    model: 'gpt-4o',
    maxSteps: 10,
    enableMemory: true,
    enableHumanInTheLoop: true,
    enableWebResearch: true,
    enabledTools: {},
    toolsRequiringConfirmation: {},
    theme: 'system',
    avatarStyle: 'default',
    customAvatarUrl: '',
    bubbleStyle: 'modern',
  };

  // Reset to default values
  const handleReset = () => {
    if (window.confirm('Are you sure you want to reset all settings to default values?')) {
      form.reset(defaultConfig);
      toast.success('Settings reset to defaults');
    }
  };

  // Export configuration as JSON
  const handleExport = () => {
    const configData = form.getValues();
    const dataStr = JSON.stringify(configData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `assistant-config-${new Date().toISOString().slice(0, 10)}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  // Import configuration from JSON file
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';

    input.onchange = (e: any) => {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const config = JSON.parse(event.target?.result as string);
          form.reset(config);
          toast.success('Configuration imported successfully');
        } catch (error) {
          toast.error('Failed to import configuration: Invalid JSON file');
        }
      };
      reader.readAsText(file);
    };

    input.click();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Tabs defaultValue="general" className="w-full">
          <div className="flex justify-between items-center mb-6">
            <TabsList className="grid grid-cols-5">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="behavior">Behavior</TabsTrigger>
              <TabsTrigger value="tools">Tools</TabsTrigger>
              <TabsTrigger value="appearance">Appearance</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <div className="flex gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleReset}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Reset
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Reset all settings to default values</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleExport}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Export configuration as JSON</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleImport}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Import
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Import configuration from JSON file</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {/* General Settings Tab */}
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Configure basic settings for your AI assistant
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Assistant Name</FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full p-0">
                                <span className="text-xs font-semibold">?</span>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[300px]">
                              <p>The name of your assistant will be used in conversations and displayed in the UI. Choose a name that reflects the assistant's purpose or personality.</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <FormControl>
                        <Input placeholder="AI Assistant" {...field} />
                      </FormControl>
                      <FormDescription>
                        Personalize your assistant with a custom name
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="greeting"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Greeting Message</FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full p-0">
                                <span className="text-xs font-semibold">?</span>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[300px]">
                              <p>This is the first message your assistant will send when starting a new conversation. Make it welcoming and informative about what the assistant can help with.</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <FormControl>
                        <Textarea
                          placeholder="Hello! I'm your AI assistant. How can I help you today?"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Customize the initial greeting message
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="model"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>AI Model</FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full p-0">
                                <span className="text-xs font-semibold">?</span>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[300px]">
                              <p>The AI model determines the capabilities, quality, and speed of your assistant's responses:</p>
                              <ul className="list-disc pl-4 mt-2 space-y-1 text-xs">
                                <li><strong>GPT-4o:</strong> Latest model with best overall performance and capabilities</li>
                                <li><strong>GPT-4 Turbo:</strong> Powerful model with strong reasoning abilities</li>
                                <li><strong>GPT-3.5 Turbo:</strong> Faster but less capable than the GPT-4 models</li>
                              </ul>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select AI model" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="gpt-4o">
                            <div className="flex items-center gap-2">
                              <Sparkles className="h-4 w-4 text-yellow-500" />
                              <span>GPT-4o (Recommended)</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="gpt-4-turbo">
                            <div className="flex items-center gap-2">
                              <Sparkles className="h-4 w-4 text-blue-500" />
                              <span>GPT-4 Turbo</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="gpt-3.5-turbo">
                            <div className="flex items-center gap-2">
                              <Bot className="h-4 w-4 text-green-500" />
                              <span>GPT-3.5 Turbo</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the AI model to power your assistant
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Behavior Settings Tab */}
          <TabsContent value="behavior">
            <Card>
              <CardHeader>
                <CardTitle>Behavior Settings</CardTitle>
                <CardDescription>
                  Configure how your assistant behaves and processes tasks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="maxSteps"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Maximum Steps</FormLabel>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full p-0">
                                <span className="text-xs font-semibold">?</span>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[300px]">
                              <p>This controls how many sequential actions the assistant can take to complete complex tasks. Higher values allow more complex tasks but may increase response time and token usage.</p>
                              <ul className="list-disc pl-4 mt-2 space-y-1 text-xs">
                                <li><strong>1-5:</strong> Simple tasks only</li>
                                <li><strong>6-10:</strong> Moderate complexity (recommended)</li>
                                <li><strong>11-20:</strong> Complex multi-step tasks</li>
                              </ul>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          max={20}
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormDescription>
                        Maximum number of steps the assistant can take to complete a task (1-20)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Separator className="my-4" />

                <FormField
                  control={form.control}
                  name="enableMemory"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <FormLabel className="text-base">Memory</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full p-0">
                                  <span className="text-xs font-semibold">?</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-[300px]">
                                <p>When enabled, the assistant will remember information from previous conversations and use it to provide more personalized and contextually relevant responses. This improves the assistant's ability to handle ongoing discussions and refer back to previously mentioned information.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <FormDescription>
                          Allow the assistant to remember past conversations and context
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="enableHumanInTheLoop"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <FormLabel className="text-base">Human-in-the-Loop</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full p-0">
                                  <span className="text-xs font-semibold">?</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-[300px]">
                                <p>When enabled, the assistant will ask for your confirmation before performing potentially risky actions like deleting data or making significant changes. This gives you control over what actions the assistant can take and prevents unintended consequences.</p>
                                <p className="mt-2 text-xs text-amber-600">Recommended: Keep this enabled for safety</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <FormDescription>
                          Require confirmation for potentially risky actions
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="enableWebResearch"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <FormLabel className="text-base">Web Research</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-5 w-5 rounded-full p-0">
                                  <span className="text-xs font-semibold">?</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-[300px]">
                                <p>When enabled, the assistant can search the web to find up-to-date information that isn't in its training data. This allows it to answer questions about recent events, specific details, or specialized topics that require current information.</p>
                                <p className="mt-2 text-xs">Note: Web research may slightly increase response time but provides more accurate and current information.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <FormDescription>
                          Allow the assistant to search the web for information
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tools Settings Tab */}
          <TabsContent value="tools">
            <Card>
              <CardHeader>
                <CardTitle>Tool Settings</CardTitle>
                <CardDescription>
                  Configure which tools your assistant can use
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid gap-6">
                    {toolCategories.map((category) => (
                      <FormField
                        key={category.id}
                        control={form.control}
                        name={`enabledTools.${category.id}`}
                        render={({ field }) => (
                          <FormItem className="flex flex-col space-y-3">
                            <div className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">{category.name}</FormLabel>
                                <FormDescription>
                                  {category.description}
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </div>

                            {field.value && availableTools[category.id] && (
                              <div className="ml-6 space-y-3 border-l pl-6">
                                <p className="text-sm font-medium">Tools requiring confirmation:</p>
                                {(availableTools[category.id] as string[]).map((tool) => (
                                  <FormField
                                    key={tool}
                                    control={form.control}
                                    name={`toolsRequiringConfirmation.${tool}`}
                                    render={({ field }) => (
                                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                        <FormControl>
                                          <Checkbox
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                          />
                                        </FormControl>
                                        <div className="space-y-1 leading-none">
                                          <FormLabel>
                                            {tool}
                                          </FormLabel>
                                        </div>
                                      </FormItem>
                                    )}
                                  />
                                ))}
                              </div>
                            )}
                          </FormItem>
                        )}
                      />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Appearance Settings Tab */}
          <TabsContent value="appearance">
            <Card>
              <CardHeader>
                <CardTitle>Appearance Settings</CardTitle>
                <CardDescription>
                  Customize how your assistant looks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="theme"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Theme</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select theme" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="system">System</SelectItem>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose the theme for your assistant
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="avatarStyle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Avatar Style</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select avatar style" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="default">Default</SelectItem>
                          <SelectItem value="minimal">Minimal</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose the avatar style for your assistant
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch('avatarStyle') === 'custom' && (
                  <FormField
                    control={form.control}
                    name="customAvatarUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Custom Avatar URL</FormLabel>
                        <FormControl>
                          <Input placeholder="https://example.com/avatar.png" {...field} />
                        </FormControl>
                        <FormDescription>
                          Enter the URL for your custom avatar image
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="bubbleStyle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Chat Bubble Style</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select chat bubble style" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="modern">Modern</SelectItem>
                          <SelectItem value="classic">Classic</SelectItem>
                          <SelectItem value="minimal">Minimal</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose the style for chat message bubbles
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview">
            <Card>
              <CardHeader>
                <CardTitle>Assistant Preview</CardTitle>
                <CardDescription>
                  Preview how your assistant will look and behave with the current settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-8 md:grid-cols-2">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Configuration Summary</h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="font-medium">Name:</div>
                        <div>{form.watch('name')}</div>

                        <div className="font-medium">Model:</div>
                        <div>{form.watch('model')}</div>

                        <div className="font-medium">Memory:</div>
                        <div>{form.watch('enableMemory') ? 'Enabled' : 'Disabled'}</div>

                        <div className="font-medium">Human-in-the-Loop:</div>
                        <div>{form.watch('enableHumanInTheLoop') ? 'Enabled' : 'Disabled'}</div>

                        <div className="font-medium">Web Research:</div>
                        <div>{form.watch('enableWebResearch') ? 'Enabled' : 'Disabled'}</div>

                        <div className="font-medium">Max Steps:</div>
                        <div>{form.watch('maxSteps')}</div>

                        <div className="font-medium">Theme:</div>
                        <div className="capitalize">{form.watch('theme')}</div>

                        <div className="font-medium">Avatar Style:</div>
                        <div className="capitalize">{form.watch('avatarStyle')}</div>

                        <div className="font-medium">Bubble Style:</div>
                        <div className="capitalize">{form.watch('bubbleStyle')}</div>
                      </div>

                      <div className="pt-4 border-t">
                        <h4 className="text-sm font-medium mb-2">Enabled Tool Categories:</h4>
                        <div className="flex flex-wrap gap-2">
                          {Object.entries(form.watch('enabledTools')).map(([category, enabled]) =>
                            enabled ? (
                              <div key={category} className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                                {category}
                              </div>
                            ) : null
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Chat Preview</h3>
                    <AssistantPreview
                      name={form.watch('name')}
                      greeting={form.watch('greeting')}
                      avatarStyle={form.watch('avatarStyle')}
                      customAvatarUrl={form.watch('customAvatarUrl')}
                      bubbleStyle={form.watch('bubbleStyle')}
                      theme={form.watch('theme')}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="mt-6 flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/dashboard/assistant')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Configuration
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}

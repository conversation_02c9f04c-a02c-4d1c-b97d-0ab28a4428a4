'use client';

import { useMemo } from 'react';

interface SimpleMarkdownProps {
  children: string;
  className?: string;
}

export function SimpleMarkdown({ children, className = '' }: SimpleMarkdownProps) {
  const formattedText = useMemo(() => {
    if (!children) return '';

    let text = children;

    // Process code blocks first to avoid conflicts with other formatting
    const codeBlocks: string[] = [];
    text = text.replace(/```([\s\S]*?)```/g, (_, code) => {
      const id = `__CODE_BLOCK_${codeBlocks.length}__`;
      codeBlocks.push(code);
      return id;
    });

    // Process inline code
    const inlineCodes: string[] = [];
    text = text.replace(/`([^`]+)`/g, (_, code) => {
      const id = `__INLINE_CODE_${inlineCodes.length}__`;
      inlineCodes.push(code);
      return id;
    });

    // Process the rest of the markdown
    text = text
      // Bold
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Links
      .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-primary underline hover:text-primary/80">$1</a>')
      // Headers
      .replace(/^### (.*?)$/gm, '<h3 class="text-lg font-bold mt-3 mb-1">$1</h3>')
      .replace(/^## (.*?)$/gm, '<h2 class="text-xl font-bold mt-4 mb-2">$1</h2>')
      .replace(/^# (.*?)$/gm, '<h1 class="text-2xl font-bold mt-5 mb-3">$1</h1>')
      // Horizontal rule
      .replace(/^---$/gm, '<hr class="my-3 border-t border-border" />')
      // Blockquote
      .replace(/^> (.*?)$/gm, '<blockquote class="pl-3 border-l-4 border-muted-foreground/30 italic text-muted-foreground">$1</blockquote>');

    // Process lists - need to handle nested lists and proper HTML structure
    const listItemRegex = /^(\s*)(?:[*\-+]|\d+\.) (.+)$/gm;
    const listItems = text.match(listItemRegex);

    if (listItems) {
      let inList = false;
      let listType = '';
      let lastIndent = -1;

      text = text.replace(listItemRegex, (match, indent, content) => {
        const isOrdered = /^\d+\./.test(match.trim());
        const currentListType = isOrdered ? 'ol' : 'ul';
        const indentLevel = indent.length;

        let result = '';

        // Start a new list or continue the current one
        if (!inList) {
          result = `<${currentListType} class="my-2 ${isOrdered ? 'list-decimal' : 'list-disc'} pl-5">`;
          inList = true;
          listType = currentListType;
          lastIndent = indentLevel;
        } else if (indentLevel > lastIndent) {
          // Start a nested list
          result = `<${currentListType} class="pl-4 ${isOrdered ? 'list-decimal' : 'list-disc'}">`;
          listType = currentListType;
          lastIndent = indentLevel;
        } else if (indentLevel < lastIndent) {
          // End the current list and start a new one at a higher level
          result = `</${listType}>`;
          result += `<${currentListType} class="my-2 ${isOrdered ? 'list-decimal' : 'list-disc'} pl-5">`;
          listType = currentListType;
          lastIndent = indentLevel;
        }

        // Add the list item
        result += `<li>${content}</li>`;

        return result;
      });

      // Close any open list
      if (inList) {
        text += `</${listType}>`;
      }
    }

    // Restore code blocks with proper formatting
    codeBlocks.forEach((code, index) => {
      const language = code.split('\n')[0].trim() || 'plaintext';
      const codeContent = language ? code.substring(language.length).trim() : code;

      const formattedCode = `<pre class="bg-muted p-3 rounded-md overflow-x-auto my-2"><code class="text-xs">${codeContent
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;')}</code></pre>`;

      text = text.replace(`__CODE_BLOCK_${index}__`, formattedCode);
    });

    // Restore inline code
    inlineCodes.forEach((code, index) => {
      const formattedCode = `<code class="bg-muted px-1.5 py-0.5 rounded text-xs">${code
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;')}</code>`;

      text = text.replace(`__INLINE_CODE_${index}__`, formattedCode);
    });

    // Handle paragraphs and line breaks
    text = text
      .replace(/\n\n/g, '</p><p class="mb-2">')
      .replace(/\n/g, '<br />');

    return text;
  }, [children]);

  return (
    <div
      className={`markdown prose-sm dark:prose-invert max-w-none ${className}`}
      dangerouslySetInnerHTML={{ __html: `<p class="mb-2">${formattedText}</p>` }}
    />
  );
}

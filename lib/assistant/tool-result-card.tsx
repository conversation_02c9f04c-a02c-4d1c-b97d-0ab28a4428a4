"use client"

import React from 'react'
import { Card } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Bot, ExternalLink, Eye, FileText, Calendar, CheckCircle2 } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface ToolResultCardProps {
  toolName: string
  args: any
  result: any
}

export function ToolResultCard({ toolName, args, result }: ToolResultCardProps) {
  const router = useRouter()
  
  const handleNavigate = (path: string) => {
    router.push(path)
  }
  
  // Get the appropriate icon based on the tool name
  const getToolIcon = () => {
    switch (toolName) {
      case 'navigateTo':
        return <ExternalLink className="h-3 w-3" />
      case 'createDocument':
      case 'updateDocument':
      case 'getDocumentDetails':
        return <FileText className="h-3 w-3" />
      case 'createMeeting':
      case 'updateMeeting':
      case 'getMeetingDetails':
        return <Calendar className="h-3 w-3" />
      case 'createTask':
      case 'updateTask':
      case 'getTaskDetails':
        return <CheckCircle2 className="h-3 w-3" />
      default:
        return <Bot className="h-3 w-3" />
    }
  }
  
  // Get the appropriate title based on the tool name
  const getToolTitle = () => {
    switch (toolName) {
      case 'navigateTo':
        return 'Navigation requested'
      case 'createDocument':
        return 'Document created'
      case 'updateDocument':
        return 'Document updated'
      case 'getDocumentDetails':
        return 'Document details'
      case 'createMeeting':
        return 'Meeting created'
      case 'updateMeeting':
        return 'Meeting updated'
      case 'getMeetingDetails':
        return 'Meeting details'
      case 'createTask':
        return 'Task created'
      case 'updateTask':
        return 'Task updated'
      case 'getTaskDetails':
        return 'Task details'
      default:
        return `${toolName} result`
    }
  }
  
  // Get the appropriate action button based on the tool name and result
  const getActionButton = () => {
    // Navigation tool
    if (toolName === 'navigateTo' && result?.success) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(args.path)}
        >
          Navigate Now
        </Button>
      )
    }
    
    // Document tools
    if (toolName === 'createDocument' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/documents/${result.id}`)}
        >
          View Document
        </Button>
      )
    }
    
    if (toolName === 'updateDocument' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/documents/${result.id}`)}
        >
          View Document
        </Button>
      )
    }
    
    if (toolName === 'getDocumentDetails' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/documents/${result.id}`)}
        >
          View Document
        </Button>
      )
    }
    
    // Meeting tools
    if (toolName === 'createMeeting' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/meetings/${result.id}`)}
        >
          View Meeting
        </Button>
      )
    }
    
    if (toolName === 'updateMeeting' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/meetings/${result.id}`)}
        >
          View Meeting
        </Button>
      )
    }
    
    if (toolName === 'getMeetingDetails' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/meetings/${result.id}`)}
        >
          View Meeting
        </Button>
      )
    }
    
    if (toolName === 'joinMeeting' && result?.url) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(result.url)}
        >
          Join Meeting
        </Button>
      )
    }
    
    // Task tools
    if (toolName === 'createTask' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/tasks/${result.id}`)}
        >
          View Task
        </Button>
      )
    }
    
    if (toolName === 'updateTask' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/tasks/${result.id}`)}
        >
          View Task
        </Button>
      )
    }
    
    if (toolName === 'getTaskDetails' && result?.id) {
      return (
        <Button
          size="sm"
          variant="default"
          className="h-7 text-xs"
          onClick={() => handleNavigate(`/dashboard/tasks/${result.id}`)}
        >
          View Task
        </Button>
      )
    }
    
    return null
  }
  
  // Format the result for display
  const formatResult = () => {
    // If the result is an error, display the error message
    if (result?.error) {
      return JSON.stringify({ error: result.error }, null, 2)
    }
    
    // For navigation, just show the path
    if (toolName === 'navigateTo') {
      return JSON.stringify(args, null, 2)
    }
    
    // For other tools, show a simplified version of the result
    const simplifiedResult = { ...result }
    
    // Remove large text fields to keep the display clean
    if (simplifiedResult.content && simplifiedResult.content.length > 100) {
      simplifiedResult.content = simplifiedResult.content.substring(0, 100) + '...'
    }
    
    return JSON.stringify(simplifiedResult, null, 2)
  }
  
  return (
    <Card className="p-3 bg-blue-50/70 dark:bg-blue-950/70 border border-blue-200/70 dark:border-blue-800/70 text-xs rounded-lg shadow-sm">
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-1.5 text-blue-800 dark:text-blue-300 font-medium">
          <div className="bg-blue-200 dark:bg-blue-800 p-1 rounded-md">
            {getToolIcon()}
          </div>
          {getToolTitle()}
        </div>
        <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-md text-[11px] font-mono border border-blue-200 dark:border-blue-800/50 overflow-auto max-h-40">
          {formatResult()}
        </div>
        <div className="flex justify-end mt-1">
          {getActionButton()}
        </div>
      </div>
    </Card>
  )
}

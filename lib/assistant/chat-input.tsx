'use client';

import { useState, FormEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { SendHorizontal, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
  disabled?: boolean;
}

export function ChatInput({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  disabled = false,
}: ChatInputProps) {
  const [rows, setRows] = useState(1);

  // Handle textarea resize based on content
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);
    
    // Reset rows to 1 to measure actual scrollHeight
    setRows(1);
    
    // Calculate rows based on scrollHeight (with a max of 5 rows)
    const lineHeight = 24; // Approximate line height in pixels
    const newRows = Math.min(5, Math.ceil(e.target.scrollHeight / lineHeight));
    
    setRows(newRows);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="flex items-end gap-2 border-t bg-background p-4 sticky bottom-0 z-10"
    >
      <Textarea
        value={input}
        onChange={handleTextareaChange}
        placeholder="Ask the assistant..."
        className={cn(
          "flex-1 resize-none overflow-hidden",
          rows > 1 && "pt-3"
        )}
        disabled={isLoading || disabled}
        rows={rows}
        maxLength={4000}
      />
      <Button
        type="submit"
        size="icon"
        disabled={isLoading || disabled || !input.trim()}
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <SendHorizontal className="h-4 w-4" />
        )}
        <span className="sr-only">Send message</span>
      </Button>
    </form>
  );
}

'use client';

import { useChat } from '@ai-sdk/react';
import { useEffect, useRef, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bot, SendHorizontal, Loader2, User, Sparkles, X, Check, ChevronDown, ChevronUp, RefreshCw, Trash2, Search, Globe, ExternalLink, Settings } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useSession } from 'next-auth/react';
import { getInitials, APPROVAL } from '@/lib/assistant/utils';
import { SimpleMarkdown } from './simple-markdown';
import { ToolResultCard } from './tool-result-card';
import { Card } from '@/components/ui/card';
import { Toolt<PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useAssistantConfig } from '@/hooks/use-assistant-config';

export function SidebarAssistant() {
  const { data: session, status: sessionStatus } = useSession();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [rows, setRows] = useState(1);
  const [showControls, setShowControls] = useState(false);
  const router = useRouter(); // Used by the ToolResultCard component
  const [agentMode, setAgentMode] = useState<string>('general');
  const [isThinking, setIsThinking] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState(false);

  // Only load the config if we have a session
  const { config, isLoading: isConfigLoading, error: configError } = useAssistantConfig();

  // Add a unique component ID for debugging
  const componentId = useRef(Math.random().toString(36).substring(2, 9));

  // Log component lifecycle for debugging
  useEffect(() => {
    console.log(`[${componentId.current}] SidebarAssistant mounted, session status: ${sessionStatus}`);

    return () => {
      console.log(`[${componentId.current}] SidebarAssistant unmounted`);
    };
  }, [sessionStatus]);

  // Set a timeout for loading to prevent infinite loading state
  useEffect(() => {
    if (isConfigLoading && sessionStatus === 'authenticated') {
      console.log(`[${componentId.current}] Setting loading timeout`);
      const timer = setTimeout(() => {
        setLoadingTimeout(true);
        console.log(`[${componentId.current}] Loading timeout triggered`);
      }, 5000); // 5 seconds timeout

      return () => {
        console.log(`[${componentId.current}] Clearing loading timeout`);
        clearTimeout(timer);
      };
    } else {
      setLoadingTimeout(false);
    }
  }, [isConfigLoading, sessionStatus]);

  // Detect agent mode from messages
  const detectAgentMode = useCallback((messages: any[]) => {
    // Look for system messages that indicate the agent mode
    for (const message of messages) {
      if (message.role === 'system' && message.content) {
        if (message.content.includes('document mode')) {
          return 'document';
        } else if (message.content.includes('meeting mode')) {
          return 'meeting';
        } else if (message.content.includes('task mode')) {
          return 'task';
        }
      }

      // Also check for assistant messages that might indicate mode changes
      if (message.role === 'assistant' && message.content) {
        if (message.content.includes('switching to document mode') ||
            message.content.includes('I\'m in document mode')) {
          return 'document';
        } else if (message.content.includes('switching to meeting mode') ||
                   message.content.includes('I\'m in meeting mode')) {
          return 'meeting';
        } else if (message.content.includes('switching to task mode') ||
                   message.content.includes('I\'m in task mode')) {
          return 'task';
        }
      }
    }

    return 'general';
  }, []);

  // Default greeting message if config is not loaded yet
  const defaultGreeting = {
    id: 'welcome',
    role: 'assistant' as const, // Use const assertion to fix type issue
    content: 'Hello! I\'m your AI assistant. How can I help you today?',
  };

  // Use config values or defaults
  const chatConfig = {
    maxSteps: config?.maxSteps || 10,
    initialMessage: config ? {
      id: 'welcome',
      role: 'assistant' as const, // Use const assertion to fix type issue
      content: config.greeting,
    } : defaultGreeting,
  };

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    error,
    addToolResult,
    reload,
    setMessages,
  } = useChat({
    api: '/api/assistant',
    id: 'sidebar-assistant',
    initialMessages: [chatConfig.initialMessage],
    maxSteps: chatConfig.maxSteps, // Use the configured maxSteps value
    onResponse: () => {
      // Set thinking state when response starts
      setIsThinking(true);
    },
    onFinish: () => {
      // Clear thinking state when response finishes
      setIsThinking(false);
    },
    // Handle client-side tools that can be automatically executed
    async onToolCall({ toolCall }) {
      // Set thinking state when a tool is called
      setIsThinking(true);

      try {
        // Handle navigation tool
        if (toolCall.toolName === 'navigateTo') {
          const args = toolCall.args as { path: string };
          if (args.path) {
            // Return success message but don't actually navigate - the UI will handle this
            router.push(args.path);
            return {
              success: true,
              message: `Navigation to ${args.path} will be handled on the client side.`,
            };
          }
        }

        // Handle web research tools on the client side if needed
        if (toolCall.toolName === 'webSearch') {
          const args = toolCall.args as { query: string, numResults?: number };
          if (args.query) {
            try {
              // Show loading toast
              toast.loading(`Searching for "${args.query}"...`, {
                id: `search-${Date.now()}`,
                duration: 10000
              });

              // Call the actual web search API
              const response = await fetch('/api/web-search', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  query: args.query,
                  numResults: args.numResults || 5
                }),
              });

              if (!response.ok) {
                throw new Error(`Search failed: ${response.status} ${response.statusText}`);
              }

              const data = await response.json();

              // Show success toast
              toast.success(`Found ${data.results?.length || 0} results for "${args.query}"`, {
                id: `search-${Date.now()}`,
                duration: 3000
              });

              return data;
            } catch (error) {
              console.error('Error performing web search:', error);

              // Show error toast
              toast.error(`Error searching for "${args.query}": ${error instanceof Error ? error.message : String(error)}`, {
                id: `search-error-${Date.now()}`,
                duration: 5000
              });

              return {
                error: `Error searching the web: ${error instanceof Error ? error.message : String(error)}`,
                message: 'Failed to search the web.',
                results: []
              };
            }
          }
        }

        // Handle web fetch tool on the client side
        if (toolCall.toolName === 'fetchWebPage') {
          const args = toolCall.args as { url: string };
          if (args.url) {
            try {
              // Show loading toast
              toast.loading(`Fetching page: ${args.url}`, {
                id: `fetch-${Date.now()}`,
                duration: 10000
              });

              // Call the web fetch API
              const response = await fetch('/api/web-fetch', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: args.url }),
              });

              if (!response.ok) {
                throw new Error(`Fetch failed: ${response.status} ${response.statusText}`);
              }

              const data = await response.json();

              // Show success toast
              toast.success(`Successfully fetched: ${data.title || args.url}`, {
                id: `fetch-${Date.now()}`,
                duration: 3000
              });

              return data;
            } catch (error) {
              console.error('Error fetching web page:', error);

              // Show error toast
              toast.error(`Error fetching ${args.url}: ${error instanceof Error ? error.message : String(error)}`, {
                id: `fetch-error-${Date.now()}`,
                duration: 5000
              });

              return {
                error: `Error fetching web page: ${error instanceof Error ? error.message : String(error)}`,
                message: 'Failed to fetch the web page.'
              };
            }
          }
        }

        // Handle summarizeWebContent tool
        if (toolCall.toolName === 'summarizeWebContent') {
          const args = toolCall.args as { url: string, maxLength?: number };
          if (args.url) {
            try {
              // First fetch the web page
              const fetchResponse = await fetch('/api/web-fetch', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: args.url }),
              });

              if (!fetchResponse.ok) {
                throw new Error(`Fetch failed: ${fetchResponse.status} ${fetchResponse.statusText}`);
              }

              const pageData = await fetchResponse.json();

              // Return the page data with a summary flag
              return {
                ...pageData,
                summary: true,
                message: `Content fetched for summarization. Please summarize the content in ${args.maxLength || 500} characters or less.`
              };
            } catch (error) {
              console.error('Error summarizing web content:', error);
              return {
                error: `Error summarizing web content: ${error instanceof Error ? error.message : String(error)}`,
                message: 'Failed to summarize the web page.'
              };
            }
          }
        }
      } catch (error) {
        console.error('Error in onToolCall:', error);
        return {
          error: `Error executing tool: ${error instanceof Error ? error.message : String(error)}`,
          message: 'Failed to execute the tool.'
        };
      } finally {
        // Clear thinking state when tool execution is complete
        setIsThinking(false);
      }

      // Return undefined for tools that should be handled on the server
      return undefined;
    }
  });

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }

    // Update agent mode based on messages
    const newMode = detectAgentMode(messages);
    if (newMode !== agentMode) {
      setAgentMode(newMode);
    }
  }, [messages, detectAgentMode, agentMode]);

  // Handle textarea resize based on content
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);

    // Reset rows to 1 to measure actual scrollHeight
    setRows(1);

    // Calculate rows based on scrollHeight (with a max of 3 rows)
    const lineHeight = 24; // Approximate line height in pixels
    const newRows = Math.min(3, Math.ceil(e.target.scrollHeight / lineHeight));

    setRows(newRows);
  };

  // No longer needed as we're using the ToolResultCard component
  // which handles its own state and navigation

  // Clear chat history
  const handleClearChat = () => {
    setMessages([
      {
        id: 'welcome-reset',
        role: 'assistant' as const,
        content: config?.greeting || 'Chat history cleared. How can I help you today?',
      },
    ]);
  };

  // Check if there's a pending tool call that requires confirmation
  const pendingToolCallConfirmation = messages.some(m =>
    m.parts?.some(
      part =>
        part.type === 'tool-invocation' &&
        part.toolInvocation.state === 'call'
    )
  );

  // Pass the navigate function to the ToolResultCard component
  // This is done automatically through the useRouter hook in the ToolResultCard component

  // Get bubble style from config
  const getBubbleStyle = () => {
    if (!config) return 'rounded-xl';

    switch (config.bubbleStyle) {
      case 'classic':
        return 'rounded-lg';
      case 'minimal':
        return 'rounded-sm';
      case 'modern':
      default:
        return 'rounded-xl';
    }
  };

  // Show a message if there's no session
  if (sessionStatus === 'loading') {
    return (
      <div className="flex h-full flex-col">
        <div className="flex items-center justify-between border-b p-4 bg-gradient-to-r from-primary/10 to-primary/5">
          <div className="flex items-center gap-2">
            <div className="bg-primary/90 text-primary-foreground p-1 rounded-md">
              <Bot className="h-4 w-4" />
            </div>
            <div className="flex flex-col">
              <span className="font-semibold text-sm">AI Assistant</span>
              <div className="flex items-center gap-2">
                <span className="text-[10px] text-muted-foreground flex items-center gap-1">
                  <Loader2 className="h-2.5 w-2.5 animate-spin" /> Loading session...
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  // Don't try to load config if there's no session
  if (sessionStatus === 'unauthenticated' || !session) {
    return (
      <div className="flex h-full flex-col">
        <div className="flex items-center justify-between border-b p-4 bg-gradient-to-r from-primary/10 to-primary/5">
          <div className="flex items-center gap-2">
            <div className="bg-primary/90 text-primary-foreground p-1 rounded-md">
              <Bot className="h-4 w-4" />
            </div>
            <div className="flex flex-col">
              <span className="font-semibold text-sm">AI Assistant</span>
            </div>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-sm text-muted-foreground max-w-xs text-center px-4">
            Please sign in to use the AI Assistant.
          </div>
        </div>
      </div>
    );
  }

  // Show loading state while config is loading
  if (isConfigLoading) {
    console.log(`[${componentId.current}] Showing loading state, timeout: ${loadingTimeout}`);
    return (
      <div className="flex h-full flex-col">
        <div className="flex items-center justify-between border-b p-4 bg-gradient-to-r from-primary/10 to-primary/5">
          <div className="flex items-center gap-2">
            <div className="bg-primary/90 text-primary-foreground p-1 rounded-md">
              <Bot className="h-4 w-4" />
            </div>
            <div className="flex flex-col">
              <span className="font-semibold text-sm">AI Assistant</span>
              <div className="flex items-center gap-2">
                <span className="text-[10px] text-muted-foreground flex items-center gap-1">
                  <Loader2 className="h-2.5 w-2.5 animate-spin" /> Loading configuration...
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex-1 flex flex-col items-center justify-center gap-3">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          {loadingTimeout && (
            <div className="text-xs text-muted-foreground max-w-xs text-center px-4">
              <p>Loading is taking longer than expected.</p>
              <p className="mt-1">The assistant will use default settings until configuration is loaded.</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // If there's a config error but we're not loading anymore, continue with default config
  if (configError && !isConfigLoading) {
    console.log(`[${componentId.current}] Config error: ${configError}`);
    toast.error("Failed to load assistant configuration. Using default settings.", {
      id: "config-error",
      duration: 5000
    });
  }

  return (
    <div className={cn(
      "flex h-full flex-col",
      config?.theme === 'dark' ? 'bg-slate-900 text-slate-100' : ''
    )}>
      <div className="flex items-center justify-between border-b p-4 bg-gradient-to-r from-primary/10 to-primary/5">
        <div className="flex items-center gap-2">
          <div className="bg-primary/90 text-primary-foreground p-1 rounded-md">
            <Bot className="h-4 w-4" />
          </div>
          <div className="flex flex-col">
            <span className="font-semibold text-sm">{config?.name || 'AI Assistant'}</span>
            <div className="flex items-center gap-2">
              <span className="text-[10px] text-muted-foreground flex items-center gap-1">
                <Sparkles className="h-2.5 w-2.5 text-yellow-500" /> Powered by {config?.model || 'GPT-4o'}
              </span>
              {agentMode !== 'general' && (
                <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-primary/10 text-primary font-medium">
                  {agentMode} mode
                </span>
              )}
              {isThinking && (
                <span className="text-[10px] px-1.5 py-0.5 rounded-full bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-300 font-medium flex items-center gap-1">
                  <Loader2 className="h-2 w-2 animate-spin" />
                  thinking
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full hover:bg-primary/10"
                  onClick={() => router.push('/dashboard/assistant/config')}
                >
                  <Settings className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="text-xs">
                Configure Assistant
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full hover:bg-primary/10"
                  onClick={() => setShowControls(!showControls)}
                >
                  {showControls ?
                    <ChevronUp className="h-3.5 w-3.5" /> :
                    <ChevronDown className="h-3.5 w-3.5" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="text-xs">
                {showControls ? 'Hide controls' : 'Show controls'}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {showControls && (
        <div className="flex items-center justify-between border-b px-4 py-2 bg-muted/30 animate-in fade-in slide-in-from-top duration-200">
          <div className="flex gap-2">
            <TooltipProvider>
              <Tooltip delayDuration={300}>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 px-2 text-xs gap-1 border-muted-foreground/20 hover:bg-background"
                    onClick={handleClearChat}
                  >
                    <Trash2 className="h-3 w-3" />
                    Clear
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="text-xs">Clear conversation history</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip delayDuration={300}>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 px-2 text-xs gap-1 border-muted-foreground/20 hover:bg-background"
                    onClick={() => reload()}
                  >
                    <RefreshCw className="h-3 w-3" />
                    Regenerate
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="text-xs">Regenerate last response</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      )}

      <ScrollArea className="flex-1 px-1">
        <div className="flex flex-col py-4 px-3 divide-y divide-border/30">
          {messages.map((message, index) => (
            <div
              key={message.id}
              className={cn(
                'flex gap-2 group py-4 first:pt-0 last:pb-0',
                message.role === 'user' ? 'justify-end' : 'justify-start',
                message.role !== 'user' && 'pl-1',
                index === 0 && 'animate-in fade-in-50 slide-in-from-bottom-3 duration-300',
                index > 0 && 'animate-in fade-in-50 slide-in-from-bottom-2 duration-200'
              )}
            >
              {message.role !== 'user' && (
                <div className={cn(
                  "flex-shrink-0 flex items-center justify-center",
                  config?.avatarStyle === 'minimal' ? 'h-6 w-6' : 'h-7 w-7'
                )}>
                  {config?.avatarStyle === 'custom' && config?.customAvatarUrl ? (
                    <Avatar className="h-5 w-5">
                      <AvatarImage src={config.customAvatarUrl} alt="Assistant" />
                      <AvatarFallback className="bg-primary/10 rounded-md">
                        <Bot size={12} className="text-primary" />
                      </AvatarFallback>
                    </Avatar>
                  ) : (
                    <div className={cn(
                      "bg-primary/10 rounded-md flex items-center justify-center",
                      config?.avatarStyle === 'minimal' ? 'h-4 w-4' : 'h-5 w-5'
                    )}>
                      <Bot size={config?.avatarStyle === 'minimal' ? 10 : 12} className="text-primary" />
                    </div>
                  )}
                </div>
              )}

              <div className="flex flex-col gap-2 max-w-[85%]">
                {message.parts?.map((part, i) => {
                  if (part.type === 'text') {
                    return (
                      <div
                        key={`text-${i}`}
                        className={cn(
                          'text-sm',
                          message.role === 'user'
                            ? `${getBubbleStyle()} px-3.5 py-2.5 shadow-sm bg-gradient-to-br from-primary to-primary/90 text-primary-foreground`
                            : 'text-foreground'
                        )}
                      >
                        <SimpleMarkdown>
                          {part.text}
                        </SimpleMarkdown>
                      </div>
                    );
                  } else if (part.type === 'step-start') {
                    // Show step boundaries for multi-step execution
                    return i > 0 ? (
                      <div key={`step-${i}`} className="my-2 border-t border-dashed border-gray-200 dark:border-gray-800 pt-2">
                        <div className="flex items-center gap-1.5 text-xs text-gray-500 dark:text-gray-400">
                          <RefreshCw className="h-3 w-3" />
                          <span>Next step</span>
                        </div>
                      </div>
                    ) : null;
                  } else if (part.type === 'tool-invocation') {
                    const { toolInvocation } = part;
                    // Type assertion to help TypeScript understand the structure
                    const typedToolInvocation = toolInvocation as {
                      toolCallId: string;
                      toolName?: string;
                      state: 'partial-call' | 'call' | 'result';
                      args: any;
                      result?: any;
                    };
                    const toolCallId = typedToolInvocation.toolCallId;

                    // Handle partial tool calls (streaming)
                    if (typedToolInvocation.state === 'partial-call') {
                      return (
                        <Card key={`partial-tool-${toolCallId}-${i}`} className="p-3 bg-blue-50/70 dark:bg-blue-950/70 text-xs rounded-lg shadow-sm border border-blue-200/70 dark:border-blue-800/70">
                          <div className="flex items-center gap-2">
                            <div className="bg-blue-200 dark:bg-blue-800 p-1.5 rounded-full">
                              <Loader2 className="h-3 w-3 animate-spin text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <span className="font-medium">Thinking</span>
                              {typedToolInvocation.toolName && (
                                <span className="text-muted-foreground"> • {typedToolInvocation.toolName}</span>
                              )}
                            </div>
                          </div>
                          {typedToolInvocation.args && (
                            <div className="mt-2 bg-blue-100/50 dark:bg-blue-900/50 p-2 rounded-md text-[11px] font-mono border border-blue-200/50 dark:border-blue-800/30">
                              {JSON.stringify(typedToolInvocation.args, null, 2)}
                            </div>
                          )}
                        </Card>
                      );
                    }

                    // Handle tool calls that require confirmation
                    if (typedToolInvocation.state === 'call') {
                      // Special handling for web research tools
                      if (typedToolInvocation.toolName === 'webSearch' ||
                          typedToolInvocation.toolName === 'fetchWebPage' ||
                          typedToolInvocation.toolName === 'summarizeWebContent' ||
                          typedToolInvocation.toolName === 'extractDataFromWebPage') {
                        return (
                          <Card key={`tool-${toolCallId}`} className="p-3 bg-indigo-50/70 dark:bg-indigo-950/70 border border-indigo-200/70 dark:border-indigo-800/70 text-xs rounded-lg shadow-sm">
                            <div className="flex flex-col gap-2">
                              <div className="flex items-center gap-1.5 text-indigo-800 dark:text-indigo-300 font-medium">
                                <div className="bg-indigo-200 dark:bg-indigo-800 p-1 rounded-md">
                                  <Bot className="h-3 w-3" />
                                </div>
                                Web Research: {typedToolInvocation.toolName}
                              </div>
                              <div className="bg-indigo-100 dark:bg-indigo-900 p-2 rounded-md text-[11px] font-mono border border-indigo-200 dark:border-indigo-800/50">
                                {JSON.stringify(typedToolInvocation.args, null, 2)}
                              </div>
                              <div className="flex gap-2 mt-1 justify-end">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-7 text-xs border-red-600 text-red-600 hover:bg-red-50 hover:text-red-700 rounded-full px-3"
                                  onClick={() =>
                                    addToolResult({
                                      toolCallId,
                                      result: APPROVAL.NO,
                                    })
                                  }
                                >
                                  <X className="mr-1 h-3 w-3" />
                                  Deny
                                </Button>
                                <Button
                                  size="sm"
                                  variant="default"
                                  className="h-7 text-xs bg-indigo-600 hover:bg-indigo-700 rounded-full px-3"
                                  onClick={() =>
                                    addToolResult({
                                      toolCallId,
                                      result: APPROVAL.YES,
                                    })
                                  }
                                >
                                  <Check className="mr-1 h-3 w-3" />
                                  Approve
                                </Button>
                              </div>
                            </div>
                          </Card>
                        );
                      }

                      // Default confirmation UI for other tools
                      return (
                        <Card key={`tool-${toolCallId}`} className="p-3 bg-amber-50/70 dark:bg-amber-950/70 border border-amber-200/70 dark:border-amber-800/70 text-xs rounded-lg shadow-sm">
                          <div className="flex flex-col gap-2">
                            <div className="flex items-center gap-1.5 text-amber-800 dark:text-amber-300 font-medium">
                              <div className="bg-amber-200 dark:bg-amber-800 p-1 rounded-md">
                                <Bot className="h-3 w-3" />
                              </div>
                              Using {typedToolInvocation.toolName}
                            </div>
                            <div className="bg-amber-100 dark:bg-amber-900 p-2 rounded-md text-[11px] font-mono border border-amber-200 dark:border-amber-800/50">
                              {JSON.stringify(typedToolInvocation.args, null, 2)}
                            </div>
                            <div className="flex gap-2 mt-1 justify-end">
                              <Button
                                size="sm"
                                variant="outline"
                                className="h-7 text-xs border-red-600 text-red-600 hover:bg-red-50 hover:text-red-700 rounded-full px-3"
                                onClick={() =>
                                  addToolResult({
                                    toolCallId,
                                    result: APPROVAL.NO,
                                  })
                                }
                              >
                                <X className="mr-1 h-3 w-3" />
                                Deny
                              </Button>
                              <Button
                                size="sm"
                                variant="default"
                                className="h-7 text-xs bg-green-600 hover:bg-green-700 rounded-full px-3"
                                onClick={() =>
                                  addToolResult({
                                    toolCallId,
                                    result: APPROVAL.YES,
                                  })
                                }
                              >
                                <Check className="mr-1 h-3 w-3" />
                                Approve
                              </Button>
                            </div>
                          </div>
                        </Card>
                      );
                    }

                    // Handle tool results with ToolResultCard
                    if (typedToolInvocation.state === 'result') {
                      // Special handling for web research tool results
                      if (typedToolInvocation.toolName === 'fetchWebPage') {
                        return (
                          <Card key={`tool-result-${toolCallId}`} className="p-3 bg-blue-50/70 dark:bg-blue-950/70 border border-blue-200/70 dark:border-blue-800/70 text-xs rounded-lg shadow-sm">
                            <div className="flex flex-col gap-2">
                              <div className="flex items-center gap-1.5 text-blue-800 dark:text-blue-300 font-medium">
                                <div className="bg-blue-200 dark:bg-blue-800 p-1 rounded-md">
                                  <Globe className="h-3 w-3" />
                                </div>
                                Web Page Content
                                {typedToolInvocation.result?.url && (
                                  <a
                                    href={typedToolInvocation.result.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="ml-auto text-[10px] text-blue-600 hover:underline flex items-center gap-0.5"
                                  >
                                    <ExternalLink className="h-2.5 w-2.5" />
                                    Visit
                                  </a>
                                )}
                              </div>
                              <div className="bg-white dark:bg-blue-900/50 p-2 rounded-md text-xs border border-blue-200 dark:border-blue-800/50">
                                <div className="flex items-start gap-2 mb-2 pb-2 border-b border-blue-100 dark:border-blue-800/30">
                                  {typedToolInvocation.result?.metadata?.favicon && (
                                    <img
                                      src={typedToolInvocation.result.metadata.favicon}
                                      alt=""
                                      className="w-4 h-4 mt-0.5 rounded-sm flex-shrink-0"
                                      onError={(e) => {
                                        (e.target as HTMLImageElement).style.display = 'none';
                                      }}
                                    />
                                  )}
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium text-blue-700 dark:text-blue-300 truncate">
                                      {typedToolInvocation.result?.title || 'Web Page'}
                                    </div>
                                    <div className="text-[10px] text-blue-600/70 dark:text-blue-400/70 truncate">
                                      {typedToolInvocation.result?.url}
                                    </div>
                                    {typedToolInvocation.result?.description && (
                                      <div className="text-[10px] text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                                        {typedToolInvocation.result.description}
                                      </div>
                                    )}
                                  </div>
                                </div>
                                {typedToolInvocation.result?.screenshot && (
                                  <div className="mb-2">
                                    <img
                                      src={typedToolInvocation.result.screenshot}
                                      alt="Page screenshot"
                                      className="w-full h-auto rounded-md border border-blue-200 dark:border-blue-800/50 shadow-sm"
                                    />
                                  </div>
                                )}
                                <div className="max-h-[200px] overflow-y-auto text-[11px] text-gray-700 dark:text-gray-200 pr-1">
                                  {typedToolInvocation.result?.content ? (
                                    <div className="whitespace-pre-line">
                                      {typedToolInvocation.result.content.substring(0, 1500)}
                                      {typedToolInvocation.result.content.length > 1500 && (
                                        <span className="text-blue-600 dark:text-blue-400"> ... (content truncated)</span>
                                      )}
                                    </div>
                                  ) : (
                                    <div className="italic text-gray-500">No content available</div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </Card>
                        );
                      } else if (typedToolInvocation.toolName === 'webSearch') {
                        return (
                          <Card key={`tool-result-${toolCallId}`} className="p-3 bg-indigo-50/70 dark:bg-indigo-950/70 border border-indigo-200/70 dark:border-indigo-800/70 text-xs rounded-lg shadow-sm">
                            <div className="flex flex-col gap-2">
                              <div className="flex items-center gap-1.5 text-indigo-800 dark:text-indigo-300 font-medium">
                                <div className="bg-indigo-200 dark:bg-indigo-800 p-1 rounded-md">
                                  <Search className="h-3 w-3" />
                                </div>
                                Web Search Results
                                {typedToolInvocation.result?.formattedTotalResults && (
                                  <span className="text-xs text-indigo-600/70 dark:text-indigo-400/70 ml-auto">
                                    {typedToolInvocation.result.formattedTotalResults} results
                                  </span>
                                )}
                              </div>
                              <div className="bg-white dark:bg-indigo-900/50 p-2 rounded-md text-xs border border-indigo-200 dark:border-indigo-800/50">
                                <div className="font-medium mb-2 flex items-center">
                                  <span className="mr-1">Query:</span>
                                  <span className="text-indigo-700 dark:text-indigo-300">{typedToolInvocation.args.query}</span>
                                  {typedToolInvocation.result?.searchTime && (
                                    <span className="text-[10px] text-indigo-600/50 dark:text-indigo-400/50 ml-auto">
                                      {typedToolInvocation.result.searchTime.toFixed(2)}s
                                    </span>
                                  )}
                                </div>
                                {Array.isArray(typedToolInvocation.result?.results) && (
                                  <div className="flex flex-col gap-2 max-h-[300px] overflow-y-auto pr-1">
                                    {typedToolInvocation.result.results.map((result: any, idx: number) => (
                                      <div key={idx} className="border-t border-indigo-100 dark:border-indigo-800/30 pt-2 mt-1 first:border-0 first:pt-0 first:mt-0">
                                        <div className="flex items-start gap-2">
                                          {result.favicon && (
                                            <img
                                              src={result.favicon}
                                              alt=""
                                              className="w-4 h-4 mt-0.5 rounded-sm flex-shrink-0"
                                              onError={(e) => {
                                                (e.target as HTMLImageElement).style.display = 'none';
                                              }}
                                            />
                                          )}
                                          <div className="flex-1 min-w-0">
                                            <div className="font-medium text-indigo-700 dark:text-indigo-300 truncate">{result.title}</div>
                                            <div className="text-[10px] text-indigo-600/70 dark:text-indigo-400/70 mb-1 truncate">
                                              {result.url}
                                            </div>
                                            <div className="text-[11px] text-gray-600 dark:text-gray-300 line-clamp-3">
                                              {result.snippet}
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>
                          </Card>
                        );
                      }

                      // Default tool result card for other tools
                      return (
                        <ToolResultCard
                          key={`tool-${toolCallId}`}
                          toolName={typedToolInvocation.toolName || ''}
                          args={typedToolInvocation.args}
                          result={typedToolInvocation.result}
                        />
                      );
                    }

                    // Handle other tool invocation states
                    return (
                      <Card key={`tool-${toolCallId}`} className="p-3 bg-gray-50/70 dark:bg-gray-900/70 text-xs rounded-lg shadow-sm border border-gray-200/70 dark:border-gray-800/70">
                        <div className="flex items-center gap-2">
                          <div className="bg-gray-200 dark:bg-gray-800 p-1.5 rounded-full">
                            <Loader2 className="h-3 w-3 animate-spin text-primary" />
                          </div>
                          <div>
                            <span className="font-medium">Processing</span>
                            {typedToolInvocation.toolName && (
                              <span className="text-muted-foreground"> • {typedToolInvocation.toolName}</span>
                            )}
                          </div>
                        </div>
                      </Card>
                    );
                  }
                  return null;
                })}

                {message.content && !message.parts && (
                  <div
                    className={cn(
                      'text-sm',
                      message.role === 'user'
                        ? `${getBubbleStyle()} px-3.5 py-2.5 shadow-sm bg-gradient-to-br from-primary to-primary/90 text-primary-foreground`
                        : 'text-foreground'
                    )}
                  >
                    <SimpleMarkdown>
                      {message.content}
                    </SimpleMarkdown>
                  </div>
                )}
              </div>

              {message.role === 'user' && (
                <Avatar className={cn(
                  "flex-shrink-0 ring-1 ring-primary/20",
                  config?.avatarStyle === 'minimal' ? 'h-6 w-6' : 'h-7 w-7'
                )}>
                  <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || 'User'} />
                  <AvatarFallback className={cn(
                    "bg-secondary/20 text-secondary-foreground",
                    config?.avatarStyle === 'minimal' ? 'text-[10px]' : 'text-xs'
                  )}>
                    {session?.user?.name ? getInitials(session.user.name) : <User size={config?.avatarStyle === 'minimal' ? 10 : 12} />}
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {error && (
        <div className="px-4 py-2 text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-950 border-t border-red-200 dark:border-red-800 animate-in fade-in slide-in-from-top duration-300">
          <div className="flex items-center gap-2">
            <X className="h-3.5 w-3.5 text-red-500" />
            <span className="font-medium">Error:</span> {error.message || 'Something went wrong. Please try again.'}
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="border-t p-4 bg-background/80 backdrop-blur-sm">
        <div className="flex gap-2 items-end">
          <div className="relative flex-1">
            <Textarea
              value={input}
              onChange={handleTextareaChange}
              placeholder="Ask the AI assistant..."
              className="min-h-0 resize-none text-sm pr-2 border-primary/20 focus-visible:ring-primary/30 rounded-xl"
              rows={rows}
              disabled={(status === 'streaming' || status === 'submitted') || pendingToolCallConfirmation}
            />
            {((status === 'streaming' || status === 'submitted') || pendingToolCallConfirmation) && (
              <div className="absolute inset-0 bg-background/50 backdrop-blur-[1px] flex items-center justify-center rounded-xl">
                <Loader2 className="h-4 w-4 animate-spin text-primary" />
              </div>
            )}
          </div>
          <Button
            type="submit"
            size="icon"
            className="h-9 w-9 rounded-full bg-primary hover:bg-primary/90 shadow-sm"
            disabled={(status === 'streaming' || status === 'submitted') || pendingToolCallConfirmation || !input.trim()}
          >
            {(status === 'streaming' || status === 'submitted') ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <SendHorizontal className="h-4 w-4" />
            )}
            <span className="sr-only">Send message</span>
          </Button>
        </div>
      </form>
    </div>
  );
}
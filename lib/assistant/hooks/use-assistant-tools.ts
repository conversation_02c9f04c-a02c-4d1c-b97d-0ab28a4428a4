// Hook for managing assistant tools
import { useCallback, useEffect, useState } from 'react';
import { useAssistantStore } from '../store';
import { ToolDefinition, ToolExecution, ToolResult } from '../types/tools';
import { 
  validateToolParameters, 
  createToolExecution, 
  updateToolExecution,
  getToolsByCategory,
  getHighRiskTools,
  getToolsRequiringConfirmation 
} from '../utils/tool-utils';
import { tools as defaultTools } from '../client-tools';

export function useAssistantTools() {
  const config = useAssistantStore((state) => state.config);
  const [availableTools, setAvailableTools] = useState<ToolDefinition[]>(defaultTools);
  const [toolExecutions, setToolExecutions] = useState<ToolExecution[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Load available tools from API
  useEffect(() => {
    const loadTools = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/assistant/tools');
        if (response.ok) {
          const data = await response.json();
          if (data.tools && Array.isArray(data.tools)) {
            setAvailableTools(data.tools);
          }
        }
      } catch (err) {
        console.error('Failed to load tools:', err);
        setError('Failed to load tools');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadTools();
  }, []);
  
  // Get enabled tools based on configuration
  const enabledTools = availableTools.filter(tool => {
    const isEnabled = config?.enabledTools?.[tool.name];
    return isEnabled !== false; // Default to enabled if not specified
  });
  
  // Get tools requiring confirmation
  const toolsRequiringConfirmation = availableTools
    .filter(tool => {
      const requiresConfirmation = config?.toolsRequiringConfirmation?.[tool.name];
      return requiresConfirmation ?? tool.requiresConfirmation;
    })
    .map(tool => tool.name);
  
  // Execute a tool
  const executeTool = useCallback(async (
    toolName: string,
    parameters: Record<string, any>,
    userId: string = 'anonymous',
    sessionId: string = 'default'
  ): Promise<ToolResult> => {
    const tool = availableTools.find(t => t.name === toolName);
    if (!tool) {
      throw new Error(`Tool '${toolName}' not found`);
    }
    
    // Check if tool is enabled
    if (!enabledTools.some(t => t.name === toolName)) {
      throw new Error(`Tool '${toolName}' is not enabled`);
    }
    
    // Validate parameters
    const validation = validateToolParameters(parameters, tool);
    if (!validation.isValid) {
      throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
    }
    
    // Create execution record
    const execution = createToolExecution(toolName, parameters, userId, sessionId);
    setToolExecutions(prev => [...prev, execution]);
    
    try {
      // Update execution status to running
      const runningExecution = updateToolExecution(execution, { status: 'running' });
      setToolExecutions(prev => 
        prev.map(e => e.id === execution.id ? runningExecution : e)
      );
      
      // Execute the tool
      let result: ToolResult;
      
      if (tool.executionMode === 'client') {
        // Execute client-side tool
        result = await executeClientTool(toolName, parameters);
      } else {
        // Execute server-side tool
        result = await executeServerTool(toolName, parameters);
      }
      
      // Update execution with result
      const completedExecution = updateToolExecution(runningExecution, {
        status: 'completed',
        result,
      });
      setToolExecutions(prev =>
        prev.map(e => e.id === execution.id ? completedExecution : e)
      );
      
      return result;
    } catch (error) {
      // Update execution with error
      const failedExecution = updateToolExecution(execution, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      setToolExecutions(prev =>
        prev.map(e => e.id === execution.id ? failedExecution : e)
      );
      
      throw error;
    }
  }, [availableTools, enabledTools]);
  
  // Execute client-side tool
  const executeClientTool = useCallback(async (
    toolName: string,
    parameters: Record<string, any>
  ): Promise<ToolResult> => {
    switch (toolName) {
      case 'navigateTo':
        const { path, newTab } = parameters;
        if (newTab) {
          window.open(path, '_blank');
        } else {
          window.location.href = path;
        }
        return { success: true, message: `Navigated to ${path}` };
      
      case 'showNotification':
        const { title, message, type, duration } = parameters;
        // This would integrate with your notification system
        console.log(`Notification: ${title} - ${message}`);
        return { success: true, message: 'Notification shown' };
      
      case 'fetchWebPage':
        const { url } = parameters;
        try {
          const response = await fetch('/api/web-fetch', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ url }),
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          
          const data = await response.json();
          return { success: true, data, message: `Fetched content from ${url}` };
        } catch (error) {
          return { 
            success: false, 
            error: `Failed to fetch ${url}: ${error instanceof Error ? error.message : 'Unknown error'}` 
          };
        }
      
      default:
        throw new Error(`Client-side execution not implemented for tool: ${toolName}`);
    }
  }, []);
  
  // Execute server-side tool
  const executeServerTool = useCallback(async (
    toolName: string,
    parameters: Record<string, any>
  ): Promise<ToolResult> => {
    const response = await fetch('/api/assistant/tools/execute', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ toolName, parameters }),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}`);
    }
    
    return await response.json();
  }, []);
  
  // Get tools by category
  const toolsByCategory = getToolsByCategory(enabledTools);
  
  // Get high-risk tools
  const highRiskTools = getHighRiskTools(enabledTools);
  
  // Get tool execution history
  const getExecutionHistory = useCallback((toolName?: string) => {
    if (toolName) {
      return toolExecutions.filter(e => e.toolName === toolName);
    }
    return toolExecutions;
  }, [toolExecutions]);
  
  // Clear execution history
  const clearExecutionHistory = useCallback(() => {
    setToolExecutions([]);
  }, []);
  
  return {
    availableTools,
    enabledTools,
    toolsRequiringConfirmation,
    toolsByCategory,
    highRiskTools,
    toolExecutions,
    isLoading,
    error,
    executeTool,
    getExecutionHistory,
    clearExecutionHistory,
  };
}

// Hook for tool permissions
export function useToolPermissions() {
  const [permissions, setPermissions] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    const loadPermissions = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/assistant/tools/permissions');
        if (response.ok) {
          const data = await response.json();
          setPermissions(data.permissions || {});
        }
      } catch (error) {
        console.error('Failed to load tool permissions:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPermissions();
  }, []);
  
  const hasPermission = useCallback((toolName: string) => {
    return permissions[toolName] !== false; // Default to allowed
  }, [permissions]);
  
  const requestPermission = useCallback(async (toolName: string) => {
    try {
      const response = await fetch('/api/assistant/tools/permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ toolName, action: 'request' }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setPermissions(prev => ({ ...prev, [toolName]: data.granted }));
        return data.granted;
      }
    } catch (error) {
      console.error('Failed to request permission:', error);
    }
    return false;
  }, []);
  
  return {
    permissions,
    isLoading,
    hasPermission,
    requestPermission,
  };
}

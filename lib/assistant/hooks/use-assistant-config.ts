// Dedicated hook for assistant configuration management
import { useCallback, useEffect, useState } from 'react';
import { useAssistantStore } from '../store';
import { AssistantConfig, UseAssistantConfigReturn } from '../types';
import { 
  validateAssistantConfig, 
  getDefaultAssistantConfig,
  sanitizeConfig,
  exportConfig,
  importConfig 
} from '../utils/config-utils';

export function useAssistantConfig(): UseAssistantConfigReturn {
  const config = useAssistantStore((state) => state.config);
  const isLoading = useAssistantStore((state) => state.isLoading);
  const error = useAssistantStore((state) => state.error);
  const setConfig = useAssistantStore((state) => state.setConfig);
  const setLoading = useAssistantStore((state) => state.setLoading);
  const setError = useAssistantStore((state) => state.setError);
  
  // Load configuration on mount
  useEffect(() => {
    const loadConfig = async () => {
      if (config) return; // Already loaded
      
      setLoading(true);
      try {
        // Try to load from API
        const response = await fetch('/api/assistant/config');
        if (response.ok) {
          const data = await response.json();
          const loadedConfig = data.config || getDefaultAssistantConfig();
          
          // Validate loaded config
          const validation = validateAssistantConfig(loadedConfig);
          if (validation.isValid) {
            setConfig(loadedConfig);
          } else {
            console.warn('Loaded config is invalid, using defaults:', validation.errors);
            setConfig(getDefaultAssistantConfig());
          }
        } else {
          // Use default config if API fails
          setConfig(getDefaultAssistantConfig());
        }
      } catch (err) {
        console.error('Failed to load assistant config:', err);
        setConfig(getDefaultAssistantConfig());
        setError('Failed to load configuration');
      } finally {
        setLoading(false);
      }
    };
    
    loadConfig();
  }, [config, setConfig, setLoading, setError]);
  
  const updateConfig = useCallback(async (updates: Partial<AssistantConfig>) => {
    if (!config) return;
    
    // Sanitize and merge updates
    const sanitizedUpdates = sanitizeConfig(updates);
    const newConfig = { ...config, ...sanitizedUpdates };
    
    // Validate the new configuration
    const validation = validateAssistantConfig(newConfig);
    if (!validation.isValid) {
      throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
    }
    
    setLoading(true);
    try {
      const response = await fetch('/api/assistant/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ config: newConfig }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to save configuration');
      }
      
      setConfig(newConfig);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update configuration';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [config, setConfig, setLoading, setError]);
  
  const resetConfig = useCallback(async () => {
    const defaultConfig = getDefaultAssistantConfig();
    await updateConfig(defaultConfig);
  }, [updateConfig]);
  
  return {
    config,
    isLoading,
    error,
    updateConfig,
    resetConfig,
  };
}

// Hook for configuration presets
export function useConfigPresets() {
  const [presets, setPresets] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { updateConfig } = useAssistantConfig();
  
  useEffect(() => {
    const loadPresets = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/assistant/config/presets');
        if (response.ok) {
          const data = await response.json();
          setPresets(data.presets || []);
        }
      } catch (error) {
        console.error('Failed to load presets:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPresets();
  }, []);
  
  const applyPreset = useCallback(async (presetId: string) => {
    const preset = presets.find(p => p.id === presetId);
    if (!preset) {
      throw new Error('Preset not found');
    }
    
    await updateConfig(preset.config);
  }, [presets, updateConfig]);
  
  return {
    presets,
    isLoading,
    applyPreset,
  };
}

// Hook for configuration import/export
export function useConfigImportExport() {
  const { config, updateConfig } = useAssistantConfig();
  
  const exportConfiguration = useCallback(() => {
    if (!config) {
      throw new Error('No configuration to export');
    }
    
    const exportData = exportConfig(config);
    const blob = new Blob([exportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `assistant-config-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }, [config]);
  
  const importConfiguration = useCallback(async (file: File) => {
    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const content = e.target?.result as string;
          const importedConfig = importConfig(content);
          
          if (!importedConfig) {
            throw new Error('Invalid configuration file');
          }
          
          await updateConfig(importedConfig);
          resolve();
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.readAsText(file);
    });
  }, [updateConfig]);
  
  return {
    exportConfiguration,
    importConfiguration,
  };
}

// Hook for configuration validation
export function useConfigValidation() {
  const { config } = useAssistantConfig();
  const [validation, setValidation] = useState<any>(null);
  
  useEffect(() => {
    if (config) {
      const result = validateAssistantConfig(config);
      setValidation(result);
    }
  }, [config]);
  
  const validateConfig = useCallback((configToValidate: Partial<AssistantConfig>) => {
    return validateAssistantConfig(configToValidate);
  }, []);
  
  return {
    validation,
    validateConfig,
    isValid: validation?.isValid ?? false,
    errors: validation?.errors ?? [],
    warnings: validation?.warnings ?? [],
  };
}

// Assistant hooks
import { useCallback, useEffect, useState } from 'react';
import { useAssistantStore, useAssistantActions } from '../store';
import { AssistantConfig, UseAssistantConfigReturn } from '../types';
import { validateAssistantConfig, getDefaultAssistantConfig } from '../utils/config-utils';

// Main assistant configuration hook
export function useAssistantConfig(): UseAssistantConfigReturn {
  const { config, isLoading, error } = useAssistantStore();
  const { setConfig, setLoading, setError } = useAssistantActions();
  
  // Load configuration on mount
  useEffect(() => {
    const loadConfig = async () => {
      if (config) return; // Already loaded
      
      setLoading(true);
      try {
        // Try to load from API
        const response = await fetch('/api/assistant/config');
        if (response.ok) {
          const data = await response.json();
          setConfig(data.config || getDefaultAssistantConfig());
        } else {
          // Use default config if API fails
          setConfig(getDefaultAssistantConfig());
        }
      } catch (err) {
        console.error('Failed to load assistant config:', err);
        setConfig(getDefaultAssistantConfig());
        setError('Failed to load configuration');
      } finally {
        setLoading(false);
      }
    };
    
    loadConfig();
  }, [config, setConfig, setLoading, setError]);
  
  const updateConfig = useCallback(async (updates: Partial<AssistantConfig>) => {
    if (!config) return;
    
    const newConfig = { ...config, ...updates };
    const validation = validateAssistantConfig(newConfig);
    
    if (!validation.isValid) {
      throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
    }
    
    setLoading(true);
    try {
      const response = await fetch('/api/assistant/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ config: newConfig }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save configuration');
      }
      
      setConfig(newConfig);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update configuration';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [config, setConfig, setLoading, setError]);
  
  const resetConfig = useCallback(async () => {
    const defaultConfig = getDefaultAssistantConfig();
    await updateConfig(defaultConfig);
  }, [updateConfig]);
  
  return {
    config,
    isLoading,
    error,
    updateConfig,
    resetConfig,
  };
}

// Hook for managing assistant tools
export function useAssistantTools() {
  const { config } = useAssistantStore();
  const [availableTools, setAvailableTools] = useState<Record<string, string[]>>({});
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    const loadTools = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/assistant/tools');
        if (response.ok) {
          const data = await response.json();
          setAvailableTools(data.tools || {});
        }
      } catch (error) {
        console.error('Failed to load tools:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadTools();
  }, []);
  
  const enabledTools = config?.enabledTools || {};
  const toolsRequiringConfirmation = config?.toolsRequiringConfirmation || {};
  
  return {
    availableTools,
    enabledTools,
    toolsRequiringConfirmation,
    isLoading,
  };
}

// Hook for assistant chat functionality
export function useAssistantChat(id?: string) {
  const [messages, setMessages] = useState<any[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;
    
    const userMessage = {
      id: `user-${Date.now()}`,
      role: 'user' as const,
      content: message,
      createdAt: new Date(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          id,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to send message');
      }
      
      const data = await response.json();
      
      if (data.message) {
        setMessages(prev => [...prev, data.message]);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [messages, id]);
  
  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);
  
  return {
    messages,
    input,
    isLoading,
    error,
    setInput,
    sendMessage,
    clearMessages,
  };
}

// Hook for assistant theme management
export function useAssistantTheme() {
  const { config } = useAssistantStore();
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);
  
  const effectiveTheme = config?.theme === 'system' ? systemTheme : config?.theme || 'light';
  
  return {
    theme: effectiveTheme,
    configuredTheme: config?.theme || 'system',
    systemTheme,
  };
}

// Hook for assistant performance monitoring
export function useAssistantPerformance() {
  const [metrics, setMetrics] = useState({
    responseTime: 0,
    messageCount: 0,
    errorCount: 0,
    uptime: 0,
  });
  
  useEffect(() => {
    const startTime = Date.now();
    
    const updateMetrics = () => {
      setMetrics(prev => ({
        ...prev,
        uptime: Date.now() - startTime,
      }));
    };
    
    const interval = setInterval(updateMetrics, 1000);
    return () => clearInterval(interval);
  }, []);
  
  const recordResponse = useCallback((responseTime: number) => {
    setMetrics(prev => ({
      ...prev,
      responseTime,
      messageCount: prev.messageCount + 1,
    }));
  }, []);
  
  const recordError = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      errorCount: prev.errorCount + 1,
    }));
  }, []);
  
  return {
    metrics,
    recordResponse,
    recordError,
  };
}

// Hook for assistant accessibility features
export function useAssistantAccessibility() {
  const [announcements, setAnnouncements] = useState<string[]>([]);
  const [focusedElement, setFocusedElement] = useState<string | null>(null);
  
  const announceToScreenReader = useCallback((message: string) => {
    setAnnouncements(prev => [...prev, message]);
    
    // Create a live region for screen readers
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.style.position = 'absolute';
    liveRegion.style.left = '-10000px';
    liveRegion.style.width = '1px';
    liveRegion.style.height = '1px';
    liveRegion.style.overflow = 'hidden';
    liveRegion.textContent = message;
    
    document.body.appendChild(liveRegion);
    
    setTimeout(() => {
      document.body.removeChild(liveRegion);
    }, 1000);
  }, []);
  
  const focusElement = useCallback((elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.focus();
      setFocusedElement(elementId);
    }
  }, []);
  
  return {
    announcements,
    focusedElement,
    announceToScreenReader,
    focusElement,
  };
}

// Export all hooks
export * from './use-assistant-config';
export * from './use-assistant-tools';
export * from './use-assistant-chat';

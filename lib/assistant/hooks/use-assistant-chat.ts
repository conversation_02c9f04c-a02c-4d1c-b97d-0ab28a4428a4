// Hook for assistant chat functionality
import { useCallback, useEffect, useState, useRef } from 'react';
import { Message } from '@ai-sdk/react';
import { useChatStore } from '../store';
import { ChatSession, ChatMetrics } from '../types/chat';
import { 
  createChatSession, 
  addMessageToSession, 
  generateChatTitle,
  calculateChatMetrics,
  exportChatSession,
  importChatSession 
} from '../utils/chat-utils';

export function useAssistantChat(sessionId?: string) {
  const messages = useChatStore((state) => state.messages);
  const input = useChatStore((state) => state.input);
  const isTyping = useChatStore((state) => state.isTyping);
  const status = useChatStore((state) => state.status);
  
  const setMessages = useChatStore((state) => state.setMessages);
  const addMessage = useChatStore((state) => state.addMessage);
  const setInput = useChatStore((state) => state.setInput);
  const setTyping = useChatStore((state) => state.setTyping);
  const setStatus = useChatStore((state) => state.setStatus);
  const clearMessages = useChatStore((state) => state.clearMessages);
  
  const [error, setError] = useState<string | null>(null);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Initialize session
  useEffect(() => {
    if (!currentSession && sessionId) {
      // Load existing session or create new one
      const session = createChatSession('consultant'); // Default type
      session.id = sessionId;
      setCurrentSession(session);
    }
  }, [sessionId, currentSession]);
  
  const sendMessage = useCallback(async (message: string) => {
    if (!message.trim()) return;
    
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: message,
      createdAt: new Date(),
    };
    
    addMessage(userMessage);
    setInput('');
    setStatus('loading');
    setError(null);
    setTyping(true);
    
    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    try {
      const response = await fetch('/api/assistant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          sessionId: currentSession?.id,
        }),
        signal: abortControllerRef.current.signal,
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.message) {
        const assistantMessage: Message = {
          ...data.message,
          createdAt: new Date(),
        };
        addMessage(assistantMessage);
        
        // Update session if exists
        if (currentSession) {
          const updatedSession = addMessageToSession(
            addMessageToSession(currentSession, userMessage),
            assistantMessage
          );
          setCurrentSession(updatedSession);
        }
      }
      
      setStatus('idle');
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        // Request was cancelled
        setStatus('idle');
        return;
      }
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      setStatus('error');
      
      // Add error message to chat
      const errorMsg: Message = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: `Sorry, I encountered an error: ${errorMessage}`,
        createdAt: new Date(),
      };
      addMessage(errorMsg);
    } finally {
      setTyping(false);
      abortControllerRef.current = null;
    }
  }, [messages, currentSession, addMessage, setInput, setStatus, setTyping]);
  
  const stopGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setStatus('idle');
      setTyping(false);
    }
  }, [setStatus, setTyping]);
  
  const retryLastMessage = useCallback(async () => {
    const lastUserMessage = messages
      .slice()
      .reverse()
      .find(m => m.role === 'user');
    
    if (lastUserMessage && typeof lastUserMessage.content === 'string') {
      await sendMessage(lastUserMessage.content);
    }
  }, [messages, sendMessage]);
  
  const regenerateResponse = useCallback(async () => {
    // Remove the last assistant message and retry
    const lastAssistantIndex = messages
      .map((m, i) => ({ message: m, index: i }))
      .reverse()
      .find(({ message }) => message.role === 'assistant')?.index;
    
    if (lastAssistantIndex !== undefined) {
      const newMessages = messages.slice(0, lastAssistantIndex);
      setMessages(newMessages);
      
      const lastUserMessage = newMessages
        .slice()
        .reverse()
        .find(m => m.role === 'user');
      
      if (lastUserMessage && typeof lastUserMessage.content === 'string') {
        await sendMessage(lastUserMessage.content);
      }
    }
  }, [messages, setMessages, sendMessage]);
  
  const clearChat = useCallback(() => {
    clearMessages();
    setError(null);
    setStatus('idle');
    setTyping(false);
    
    if (currentSession) {
      const clearedSession = { ...currentSession, messages: [] };
      setCurrentSession(clearedSession);
    }
  }, [clearMessages, setStatus, setTyping, currentSession]);
  
  const exportChat = useCallback(() => {
    if (currentSession) {
      const exportData = exportChatSession(currentSession);
      const blob = new Blob([exportData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `chat-${currentSession.id}-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    }
  }, [currentSession]);
  
  const importChat = useCallback(async (file: File) => {
    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const importedSession = importChatSession(content);
          
          if (!importedSession) {
            throw new Error('Invalid chat file');
          }
          
          setCurrentSession(importedSession);
          setMessages(importedSession.messages);
          resolve();
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.readAsText(file);
    });
  }, [setMessages]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  
  return {
    messages,
    input,
    isLoading: status === 'loading' || status === 'streaming',
    isTyping,
    error,
    status,
    currentSession,
    setInput,
    sendMessage,
    stopGeneration,
    retryLastMessage,
    regenerateResponse,
    clearChat,
    exportChat,
    importChat,
  };
}

// Hook for chat history management
export function useChatHistory() {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const loadHistory = async () => {
      setIsLoading(true);
      try {
        const response = await fetch('/api/assistant/chat/history');
        if (response.ok) {
          const data = await response.json();
          setSessions(data.sessions || []);
        }
      } catch (err) {
        console.error('Failed to load chat history:', err);
        setError('Failed to load chat history');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadHistory();
  }, []);
  
  const saveSession = useCallback(async (session: ChatSession) => {
    try {
      const response = await fetch('/api/assistant/chat/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ session }),
      });
      
      if (response.ok) {
        setSessions(prev => {
          const existing = prev.find(s => s.id === session.id);
          if (existing) {
            return prev.map(s => s.id === session.id ? session : s);
          } else {
            return [...prev, session];
          }
        });
      }
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  }, []);
  
  const deleteSession = useCallback(async (sessionId: string) => {
    try {
      const response = await fetch(`/api/assistant/chat/sessions/${sessionId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        setSessions(prev => prev.filter(s => s.id !== sessionId));
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
    }
  }, []);
  
  const getMetrics = useCallback(() => {
    return calculateChatMetrics(sessions);
  }, [sessions]);
  
  return {
    sessions,
    isLoading,
    error,
    saveSession,
    deleteSession,
    getMetrics,
  };
}

'use client';

import { Message, UIMessage } from 'ai';
import { useRouter } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card } from '@/components/ui/card';
import { APPROVAL } from '@/lib/assistant/utils';
import { Button } from '@/components/ui/button';
import { Check, X, Bot, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getInitials } from '@/lib/assistant/utils';
import { useSession } from 'next-auth/react';
import { useState } from 'react';
import { SimpleMarkdown } from './simple-markdown';

interface ChatMessageProps {
  message: Message | UIMessage;
  addToolResult: (params: { toolCallId: string; result: string }) => void;
  toolsRequiringConfirmation: string[];
  bubbleStyle?: string;
  avatarStyle?: string;
  customAvatarUrl?: string;
}

export function ChatMessage({
  message,
  addToolResult,
  toolsRequiringConfirmation,
  bubbleStyle = 'rounded-2xl',
  avatarStyle = 'default',
  customAvatarUrl,
}: ChatMessageProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [expandedToolResults, setExpandedToolResults] = useState<Set<string>>(new Set());

  const toggleToolResult = (toolCallId: string) => {
    setExpandedToolResults(prev => {
      const newSet = new Set(prev);
      if (newSet.has(toolCallId)) {
        newSet.delete(toolCallId);
      } else {
        newSet.add(toolCallId);
      }
      return newSet;
    });
  };

  // Handle navigation tool
  const handleNavigate = (path: string) => {
    router.push(path);
  };

  return (
    <div
      className={cn(
        'flex w-full items-start gap-4 py-4',
        message.role === 'user' ? 'justify-end' : 'justify-start'
      )}
    >
      {message.role !== 'user' && (
        <Avatar className={cn("h-8 w-8", avatarStyle === 'minimal' ? 'h-6 w-6' : '')}>
          {customAvatarUrl && avatarStyle === 'custom' ? (
            <AvatarImage src={customAvatarUrl} alt="Assistant" />
          ) : null}
          <AvatarFallback className={cn(
            "bg-primary text-primary-foreground",
            avatarStyle === 'minimal' ? 'text-xs' : ''
          )}>
            <Bot size={avatarStyle === 'minimal' ? 12 : 16} />
          </AvatarFallback>
        </Avatar>
      )}

      <div
        className={cn(
          'flex max-w-[80%] flex-col gap-2',
          message.role === 'user' ? 'items-end' : 'items-start'
        )}
      >
        {message.parts?.map((part, i) => {
          switch (part.type) {
            case 'text':
              return (
                <Card
                  key={i}
                  className={cn(
                    'px-4 py-3 shadow-sm',
                    bubbleStyle,
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  )}
                >
                  <SimpleMarkdown className="text-sm">
                    {part.text}
                  </SimpleMarkdown>
                </Card>
              );

            case 'tool-invocation':
              const { toolInvocation } = part;
              const toolCallId = toolInvocation.toolCallId;
              const isExpanded = expandedToolResults.has(toolCallId);

              // Handle tool calls that require confirmation
              if (
                toolsRequiringConfirmation.includes(toolInvocation.toolName) &&
                toolInvocation.state === 'call'
              ) {
                return (
                  <Card key={toolCallId} className="p-4 bg-amber-50 dark:bg-amber-950 border-amber-200 dark:border-amber-800">
                    <div className="flex flex-col gap-2">
                      <div className="text-sm font-medium">
                        Assistant wants to {toolInvocation.toolName}:
                      </div>
                      <div className="text-sm bg-amber-100 dark:bg-amber-900 p-2 rounded">
                        {JSON.stringify(toolInvocation.args, null, 2)}
                      </div>
                      <div className="flex gap-2 mt-2">
                        <Button
                          size="sm"
                          variant="default"
                          className="bg-green-600 hover:bg-green-700"
                          onClick={() =>
                            addToolResult({
                              toolCallId,
                              result: APPROVAL.YES,
                            })
                          }
                        >
                          <Check className="mr-2 h-4 w-4" />
                          Approve
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-red-600 text-red-600 hover:bg-red-50 hover:text-red-700"
                          onClick={() =>
                            addToolResult({
                              toolCallId,
                              result: APPROVAL.NO,
                            })
                          }
                        >
                          <X className="mr-2 h-4 w-4" />
                          Deny
                        </Button>
                      </div>
                    </div>
                  </Card>
                );
              }

              // Handle navigation tool result
              if (
                toolInvocation.toolName === 'navigateTo' &&
                toolInvocation.state === 'result' &&
                toolInvocation.result &&
                typeof toolInvocation.result === 'object' &&
                'success' in toolInvocation.result
              ) {
                return (
                  <Card key={toolCallId} className="p-4 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
                    <div className="flex flex-col gap-2">
                      <div className="text-sm font-medium">
                        Navigation requested:
                      </div>
                      <div className="text-sm">
                        {toolInvocation.args.path}
                      </div>
                      <Button
                        size="sm"
                        variant="default"
                        onClick={() => handleNavigate(toolInvocation.args.path)}
                      >
                        Navigate Now
                      </Button>
                    </div>
                  </Card>
                );
              }

              // Handle other tool results
              if (toolInvocation.state === 'result') {
                return (
                  <Card key={toolCallId} className="p-4 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
                    <div className="flex flex-col gap-2">
                      <div
                        className="flex justify-between items-center cursor-pointer"
                        onClick={() => toggleToolResult(toolCallId)}
                      >
                        <div className="text-sm font-medium">
                          {toolInvocation.toolName} result:
                        </div>
                        <Button variant="ghost" size="sm">
                          {isExpanded ? 'Hide Details' : 'Show Details'}
                        </Button>
                      </div>
                      {isExpanded && (
                        <pre className="text-xs bg-blue-100 dark:bg-blue-900 p-2 rounded overflow-auto max-h-[200px]">
                          {JSON.stringify(toolInvocation.result, null, 2)}
                        </pre>
                      )}
                    </div>
                  </Card>
                );
              }

              // Handle tool calls in progress
              return (
                <Card key={toolCallId} className="p-4 bg-gray-50 dark:bg-gray-900">
                  <div className="text-sm">
                    <span className="font-medium">Using tool:</span> {toolInvocation.toolName}
                  </div>
                </Card>
              );

            default:
              return null;
          }
        })}
      </div>

      {message.role === 'user' && (
        <Avatar className={cn("h-8 w-8", avatarStyle === 'minimal' ? 'h-6 w-6' : '')}>
          <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || 'User'} />
          <AvatarFallback className={cn(
            "bg-secondary",
            avatarStyle === 'minimal' ? 'text-xs' : ''
          )}>
            {session?.user?.name ? getInitials(session.user.name) : <User size={avatarStyle === 'minimal' ? 12 : 16} />}
          </AvatarFallback>
        </Avatar>
      )}
    </div>
  );
}

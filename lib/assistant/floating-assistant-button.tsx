'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Bot, X, Sparkles, Maximize2, Minimize2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { AssistantChat } from './assistant-chat';
import { cn } from '@/lib/utils';

export function FloatingAssistantButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleAssistant = () => {
    setIsOpen(!isOpen);
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      {isOpen && (
        <Card
          className={cn(
            'fixed bottom-20 right-4 z-50 flex flex-col overflow-hidden shadow-lg transition-all duration-300',
            isExpanded
              ? 'h-[80vh] w-[80vw] max-w-4xl'
              : 'h-[500px] w-[350px]'
          )}
        >
          <div className="flex items-center justify-between border-b bg-primary p-2 text-primary-foreground">
            <div className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              <span className="font-medium">AI Assistant</span>
              <Sparkles className="h-4 w-4 text-yellow-300" />
            </div>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-primary-foreground hover:bg-primary/80"
                onClick={toggleExpand}
              >
                {isExpanded ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 text-primary-foreground hover:bg-primary/80"
                onClick={toggleAssistant}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <CardContent className="flex-1 p-0">
            <AssistantChat />
          </CardContent>
        </Card>
      )}

      <Button
        onClick={toggleAssistant}
        className={cn(
          'fixed bottom-4 right-4 z-50 h-12 w-12 rounded-full shadow-lg',
          isOpen ? 'bg-red-500 hover:bg-red-600' : 'bg-primary hover:bg-primary/90'
        )}
        size="icon"
      >
        {isOpen ? <X className="h-5 w-5" /> : <Bot className="h-5 w-5" />}
      </Button>
    </>
  );
}

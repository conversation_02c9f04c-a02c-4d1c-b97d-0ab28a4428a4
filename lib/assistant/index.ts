// Main exports for the Assistant Chat Interface Library

// Components
export { AssistantChat } from './assistant-chat';
export { AssistantConfig } from './assistant-config';
export { AssistantPreview } from './assistant-preview';
export { ChatMessage } from './chat-message';
export { ChatInput } from './chat-input';
export { FloatingAssistantButton } from './floating-assistant-button';
export { SidebarAssistant } from './sidebar-assistant';
export { SimpleMarkdown } from './simple-markdown';
export { ToolResultCard } from './tool-result-card';

// Types
export type {
  AssistantProps,
  AssistantConfig,
  AssistantState,
  ChatMessageProps,
  ChatInputProps,
  Tool,
  ToolCategory,
  ToolResult,
  AssistantStore,
  ChatStore,
  UIStore,
  AssistantChatStore,
  UseAssistantConfigReturn,
  UseChatReturn,
  AssistantEvent,
  AssistantType,
  AssistantCapability,
} from './types';

export type {
  ChatSession,
  ChatHistory,
  ChatMetrics,
  ChatSettings,
  ChatContext,
  StreamingState,
  MessageReaction,
  ChatError,
  ChatNotification,
} from './types/chat';

export type {
  ModelConfig,
  SecurityConfig,
  PerformanceConfig,
  IntegrationConfig,
  AnalyticsConfig,
  NotificationConfig,
  AccessibilityConfig,
  ExperimentalConfig,
  ConfigValidation,
  ConfigPreset,
  ConfigHistory,
  ExtendedAssistantConfig,
} from './types/config';

export type {
  ToolDefinition,
  ToolParameters,
  ToolParameter,
  ToolExecution,
  ToolPermission,
  ToolUsageStats,
  ToolConfiguration,
  ClientTool,
  ServerTool,
  ToolRegistry,
  ToolEvent,
  ToolValidationResult,
  NavigationToolArgs,
  DocumentToolArgs,
  MeetingToolArgs,
  TaskToolArgs,
  WebResearchToolArgs,
} from './types/tools';

// Hooks
export {
  useAssistantConfig,
  useConfigPresets,
  useConfigImportExport,
  useConfigValidation,
} from './hooks/use-assistant-config';

export {
  useAssistantTools,
  useToolPermissions,
} from './hooks/use-assistant-tools';

export {
  useAssistantChat,
  useChatHistory,
} from './hooks/use-assistant-chat';

export {
  useAssistantTheme,
  useAssistantPerformance,
  useAssistantAccessibility,
} from './hooks';

// Store
export {
  useAssistantStore,
  useChatStore,
  useUIStore,
  useAssistantState,
  useAssistantActions,
  useChatState,
  useChatActions,
  useUIState,
  useUIActions,
  useAssistantPerformance,
  resetAllStores,
} from './store';

// Utils
export {
  APPROVAL,
  getInitials,
  getToolsRequiringConfirmation,
  categorizeTools,
  getHighRiskTools,
  isHighRiskTool,
  validateConfig,
  getDefaultConfig,
  formatTimestamp,
  truncateMessage,
  extractCodeBlocks,
  debounce,
  throttle,
  saveToLocalStorage,
  loadFromLocalStorage,
  removeFromLocalStorage,
  createErrorMessage,
  isNetworkError,
  generateId,
} from './utils';

export {
  createChatSession,
  updateChatSession,
  addMessageToSession,
  generateChatTitle,
  calculateChatMetrics,
  filterMessagesByRole,
  getLastUserMessage,
  getLastAssistantMessage,
  hasToolCalls,
  extractToolCalls,
  isMessageComplete,
  getMessageWordCount,
  getMessageCharacterCount,
  searchMessages,
  exportChatSession,
  importChatSession,
} from './utils/chat-utils';

export {
  createToolDefinition,
  validateToolParameters,
  createToolExecution,
  updateToolExecution,
  createSuccessResult,
  createErrorResult,
  getToolsByCategory,
  getHighRiskTools as getHighRiskToolDefinitions,
  getToolsRequiringConfirmation as getToolsRequiringConfirmationDefinitions,
  getClientSideTools,
  getServerSideTools,
  calculateToolExecutionTime,
  getToolExecutionStatus,
  formatToolResult,
  isToolExecutionComplete,
  canRetryToolExecution,
  getToolDisplayName,
  getToolCategoryDisplayName,
  sortToolsByUsage,
  filterToolsByRiskLevel,
} from './utils/tool-utils';

export {
  validateAssistantConfig,
  mergeConfigs,
  getDefaultAssistantConfig,
  createConfigPreset,
  getBuiltInPresets,
  exportConfig,
  importConfig,
  compareConfigs,
  sanitizeConfig,
  getConfigSummary,
} from './utils/config-utils';

// Tools
export {
  tools,
  toolsByCategory,
  toolNames,
  highRiskTools,
  toolsRequiringConfirmation,
  clientSideTools,
  navigationTools,
  documentTools,
  meetingTools,
  taskTools,
  webResearchTools,
  userTools,
  systemTools,
} from './client-tools';

// Constants
export const ASSISTANT_LIBRARY_VERSION = '1.0.0';

export const DEFAULT_ASSISTANT_CONFIG = {
  name: 'AI Assistant',
  greeting: 'Hello! I\'m your AI assistant. How can I help you today?',
  model: 'gpt-4o',
  maxSteps: 10,
  enableMemory: true,
  enableHumanInTheLoop: true,
  enableWebResearch: true,
  enabledTools: {},
  toolsRequiringConfirmation: {},
  theme: 'system' as const,
  avatarStyle: 'default' as const,
  customAvatarUrl: '',
  bubbleStyle: 'modern' as const,
};

export const TOOL_CATEGORIES = [
  'navigation',
  'document',
  'meeting',
  'task',
  'user',
  'feedback',
  'plugin',
  'transcript',
  'ai',
  'folder',
  'tag',
  'version',
  'webResearch',
  'system',
] as const;

export const RISK_LEVELS = ['low', 'medium', 'high'] as const;

export const EXECUTION_MODES = ['client', 'server', 'hybrid'] as const;

export const THEMES = ['system', 'light', 'dark'] as const;

export const AVATAR_STYLES = ['default', 'minimal', 'custom'] as const;

export const BUBBLE_STYLES = ['modern', 'classic', 'minimal'] as const;

// Library metadata
export const LIBRARY_INFO = {
  name: 'Assistant Chat Interface',
  version: ASSISTANT_LIBRARY_VERSION,
  description: 'A comprehensive assistant chat interface library with modular architecture',
  features: [
    'Modular component architecture',
    'Zustand state management',
    'TypeScript support',
    'Tool system with client/server execution',
    'Configuration management',
    'Chat history and sessions',
    'Theme support',
    'Accessibility features',
    'Performance monitoring',
    'Import/export functionality',
  ],
  dependencies: [
    '@ai-sdk/react',
    'zustand',
    'framer-motion',
    'lucide-react',
  ],
} as const;

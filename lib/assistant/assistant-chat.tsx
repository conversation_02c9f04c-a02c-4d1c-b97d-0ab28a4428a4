'use client';

import { useChat } from '@ai-sdk/react';
import { useEffect, useRef } from 'react';
import { ChatMessage } from './chat-message';
import { ChatInput } from './chat-input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bot, Sparkles, Loader2 } from 'lucide-react';
import { getToolsRequiringConfirmation } from './utils/index';
import { AssistantProps } from './types/index';
import { useAssistantConfig } from './hooks/index';
import { cn } from '@/lib/utils';

export function AssistantChat({ initialMessages, id }: AssistantProps) {
  const { config, isLoading: isConfigLoading } = useAssistantConfig();

  // Default greeting message if config is not loaded yet
  const defaultGreeting = {
    id: 'welcome',
    role: 'assistant' as const,
    content: 'Hello! I\'m your AI assistant. How can I help you today?',
  };

  // Use config values or defaults
  const chatConfig = {
    maxSteps: config?.maxSteps || 10,
    initialMessage: config ? {
      id: 'welcome',
      role: 'assistant' as const,
      content: config.greeting,
    } : defaultGreeting,
  };

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    error,
    addToolResult,
  } = useChat({
    api: '/api/assistant',
    id,
    initialMessages: initialMessages || [chatConfig.initialMessage],
    maxSteps: chatConfig.maxSteps,
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Use custom tool confirmation settings from config if available
  const toolsRequiringConfirmation = config?.toolsRequiringConfirmation && !isConfigLoading
    ? Object.entries(config.toolsRequiringConfirmation)
        .filter(([_, requiresConfirmation]) => requiresConfirmation)
        .map(([toolName]) => toolName)
    : getToolsRequiringConfirmation([]);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Check if there's a pending tool call that requires confirmation
  const pendingToolCallConfirmation = messages.some(m =>
    m.parts?.some(
      part =>
        part.type === 'tool-invocation' &&
        'toolInvocation' in part &&
        part.toolInvocation.state === 'call' &&
        toolsRequiringConfirmation.includes(part.toolInvocation.toolName),
    ),
  );

  // Get bubble style from config
  const getBubbleStyle = () => {
    if (!config) return '';

    switch (config.bubbleStyle) {
      case 'classic':
        return 'rounded-lg';
      case 'minimal':
        return 'rounded-sm';
      case 'modern':
      default:
        return 'rounded-2xl';
    }
  };

  return (
    <Card className="flex h-[calc(100vh-10rem)] flex-col overflow-hidden">
      <CardHeader className="border-b px-4 py-3">
        <CardTitle className="flex items-center text-lg font-medium">
          <Bot className="mr-2 h-5 w-5 text-primary" />
          {config?.name || 'AI Assistant'}
          <Sparkles className="ml-2 h-4 w-4 text-yellow-500" />
        </CardTitle>
      </CardHeader>
      <CardContent className={cn("flex-1 p-0", config?.theme === 'dark' ? 'bg-slate-900' : '')}>
        <ScrollArea className="h-full">
          <div className="flex flex-col p-4">
            {isConfigLoading ? (
              <div className="flex h-full flex-col items-center justify-center p-8 text-center">
                <Loader2 className="mb-4 h-8 w-8 animate-spin text-primary" />
                <p className="text-sm text-muted-foreground">Loading assistant...</p>
              </div>
            ) : messages.length === 0 ? (
              <div className="flex h-full flex-col items-center justify-center p-8 text-center">
                <Bot className="mb-4 h-12 w-12 text-muted-foreground" />
                <h3 className="mb-2 text-xl font-medium">How can I help you today?</h3>
                <p className="text-sm text-muted-foreground">
                  Ask me about your documents, meetings, or tasks. I can help you navigate the dashboard,
                  create new items, or find information.
                </p>
              </div>
            ) : (
              messages.map(message => (
                <ChatMessage
                  key={message.id}
                  message={message as any}
                  addToolResult={addToolResult}
                  toolsRequiringConfirmation={toolsRequiringConfirmation}
                  bubbleStyle={getBubbleStyle()}
                  avatarStyle={config?.avatarStyle}
                  customAvatarUrl={config?.customAvatarUrl}
                />
              ))
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </CardContent>
      <ChatInput
        input={input}
        handleInputChange={handleInputChange}
        handleSubmit={handleSubmit}
        isLoading={status === 'streaming' || status === 'submitted'}
        disabled={pendingToolCallConfirmation || isConfigLoading}
      />
      {error && (
        <div className="px-4 py-2 text-sm text-red-500 bg-red-50 dark:bg-red-950 border-t border-red-200 dark:border-red-800">
          Error: {error.message || 'Something went wrong. Please try again.'}
        </div>
      )}
    </Card>
  );
}

export const CONSTANTS = {
  // Minimum sizes
  MIN_PANEL_SIZE: 80,
  MIN_TAB_HEIGHT: 32,
  
  // Drag and drop
  DRAG_THRESHOLD: 5,
  DRAG_DELAY: 150,
  
  // Animation durations
  ANIMATION_DURATION: {
    SHORT: 0.15,
    MEDIUM: 0.2,
    LONG: 0.3
  },
  
  // Z-indices
  Z_INDEX: {
    BASE: 1,
    DRAGOVER: 2,
    DRAGGING: 3,
    CONTEXT_MENU: 10,
    MODAL: 100
  },
  
  // CSS classes
  CSS_CLASSES: {
    PANEL: 'vscode-panel',
    TAB: 'vscode-tab',
    ACTIVE: 'is-active',
    DRAGGING: 'is-dragging',
    DRAGOVER: 'is-dragover',
    PINNED: 'is-pinned',
    DIRTY: 'is-dirty',
    LOADING: 'is-loading'
  },
  
  // Local storage keys
  STORAGE_KEYS: {
    LAYOUT: 'vscode-panels-layout',
    THEME: 'vscode-panels-theme'
  }
} as const

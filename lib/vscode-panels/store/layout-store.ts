import { create } from 'zustand'
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import type { 
  LayoutStore, 
  LayoutConfig, 
  LayoutNode, 
  LayoutPreset,
  LayoutValidationResult,
  LayoutConstraints
} from '../types/layout'
import type { Direction } from '../types'

// Helper function to create a default layout
const createDefaultLayout = (): LayoutConfig => ({
  id: 'default',
  name: 'Default Layout',
  version: '1.0.0',
  root: {
    id: 'root',
    type: 'group',
    direction: 'horizontal',
    size: 1,
    children: []
  },
  createdAt: new Date(),
  updatedAt: new Date()
})

// Initial state
const initialState = {
  currentLayout: createDefaultLayout(),
  savedLayouts: {} as Record<string, LayoutConfig>,
  presets: [] as LayoutPreset[],
  history: [] as LayoutConfig[],
  historyIndex: -1,
  isModified: false,
  constraints: undefined as LayoutConstraints | undefined
}

// Create the layout store
export const useLayoutStore = create<LayoutStore>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          ...initialState,

          // Layout management
          setLayout: (layout: LayoutConfig) => {
            set((state) => {
              // Add current layout to history before changing
              if (state.historyIndex < state.history.length - 1) {
                state.history = state.history.slice(0, state.historyIndex + 1)
              }
              state.history.push(state.currentLayout)
              state.historyIndex = state.history.length - 1

              // Set new layout
              state.currentLayout = {
                ...layout,
                updatedAt: new Date()
              }
              state.isModified = true
            })
          },

          saveLayout: (name: string, description?: string) => {
            set((state) => {
              const layoutToSave: LayoutConfig = {
                ...state.currentLayout,
                id: `layout-${Date.now()}`,
                name,
                description,
                updatedAt: new Date()
              }

              state.savedLayouts[layoutToSave.id] = layoutToSave
              state.isModified = false
            })
          },

          loadLayout: (layoutId: string) => {
            set((state) => {
              const layout = state.savedLayouts[layoutId]
              if (layout) {
                // Add current layout to history
                if (state.historyIndex < state.history.length - 1) {
                  state.history = state.history.slice(0, state.historyIndex + 1)
                }
                state.history.push(state.currentLayout)
                state.historyIndex = state.history.length - 1

                // Load the layout
                state.currentLayout = {
                  ...layout,
                  updatedAt: new Date()
                }
                state.isModified = false
              }
            })
          },

          deleteLayout: (layoutId: string) => {
            set((state) => {
              delete state.savedLayouts[layoutId]
            })
          },

          duplicateLayout: (layoutId: string, newName: string) => {
            set((state) => {
              const originalLayout = state.savedLayouts[layoutId]
              if (originalLayout) {
                const duplicatedLayout: LayoutConfig = {
                  ...originalLayout,
                  id: `layout-${Date.now()}`,
                  name: newName,
                  createdAt: new Date(),
                  updatedAt: new Date()
                }
                state.savedLayouts[duplicatedLayout.id] = duplicatedLayout
              }
            })
          },

          // Layout modification
          addPanel: (panelId: string, targetNode?: string, position: 'before' | 'after' | 'child' = 'child') => {
            set((state) => {
              const newPanelNode: LayoutNode = {
                id: `panel-${panelId}`,
                type: 'panel',
                panelId,
                size: 0.5
              }

              if (!targetNode) {
                // Add to root
                if (state.currentLayout.root.children) {
                  state.currentLayout.root.children.push(newPanelNode)
                } else {
                  state.currentLayout.root.children = [newPanelNode]
                }
              } else {
                // Find target node and add relative to it
                const findAndInsert = (node: LayoutNode): boolean => {
                  if (node.id === targetNode) {
                    if (position === 'child' && node.type === 'group') {
                      if (!node.children) node.children = []
                      node.children.push(newPanelNode)
                      return true
                    }
                    return false
                  }

                  if (node.children) {
                    for (let i = 0; i < node.children.length; i++) {
                      const child = node.children[i]
                      if (child.id === targetNode) {
                        if (position === 'before') {
                          node.children.splice(i, 0, newPanelNode)
                        } else if (position === 'after') {
                          node.children.splice(i + 1, 0, newPanelNode)
                        }
                        return true
                      }
                      if (findAndInsert(child)) return true
                    }
                  }
                  return false
                }

                findAndInsert(state.currentLayout.root)
              }

              state.isModified = true
            })
          },

          removePanel: (panelId: string) => {
            set((state) => {
              const removeFromNode = (node: LayoutNode): LayoutNode | null => {
                if (node.type === 'panel' && node.panelId === panelId) {
                  return null
                }

                if (node.children) {
                  node.children = node.children
                    .map(removeFromNode)
                    .filter((child): child is LayoutNode => child !== null)
                }

                return node
              }

              const newRoot = removeFromNode(state.currentLayout.root)
              if (newRoot) {
                state.currentLayout.root = newRoot
                state.isModified = true
              }
            })
          },

          movePanel: (panelId: string, targetNode: string, position: 'before' | 'after' | 'child') => {
            set((state) => {
              // First remove the panel
              get().removePanel(panelId)
              // Then add it at the new location
              get().addPanel(panelId, targetNode, position)
            })
          },

          resizeNode: (nodeId: string, size: number) => {
            set((state) => {
              const updateNodeSize = (node: LayoutNode): void => {
                if (node.id === nodeId) {
                  node.size = Math.max(0.1, Math.min(1, size))
                  return
                }

                if (node.children) {
                  node.children.forEach(updateNodeSize)
                }
              }

              updateNodeSize(state.currentLayout.root)
              state.isModified = true
            })
          },

          splitNode: (nodeId: string, direction: Direction, ratio: number = 0.5) => {
            set((state) => {
              const splitNodeRecursive = (node: LayoutNode): LayoutNode => {
                if (node.id === nodeId && node.type === 'panel') {
                  // Convert panel to group with two children
                  const originalPanel = { ...node }
                  const newPanel: LayoutNode = {
                    id: `panel-${Date.now()}`,
                    type: 'panel',
                    size: 1 - ratio
                  }

                  return {
                    id: `group-${Date.now()}`,
                    type: 'group',
                    direction,
                    size: node.size,
                    children: [
                      { ...originalPanel, size: ratio },
                      newPanel
                    ]
                  }
                }

                if (node.children) {
                  node.children = node.children.map(splitNodeRecursive)
                }

                return node
              }

              state.currentLayout.root = splitNodeRecursive(state.currentLayout.root)
              state.isModified = true
            })
          },

          mergeNodes: (nodeIds: string[]) => {
            set((state) => {
              // TODO: Implement node merging logic
              state.isModified = true
            })
          },

          // Layout navigation
          undo: () => {
            set((state) => {
              if (state.historyIndex > 0) {
                state.historyIndex--
                state.currentLayout = state.history[state.historyIndex]
                state.isModified = true
              }
            })
          },

          redo: () => {
            set((state) => {
              if (state.historyIndex < state.history.length - 1) {
                state.historyIndex++
                state.currentLayout = state.history[state.historyIndex]
                state.isModified = true
              }
            })
          },

          reset: () => {
            set((state) => {
              state.currentLayout = createDefaultLayout()
              state.history = []
              state.historyIndex = -1
              state.isModified = false
            })
          },

          // Presets
          applyPreset: (presetId: string) => {
            set((state) => {
              const preset = state.presets.find(p => p.id === presetId)
              if (preset) {
                get().setLayout(preset.layout)
              }
            })
          },

          saveAsPreset: (name: string, description?: string, category?: string) => {
            set((state) => {
              const preset: LayoutPreset = {
                id: `preset-${Date.now()}`,
                name,
                description,
                layout: { ...state.currentLayout },
                category,
                isCustom: true
              }

              state.presets.push(preset)
            })
          },

          deletePreset: (presetId: string) => {
            set((state) => {
              state.presets = state.presets.filter(p => p.id !== presetId)
            })
          },

          // Validation
          validateLayout: (layout: LayoutConfig): LayoutValidationResult => {
            const errors: any[] = []
            const warnings: any[] = []

            // Basic validation
            if (!layout.root) {
              errors.push({
                code: 'MISSING_ROOT',
                message: 'Layout must have a root node',
                severity: 'error'
              })
            }

            // Validate node structure
            const validateNode = (node: LayoutNode, path: string[] = []): void => {
              if (!node.id) {
                errors.push({
                  code: 'MISSING_NODE_ID',
                  message: `Node at path ${path.join('.')} is missing an ID`,
                  severity: 'error'
                })
              }

              if (node.type === 'group' && (!node.children || node.children.length === 0)) {
                warnings.push({
                  code: 'EMPTY_GROUP',
                  message: `Group node ${node.id} has no children`,
                  severity: 'warning'
                })
              }

              if (node.children) {
                node.children.forEach((child, index) => {
                  validateNode(child, [...path, node.id, index.toString()])
                })
              }
            }

            if (layout.root) {
              validateNode(layout.root)
            }

            return {
              isValid: errors.length === 0,
              errors,
              warnings
            }
          },

          optimizeLayout: () => {
            set((state) => {
              // TODO: Implement layout optimization
              // - Remove empty groups
              // - Normalize sizes
              // - Flatten unnecessary nesting
              state.isModified = true
            })
          }
        }))
      ),
      {
        name: 'vscode-panels-layout-store',
        partialize: (state) => ({
          currentLayout: state.currentLayout,
          savedLayouts: state.savedLayouts,
          presets: state.presets.filter(p => p.isCustom)
        })
      }
    ),
    {
      name: 'layout-store'
    }
  )
)

// Selector hooks for performance
export const useCurrentLayout = () => useLayoutStore(state => state.currentLayout)
export const useSavedLayouts = () => useLayoutStore(state => state.savedLayouts)
export const useLayoutPresets = () => useLayoutStore(state => state.presets)
export const useLayoutHistory = () => useLayoutStore(state => ({
  history: state.history,
  historyIndex: state.historyIndex,
  canUndo: state.historyIndex > 0,
  canRedo: state.historyIndex < state.history.length - 1
}))
export const useLayoutModified = () => useLayoutStore(state => state.isModified)

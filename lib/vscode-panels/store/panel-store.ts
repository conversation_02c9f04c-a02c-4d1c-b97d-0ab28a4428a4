import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { findNode, insertNode, removeNode } from '../utils/layout'
import type { PanelStore, PanelState } from '../types/panel'

export const usePanelStore = create<PanelStore>()(
  persist(
    (set, get) => ({
      panels: {},
      activePanel: null,
      draggedPanel: null,

      // Panel operations
      updatePanel: (id, update) => {
        const panels = get().panels
        const panel = panels[id]
        
        if (!panel) {
          // Create new panel if it doesn't exist
          set(state => ({
            panels: {
              ...state.panels,
              [id]: {
                id,
                title: update.title || '',
                width: update.width,
                height: update.height,
                minWidth: update.minWidth,
                minHeight: update.minHeight,
                defaultWidth: update.defaultWidth,
                defaultHeight: update.defaultHeight,
                isActive: update.isActive || false,
                isCollapsed: update.isCollapsed || false,
                isClosable: update.isClosable ?? true,
                direction: update.direction || 'vertical',
                metadata: update.metadata || {},
                ...update
              }
            }
          }))
        } else {
          // Update existing panel
          set(state => ({
            panels: {
              ...state.panels,
              [id]: {
                ...panel,
                ...update,
                // Ensure required fields aren't accidentally removed
                id: panel.id,
                title: update.title || panel.title
              }
            }
          }))
        }
      },

      activatePanel: (id) => {
        const panels = get().panels
        const panel = panels[id]
        if (!panel) return

        set(state => ({
          panels: {
            ...state.panels,
            [id]: { ...panel, isActive: true }
          },
          activePanel: id
        }))

        // Deactivate other panels
        Object.keys(panels)
          .filter(panelId => panelId !== id)
          .forEach(panelId => {
            set(state => ({
              panels: {
                ...state.panels,
                [panelId]: { ...state.panels[panelId], isActive: false }
              }
            }))
          })
      },

      removePanel: (id) => {
        const panels = get().panels
        const { [id]: removed, ...remaining } = panels

        set(state => ({
          panels: remaining,
          activePanel: state.activePanel === id ? null : state.activePanel
        }))
      },

      collapsePanel: (id, isCollapsed) => {
        const panels = get().panels
        const panel = panels[id]
        if (!panel) return

        set(state => ({
          panels: {
            ...state.panels,
            [id]: { ...panel, isCollapsed }
          }
        }))
      },

      resizePanel: (id, { width, height }) => {
        const panels = get().panels
        const panel = panels[id]
        if (!panel) return

        set(state => ({
          panels: {
            ...state.panels,
            [id]: {
              ...panel,
              width: width ?? panel.width,
              height: height ?? panel.height
            }
          }
        }))
      },

      reorderPanel: (sourceId, targetId, position) => {
        const { panels, layout } = get()
        const sourcePanel = panels[sourceId]
        const targetPanel = panels[targetId]
        
        if (!sourcePanel || !targetPanel || !layout?.root) return

        // Remove panel from its current position
        const newRoot = removeNode(layout.root, sourceId)
        if (!newRoot) return

        // Find target panel's parent group
        const targetNode = findNode(newRoot, targetId)
        if (!targetNode?.id) return

        // Insert panel at new position
        const updatedRoot = insertNode(
          newRoot,
          targetNode.id,
          { id: sourceId, type: 'panel' },
          position === 'before' ? -1 : undefined
        )

        if (!updatedRoot) return

        // Update layout
        set(state => ({
          layout: {
            ...state.layout!,
            root: updatedRoot
          }
        }))
      },

      startPanelDrag: (id) => {
        set({ draggedPanel: id })
      },

      endPanelDrag: (dropped) => {
        if (!dropped) {
          // Reset panel position if drop was cancelled
          // This will be handled by the layout system
        }
        set({ draggedPanel: null })
      },

      resetPanels: () => {
        set({
          panels: {},
          activePanel: null,
          draggedPanel: null,
          layout: undefined
        })
      },

      loadPanels: (panels) => {
        set({ panels })
      }
    }),
    {
      name: 'vscode-panel-store',
      partialize: (state) => ({
        panels: state.panels,
        activePanel: state.activePanel
      })
    }
  )
)

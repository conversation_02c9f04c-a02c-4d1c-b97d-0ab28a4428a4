import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { LayoutConfig } from '../types/layout'

interface WorkspaceState {
  layout: LayoutConfig | null
  theme: 'light' | 'dark' | 'auto'
  
  // Layout operations
  setLayout: (layout: LayoutConfig) => void
  resetLayout: () => void
  
  // Theme operations
  setTheme: (theme: 'light' | 'dark' | 'auto') => void
  getEffectiveTheme: () => 'light' | 'dark'
  
  // Panel creation
  createPanelWithTab: (
    panelConfig: {
      id: string
      title: string
      defaultWidth?: number
      defaultHeight?: number
    },
    tabConfig: {
      id: string
      title: string
      content: React.ReactNode
    }
  ) => void
}

export const useWorkspaceStore = create<WorkspaceState>()(
  persist(
    (set, get) => ({
      layout: null,
      theme: 'auto',

      setLayout: (layout) => set({ layout }),
      resetLayout: () => set({ layout: null }),

      setTheme: (theme) => set({ theme }),
      getEffectiveTheme: () => {
        const { theme } = get()
        if (theme === 'auto') {
          return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        }
        return theme
      },

      createPanelWithTab: (panelConfig, tabConfig) => {
        const { layout } = get()
        if (!layout) return

        // TODO: Implement panel and tab creation in layout
        console.log('Creating panel with tab:', { panelConfig, tabConfig })
      }
    }),
    {
      name: 'vscode-workspace-store',
      partialize: (state) => ({
        layout: state.layout,
        theme: state.theme
      })
    }
  )
)

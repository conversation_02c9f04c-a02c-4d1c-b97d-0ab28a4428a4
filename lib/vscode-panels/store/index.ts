// Re-export all stores and hooks
export * from './panel-store'
export * from './tab-store'
export * from './layout-store'

// Combined store hooks for convenience
import { usePanelStore } from './panel-store'
import { useTabStore } from './tab-store'
import { useLayoutStore } from './layout-store'

// Combined workspace store hook
export function useWorkspaceStore() {
  const panelStore = usePanelStore()
  const tabStore = useTabStore()
  const layoutStore = useLayoutStore()
  
  return {
    // Panel operations
    panels: panelStore.panels,
    activePanelId: panelStore.activePanelId,
    createPanel: panelStore.createPanel,
    removePanel: panelStore.removePanel,
    activatePanel: panelStore.activatePanel,
    resizePanel: panelStore.resizePanel,
    
    // Tab operations
    tabs: tabStore.tabs,
    tabsByPanel: tabStore.tabsByPanel,
    activeTabByPanel: tabStore.activeTabByPanel,
    createTab: tabStore.createTab,
    removeTab: tabStore.removeTab,
    activateTab: tabStore.activateTab,
    moveTab: tabStore.moveTab,
    
    // Layout operations
    layout: layoutStore.currentLayout,
    savedLayouts: layoutStore.savedLayouts,
    setLayout: layoutStore.setLayout,
    saveLayout: layoutStore.saveLayout,
    loadLayout: layoutStore.loadLayout,
    
    // Combined operations
    createPanelWithTab: (panelConfig: any, tabConfig: any) => {
      panelStore.createPanel(panelConfig)
      tabStore.createTab({ ...tabConfig, panelId: panelConfig.id })
      panelStore.activatePanel(panelConfig.id)
      tabStore.activateTab(tabConfig.id, panelConfig.id)
    },
    
    closePanelWithTabs: (panelId: string) => {
      // Close all tabs in the panel first
      const tabs = tabStore.tabsByPanel[panelId] || []
      tabs.forEach(tabId => tabStore.removeTab(tabId))
      
      // Then close the panel
      panelStore.removePanel(panelId)
    },
    
    moveTabToNewPanel: (tabId: string, newPanelConfig: any) => {
      const tab = tabStore.tabs[tabId]
      if (!tab) return
      
      // Create new panel
      panelStore.createPanel(newPanelConfig)
      
      // Move tab to new panel
      tabStore.moveTab(tabId, newPanelConfig.id)
      
      // Activate new panel and tab
      panelStore.activatePanel(newPanelConfig.id)
      tabStore.activateTab(tabId, newPanelConfig.id)
    },
    
    // Utility functions
    getPanelTabs: (panelId: string) => {
      const tabIds = tabStore.tabsByPanel[panelId] || []
      return tabIds.map(id => tabStore.tabs[id]).filter(Boolean)
    },
    
    getActiveTab: (panelId: string) => {
      const activeTabId = tabStore.activeTabByPanel[panelId]
      return activeTabId ? tabStore.tabs[activeTabId] : undefined
    },
    
    // State queries
    hasUnsavedChanges: () => {
      return Object.values(tabStore.tabs).some(tab => tab.isDirty) || layoutStore.isModified
    },
    
    canClosePanel: (panelId: string) => {
      const tabs = tabStore.tabsByPanel[panelId] || []
      const dirtyTabs = tabs.filter(tabId => tabStore.tabs[tabId]?.isDirty)
      return dirtyTabs.length === 0
    },
    
    // Bulk operations
    closeAllTabs: () => {
      Object.keys(tabStore.tabsByPanel).forEach(panelId => {
        tabStore.closeAllTabs(panelId)
      })
    },
    
    resetWorkspace: () => {
      tabStore.closeAllTabs()
      panelStore.resetLayout()
      layoutStore.reset()
    }
  }
}

// Selector hooks for performance optimization
export function usePanelById(panelId: string) {
  return usePanelStore(state => state.panels[panelId])
}

export function useTabById(tabId: string) {
  return useTabStore(state => state.tabs[tabId])
}

export function usePanelTabs(panelId: string) {
  return useTabStore(state => {
    const tabIds = state.tabsByPanel[panelId] || []
    return tabIds.map(id => state.tabs[id]).filter(Boolean)
  })
}

export function useActiveTab(panelId: string) {
  return useTabStore(state => {
    const activeTabId = state.activeTabByPanel[panelId]
    return activeTabId ? state.tabs[activeTabId] : undefined
  })
}

export function useLayoutNode(nodeId: string) {
  return useLayoutStore(state => {
    const findNode = (node: any): any => {
      if (node.id === nodeId) return node
      if (node.children) {
        for (const child of node.children) {
          const found = findNode(child)
          if (found) return found
        }
      }
      return undefined
    }
    return findNode(state.currentLayout.root)
  })
}

// Performance monitoring hooks
export function useStorePerformance() {
  const panelCount = usePanelStore(state => Object.keys(state.panels).length)
  const tabCount = useTabStore(state => Object.keys(state.tabs).length)
  const layoutDepth = useLayoutStore(state => {
    const calculateDepth = (node: any, depth = 0): number => {
      if (!node.children || node.children.length === 0) return depth
      return Math.max(...node.children.map((child: any) => calculateDepth(child, depth + 1)))
    }
    return calculateDepth(state.currentLayout.root)
  })
  
  return {
    panelCount,
    tabCount,
    layoutDepth,
    memoryUsage: {
      panels: panelCount * 1024, // Rough estimate
      tabs: tabCount * 512,
      layout: layoutDepth * 256
    }
  }
}

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { TabStore, TabState } from '../types/tab'

export const useTabStore = create<TabStore>()(
  persist(
    (set, get) => ({
      tabs: {},
      activeTab: null,
      draggedTab: null,

      // Tab operations
      updateTab: (id, update) => {
        const tabs = get().tabs
        const tab = tabs[id]
        
        if (!tab) {
          // Create new tab if it doesn't exist
          set(state => ({
            tabs: {
              ...state.tabs,
              [id]: {
                id,
                title: update.title || '',
                isPinned: update.isPinned || false,
                isDirty: update.isDirty || false,
                isActive: update.isActive || false,
                isClosable: update.isClosable ?? true,
                isLoading: update.isLoading || false,
                panelId: update.panelId || '',
                order: update.order || Object.keys(state.tabs).length,
                metadata: update.metadata || {},
                ...update
              }
            }
          }))
        } else {
          // Update existing tab
          set(state => ({
            tabs: {
              ...state.tabs,
              [id]: {
                ...tab,
                ...update,
                // Ensure required fields aren't accidentally removed
                id: tab.id,
                title: update.title || tab.title,
                panelId: update.panelId || tab.panelId
              }
            }
          }))
        }
      },

      activateTab: (id, panelId) => {
        const tab = get().tabs[id]
        if (!tab) return

        set(state => ({
          tabs: {
            ...state.tabs,
            [id]: { ...tab, isActive: true, panelId }
          },
          activeTab: id
        }))

        // Deactivate other tabs in the same panel
        Object.keys(get().tabs)
          .filter(tabId => tabId !== id && get().tabs[tabId].panelId === panelId)
          .forEach(tabId => {
            set(state => ({
              tabs: {
                ...state.tabs,
                [tabId]: { ...state.tabs[tabId], isActive: false }
              }
            }))
          })
      },

      closeTab: (id) => {
        const tabs = get().tabs
        const { [id]: removed, ...remaining } = tabs

        set(state => ({
          tabs: remaining,
          activeTab: state.activeTab === id ? null : state.activeTab
        }))
      },

      pinTab: (id, isPinned) => {
        const tab = get().tabs[id]
        if (!tab) return

        set(state => ({
          tabs: {
            ...state.tabs,
            [id]: { ...tab, isPinned }
          }
        }))
      },

      markTabDirty: (id, isDirty) => {
        const tab = get().tabs[id]
        if (!tab) return

        set(state => ({
          tabs: {
            ...state.tabs,
            [id]: { ...tab, isDirty }
          }
        }))
      },

      reorderTabs: (panelId, orderedIds) => {
        const updates: Record<string, TabState> = {}
        
        orderedIds.forEach((id, index) => {
          const tab = get().tabs[id]
          if (tab && tab.panelId === panelId) {
            updates[id] = { ...tab, order: index }
          }
        })

        set(state => ({
          tabs: {
            ...state.tabs,
            ...updates
          }
        }))
      },

      startTabDrag: (id) => {
        set({ draggedTab: id })
      },

      endTabDrag: (dropped) => {
        set({ draggedTab: null })
      },

      resetTabs: () => {
        set({ tabs: {}, activeTab: null, draggedTab: null })
      },

      loadTabs: (tabs) => {
        set({ tabs })
      }
    }),
    {
      name: 'vscode-tabs-store',
      partialize: (state) => ({
        tabs: state.tabs
      })
    }
  )
)

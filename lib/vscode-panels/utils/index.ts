import { CONSTANTS } from '../constants'

// Storage utils
export function saveLayout(layout: Record<string, any>) {
  try {
    localStorage.setItem(CONSTANTS.STORAGE_KEYS.LAYOUT, JSON.stringify(layout))
    return true
  } catch (error) {
    console.error('Failed to save layout:', error)
    return false
  }
}

export function loadLayout(): Record<string, any> | null {
  try {
    const saved = localStorage.getItem(CONSTANTS.STORAGE_KEYS.LAYOUT)
    return saved ? JSON.parse(saved) : null
  } catch (error) {
    console.error('Failed to load layout:', error)
    return null
  }
}

export function saveTheme(theme: 'light' | 'dark' | 'auto') {
  try {
    localStorage.setItem(CONSTANTS.STORAGE_KEYS.THEME, theme)
    return true
  } catch (error) {
    console.error('Failed to save theme:', error)
    return false
  }
}

export function loadTheme(): 'light' | 'dark' | 'auto' | null {
  try {
    return localStorage.getItem(CONSTANTS.STORAGE_KEYS.THEME) as 'light' | 'dark' | 'auto' | null
  } catch (error) {
    console.error('Failed to load theme:', error)
    return null
  }
}

// Theme utils
export function getSystemTheme(): 'light' | 'dark' {
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

// DOM utils
export function getElementDimensions(element: HTMLElement | null) {
  if (!element) return { width: 0, height: 0 }
  
  const rect = element.getBoundingClientRect()
  return {
    width: rect.width,
    height: rect.height
  }
}

// Math utils
export function clamp(value: number, min: number, max: number) {
  return Math.min(Math.max(value, min), max)
}

// Event utils
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }
    
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(later, wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let waiting = false
  
  return function throttledFunction(...args: Parameters<T>) {
    if (!waiting) {
      func(...args)
      waiting = true
      setTimeout(() => {
        waiting = false
      }, limit)
    }
  }
}

// Re-export layout utils
export * from './layout'

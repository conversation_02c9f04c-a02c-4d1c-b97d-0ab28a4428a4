// Common utility functions that don't depend on other modules

export function generateId(prefix = 'id'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
}

export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// Object utilities
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T
    Object.keys(obj).forEach(key => {
      (cloned as any)[key] = deepClone((obj as any)[key])
    })
    return cloned
  }
  
  return obj
}

export function mergeDeep<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target } as T
  
  Object.keys(source).forEach(key => {
    const sourceValue = source[key as keyof T]
    if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
      (result as any)[key] = mergeDeep((result as any)[key] || {}, sourceValue)
    } else {
      (result as any)[key] = sourceValue
    }
  })
  
  return result
}

// Array utilities
export function moveArrayItem<T>(array: T[], fromIndex: number, toIndex: number): T[] {
  const newArray = [...array]
  const item = newArray.splice(fromIndex, 1)[0]
  newArray.splice(toIndex, 0, item)
  return newArray
}

export function removeArrayItem<T>(array: T[], index: number): T[] {
  return array.filter((_, i) => i !== index)
}

export function insertArrayItem<T>(array: T[], index: number, item: T): T[] {
  const newArray = [...array]
  newArray.splice(index, 0, item)
  return newArray
}

// Validation utilities
export function isValidId(id: string): boolean {
  return typeof id === 'string' && id.length > 0 && /^[a-zA-Z0-9_-]+$/.test(id)
}

export function isValidSize(size: number): boolean {
  return typeof size === 'number' && size >= 0 && size <= 1
}

export function isValidDimension(dimension: number): boolean {
  return typeof dimension === 'number' && dimension >= 0
}

// Type guards
export function isString(value: any): value is string {
  return typeof value === 'string'
}

export function isNumber(value: any): value is number {
  return typeof value === 'number' && !isNaN(value)
}

export function isObject(value: any): value is Record<string, any> {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

export function isFunction(value: any): value is Function {
  return typeof value === 'function'
}

// DOM utilities
export function getElementSize(element: HTMLElement): { width: number; height: number } {
  const rect = element.getBoundingClientRect()
  return {
    width: rect.width,
    height: rect.height
  }
}

export function getElementPosition(element: HTMLElement): { x: number; y: number } {
  const rect = element.getBoundingClientRect()
  return {
    x: rect.left,
    y: rect.top
  }
}

// Event utilities
export function stopPropagation(e: Event): void {
  e.stopPropagation()
}

export function preventDefault(e: Event): void {
  e.preventDefault()
}

export function stopAndPrevent(e: Event): void {
  e.stopPropagation()
  e.preventDefault()
}

// CSS utilities
export function createCSSVariables(theme: Record<string, string>): Record<string, string> {
  const cssVars: Record<string, string> = {}
  
  Object.entries(theme).forEach(([key, value]) => {
    cssVars[`--vscode-${key}`] = value
  })
  
  return cssVars
}

// Performance utilities
export function measurePerformance<T>(
  name: string,
  fn: () => T
): { result: T; duration: number } {
  const start = performance.now()
  const result = fn()
  const end = performance.now()
  const duration = end - start
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`)
  }
  
  return { result, duration }
}

// Browser compatibility utilities
export function supportsLocalStorage(): boolean {
  try {
    const test = '__vscode_panels_test__'
    localStorage.setItem(test, test)
    localStorage.removeItem(test)
    return true
  } catch {
    return false
  }
}

export function supportsDragAndDrop(): boolean {
  return 'draggable' in document.createElement('div')
}

export function supportsResizeObserver(): boolean {
  return typeof ResizeObserver !== 'undefined'
}

// Error handling utilities
export function createError(code: string, message: string, context?: any): Error {
  const error = new Error(message) as any
  error.code = code
  error.context = context
  return error
}

export function handleError(error: Error, context?: string): void {
  if (process.env.NODE_ENV === 'development') {
    console.error(`[VSCode Panels Error]${context ? ` ${context}:` : ''}`, error)
  }
}

// Constants
export const CONSTANTS = {
  MIN_PANEL_SIZE: 100,
  MIN_TAB_WIDTH: 80,
  MAX_TAB_WIDTH: 200,
  DEFAULT_PANEL_WIDTH: 300,
  DEFAULT_PANEL_HEIGHT: 200,
  RESIZE_THRESHOLD: 5,
  ANIMATION_DURATION: 200,
  DEBOUNCE_DELAY: 100,
  THROTTLE_DELAY: 16, // ~60fps
  Z_INDEX: {
    PANEL: 10,
    TAB: 20,
    DRAG_PREVIEW: 9999,
    MODAL: 1000
  }
} as const

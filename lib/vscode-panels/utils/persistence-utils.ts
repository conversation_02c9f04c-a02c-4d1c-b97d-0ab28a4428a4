import type { LayoutConfig } from '../types/layout'

// Storage interface for different persistence backends
export interface StorageAdapter {
  getItem(key: string): string | null
  setItem(key: string, value: string): void
  removeItem(key: string): void
  clear(): void
}

// Local storage adapter
export class LocalStorageAdapter implements StorageAdapter {
  getItem(key: string): string | null {
    try {
      return localStorage.getItem(key)
    } catch {
      return null
    }
  }

  setItem(key: string, value: string): void {
    try {
      localStorage.setItem(key, value)
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }

  removeItem(key: string): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error)
    }
  }

  clear(): void {
    try {
      localStorage.clear()
    } catch (error) {
      console.warn('Failed to clear localStorage:', error)
    }
  }
}

// Session storage adapter
export class SessionStorageAdapter implements StorageAdapter {
  getItem(key: string): string | null {
    try {
      return sessionStorage.getItem(key)
    } catch {
      return null
    }
  }

  setItem(key: string, value: string): void {
    try {
      sessionStorage.setItem(key, value)
    } catch (error) {
      console.warn('Failed to save to sessionStorage:', error)
    }
  }

  removeItem(key: string): void {
    try {
      sessionStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to remove from sessionStorage:', error)
    }
  }

  clear(): void {
    try {
      sessionStorage.clear()
    } catch (error) {
      console.warn('Failed to clear sessionStorage:', error)
    }
  }
}

// Memory storage adapter (for testing or fallback)
export class MemoryStorageAdapter implements StorageAdapter {
  private storage = new Map<string, string>()

  getItem(key: string): string | null {
    return this.storage.get(key) || null
  }

  setItem(key: string, value: string): void {
    this.storage.set(key, value)
  }

  removeItem(key: string): void {
    this.storage.delete(key)
  }

  clear(): void {
    this.storage.clear()
  }
}

// Persistence manager
export class PersistenceManager {
  private adapter: StorageAdapter

  constructor(adapter: StorageAdapter = new LocalStorageAdapter()) {
    this.adapter = adapter
  }

  // Save layout
  saveLayout(key: string, layout: LayoutConfig): boolean {
    try {
      const serialized = JSON.stringify({
        layout,
        timestamp: Date.now(),
        version: '1.0.0'
      })
      this.adapter.setItem(key, serialized)
      return true
    } catch (error) {
      console.error('Failed to save layout:', error)
      return false
    }
  }

  // Load layout
  loadLayout(key: string): LayoutConfig | null {
    try {
      const data = this.adapter.getItem(key)
      if (!data) return null

      const parsed = JSON.parse(data)
      
      // Validate the loaded data
      if (parsed.layout && this.isValidLayout(parsed.layout)) {
        return parsed.layout
      }
      
      return null
    } catch (error) {
      console.error('Failed to load layout:', error)
      return null
    }
  }

  // Remove layout
  removeLayout(key: string): boolean {
    try {
      this.adapter.removeItem(key)
      return true
    } catch (error) {
      console.error('Failed to remove layout:', error)
      return false
    }
  }

  // List all saved layouts
  listLayouts(prefix = 'vscode-panels-'): string[] {
    try {
      const keys: string[] = []
      
      // For localStorage/sessionStorage, we need to iterate through all keys
      if (typeof Storage !== 'undefined' && this.adapter instanceof LocalStorageAdapter) {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.startsWith(prefix)) {
            keys.push(key)
          }
        }
      }
      
      return keys
    } catch (error) {
      console.error('Failed to list layouts:', error)
      return []
    }
  }

  // Clear all layouts
  clearLayouts(prefix = 'vscode-panels-'): boolean {
    try {
      const keys = this.listLayouts(prefix)
      keys.forEach(key => this.adapter.removeItem(key))
      return true
    } catch (error) {
      console.error('Failed to clear layouts:', error)
      return false
    }
  }

  // Validate layout structure
  private isValidLayout(layout: any): layout is LayoutConfig {
    return (
      layout &&
      typeof layout === 'object' &&
      typeof layout.id === 'string' &&
      typeof layout.name === 'string' &&
      typeof layout.version === 'string' &&
      layout.root &&
      typeof layout.root === 'object'
    )
  }

  // Export layouts to JSON
  exportLayouts(prefix = 'vscode-panels-'): string {
    const layouts: Record<string, any> = {}
    const keys = this.listLayouts(prefix)
    
    keys.forEach(key => {
      const layout = this.loadLayout(key)
      if (layout) {
        layouts[key] = layout
      }
    })
    
    return JSON.stringify({
      layouts,
      exportedAt: new Date().toISOString(),
      version: '1.0.0'
    }, null, 2)
  }

  // Import layouts from JSON
  importLayouts(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData)
      
      if (data.layouts && typeof data.layouts === 'object') {
        Object.entries(data.layouts).forEach(([key, layout]) => {
          if (this.isValidLayout(layout)) {
            this.saveLayout(key, layout as LayoutConfig)
          }
        })
        return true
      }
      
      return false
    } catch (error) {
      console.error('Failed to import layouts:', error)
      return false
    }
  }
}

// Default persistence manager instance
export const defaultPersistenceManager = new PersistenceManager()

// Utility functions
export function isStorageAvailable(type: 'localStorage' | 'sessionStorage' = 'localStorage'): boolean {
  try {
    const storage = type === 'localStorage' ? localStorage : sessionStorage
    const test = '__storage_test__'
    storage.setItem(test, test)
    storage.removeItem(test)
    return true
  } catch {
    return false
  }
}

export function getStorageSize(type: 'localStorage' | 'sessionStorage' = 'localStorage'): number {
  try {
    const storage = type === 'localStorage' ? localStorage : sessionStorage
    let total = 0
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key) {
        const value = storage.getItem(key)
        if (value) {
          total += key.length + value.length
        }
      }
    }
    
    return total
  } catch {
    return 0
  }
}

export function createStorageKey(prefix: string, id: string): string {
  return `${prefix}-${id}`
}

export function parseStorageKey(key: string, prefix: string): string | null {
  if (key.startsWith(`${prefix}-`)) {
    return key.substring(prefix.length + 1)
  }
  return null
}

// Debounced save function
export function createDebouncedSave(
  saveFunction: (key: string, data: any) => void,
  delay = 1000
) {
  let timeoutId: NodeJS.Timeout | null = null
  
  return (key: string, data: any) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      saveFunction(key, data)
    }, delay)
  }
}

// Auto-save hook utility
export function createAutoSave(
  key: string,
  getData: () => any,
  interval = 30000 // 30 seconds
) {
  const persistenceManager = new PersistenceManager()
  
  const save = () => {
    const data = getData()
    if (data) {
      persistenceManager.saveLayout(key, data)
    }
  }
  
  const intervalId = setInterval(save, interval)
  
  return {
    save,
    stop: () => clearInterval(intervalId)
  }
}

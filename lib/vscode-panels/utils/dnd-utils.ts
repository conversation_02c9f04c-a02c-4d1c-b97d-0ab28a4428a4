import type { DragData, DropResult } from '../types'

// Drag and drop utility functions
export function createDragData(type: 'tab' | 'panel', id: string, sourcePanel?: string, data?: any): DragData {
  return {
    type,
    id,
    sourcePanel,
    data
  }
}

export function createDropResult(success: boolean, targetPanel?: string, targetIndex?: number): DropResult {
  return {
    success,
    targetPanel,
    targetIndex
  }
}

// Check if drag data is valid
export function isValidDragData(data: any): data is DragData {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.type === 'string' &&
    ['tab', 'panel'].includes(data.type) &&
    typeof data.id === 'string'
  )
}

// Get drag preview element
export function createDragPreview(element: HTMLElement, opacity = 0.8): HTMLElement {
  const preview = element.cloneNode(true) as HTMLElement
  preview.style.opacity = opacity.toString()
  preview.style.pointerEvents = 'none'
  preview.style.position = 'fixed'
  preview.style.zIndex = '9999'
  return preview
}

// Calculate drop position
export function calculateDropPosition(
  event: DragEvent,
  targetElement: HTMLElement,
  direction: 'horizontal' | 'vertical' = 'horizontal'
): 'before' | 'after' | 'center' {
  const rect = targetElement.getBoundingClientRect()
  const threshold = 0.3 // 30% from edges
  
  if (direction === 'horizontal') {
    const relativeX = (event.clientX - rect.left) / rect.width
    if (relativeX < threshold) return 'before'
    if (relativeX > 1 - threshold) return 'after'
    return 'center'
  } else {
    const relativeY = (event.clientY - rect.top) / rect.height
    if (relativeY < threshold) return 'before'
    if (relativeY > 1 - threshold) return 'after'
    return 'center'
  }
}

// Get drop zone from point
export function getDropZoneFromPoint(x: number, y: number): Element | null {
  const elements = document.elementsFromPoint(x, y)
  return elements.find(el => el.hasAttribute('data-drop-zone')) || null
}

// Drag and drop event handlers
export function handleDragStart(
  event: DragEvent,
  data: DragData,
  onDragStart?: (data: DragData) => void
): void {
  if (!event.dataTransfer) return
  
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('text/plain', data.id)
  event.dataTransfer.setData('application/json', JSON.stringify(data))
  
  onDragStart?.(data)
}

export function handleDragOver(event: DragEvent): void {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

export function handleDrop(
  event: DragEvent,
  onDrop: (data: DragData) => DropResult
): DropResult {
  event.preventDefault()
  
  try {
    const jsonData = event.dataTransfer?.getData('application/json')
    if (jsonData) {
      const data: DragData = JSON.parse(jsonData)
      if (isValidDragData(data)) {
        return onDrop(data)
      }
    }
  } catch (error) {
    console.warn('Failed to parse drop data:', error)
  }
  
  return createDropResult(false)
}

// Touch event utilities for mobile drag and drop
export function getTouchPosition(event: TouchEvent): { x: number; y: number } {
  const touch = event.touches[0] || event.changedTouches[0]
  return {
    x: touch.clientX,
    y: touch.clientY
  }
}

export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

// Drag threshold utilities
export function exceedsDragThreshold(
  startPos: { x: number; y: number },
  currentPos: { x: number; y: number },
  threshold = 5
): boolean {
  const deltaX = Math.abs(currentPos.x - startPos.x)
  const deltaY = Math.abs(currentPos.y - startPos.y)
  return deltaX > threshold || deltaY > threshold
}

// Auto-scroll during drag
export function autoScrollDuringDrag(
  event: DragEvent | MouseEvent,
  scrollContainer: HTMLElement,
  scrollSpeed = 10,
  scrollZone = 50
): void {
  const rect = scrollContainer.getBoundingClientRect()
  const clientY = 'clientY' in event ? event.clientY : 0
  
  // Scroll up
  if (clientY < rect.top + scrollZone) {
    scrollContainer.scrollTop -= scrollSpeed
  }
  // Scroll down
  else if (clientY > rect.bottom - scrollZone) {
    scrollContainer.scrollTop += scrollSpeed
  }
}

// Drag ghost image utilities
export function setDragGhostImage(
  event: DragEvent,
  element: HTMLElement,
  offsetX = 0,
  offsetY = 0
): void {
  if (event.dataTransfer) {
    event.dataTransfer.setDragImage(element, offsetX, offsetY)
  }
}

export function createCustomDragGhost(
  content: string,
  className?: string
): HTMLElement {
  const ghost = document.createElement('div')
  ghost.textContent = content
  ghost.className = className || 'drag-ghost'
  ghost.style.position = 'absolute'
  ghost.style.top = '-1000px'
  ghost.style.left = '-1000px'
  ghost.style.padding = '4px 8px'
  ghost.style.backgroundColor = 'rgba(0, 0, 0, 0.8)'
  ghost.style.color = 'white'
  ghost.style.borderRadius = '4px'
  ghost.style.fontSize = '12px'
  ghost.style.pointerEvents = 'none'
  
  document.body.appendChild(ghost)
  
  // Clean up after a short delay
  setTimeout(() => {
    if (ghost.parentNode) {
      ghost.parentNode.removeChild(ghost)
    }
  }, 100)
  
  return ghost
}

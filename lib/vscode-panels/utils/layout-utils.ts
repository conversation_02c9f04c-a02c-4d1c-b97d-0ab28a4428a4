import type { LayoutNode, LayoutConfig } from '../types/layout'
import type { Direction, Size } from '../types'
import { generateId, deepClone } from './common'

// Layout node creation utilities
export function createLayoutNode(
  type: 'panel' | 'group' | 'placeholder',
  props: Partial<LayoutNode> = {}
): LayoutNode {
  const baseNode: LayoutNode = {
    id: generateId(type),
    type,
    size: 1,
    ...props
  }

  if (type === 'group') {
    baseNode.direction = props.direction || 'horizontal'
    baseNode.children = props.children || []
  }

  if (type === 'panel') {
    baseNode.panelId = props.panelId || baseNode.id
  }

  return baseNode
}

export function createPanelNode(panelId: string, size = 1): LayoutNode {
  return createLayoutNode('panel', { panelId, size })
}

export function createGroupNode(
  direction: Direction,
  children: LayoutNode[] = [],
  size = 1
): LayoutNode {
  return createLayoutNode('group', { direction, children, size })
}

export function createPlaceholderNode(size = 1): LayoutNode {
  return createLayoutNode('placeholder', { size })
}

// Layout traversal utilities
export function findNode(root: LayoutNode, nodeId: string): LayoutNode | undefined {
  if (root.id === nodeId) {
    return root
  }

  if (root.children) {
    for (const child of root.children) {
      const found = findNode(child, nodeId)
      if (found) return found
    }
  }

  return undefined
}

export function findParent(root: LayoutNode, nodeId: string): LayoutNode | undefined {
  if (root.children) {
    for (const child of root.children) {
      if (child.id === nodeId) {
        return root
      }

      const found = findParent(child, nodeId)
      if (found) return found
    }
  }

  return undefined
}

export function getNodePath(root: LayoutNode, nodeId: string): string[] {
  const path: string[] = []

  function buildPath(node: LayoutNode, currentPath: string[]): boolean {
    if (node.id === nodeId) {
      path.push(...currentPath, node.id)
      return true
    }

    if (node.children) {
      for (const child of node.children) {
        if (buildPath(child, [...currentPath, node.id])) {
          return true
        }
      }
    }

    return false
  }

  buildPath(root, [])
  return path
}

export function getNodeDepth(root: LayoutNode, nodeId: string): number {
  const path = getNodePath(root, nodeId)
  return path.length - 1
}

export function getMaxDepth(node: LayoutNode): number {
  if (!node.children || node.children.length === 0) {
    return 0
  }

  return 1 + Math.max(...node.children.map(child => getMaxDepth(child)))
}

// Layout manipulation utilities
export function insertNode(
  root: LayoutNode,
  node: LayoutNode,
  targetId: string,
  position: 'before' | 'after' | 'child'
): LayoutNode {
  const newRoot = deepClone(root)

  function insert(current: LayoutNode): boolean {
    if (current.id === targetId) {
      if (position === 'child' && current.type === 'group') {
        if (!current.children) current.children = []
        current.children.push(node)
        return true
      }
      return false
    }

    if (current.children) {
      for (let i = 0; i < current.children.length; i++) {
        const child = current.children[i]
        
        if (child.id === targetId) {
          if (position === 'before') {
            current.children.splice(i, 0, node)
          } else if (position === 'after') {
            current.children.splice(i + 1, 0, node)
          }
          return true
        }

        if (insert(child)) return true
      }
    }

    return false
  }

  insert(newRoot)
  return newRoot
}

export function removeNode(root: LayoutNode, nodeId: string): LayoutNode {
  const newRoot = deepClone(root)

  function remove(current: LayoutNode): LayoutNode | null {
    if (current.id === nodeId) {
      return null
    }

    if (current.children) {
      current.children = current.children
        .map(remove)
        .filter((child): child is LayoutNode => child !== null)
    }

    return current
  }

  const result = remove(newRoot)
  return result || createLayoutNode('group', { direction: 'horizontal' })
}

export function moveNode(
  root: LayoutNode,
  nodeId: string,
  targetId: string,
  position: 'before' | 'after' | 'child'
): LayoutNode {
  const nodeToMove = findNode(root, nodeId)
  if (!nodeToMove) return root

  const withoutNode = removeNode(root, nodeId)
  return insertNode(withoutNode, nodeToMove, targetId, position)
}

// Layout size utilities
export function normalizeSizes(nodes: LayoutNode[]): LayoutNode[] {
  if (nodes.length === 0) return nodes

  const totalSize = nodes.reduce((sum, node) => sum + (node.size || 0), 0)
  
  if (totalSize === 0) {
    // Equal distribution
    const equalSize = 1 / nodes.length
    return nodes.map(node => ({ ...node, size: equalSize }))
  }

  if (Math.abs(totalSize - 1) < 0.001) {
    // Already normalized
    return nodes
  }

  // Normalize to sum to 1
  return nodes.map(node => ({
    ...node,
    size: (node.size || 0) / totalSize
  }))
}

export function calculateSizes(
  node: LayoutNode,
  containerSize: Size
): Record<string, Size> {
  const sizes: Record<string, Size> = {}

  function calculate(current: LayoutNode, availableSize: Size): void {
    sizes[current.id] = availableSize

    if (current.children && current.children.length > 0) {
      const normalizedChildren = normalizeSizes(current.children)
      
      normalizedChildren.forEach(child => {
        const childSize = current.direction === 'horizontal'
          ? {
              width: availableSize.width * (child.size || 0),
              height: availableSize.height
            }
          : {
              width: availableSize.width,
              height: availableSize.height * (child.size || 0)
            }

        calculate(child, childSize)
      })
    }
  }

  calculate(node, containerSize)
  return sizes
}

// Layout validation utilities
export function validateLayoutNode(node: LayoutNode): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!node.id) {
    errors.push('Node is missing an ID')
  }

  if (!['panel', 'group', 'placeholder'].includes(node.type)) {
    errors.push(`Invalid node type: ${node.type}`)
  }

  if (typeof node.size !== 'number' || node.size < 0 || node.size > 1) {
    errors.push(`Invalid size: ${node.size}`)
  }

  if (node.type === 'group') {
    if (!node.direction || !['horizontal', 'vertical'].includes(node.direction)) {
      errors.push(`Invalid direction for group: ${node.direction}`)
    }

    if (node.children) {
      node.children.forEach((child, index) => {
        const childValidation = validateLayoutNode(child)
        if (!childValidation.isValid) {
          errors.push(`Child ${index}: ${childValidation.errors.join(', ')}`)
        }
      })
    }
  }

  if (node.type === 'panel' && !node.panelId) {
    errors.push('Panel node is missing panelId')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function validateLayout(layout: LayoutConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!layout.id) {
    errors.push('Layout is missing an ID')
  }

  if (!layout.name) {
    errors.push('Layout is missing a name')
  }

  if (!layout.version) {
    errors.push('Layout is missing a version')
  }

  if (!layout.root) {
    errors.push('Layout is missing a root node')
  } else {
    const rootValidation = validateLayoutNode(layout.root)
    if (!rootValidation.isValid) {
      errors.push(`Root node: ${rootValidation.errors.join(', ')}`)
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Layout optimization utilities
export function optimizeLayout(root: LayoutNode): LayoutNode {
  const optimized = deepClone(root)

  function optimize(node: LayoutNode): LayoutNode {
    // Remove empty groups
    if (node.type === 'group' && (!node.children || node.children.length === 0)) {
      return createPlaceholderNode(node.size)
    }

    // Flatten single-child groups
    if (node.type === 'group' && node.children && node.children.length === 1) {
      const child = optimize(node.children[0])
      child.size = node.size
      return child
    }

    // Optimize children
    if (node.children) {
      node.children = node.children.map(optimize)
      
      // Merge consecutive groups with same direction
      const mergedChildren: LayoutNode[] = []
      for (const child of node.children) {
        if (
          child.type === 'group' &&
          node.type === 'group' &&
          child.direction === node.direction &&
          child.children
        ) {
          // Merge child's children into current level
          mergedChildren.push(...child.children.map(grandchild => ({
            ...grandchild,
            size: (grandchild.size || 0) * (child.size || 0)
          })))
        } else {
          mergedChildren.push(child)
        }
      }
      
      node.children = normalizeSizes(mergedChildren)
    }

    return node
  }

  return optimize(optimized)
}

// Layout serialization utilities
export function serializeLayout(layout: LayoutConfig): string {
  return JSON.stringify(layout, null, 2)
}

export function deserializeLayout(data: string): LayoutConfig {
  try {
    const parsed = JSON.parse(data)
    
    // Validate the parsed layout
    const validation = validateLayout(parsed)
    if (!validation.isValid) {
      throw new Error(`Invalid layout: ${validation.errors.join(', ')}`)
    }
    
    return parsed
  } catch (error) {
    throw new Error(`Failed to deserialize layout: ${error}`)
  }
}

import type { LayoutNode, LayoutConfig } from '../types/layout'

export function findNode(rootNode: LayoutNode, nodeId: string): LayoutNode | null {
  if (rootNode.id === nodeId) return rootNode
  
  if (!rootNode.children) return null
  
  for (const child of rootNode.children) {
    const found = findNode(child, nodeId)
    if (found) return found
  }
  
  return null
}

export function removeNode(rootNode: LayoutNode, nodeId: string): LayoutNode | null {
  if (!rootNode.children) return rootNode

  rootNode.children = rootNode.children.filter(child => child.id !== nodeId)
  
  for (const child of rootNode.children) {
    removeNode(child, nodeId)
  }
  
  return rootNode
}

export function insertNode(
  rootNode: LayoutNode,
  parentId: string,
  newNode: LayoutNode,
  index?: number
): LayoutNode {
  if (rootNode.id === parentId) {
    if (!rootNode.children) {
      rootNode.children = []
    }
    
    if (typeof index === 'number') {
      rootNode.children.splice(index, 0, newNode)
    } else {
      rootNode.children.push(newNode)
    }
    
    return rootNode
  }
  
  if (!rootNode.children) return rootNode
  
  for (const child of rootNode.children) {
    insertNode(child, parentId, newNode, index)
  }
  
  return rootNode
}

export function createLayout(
  id: string,
  name: string,
  panels: Record<string, any> = {},
  root?: LayoutNode
): LayoutConfig {
  return {
    id,
    name,
    version: '1.0.0',
    root: root || {
      id: 'root',
      type: 'group',
      direction: 'horizontal',
      children: []
    },
    panels
  }
}

export function createPanelNode(
  id: string,
  size?: number
): LayoutNode {
  return {
    id,
    type: 'panel',
    size
  }
}

export function createGroupNode(
  id: string,
  direction: 'horizontal' | 'vertical',
  children: LayoutNode[] = [],
  size?: number
): LayoutNode {
  return {
    id,
    type: 'group',
    direction,
    children,
    size
  }
}

export function createPlaceholderNode(
  id: string,
  size?: number
): LayoutNode {
  return {
    id,
    type: 'placeholder',
    size
  }
}

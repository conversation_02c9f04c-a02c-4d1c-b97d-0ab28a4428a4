# VS Code Panels and Tabs System - Library Structure

## 📁 Directory Structure

```
lib/vscode-panels/
├── README.md                          # Main documentation
├── LIBRARY_STRUCTURE.md              # This file
├── index.ts                           # Main exports
├── types/                            # TypeScript type definitions
│   ├── index.ts                      # Common types and re-exports
│   ├── panel.ts                      # Panel-related types
│   ├── tab.ts                        # Tab-related types
│   └── layout.ts                     # Layout-related types
├── store/                            # Zustand state management
│   ├── index.ts                      # Store exports and combined hooks
│   ├── panel-store.ts                # Panel state management
│   ├── tab-store.ts                  # Tab state management
│   └── layout-store.ts               # Layout state management
├── components/                       # React components
│   ├── index.ts                      # Component exports
│   ├── layout/                       # Layout components
│   │   ├── workspace.tsx             # Main workspace container
│   │   ├── layout-provider.tsx       # Layout context provider
│   │   └── layout-manager.tsx        # Layout orchestrator
│   ├── panel/                        # Panel components
│   │   ├── panel.tsx                 # Base panel component
│   │   ├── panel-group.tsx           # Panel group container
│   │   ├── panel-header.tsx          # Panel header with controls
│   │   ├── panel-content.tsx         # Panel content area
│   │   └── resize-handle.tsx         # Drag handle for resizing
│   ├── tabs/                         # Tab components
│   │   ├── tab.tsx                   # Individual tab
│   │   ├── tab-bar.tsx               # Tab container
│   │   ├── tab-content.tsx           # Tab content wrapper
│   │   └── tab-actions.tsx           # Tab actions (close, pin, etc.)
│   └── dnd/                          # Drag and drop components
│       ├── dnd-provider.tsx          # DnD context provider
│       ├── draggable-tab.tsx         # Draggable tab wrapper
│       └── droppable-area.tsx        # Drop zone component
├── hooks/                            # Custom React hooks
│   ├── index.ts                      # Hook exports
│   ├── use-panel.ts                  # Panel management hooks
│   ├── use-tabs.ts                   # Tab management hooks
│   ├── use-layout.ts                 # Layout management hooks
│   ├── use-drag-drop.ts              # Drag and drop hooks
│   └── use-persistence.ts            # Layout persistence hooks
├── utils/                            # Utility functions
│   ├── index.ts                      # Utility exports
│   ├── layout-utils.ts               # Layout calculations
│   ├── dnd-utils.ts                  # Drag and drop utilities
│   └── persistence-utils.ts          # Persistence utilities
├── plugins/                          # Plugin system
│   ├── index.ts                      # Plugin exports
│   ├── plugin-manager.ts             # Plugin management
│   ├── base-plugin.ts                # Base plugin class
│   └── built-in/                     # Built-in plugins
│       ├── theme-plugin.ts           # Theme management
│       ├── keyboard-shortcuts-plugin.ts # Keyboard shortcuts
│       └── persistence-plugin.ts     # Layout persistence
└── demo/                             # Demo components
    └── basic-demo.tsx                # Basic usage examples
```

## 🏗️ Architecture Overview

### Core Components

1. **Workspace** - Main container that orchestrates the entire system
2. **Panel** - Individual resizable containers that can hold tabs
3. **Tab** - Individual tabs within panels with content
4. **PanelGroup** - Groups panels with resizable dividers
5. **Layout Manager** - Handles layout rendering and management

### State Management

The library uses Zustand for state management with three main stores:

- **Panel Store** - Manages panel state, sizing, and positioning
- **Tab Store** - Manages tab state, ordering, and content
- **Layout Store** - Manages overall layout configuration and persistence

### Key Features

#### ✅ Implemented Features

- **Resizable Panels** - Drag handles for intuitive panel resizing
- **Panel Groups** - Horizontal and vertical panel grouping
- **Tab System** - Full tab management with activation and ordering
- **Tab Pinning** - Pin important tabs to prevent accidental closure
- **Layout Management** - Flexible layout configurations with nesting
- **State Persistence** - Save and restore layout configurations
- **TypeScript Support** - Full type safety throughout the library
- **Framer Motion Animations** - Smooth animations and transitions
- **Extensible Architecture** - Plugin system for custom functionality
- **Drag and Drop Foundation** - Basic DnD infrastructure
- **Theme Support** - Light/dark theme compatibility
- **Error Boundaries** - Graceful error handling
- **Performance Optimized** - Efficient re-rendering with selectors

#### 🚧 Partially Implemented

- **Drag and Drop** - Basic infrastructure in place, needs full implementation
- **Plugin System** - Foundation created, needs built-in plugins
- **Keyboard Shortcuts** - Hooks created, needs implementation
- **Context Menus** - Structure defined, needs implementation

#### 📋 Future Enhancements

- **Tab Groups** - Grouping related tabs together
- **Split Panels** - Split single panels into multiple panes
- **Panel Floating** - Detach panels into floating windows
- **Advanced Persistence** - Cloud sync and multiple layout profiles
- **Accessibility** - Full ARIA support and keyboard navigation
- **Mobile Support** - Touch-friendly interactions
- **Performance Monitoring** - Built-in performance metrics

## 🎯 Usage Examples

### Basic Usage

```tsx
import { Workspace, Panel, Tab, PanelGroup } from '@/lib/vscode-panels'

function App() {
  return (
    <Workspace>
      <PanelGroup direction="horizontal">
        <Panel id="sidebar" title="Explorer">
          <Tab id="files" title="Files">
            <FileExplorer />
          </Tab>
        </Panel>
        <Panel id="editor" title="Editor">
          <Tab id="main" title="main.tsx" pinned>
            <CodeEditor />
          </Tab>
        </Panel>
      </PanelGroup>
    </Workspace>
  )
}
```

### Advanced Usage with Hooks

```tsx
import { useWorkspaceStore, useTabs, usePanel } from '@/lib/vscode-panels'

function CustomPanel({ panelId }: { panelId: string }) {
  const { tabs, create, remove } = useTabs(panelId)
  const { isActive, activate } = usePanel(panelId)
  
  return (
    <div>
      {tabs.map(tab => (
        <div key={tab.id} onClick={() => activate()}>
          {tab.title}
        </div>
      ))}
    </div>
  )
}
```

## 🔧 Configuration

### Default Configuration

```tsx
const DEFAULT_CONFIG = {
  theme: 'auto',
  animations: {
    duration: 200,
    easing: 'ease-in-out',
    enabled: true,
  },
  persistence: {
    enabled: true,
    key: 'vscode-panels-layout',
    storage: 'localStorage',
  },
  dragDrop: {
    enabled: true,
    threshold: 5,
    ghostOpacity: 0.5,
  },
  panels: {
    minWidth: 100,
    minHeight: 100,
    defaultWidth: 300,
    defaultHeight: 200,
  },
  tabs: {
    maxWidth: 200,
    showCloseButton: true,
    showPinButton: true,
  },
}
```

## 🎨 Theming

The library supports CSS custom properties for theming:

```css
:root {
  --vscode-panel-bg: #1e1e1e;
  --vscode-panel-border: #333;
  --vscode-tab-bg: #2d2d30;
  --vscode-tab-active-bg: #1e1e1e;
  --vscode-tab-text: #cccccc;
  --vscode-tab-active-text: #ffffff;
  --vscode-accent-color: #007acc;
}
```

## 🧪 Testing

The library includes comprehensive TypeScript types and error boundaries for robust development experience. Each component is designed to fail gracefully and provide helpful error messages.

## 📈 Performance

- **Optimized Re-rendering** - Uses Zustand selectors to minimize re-renders
- **Lazy Loading** - Tab content can be lazily loaded
- **Memory Management** - Proper cleanup of event listeners and subscriptions
- **Animation Performance** - Hardware-accelerated animations with Framer Motion

## 🤝 Contributing

The library is designed to be extensible and maintainable:

- **Modular Architecture** - Each feature is self-contained
- **TypeScript First** - Full type safety and IntelliSense support
- **Plugin System** - Easy to extend with custom functionality
- **Consistent Patterns** - Follows established React and TypeScript patterns

## 📄 License

Part of the Soimagine Digital Agency project.

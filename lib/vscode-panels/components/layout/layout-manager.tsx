"use client"

import React, { useMemo } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { useLayoutContext } from './layout-provider'
import { PanelGroup } from '../panel/panel-group'
import { Panel } from '../panel/panel'
import { ResizeHandle } from '../panel/resize-handle'
import type { LayoutNode } from '../../types/layout'
import type { Size } from '../../types'

interface LayoutManagerProps {
  allowModification?: boolean
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}

export function LayoutManager({
  allowModification = true,
  className,
  style,
  children
}: LayoutManagerProps) {
  const { layout, resizeNode } = useLayoutContext()

  // Render a layout node recursively
  const renderNode = (node: LayoutNode, depth = 0): React.ReactNode => {
    const key = `${node.id}-${depth}`

    if (node.type === 'panel') {
      return (
        <Panel
          key={key}
          id={node.panelId || node.id}
          title={`Panel ${node.panelId || node.id}`}
          className="flex-1"
          style={{
            flex: node.size || 1,
            minWidth: node.minSize || 100,
            maxWidth: node.maxSize || 'none'
          }}
        >
          {/* Panel content will be provided by children or default content */}
          <div className="p-4 text-sm text-muted-foreground">
            Panel content for {node.panelId || node.id}
          </div>
        </Panel>
      )
    }

    if (node.type === 'group' && node.children && node.children.length > 0) {
      return (
        <PanelGroup
          key={key}
          id={node.id}
          direction={node.direction || 'horizontal'}
          className="flex-1"
          style={{
            flex: node.size || 1
          }}
        >
          {node.children.map((child, index) => (
            <React.Fragment key={`${child.id}-${index}`}>
              {renderNode(child, depth + 1)}
              
              {/* Add resize handle between children (except after the last one) */}
              {allowModification && index < node.children!.length - 1 && (
                <ResizeHandle
                  direction={node.direction || 'horizontal'}
                  onResize={(delta) => {
                    // Calculate new sizes for adjacent children
                    const currentChild = node.children![index]
                    const nextChild = node.children![index + 1]
                    
                    if (currentChild && nextChild) {
                      const currentSize = currentChild.size || 0.5
                      const nextSize = nextChild.size || 0.5
                      const totalSize = currentSize + nextSize
                      
                      // Calculate delta as percentage of total
                      const deltaPercent = delta / 1000 // Normalize delta
                      
                      const newCurrentSize = Math.max(0.1, Math.min(0.9, currentSize + deltaPercent))
                      const newNextSize = totalSize - newCurrentSize
                      
                      // Update both nodes
                      resizeNode(currentChild.id, newCurrentSize)
                      resizeNode(nextChild.id, newNextSize)
                    }
                  }}
                  className="bg-border hover:bg-accent transition-colors"
                />
              )}
            </React.Fragment>
          ))}
        </PanelGroup>
      )
    }

    if (node.type === 'placeholder') {
      return (
        <div
          key={key}
          className={cn(
            'flex-1 border-2 border-dashed border-muted-foreground/20',
            'flex items-center justify-center',
            'text-muted-foreground text-sm',
            'bg-muted/10 rounded-md'
          )}
          style={{
            flex: node.size || 1,
            minWidth: node.minSize || 100,
            minHeight: 100
          }}
        >
          Drop panel here
        </div>
      )
    }

    return null
  }

  // Calculate layout statistics for debugging
  const layoutStats = useMemo(() => {
    const calculateStats = (node: LayoutNode): { panels: number; groups: number; depth: number } => {
      let panels = 0
      let groups = 0
      let maxDepth = 0

      if (node.type === 'panel') {
        panels = 1
      } else if (node.type === 'group') {
        groups = 1
      }

      if (node.children) {
        for (const child of node.children) {
          const childStats = calculateStats(child)
          panels += childStats.panels
          groups += childStats.groups
          maxDepth = Math.max(maxDepth, childStats.depth + 1)
        }
      }

      return { panels, groups, depth: maxDepth }
    }

    return calculateStats(layout.root)
  }, [layout])

  return (
    <motion.div
      className={cn(
        'layout-manager',
        'w-full h-full flex flex-col',
        'bg-background',
        className
      )}
      style={style}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
    >
      {/* Debug info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-0 left-0 z-50 p-2 bg-black/80 text-white text-xs rounded-br-md">
          Panels: {layoutStats.panels} | Groups: {layoutStats.groups} | Depth: {layoutStats.depth}
        </div>
      )}

      {/* Main layout content */}
      <div className="flex-1 flex overflow-hidden">
        {layout.root ? renderNode(layout.root) : (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <div className="text-lg font-medium mb-2">No layout defined</div>
              <div className="text-sm">Add panels to get started</div>
            </div>
          </div>
        )}
      </div>

      {/* Custom children (for additional UI elements) */}
      {children}
    </motion.div>
  )
}

// Layout renderer with error boundary
export function LayoutRenderer({ node, depth = 0 }: { node: LayoutNode; depth?: number }) {
  const { resizeNode } = useLayoutContext()

  try {
    if (node.type === 'panel') {
      return (
        <Panel
          id={node.panelId || node.id}
          title={`Panel ${node.panelId || node.id}`}
          style={{
            flex: node.size || 1,
            minWidth: node.minSize || 100,
            maxWidth: node.maxSize || 'none'
          }}
        >
          <div className="p-4">
            Panel content for {node.panelId || node.id}
          </div>
        </Panel>
      )
    }

    if (node.type === 'group' && node.children) {
      return (
        <PanelGroup
          id={node.id}
          direction={node.direction || 'horizontal'}
          style={{
            flex: node.size || 1
          }}
        >
          {node.children.map((child, index) => (
            <React.Fragment key={child.id}>
              <LayoutRenderer node={child} depth={depth + 1} />
              
              {index < node.children!.length - 1 && (
                <ResizeHandle
                  direction={node.direction || 'horizontal'}
                  onResize={(delta) => {
                    const currentChild = node.children![index]
                    const nextChild = node.children![index + 1]
                    
                    if (currentChild && nextChild) {
                      const deltaPercent = delta / 1000
                      const newCurrentSize = Math.max(0.1, Math.min(0.9, (currentChild.size || 0.5) + deltaPercent))
                      const newNextSize = Math.max(0.1, Math.min(0.9, (nextChild.size || 0.5) - deltaPercent))
                      
                      resizeNode(currentChild.id, newCurrentSize)
                      resizeNode(nextChild.id, newNextSize)
                    }
                  }}
                />
              )}
            </React.Fragment>
          ))}
        </PanelGroup>
      )
    }

    return (
      <div className="flex-1 border-2 border-dashed border-muted-foreground/20 flex items-center justify-center">
        <span className="text-muted-foreground text-sm">Empty layout node</span>
      </div>
    )
  } catch (error) {
    console.error('Error rendering layout node:', error)
    return (
      <div className="flex-1 border border-destructive bg-destructive/10 flex items-center justify-center">
        <span className="text-destructive text-sm">Error rendering node: {node.id}</span>
      </div>
    )
  }
}

export type { LayoutManagerProps }

"use client"

import React, { useEffect } from 'react'
import { cn } from '@/lib/utils'
import { loadLayout, saveLayout, getSystemTheme } from '../../utils'
import { useWorkspaceStore } from '../../store/workspace-store'
import { isLayoutConfig } from '../../types/layout'
import type { WorkspaceProps } from '../../types/layout'

export function Workspace({
  children,
  theme = 'auto',
  persistLayout = true,
  allowLayoutModification = true,
  showLayoutControls = true,
  layoutConfig,
  onLayoutChange,
  className,
  style
}: WorkspaceProps) {
  const { layout, setLayout, setTheme, getEffectiveTheme } = useWorkspaceStore()

  // Load saved layout on mount
  useEffect(() => {
    if (persistLayout) {
      const savedLayout = loadLayout()
      if (savedLayout && isLayoutConfig(savedLayout)) {
        setLayout(savedLayout)
      } else if (layoutConfig && isLayoutConfig(layoutConfig)) {
        setLayout(layoutConfig)
      }
    } else if (layoutConfig && isLayoutConfig(layoutConfig)) {
      setLayout(layoutConfig)
    }
  }, [persistLayout, layoutConfig, setLayout])

  // Save layout when it changes
  useEffect(() => {
    if (layout && persistLayout) {
      saveLayout(layout)
      onLayoutChange?.(layout)
    }
  }, [layout, persistLayout, onLayoutChange])

  // Update theme when prop changes
  useEffect(() => {
    setTheme(theme)
  }, [theme, setTheme])

  // Get effective theme to apply
  const currentTheme = getEffectiveTheme()

  return (
    <div
      className={cn(
        'vscode-workspace',
        'flex flex-col',
        'bg-[var(--vscode-workspace-bg)]',
        'overflow-hidden',
        currentTheme === 'dark' ? 'dark' : '',
        className
      )}
      style={style}
      data-theme={currentTheme}
      data-allow-modify={allowLayoutModification}
    >
      {/* Layout controls */}
      {showLayoutControls && (
        <div className="vscode-layout-controls flex gap-2 p-2 border-b border-[var(--vscode-panel-border)]">
          {/* Implement layout controls */}
        </div>
      )}

      {/* Workspace content */}
      <div className="flex-1 flex">
        {children}
      </div>
    </div>
  )
}

Workspace.displayName = 'VSCodeWorkspace'

export default Workspace

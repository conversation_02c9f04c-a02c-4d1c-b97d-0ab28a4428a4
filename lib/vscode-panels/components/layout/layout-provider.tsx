"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import type { 
  LayoutProviderProps, 
  LayoutContext, 
  LayoutConfig, 
  LayoutNode,
  LayoutConstraints,
  LayoutValidationResult
} from '../../types/layout'
import type { Direction } from '../../types'
import { useLayoutStore } from '../../store/layout-store'

// Create the layout context
const LayoutContextProvider = createContext<LayoutContext | null>(null)

// Layout provider component
export function LayoutProvider({
  children,
  initialLayout,
  constraints,
  persistLayout = true,
  persistenceKey = 'vscode-panels-layout'
}: LayoutProviderProps) {
  const {
    currentLayout,
    setLayout,
    addPanel,
    removePanel,
    movePanel,
    resizeNode,
    splitNode,
    undo,
    redo,
    reset,
    validateLayout,
    history,
    historyIndex,
    isModified
  } = useLayoutStore()

  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize layout
  useEffect(() => {
    if (!isInitialized) {
      if (initialLayout) {
        setLayout(initialLayout)
      } else if (persistLayout) {
        // Try to load from storage
        try {
          const stored = localStorage.getItem(persistenceKey)
          if (stored) {
            const layout: LayoutConfig = JSON.parse(stored)
            setLayout(layout)
          }
        } catch (error) {
          console.warn('Failed to load layout from storage:', error)
        }
      }
      setIsInitialized(true)
    }
  }, [initialLayout, persistLayout, persistenceKey, setLayout, isInitialized])

  // Save layout to storage when it changes
  useEffect(() => {
    if (isInitialized && persistLayout && currentLayout) {
      try {
        localStorage.setItem(persistenceKey, JSON.stringify(currentLayout))
      } catch (error) {
        console.warn('Failed to save layout to storage:', error)
      }
    }
  }, [currentLayout, persistLayout, persistenceKey, isInitialized])

  // Utility functions
  const findNode = (nodeId: string): LayoutNode | undefined => {
    const searchNode = (node: LayoutNode): LayoutNode | undefined => {
      if (node.id === nodeId) return node
      
      if (node.children) {
        for (const child of node.children) {
          const found = searchNode(child)
          if (found) return found
        }
      }
      
      return undefined
    }
    
    return searchNode(currentLayout.root)
  }

  const findParent = (nodeId: string): LayoutNode | undefined => {
    const searchParent = (node: LayoutNode): LayoutNode | undefined => {
      if (node.children) {
        for (const child of node.children) {
          if (child.id === nodeId) return node
          
          const found = searchParent(child)
          if (found) return found
        }
      }
      
      return undefined
    }
    
    return searchParent(currentLayout.root)
  }

  const getPath = (nodeId: string): string[] => {
    const path: string[] = []
    
    const buildPath = (node: LayoutNode, currentPath: string[]): boolean => {
      if (node.id === nodeId) {
        path.push(...currentPath, node.id)
        return true
      }
      
      if (node.children) {
        for (const child of node.children) {
          if (buildPath(child, [...currentPath, node.id])) {
            return true
          }
        }
      }
      
      return false
    }
    
    buildPath(currentLayout.root, [])
    return path
  }

  const validateCurrentLayout = (): LayoutValidationResult => {
    return validateLayout(currentLayout)
  }

  // Context value
  const contextValue: LayoutContext = {
    layout: currentLayout,
    constraints,
    isModified,
    canUndo: historyIndex > 0,
    canRedo: historyIndex < history.length - 1,
    
    // Actions
    setLayout,
    addPanel,
    removePanel,
    movePanel,
    resizeNode,
    splitNode,
    undo,
    redo,
    reset,
    
    // Utilities
    findNode,
    findParent,
    getPath,
    validateLayout: validateCurrentLayout
  }

  return (
    <LayoutContextProvider.Provider value={contextValue}>
      {children}
    </LayoutContextProvider.Provider>
  )
}

// Hook to use the layout context
export function useLayoutContext(): LayoutContext {
  const context = useContext(LayoutContextProvider)
  
  if (!context) {
    throw new Error('useLayoutContext must be used within a LayoutProvider')
  }
  
  return context
}

// Convenience hooks for specific layout operations
export function useLayoutActions() {
  const context = useLayoutContext()
  
  return {
    addPanel: context.addPanel,
    removePanel: context.removePanel,
    movePanel: context.movePanel,
    resizeNode: context.resizeNode,
    splitNode: context.splitNode,
    undo: context.undo,
    redo: context.redo,
    reset: context.reset
  }
}

export function useLayoutState() {
  const context = useLayoutContext()
  
  return {
    layout: context.layout,
    constraints: context.constraints,
    isModified: context.isModified,
    canUndo: context.canUndo,
    canRedo: context.canRedo
  }
}

export function useLayoutUtils() {
  const context = useLayoutContext()
  
  return {
    findNode: context.findNode,
    findParent: context.findParent,
    getPath: context.getPath,
    validateLayout: context.validateLayout
  }
}

// Hook for layout validation
export function useLayoutValidation() {
  const { validateLayout } = useLayoutContext()
  const [validation, setValidation] = useState<LayoutValidationResult>({ isValid: true, errors: [], warnings: [] })
  
  useEffect(() => {
    const result = validateLayout()
    setValidation(result)
  }, [validateLayout])
  
  return validation
}

// Hook for layout constraints checking
export function useLayoutConstraints() {
  const { layout, constraints } = useLayoutContext()
  
  const checkConstraints = (operation: string, ...args: any[]): boolean => {
    if (!constraints) return true
    
    // TODO: Implement constraint checking logic
    // This would check things like:
    // - Maximum number of panels
    // - Allowed directions
    // - Nesting depth limits
    // - Required panels
    // - Forbidden combinations
    
    return true
  }
  
  return {
    constraints,
    checkConstraints,
    canAddPanel: () => checkConstraints('addPanel'),
    canRemovePanel: (panelId: string) => checkConstraints('removePanel', panelId),
    canMovePanel: (panelId: string, targetNode: string) => checkConstraints('movePanel', panelId, targetNode),
    canSplitNode: (nodeId: string, direction: Direction) => checkConstraints('splitNode', nodeId, direction)
  }
}

export type { LayoutProviderProps, LayoutContext }

"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, MoreHorizontal } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import type { TabAction, TabContextMenuItem } from '../../types/tab'

interface TabActionsProps {
  tabId: string
  actions?: TabAction[]
  contextMenu?: TabContextMenuItem[]
  showPin?: boolean
  showClose?: boolean
  isPinned?: boolean
  isClosable?: boolean
  onPin?: (pinned: boolean) => void
  onClose?: () => void
  className?: string
}

export function TabActions({
  tabId,
  actions = [],
  contextMenu = [],
  showPin = true,
  showClose = true,
  isPinned = false,
  isClosable = true,
  onPin,
  onClose,
  className
}: TabActionsProps) {
  return (
    <div className={cn('flex items-center gap-1', className)}>
      {/* Custom actions */}
      {actions.map((action) => (
        <Button
          key={action.id}
          variant="ghost"
          size="sm"
          className={cn(
            'h-4 w-4 p-0',
            'opacity-0 group-hover:opacity-100',
            'hover:bg-accent',
            action.disabled && 'opacity-50 cursor-not-allowed'
          )}
          onClick={(e) => {
            e.stopPropagation()
            action.onClick()
          }}
          disabled={action.disabled}
          title={action.tooltip || action.label}
        >
          {action.icon ? (
            <action.icon className="w-3 h-3" />
          ) : (
            <span className="text-xs">{action.label.charAt(0)}</span>
          )}
        </Button>
      ))}

      {/* Pin/Unpin button */}
      {showPin && (
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'h-4 w-4 p-0',
            'opacity-0 group-hover:opacity-100',
            'hover:bg-accent',
            isPinned && 'opacity-100 text-accent-foreground'
          )}
          onClick={(e) => {
            e.stopPropagation()
            onPin?.(!isPinned)
          }}
          title={isPinned ? 'Unpin tab' : 'Pin tab'}
        >
          <Pin className={cn('w-3 h-3', isPinned && 'fill-current')} />
        </Button>
      )}

      {/* Context menu */}
      {contextMenu.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                'h-4 w-4 p-0',
                'opacity-0 group-hover:opacity-100',
                'hover:bg-accent'
              )}
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="w-3 h-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {contextMenu.map((item, index) => (
              <React.Fragment key={item.id}>
                {item.separator ? (
                  <DropdownMenuSeparator />
                ) : (
                  <DropdownMenuItem
                    onClick={item.onClick}
                    disabled={item.disabled}
                    className="flex items-center gap-2"
                  >
                    {item.icon && <item.icon className="w-4 h-4" />}
                    <span>{item.label}</span>
                    {item.shortcut && (
                      <span className="ml-auto text-xs text-muted-foreground">
                        {item.shortcut}
                      </span>
                    )}
                  </DropdownMenuItem>
                )}
              </React.Fragment>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Close button */}
      {showClose && isClosable && !isPinned && (
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'h-4 w-4 p-0',
            'opacity-0 group-hover:opacity-100',
            'hover:bg-destructive hover:text-destructive-foreground'
          )}
          onClick={(e) => {
            e.stopPropagation()
            onClose?.()
          }}
          title="Close tab"
        >
          <X className="w-3 h-3" />
        </Button>
      )}
    </div>
  )
}

// Compact tab actions for minimal layouts
export function CompactTabActions({
  tabId,
  isPinned = false,
  isClosable = true,
  onPin,
  onClose,
  className
}: Pick<TabActionsProps, 'tabId' | 'isPinned' | 'isClosable' | 'onPin' | 'onClose' | 'className'>) {
  return (
    <div className={cn('flex items-center gap-0.5', className)}>
      {isPinned && <Pin className="w-2 h-2 fill-current text-accent-foreground" />}
      
      {isClosable && !isPinned && (
        <Button
          variant="ghost"
          size="sm"
          className="h-3 w-3 p-0 opacity-0 hover:opacity-100"
          onClick={(e) => {
            e.stopPropagation()
            onClose?.()
          }}
        >
          <X className="w-2 h-2" />
        </Button>
      )}
    </div>
  )
}

// Default context menu items
export const DEFAULT_TAB_CONTEXT_MENU: TabContextMenuItem[] = [
  {
    id: 'close',
    label: 'Close',
    shortcut: 'Ctrl+W',
    onClick: () => {}
  },
  {
    id: 'close-others',
    label: 'Close Others',
    onClick: () => {}
  },
  {
    id: 'close-to-right',
    label: 'Close to the Right',
    onClick: () => {}
  },
  {
    id: 'separator-1',
    label: '',
    separator: true
  },
  {
    id: 'pin',
    label: 'Pin Tab',
    onClick: () => {}
  },
  {
    id: 'duplicate',
    label: 'Duplicate Tab',
    onClick: () => {}
  },
  {
    id: 'separator-2',
    label: '',
    separator: true
  },
  {
    id: 'copy-path',
    label: 'Copy Path',
    icon: Copy,
    onClick: () => {}
  }
]

export { TabActions as default }

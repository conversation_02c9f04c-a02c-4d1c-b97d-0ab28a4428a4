"use client"

import React, { useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Pin, Circle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useTabStore } from '../../store/tab-store'
import type { TabProps } from '../../types/tab'
import { TabContent } from './tab-content'

export function Tab({
  id,
  title,
  children,
  className,
  style,
  
  // Visual props
  icon: Icon,
  tooltip,
  badge,
  
  // State flags
  isActive,
  isPinned,
  isDirty,
  isClosable = true,
  isLoading,
  
  // Event handlers
  onActivate,
  onClose,
  onPin,
  onContextMenu,
  onDoubleClick,
  
  // Drag and drop
  draggable = true,
  onDragStart,
  onDragEnd,
  
  // Custom props
  actions = [],
  contextMenu = [],
  metadata = {}
}: TabProps) {
  const tabRef = useRef<HTMLDivElement>(null)
  const dragWrapperRef = useRef<HTMLDivElement>(null)
  
  // Store hooks
  const { 
    tabs, 
    updateTab, 
    activateTab, 
    closeTab, 
    pinTab,
    markTabDirty,
    startTabDrag,
    endTabDrag
  } = useTabStore()

  // Get tab state from store
  const tabState = tabs[id]

  // Initialize tab in store if it doesn't exist
  useEffect(() => {
    if (!tabState) {
      updateTab(id, {
        title,
        isPinned,
        isDirty,
        isClosable,
        isLoading,
        icon: Icon,
        metadata
      })
    }
  }, [id, title, isPinned, isDirty, isClosable, isLoading, Icon, metadata, tabState, updateTab])

  // Update tab state when props change
  useEffect(() => {
    if (tabState) {
      updateTab(id, {
        title,
        isPinned,
        isDirty,
        isClosable,
        isLoading,
        icon: Icon,
        metadata
      })
    }
  }, [
    id, title, isPinned, isDirty, isClosable, isLoading, 
    Icon, metadata, tabState, updateTab
  ])

  // Handle tab activation
  const handleActivate = () => {
    if (tabState) {
      activateTab(id, tabState.panelId)
    }
    onActivate?.()
  }

  // Handle tab close
  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isClosable && tabState?.isClosable) {
      closeTab(id)
      onClose?.()
    }
  }

  // Handle tab pin/unpin
  const handlePin = (e: React.MouseEvent) => {
    e.stopPropagation()
    const newPinned = !tabState?.isPinned
    pinTab(id, newPinned)
    onPin?.(newPinned)
  }

  // Handle drag start
  const handleDragStart = (e: React.DragEvent) => {
    if (!draggable || !tabState) return
    
    startTabDrag(id)
    
    // Set drag data
    e.dataTransfer.setData('text/plain', id)
    e.dataTransfer.setData('application/vscode-tab', JSON.stringify({
      tabId: id,
      panelId: tabState.panelId,
      title: tabState.title,
      index: tabState.order
    }))
    
    onDragStart?.(e)
  }

  // Handle drag end
  const handleDragEnd = (e: React.DragEvent) => {
    endTabDrag(e.dataTransfer.dropEffect === 'move')
    onDragEnd?.(e)
  }

  // Handle context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault()
    onContextMenu?.(e)
    // TODO: Show context menu
  }

  // Determine tab state
  const isActiveTab = isActive ?? tabState?.isActive ?? false
  const isPinnedTab = isPinned ?? tabState?.isPinned ?? false
  const isDirtyTab = isDirty ?? tabState?.isDirty ?? false
  const isLoadingTab = isLoading ?? tabState?.isLoading ?? false
  const isClosableTab = isClosable && (tabState?.isClosable ?? true)

  const tabContent = (
    <motion.div
      ref={tabRef}
      className={cn(
        'vscode-tab',
        'relative flex items-center gap-2',
        'h-8 px-3 py-1',
        'bg-[var(--vscode-tab-bg)]',
        'border-r border-[var(--vscode-panel-border)]',
        'text-[var(--vscode-tab-text)]',
        'cursor-pointer select-none',
        'hover:bg-[var(--vscode-tab-active-bg)]/50',
        'transition-colors duration-150',
        
        // State classes
        isActiveTab && [
          'bg-[var(--vscode-tab-active-bg)]',
          'text-[var(--vscode-tab-active-text)]',
          'border-b-2 border-[var(--vscode-accent-color)]'
        ],
        
        isPinnedTab && 'min-w-[120px]',
        isDirtyTab && 'italic',
        isLoadingTab && 'opacity-75',
        
        className
      )}
      style={style}
      onClick={handleActivate}
      onContextMenu={handleContextMenu}
      onDoubleClick={onDoubleClick}
      title={tooltip || title}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.2 }}
      layout
    >
      {/* Loading indicator */}
      <AnimatePresence>
        {isLoadingTab && (
          <motion.div
            className="w-3 h-3 border border-[var(--vscode-accent-color)] border-t-transparent rounded-full animate-spin"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
          />
        )}
      </AnimatePresence>

      {/* Icon */}
      {Icon && !isLoadingTab && (
        <Icon className="w-4 h-4 flex-shrink-0" />
      )}

      {/* Pin indicator */}
      {isPinnedTab && (
        <Pin className="w-3 h-3 flex-shrink-0 text-[var(--vscode-accent-color)]" />
      )}

      {/* Title */}
      <span className="text-sm truncate flex-1 min-w-0">
        {title}
      </span>

      {/* Dirty indicator */}
      {isDirtyTab && !isLoadingTab && (
        <Circle className="w-2 h-2 flex-shrink-0 fill-current text-[var(--vscode-accent-color)]" />
      )}

      {/* Badge */}
      {badge && (
        <span className="px-1 py-0.5 text-xs bg-[var(--vscode-accent-color)] text-white rounded">
          {badge}
        </span>
      )}

      {/* Custom actions */}
      {actions.length > 0 && (
        <div className="flex items-center gap-1">
          {actions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100"
              onClick={(e) => {
                e.stopPropagation()
                action.onClick()
              }}
              disabled={action.disabled}
              title={action.tooltip || action.label}
            >
              {action.icon ? (
                <action.icon className="w-3 h-3" />
              ) : (
                <span className="text-xs">{action.label.charAt(0)}</span>
              )}
            </Button>
          ))}
        </div>
      )}

      {/* Pin/Unpin button */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'h-4 w-4 p-0',
          'opacity-0 hover:opacity-100',
          isActiveTab && 'opacity-60 hover:opacity-100',
          isPinnedTab && 'opacity-100'
        )}
        onClick={handlePin}
        title={isPinnedTab ? 'Unpin tab' : 'Pin tab'}
      >
        <Pin className={cn(
          'w-3 h-3',
          isPinnedTab && 'text-[var(--vscode-accent-color)]'
        )} />
      </Button>

      {/* Close button */}
      {isClosableTab && !isPinnedTab && (
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'h-4 w-4 p-0',
            'opacity-0 hover:opacity-100',
            'hover:bg-destructive hover:text-destructive-foreground',
            isActiveTab && 'opacity-60 hover:opacity-100'
          )}
          onClick={handleClose}
          title="Close tab"
        >
          <X className="w-3 h-3" />
        </Button>
      )}

      {/* Active tab indicator */}
      {isActiveTab && (
        <motion.div
          className="absolute bottom-0 left-0 right-0 h-0.5 bg-[var(--vscode-accent-color)]"
          layoutId="activeTabIndicator"
          transition={{ duration: 0.2 }}
        />
      )}
    </motion.div>
  )

  return (
    <>
      {draggable ? (
        <div
          ref={dragWrapperRef}
          draggable
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          {tabContent}
        </div>
      ) : tabContent}

      {/* Tab content */}
      {isActiveTab && (
        <TabContent>
          {children}
        </TabContent>
      )}
    </>
  )
}

Tab.displayName = 'VSCodeTab'

export default Tab

"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { TabContentProps } from '../../types/tab'

export function TabContent({
  children,
  className,
  style
}: TabContentProps) {
  return (
    <motion.div
      className={cn(
        'vscode-tab-content',
        'flex-1 overflow-auto',
        className
      )}
      style={style}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      {children}
    </motion.div>
  )
}

TabContent.displayName = 'VSCodeTabContent'

export default TabContent

"use client"

import React, { type ReactElement } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { TabBarProps, TabProps } from '../../types/tab'
import { Tab } from './tab'

export function TabBar({
  children,
  className,
  style,
  activeTabId,
  onTabActivate
}: TabBarProps) {
  return (
    <motion.div
      className={cn(
        'vscode-tab-bar',
        'flex items-center',
        'h-9 min-h-[2.25rem]',
        'bg-[var(--vscode-tab-bg)]',
        'border-b border-[var(--vscode-panel-border)]',
        className
      )}
      style={style}
      layout
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement<TabProps>(child) && child.type === Tab) {
          return React.cloneElement(child as ReactElement<TabProps>, {
            ...child.props,
            isActive: child.props.id === activeTabId,
            onActivate: () => onTabActivate?.(child.props.id)
          })
        }
        return child
      })}
    </motion.div>
  )
}

TabBar.displayName = 'VSCodeTabBar'

export default TabBar

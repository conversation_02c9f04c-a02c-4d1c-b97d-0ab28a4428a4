"use client"

import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { usePanelStore } from '../../store'

export interface DragOverlayProps {
  className?: string
  style?: React.CSSProperties
}

export function DragOverlay({ className, style }: DragOverlayProps) {
  const overlayRef = useRef<HTMLDivElement>(null)
  const { draggedPanel } = usePanelStore()
  
  // Track mouse movement when dragging
  useEffect(() => {
    if (!draggedPanel) return

    const handleMouseMove = (e: MouseEvent) => {
      if (!overlayRef.current) return

      // Position overlay at mouse cursor
      overlayRef.current.style.left = `${e.pageX}px`
      overlayRef.current.style.top = `${e.pageY}px`
    }

    window.addEventListener('mousemove', handleMouseMove)
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
    }
  }, [draggedPanel])

  if (!draggedPanel) return null

  return (
    <motion.div
      ref={overlayRef}
      className={cn(
        'vscode-drag-overlay',
        'fixed z-50',
        'pointer-events-none',
        'bg-[var(--vscode-panel-bg)]',
        'border-2 border-[var(--vscode-accent-color)]',
        'rounded-md shadow-lg',
        'opacity-75',
        'w-64 h-24',
        'flex items-center justify-center',
        'transform -translate-x-1/2 -translate-y-1/2',
        className
      )}
      style={style}
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 0.75 }}
      exit={{ scale: 0.8, opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className="text-sm font-medium text-[var(--vscode-panel-text)]">
        Moving panel...
      </div>
    </motion.div>
  )
}

DragOverlay.displayName = 'VSCodeDragOverlay'

export default DragOverlay

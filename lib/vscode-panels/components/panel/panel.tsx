"use client"

import React, { useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { usePanelStore } from '../../store'
import { ErrorBoundary } from '../error'
import { ResizeHandle } from './resize-handle'
import { PanelHeader } from './panel-header'
import { PanelContent } from './panel-content'
import type { PanelProps } from '../../types/panel'

export function Panel({
  id,
  title,
  children,
  className,
  style,
  
  // Visual props
  icon,
  minWidth = 100,
  minHeight = 100,
  defaultWidth = 300,
  defaultHeight = 200,
  
  // State flags
  isActive,
  isCollapsed,
  isClosable = true,
  
  // Event handlers
  onActivate,
  onClose,
  onCollapse,
  onResize,
  onContextMenu,
  
  // Drag and drop
  draggable = true,
  onDragStart,
  onDragEnd,
  
  // Layout props
  direction = 'vertical',
  
  // Custom props
  headerActions = [],
  contextMenu = [],
  metadata = {}
}: PanelProps) {
  const panelRef = useRef<HTMLDivElement>(null)
  const dragRef = useRef<HTMLDivElement>(null)
  
  // Store hooks
  const {
    panels,
    updatePanel,
    activatePanel,
    removePanel,
    collapsePanel,
    resizePanel,
    startPanelDrag,
    endPanelDrag
  } = usePanelStore()

  // Get panel state from store
  const panelState = panels[id]

  // Initialize panel in store if it doesn't exist
  useEffect(() => {
    if (!panelState) {
      updatePanel(id, {
        id,
        title,
        icon,
        minWidth,
        minHeight,
        defaultWidth,
        defaultHeight,
        isActive: isActive ?? false,
        isCollapsed: isCollapsed ?? false,
        isClosable,
        direction,
        metadata
      })
    }
  }, [id, panelState, updatePanel])

  // Update panel state when props change
  useEffect(() => {
    if (panelState) {
      updatePanel(id, {
        title,
        icon,
        minWidth,
        minHeight,
        defaultWidth,
        defaultHeight,
        isActive: isActive ?? panelState.isActive,
        isCollapsed: isCollapsed ?? panelState.isCollapsed,
        isClosable,
        direction,
        metadata
      })
    }
  }, [
    id, title, icon, minWidth, minHeight, defaultWidth, defaultHeight,
    isActive, isCollapsed, isClosable, direction, metadata, panelState, updatePanel
  ])

  // Handle activation
  const handleActivate = () => {
    if (panelState) {
      activatePanel(id)
      onActivate?.()
    }
  }

  // Handle close
  const handleClose = () => {
    if (isClosable && panelState?.isClosable) {
      removePanel(id)
      onClose?.()
    }
  }

  // Handle collapse
  const handleCollapse = () => {
    const newCollapsed = !panelState?.isCollapsed
    collapsePanel(id, newCollapsed)
    onCollapse?.(newCollapsed)
  }

  // Handle resize
  const handleResize = (delta: number) => {
    if (!panelState) return

    if (direction === 'horizontal') {
      const newWidth = (panelState.width || defaultWidth) + delta
      if (newWidth >= minWidth) {
        resizePanel(id, { width: newWidth })
        onResize?.(newWidth, undefined)
      }
    } else {
      const newHeight = (panelState.height || defaultHeight) + delta
      if (newHeight >= minHeight) {
        resizePanel(id, { height: newHeight })
        onResize?.(undefined, newHeight)
      }
    }
  }

  // Handle drag start
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    if (!panelState || !draggable) return

    startPanelDrag(id)
    
    e.dataTransfer.setData('text/plain', id)
    e.dataTransfer.setData('application/vscode-panel', JSON.stringify({
      panelId: id,
      title: panelState.title,
      size: {
        width: panelState.width,
        height: panelState.height
      }
    }))
    
    onDragStart?.(e)
  }

  // Handle drag end
  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    if (!draggable) return
    
    endPanelDrag(e.dataTransfer.dropEffect === 'move')
    onDragEnd?.(e)
  }

  // Handle context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault()
    onContextMenu?.(e)
    
    // TODO: Show context menu with actions
  }

  // Get dimensions
  const width = panelState?.width ?? defaultWidth
  const height = panelState?.height ?? defaultHeight
  const isCollapsedPanel = isCollapsed ?? panelState?.isCollapsed ?? false
  const isActivePanel = isActive ?? panelState?.isActive ?? false

  return (
    <ErrorBoundary>
      <motion.div
        ref={panelRef}
        className={cn(
          'vscode-panel',
          'relative flex',
          'bg-[var(--vscode-panel-bg)]',
          'border-r border-[var(--vscode-panel-border)]',
          direction === 'vertical' ? 'flex-col' : 'flex-row',
          className
        )}
        style={{
          ...style,
          width: isCollapsedPanel ? 'auto' : width,
          height: isCollapsedPanel ? 'auto' : height,
          minWidth: isCollapsedPanel ? 'auto' : minWidth,
          minHeight: isCollapsedPanel ? 'auto' : minHeight
        }}
        onClick={handleActivate}
        onContextMenu={handleContextMenu}
        initial={false}
        animate={{
          width: isCollapsedPanel ? 'auto' : width,
          height: isCollapsedPanel ? 'auto' : height
        }}
        transition={{ duration: 0.2 }}
        layout
      >
        {/* Panel Header */}
        <PanelHeader
          title={title}
          icon={icon}
          isActive={isActivePanel}
          isCollapsed={isCollapsedPanel}
          isClosable={isClosable}
          onClose={handleClose}
          onCollapse={handleCollapse}
          actions={headerActions}
        />

        {/* Panel Content */}
        {!isCollapsedPanel && (
          <PanelContent>
            <div
              ref={dragRef}
              draggable={draggable}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              className="h-full"
            >
              {children}
            </div>
          </PanelContent>
        )}

        {/* Resize Handles */}
        {!isCollapsedPanel && (
          <>
            {direction === 'horizontal' && (
              <ResizeHandle
                direction="horizontal"
                minSize={minWidth}
                onResize={handleResize}
              />
            )}
            {direction === 'vertical' && (
              <ResizeHandle
                direction="vertical"
                minSize={minHeight}
                onResize={handleResize}
              />
            )}
          </>
        )}

        {/* Active indicator */}
        {isActivePanel && (
          <motion.div
            className="absolute inset-0 pointer-events-none border-2 border-[var(--vscode-accent-color)]"
            layoutId={`panel-active-${id}`}
            transition={{ duration: 0.2 }}
          />
        )}
      </motion.div>
    </ErrorBoundary>
  )
}

Panel.displayName = 'VSCodePanel'

export default Panel
export type { PanelProps }

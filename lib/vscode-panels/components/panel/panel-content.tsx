"use client"

import React from 'react'
import { cn } from '@/lib/utils'
import type { PanelContentProps } from '../../types/panel'

export function PanelContent({
  children,
  className,
  style
}: PanelContentProps) {
  return (
    <div
      className={cn(
        'vscode-panel-content',
        'flex-1 overflow-hidden',
        className
      )}
      style={style}
    >
      {children}
    </div>
  )
}

PanelContent.displayName = 'VSCodePanelContent'

export default PanelContent

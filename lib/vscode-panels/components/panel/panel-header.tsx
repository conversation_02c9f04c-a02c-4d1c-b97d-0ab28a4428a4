"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { Maximize2, Minimize2, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import type { PanelHeaderProps } from '../../types/panel'

export function PanelHeader({
  title,
  icon: Icon,
  isActive,
  isCollapsed,
  isClosable,
  onClose,
  onCollapse,
  actions = []
}: PanelHeaderProps) {
  return (
    <div
      className={cn(
        'vscode-panel-header',
        'flex items-center gap-2',
        'h-10 px-3 py-1',
        'bg-[var(--vscode-panel-bg)]',
        'border-b border-[var(--vscode-panel-border)]',
        'text-[var(--vscode-tab-text)]',
        'select-none',
        isActive && [
          'bg-[var(--vscode-tab-active-bg)]',
          'text-[var(--vscode-tab-active-text)]'
        ]
      )}
    >
      {/* Icon */}
      {Icon && <Icon className="w-4 h-4 flex-shrink-0" />}

      {/* Title */}
      <motion.div 
        className="text-sm font-medium truncate flex-1"
        layout
      >
        {title}
      </motion.div>

      {/* Custom actions */}
      {actions.length > 0 && (
        <div className="flex items-center gap-1">
          {actions.map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              className={cn(
                'h-6 w-6 p-0',
                'opacity-60 hover:opacity-100',
                'hover:bg-[var(--vscode-tab-active-bg)]'
              )}
              onClick={action.onClick}
              disabled={action.disabled}
              title={action.tooltip || action.label}
            >
              {action.icon ? (
                <action.icon className="w-4 h-4" />
              ) : (
                <span className="text-xs">{action.label.charAt(0)}</span>
              )}
            </Button>
          ))}
        </div>
      )}

      {/* Collapse/Expand button */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'h-6 w-6 p-0',
          'opacity-60 hover:opacity-100',
          'hover:bg-[var(--vscode-tab-active-bg)]'
        )}
        onClick={onCollapse}
        title={isCollapsed ? 'Expand panel' : 'Collapse panel'}
      >
        {isCollapsed ? (
          <Maximize2 className="w-4 h-4" />
        ) : (
          <Minimize2 className="w-4 h-4" />
        )}
      </Button>

      {/* Close button */}
      {isClosable && (
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'h-6 w-6 p-0',
            'opacity-60 hover:opacity-100',
            'hover:bg-destructive hover:text-destructive-foreground'
          )}
          onClick={onClose}
          title="Close panel"
        >
          <X className="w-4 h-4" />
        </Button>
      )}
    </div>
  )
}

PanelHeader.displayName = 'VSCodePanelHeader'

export default PanelHeader

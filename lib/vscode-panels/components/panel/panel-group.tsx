"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { ResizeHandle } from './resize-handle'
import type { PanelGroupProps } from '../../types/layout'

export function PanelGroup({
  id,
  direction = 'horizontal',
  size,
  minSize,
  children,
  metadata,
  className,
  style
}: PanelGroupProps) {
  const isHorizontal = direction === 'horizontal'

  return (
    <motion.div
      className={cn(
        'vscode-panel-group',
        'relative flex',
        'bg-[var(--vscode-panel-bg)]',
        isHorizontal ? 'flex-row' : 'flex-col',
        className
      )}
      style={{
        ...style,
        width: isHorizontal ? size : '100%',
        height: isHorizontal ? '100%' : size,
        minWidth: isHorizontal ? minSize : undefined,
        minHeight: isHorizontal ? undefined : minSize
      }}
      initial={false}
      animate={{
        width: isHorizontal ? size : '100%',
        height: isHorizontal ? '100%' : size
      }}
      transition={{ duration: 0.2 }}
      layout
      data-group-id={id}
      data-direction={direction}
    >
      {/* Panel group content */}
      {React.Children.map(children, (child, index) => {
        if (!child) return null

        const isLast = index === React.Children.count(children) - 1

        return (
          <>
            {child}
            {!isLast && (
              <ResizeHandle
                direction={direction}
                minSize={minSize}
                onResize={(delta) => {
                  // TODO: Implement group resize logic
                  console.log('Group resize:', { delta })
                }}
              />
            )}
          </>
        )
      })}
    </motion.div>
  )
}

PanelGroup.displayName = 'VSCodePanelGroup'

export default PanelGroup

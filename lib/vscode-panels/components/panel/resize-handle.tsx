"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { CONSTANTS } from '../../constants'

export interface ResizeHandleProps {
  direction: 'horizontal' | 'vertical'
  minSize?: number
  onResize: (delta: number) => void
  className?: string
  style?: React.CSSProperties
}

export function ResizeHandle({
  direction,
  minSize = CONSTANTS.MIN_PANEL_SIZE,
  onResize,
  className,
  style
}: ResizeHandleProps) {
  // Track resize state
  let startX = 0
  let startY = 0

  // Handle mouse down to start resize
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    startX = e.pageX
    startY = e.pageY

    // Add window event listeners
    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('mouseup', handleMouseUp)
  }

  // Handle mouse move during resize
  const handleMouseMove = (e: MouseEvent) => {
    if (direction === 'horizontal') {
      const deltaX = e.pageX - startX
      onResize(deltaX)
      startX = e.pageX
    } else {
      const deltaY = e.pageY - startY
      onResize(deltaY)
      startY = e.pageY
    }
  }

  // Handle mouse up to end resize
  const handleMouseUp = () => {
    window.removeEventListener('mousemove', handleMouseMove)
    window.removeEventListener('mouseup', handleMouseUp)
  }

  return (
    <motion.div
      className={cn(
        'vscode-resize-handle',
        'absolute z-10',
        'hover:bg-[var(--vscode-accent-color)] hover:opacity-50',
        direction === 'horizontal'
          ? 'right-0 top-0 bottom-0 w-1 cursor-col-resize'
          : 'bottom-0 left-0 right-0 h-1 cursor-row-resize',
        className
      )}
      style={style}
      onMouseDown={handleMouseDown}
      initial={false}
      whileHover={{ scale: 1.5 }}
      transition={{ duration: 0.2 }}
      layout
    />
  )
}

ResizeHandle.displayName = 'VSCodeResizeHandle'

export default ResizeHandle

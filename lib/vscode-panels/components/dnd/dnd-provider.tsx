"use client"

import React, { createContext, useContext, useState, useCallback } from 'react'
import type { DragData, DropResult } from '../../types'

// Drag and drop context
interface DndContextType {
  isDragging: boolean
  dragData: DragData | null
  dragPreview: React.ReactNode | null
  
  // Actions
  startDrag: (data: DragData, preview?: React.ReactNode) => void
  endDrag: (result?: DropResult) => void
  updateDragPreview: (preview: React.ReactNode) => void
  
  // Drop zone registration
  registerDropZone: (id: string, onDrop: (data: DragData) => DropResult) => void
  unregisterDropZone: (id: string) => void
}

const DndContext = createContext<DndContextType | null>(null)

// Drag and drop provider
export function DndProvider({ children }: { children: React.ReactNode }) {
  const [isDragging, setIsDragging] = useState(false)
  const [dragData, setDragData] = useState<DragData | null>(null)
  const [dragPreview, setDragPreview] = useState<React.ReactNode | null>(null)
  const [dropZones, setDropZones] = useState<Map<string, (data: DragData) => DropResult>>(new Map())

  // Start drag operation
  const startDrag = useCallback((data: DragData, preview?: React.ReactNode) => {
    setIsDragging(true)
    setDragData(data)
    setDragPreview(preview || null)
  }, [])

  // End drag operation
  const endDrag = useCallback((result?: DropResult) => {
    setIsDragging(false)
    setDragData(null)
    setDragPreview(null)
    
    // TODO: Handle drag end result
    if (result?.success) {
      console.log('Drag operation successful:', result)
    }
  }, [])

  // Update drag preview
  const updateDragPreview = useCallback((preview: React.ReactNode) => {
    setDragPreview(preview)
  }, [])

  // Register drop zone
  const registerDropZone = useCallback((id: string, onDrop: (data: DragData) => DropResult) => {
    setDropZones(prev => new Map(prev).set(id, onDrop))
  }, [])

  // Unregister drop zone
  const unregisterDropZone = useCallback((id: string) => {
    setDropZones(prev => {
      const newMap = new Map(prev)
      newMap.delete(id)
      return newMap
    })
  }, [])

  const contextValue: DndContextType = {
    isDragging,
    dragData,
    dragPreview,
    startDrag,
    endDrag,
    updateDragPreview,
    registerDropZone,
    unregisterDropZone
  }

  return (
    <DndContext.Provider value={contextValue}>
      {children}
      
      {/* Drag preview overlay */}
      {isDragging && dragPreview && (
        <DragPreviewOverlay preview={dragPreview} />
      )}
    </DndContext.Provider>
  )
}

// Hook to use drag and drop context
export function useDnd() {
  const context = useContext(DndContext)
  if (!context) {
    throw new Error('useDnd must be used within a DndProvider')
  }
  return context
}

// Drag preview overlay component
function DragPreviewOverlay({ preview }: { preview: React.ReactNode }) {
  const [position, setPosition] = useState({ x: 0, y: 0 })

  React.useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setPosition({ x: e.clientX + 10, y: e.clientY + 10 })
    }

    document.addEventListener('mousemove', handleMouseMove)
    return () => document.removeEventListener('mousemove', handleMouseMove)
  }, [])

  return (
    <div
      className="fixed pointer-events-none z-[9999] opacity-80"
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -50%)'
      }}
    >
      {preview}
    </div>
  )
}

// Hook for draggable elements
export function useDraggable(data: DragData, preview?: React.ReactNode) {
  const { startDrag, endDrag } = useDnd()

  const handleDragStart = useCallback((e: React.DragEvent) => {
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/plain', data.id)
    e.dataTransfer.setData('application/json', JSON.stringify(data))
    
    startDrag(data, preview)
  }, [data, preview, startDrag])

  const handleDragEnd = useCallback((e: React.DragEvent) => {
    const success = e.dataTransfer.dropEffect !== 'none'
    endDrag({ success })
  }, [endDrag])

  return {
    draggable: true,
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd
  }
}

// Hook for droppable elements
export function useDroppable(
  id: string,
  onDrop: (data: DragData) => DropResult,
  canDrop?: (data: DragData) => boolean
) {
  const { registerDropZone, unregisterDropZone, dragData } = useDnd()
  const [isOver, setIsOver] = useState(false)
  const [canDropHere, setCanDropHere] = useState(false)

  // Register/unregister drop zone
  React.useEffect(() => {
    registerDropZone(id, onDrop)
    return () => unregisterDropZone(id)
  }, [id, onDrop, registerDropZone, unregisterDropZone])

  // Check if can drop
  React.useEffect(() => {
    if (dragData && canDrop) {
      setCanDropHere(canDrop(dragData))
    } else {
      setCanDropHere(true)
    }
  }, [dragData, canDrop])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    if (canDropHere) {
      e.preventDefault()
      e.dataTransfer.dropEffect = 'move'
    }
  }, [canDropHere])

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    if (canDropHere) {
      e.preventDefault()
      setIsOver(true)
    }
  }, [canDropHere])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    // Only set isOver to false if we're actually leaving the element
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsOver(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsOver(false)

    try {
      const jsonData = e.dataTransfer.getData('application/json')
      if (jsonData) {
        const data: DragData = JSON.parse(jsonData)
        const result = onDrop(data)
        return result
      }
    } catch (error) {
      console.warn('Failed to parse drop data:', error)
    }

    return { success: false }
  }, [onDrop])

  return {
    isOver: isOver && canDropHere,
    canDrop: canDropHere,
    onDragOver: handleDragOver,
    onDragEnter: handleDragEnter,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop
  }
}

export type { DndContextType }

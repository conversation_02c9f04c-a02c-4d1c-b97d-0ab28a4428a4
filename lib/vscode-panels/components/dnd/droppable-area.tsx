"use client"

import React, { forwardRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { useDroppable } from '../../hooks/use-drag-drop'
import { createDropResult } from '../../utils/dnd-utils'
import type { DragData, DropResult } from '../../types'

interface DroppableAreaProps {
  children: React.ReactNode
  className?: string
  disabled?: boolean
  panelId?: string
  acceptTypes?: ('tab' | 'panel')[]
  onDrop?: (data: DragData) => DropResult
  onDragEnter?: (data: DragData) => void
  onDragLeave?: () => void
  showDropIndicator?: boolean
  dropIndicatorText?: string
}

export const DroppableArea = forwardRef<HTMLDivElement, DroppableAreaProps>(
  ({ 
    children, 
    className, 
    disabled = false,
    panelId,
    acceptTypes = ['tab'],
    onDrop,
    onDragEnter,
    onDragLeave,
    showDropIndicator = true,
    dropIndicatorText = 'Drop here'
  }, ref) => {
    
    const handleDrop = (data: DragData): DropResult => {
      if (onDrop) {
        return onDrop(data)
      }
      
      // Default drop behavior
      return createDropResult(true, panelId)
    }

    const canDrop = (data: DragData): boolean => {
      // Check if the drag type is accepted
      if (!acceptTypes.includes(data.type)) {
        return false
      }
      
      // Don't allow dropping on the same panel for tabs
      if (data.type === 'tab' && data.sourcePanel === panelId) {
        return false
      }
      
      return true
    }

    const { isOver, canDrop: canDropHere, dropProps } = useDroppable(handleDrop, {
      disabled,
      canDrop,
      onDragEnter,
      onDragLeave
    })

    return (
      <div
        ref={ref}
        className={cn(
          'droppable-area relative',
          isOver && canDropHere && 'drop-target-active',
          disabled && 'drop-disabled',
          className
        )}
        {...dropProps}
        data-drop-zone="true"
        data-panel-id={panelId}
      >
        {children}
        
        {/* Drop indicator overlay */}
        <AnimatePresence>
          {showDropIndicator && isOver && canDropHere && (
            <motion.div
              className={cn(
                'absolute inset-0 z-50',
                'border-2 border-dashed border-primary',
                'bg-primary/10 backdrop-blur-sm',
                'flex items-center justify-center',
                'pointer-events-none'
              )}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.15 }}
            >
              <div className={cn(
                'px-4 py-2 rounded-md',
                'bg-primary text-primary-foreground',
                'text-sm font-medium',
                'shadow-lg'
              )}>
                {dropIndicatorText}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Invalid drop indicator */}
        <AnimatePresence>
          {showDropIndicator && isOver && !canDropHere && (
            <motion.div
              className={cn(
                'absolute inset-0 z-50',
                'border-2 border-dashed border-destructive',
                'bg-destructive/10 backdrop-blur-sm',
                'flex items-center justify-center',
                'pointer-events-none'
              )}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.15 }}
            >
              <div className={cn(
                'px-4 py-2 rounded-md',
                'bg-destructive text-destructive-foreground',
                'text-sm font-medium',
                'shadow-lg'
              )}>
                Cannot drop here
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }
)

DroppableArea.displayName = 'DroppableArea'

// Specialized droppable area for tab bars
export const TabDropArea = forwardRef<HTMLDivElement, Omit<DroppableAreaProps, 'acceptTypes'> & {
  insertIndex?: number
}>(({ insertIndex, onDrop, ...props }, ref) => {
  const handleTabDrop = (data: DragData): DropResult => {
    if (data.type === 'tab') {
      return createDropResult(true, props.panelId, insertIndex)
    }
    return createDropResult(false)
  }

  return (
    <DroppableArea
      ref={ref}
      {...props}
      acceptTypes={['tab']}
      onDrop={onDrop || handleTabDrop}
      dropIndicatorText={`Insert tab${insertIndex !== undefined ? ` at position ${insertIndex + 1}` : ''}`}
    />
  )
})

TabDropArea.displayName = 'TabDropArea'

// Specialized droppable area for panel groups
export const PanelDropArea = forwardRef<HTMLDivElement, Omit<DroppableAreaProps, 'acceptTypes'> & {
  direction?: 'horizontal' | 'vertical'
  position?: 'before' | 'after' | 'center'
}>(({ direction = 'horizontal', position = 'center', onDrop, ...props }, ref) => {
  const handlePanelDrop = (data: DragData): DropResult => {
    if (data.type === 'panel' || data.type === 'tab') {
      return createDropResult(true, props.panelId)
    }
    return createDropResult(false)
  }

  const getDropText = () => {
    if (position === 'before') return `Drop ${direction === 'horizontal' ? 'left' : 'above'}`
    if (position === 'after') return `Drop ${direction === 'horizontal' ? 'right' : 'below'}`
    return 'Drop in panel'
  }

  return (
    <DroppableArea
      ref={ref}
      {...props}
      acceptTypes={['tab', 'panel']}
      onDrop={onDrop || handlePanelDrop}
      dropIndicatorText={getDropText()}
    />
  )
})

PanelDropArea.displayName = 'PanelDropArea'

export default DroppableArea

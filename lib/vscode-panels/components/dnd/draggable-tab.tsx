"use client"

import React, { forwardRef } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import { useDraggable } from '../../hooks/use-drag-drop'
import { createDragData } from '../../utils/dnd-utils'
import type { TabState } from '../../types/tab'

interface DraggableTabProps {
  tab: TabState
  children: React.ReactNode
  className?: string
  disabled?: boolean
  onDragStart?: (tabId: string) => void
  onDragEnd?: (tabId: string, success: boolean) => void
}

export const DraggableTab = forwardRef<HTMLDivElement, DraggableTabProps>(
  ({ tab, children, className, disabled = false, onDragStart, onDragEnd }, ref) => {
    const dragData = createDragData('tab', tab.id, tab.panelId, {
      title: tab.title,
      isPinned: tab.isPinned,
      isDirty: tab.isDirty
    })

    const { isDragging, dragProps } = useDraggable(dragData, {
      disabled: disabled || tab.isPinned, // Pinned tabs can't be dragged
      onDragStart: () => onDragStart?.(tab.id),
      onDragEnd: (success) => onDragEnd?.(tab.id, success),
      createPreview: () => {
        // Create a custom drag preview
        const preview = document.createElement('div')
        preview.className = 'drag-preview-tab'
        preview.style.cssText = `
          padding: 4px 12px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          border-radius: 4px;
          font-size: 12px;
          white-space: nowrap;
          pointer-events: none;
          position: absolute;
          top: -1000px;
          left: -1000px;
          z-index: 9999;
        `
        preview.textContent = tab.title
        document.body.appendChild(preview)
        
        // Clean up after drag
        setTimeout(() => {
          if (preview.parentNode) {
            preview.parentNode.removeChild(preview)
          }
        }, 100)
        
        return preview
      }
    })

    return (
      <motion.div
        ref={ref}
        className={cn(
          'draggable-tab',
          isDragging && 'opacity-50 scale-95',
          disabled && 'cursor-not-allowed',
          className
        )}
        {...dragProps}
        animate={{
          opacity: isDragging ? 0.5 : 1,
          scale: isDragging ? 0.95 : 1
        }}
        transition={{ duration: 0.1 }}
      >
        {children}
      </motion.div>
    )
  }
)

DraggableTab.displayName = 'DraggableTab'

export default DraggableTab

"use client"

import React from 'react'
import { X, AlertTriangle, RefreshCcw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export interface ErrorBoundaryProps {
  children: React.ReactNode
  onReset?: () => void
  className?: string
  style?: React.CSSProperties
}

interface State {
  hasError: boolean
  error: Error | null
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, State> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('VSCode Panel error:', error, errorInfo)
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null })
    this.props.onReset?.()
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          className={cn(
            'vscode-error-boundary',
            'flex flex-col items-center justify-center',
            'p-4 min-h-[200px]',
            'bg-destructive/10 text-destructive',
            this.props.className
          )}
          style={this.props.style}
        >
          <AlertTriangle className="w-12 h-12 mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            Something went wrong
          </h3>
          <p className="text-sm text-muted-foreground mb-4 text-center">
            {this.state.error?.message || 'An error occurred while rendering this panel'}
          </p>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={this.handleReset}
            >
              <RefreshCcw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => window.location.reload()}
            >
              <X className="w-4 h-4 mr-2" />
              Close
            </Button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary

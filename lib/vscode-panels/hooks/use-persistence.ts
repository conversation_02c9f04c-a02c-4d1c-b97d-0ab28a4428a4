import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  PersistenceManager, 
  LocalStorageAdapter, 
  SessionStorageAdapter,
  MemoryStorageAdapter,
  isStorageAvailable,
  createDebouncedSave
} from '../utils/persistence-utils'
import type { LayoutConfig } from '../types/layout'

// Hook for layout persistence
export function usePersistence(
  key: string,
  options: {
    storage?: 'localStorage' | 'sessionStorage' | 'memory'
    debounceDelay?: number
    autoSave?: boolean
    onSave?: (layout: LayoutConfig) => void
    onLoad?: (layout: LayoutConfig) => void
    onError?: (error: Error) => void
  } = {}
) {
  const {
    storage = 'localStorage',
    debounceDelay = 1000,
    autoSave = true,
    onSave,
    onLoad,
    onError
  } = options

  // Create persistence manager with appropriate adapter
  const persistenceManager = useRef<PersistenceManager>()
  
  useEffect(() => {
    let adapter
    
    switch (storage) {
      case 'sessionStorage':
        adapter = isStorageAvailable('sessionStorage') 
          ? new SessionStorageAdapter() 
          : new MemoryStorageAdapter()
        break
      case 'memory':
        adapter = new MemoryStorageAdapter()
        break
      default:
        adapter = isStorageAvailable('localStorage') 
          ? new LocalStorageAdapter() 
          : new MemoryStorageAdapter()
    }
    
    persistenceManager.current = new PersistenceManager(adapter)
  }, [storage])

  // Debounced save function
  const debouncedSave = useRef<((key: string, layout: LayoutConfig) => void) | null>(null)
  
  useEffect(() => {
    if (persistenceManager.current) {
      debouncedSave.current = createDebouncedSave(
        (key: string, layout: LayoutConfig) => {
          try {
            const success = persistenceManager.current!.saveLayout(key, layout)
            if (success) {
              onSave?.(layout)
            } else {
              onError?.(new Error('Failed to save layout'))
            }
          } catch (error) {
            onError?.(error as Error)
          }
        },
        debounceDelay
      )
    }
  }, [debounceDelay, onSave, onError])

  // Save layout
  const save = useCallback((layout: LayoutConfig, immediate = false) => {
    if (!persistenceManager.current) return false

    try {
      if (immediate) {
        const success = persistenceManager.current.saveLayout(key, layout)
        if (success) {
          onSave?.(layout)
        }
        return success
      } else if (debouncedSave.current) {
        debouncedSave.current(key, layout)
        return true
      }
      return false
    } catch (error) {
      onError?.(error as Error)
      return false
    }
  }, [key, onSave, onError])

  // Load layout
  const load = useCallback((): LayoutConfig | null => {
    if (!persistenceManager.current) return null

    try {
      const layout = persistenceManager.current.loadLayout(key)
      if (layout) {
        onLoad?.(layout)
      }
      return layout
    } catch (error) {
      onError?.(error as Error)
      return null
    }
  }, [key, onLoad, onError])

  // Remove layout
  const remove = useCallback((): boolean => {
    if (!persistenceManager.current) return false

    try {
      return persistenceManager.current.removeLayout(key)
    } catch (error) {
      onError?.(error as Error)
      return false
    }
  }, [key, onError])

  // Check if layout exists
  const exists = useCallback((): boolean => {
    if (!persistenceManager.current) return false
    
    try {
      const layout = persistenceManager.current.loadLayout(key)
      return layout !== null
    } catch {
      return false
    }
  }, [key])

  return {
    save,
    load,
    remove,
    exists,
    isAvailable: !!persistenceManager.current
  }
}

// Hook for managing multiple saved layouts
export function useSavedLayouts(
  prefix = 'vscode-panels-layout-',
  options: {
    storage?: 'localStorage' | 'sessionStorage' | 'memory'
    onError?: (error: Error) => void
  } = {}
) {
  const [layouts, setLayouts] = useState<Record<string, LayoutConfig>>({})
  const [isLoading, setIsLoading] = useState(true)
  
  const { storage = 'localStorage', onError } = options

  // Create persistence manager
  const persistenceManager = useRef<PersistenceManager>()
  
  useEffect(() => {
    let adapter
    
    switch (storage) {
      case 'sessionStorage':
        adapter = isStorageAvailable('sessionStorage') 
          ? new SessionStorageAdapter() 
          : new MemoryStorageAdapter()
        break
      case 'memory':
        adapter = new MemoryStorageAdapter()
        break
      default:
        adapter = isStorageAvailable('localStorage') 
          ? new LocalStorageAdapter() 
          : new MemoryStorageAdapter()
    }
    
    persistenceManager.current = new PersistenceManager(adapter)
    loadAllLayouts()
  }, [storage])

  // Load all layouts
  const loadAllLayouts = useCallback(async () => {
    if (!persistenceManager.current) return

    setIsLoading(true)
    
    try {
      const keys = persistenceManager.current.listLayouts(prefix)
      const loadedLayouts: Record<string, LayoutConfig> = {}
      
      keys.forEach(key => {
        try {
          const layout = persistenceManager.current!.loadLayout(key)
          if (layout) {
            loadedLayouts[key] = layout
          }
        } catch (error) {
          console.warn(`Failed to load layout ${key}:`, error)
        }
      })
      
      setLayouts(loadedLayouts)
    } catch (error) {
      onError?.(error as Error)
    } finally {
      setIsLoading(false)
    }
  }, [prefix, onError])

  // Save layout
  const saveLayout = useCallback((id: string, layout: LayoutConfig): boolean => {
    if (!persistenceManager.current) return false

    const key = `${prefix}${id}`
    
    try {
      const success = persistenceManager.current.saveLayout(key, layout)
      if (success) {
        setLayouts(prev => ({ ...prev, [key]: layout }))
      }
      return success
    } catch (error) {
      onError?.(error as Error)
      return false
    }
  }, [prefix, onError])

  // Delete layout
  const deleteLayout = useCallback((id: string): boolean => {
    if (!persistenceManager.current) return false

    const key = `${prefix}${id}`
    
    try {
      const success = persistenceManager.current.removeLayout(key)
      if (success) {
        setLayouts(prev => {
          const newLayouts = { ...prev }
          delete newLayouts[key]
          return newLayouts
        })
      }
      return success
    } catch (error) {
      onError?.(error as Error)
      return false
    }
  }, [prefix, onError])

  // Export layouts
  const exportLayouts = useCallback((): string => {
    if (!persistenceManager.current) return '{}'
    
    try {
      return persistenceManager.current.exportLayouts(prefix)
    } catch (error) {
      onError?.(error as Error)
      return '{}'
    }
  }, [prefix, onError])

  // Import layouts
  const importLayouts = useCallback((jsonData: string): boolean => {
    if (!persistenceManager.current) return false
    
    try {
      const success = persistenceManager.current.importLayouts(jsonData)
      if (success) {
        loadAllLayouts() // Reload all layouts
      }
      return success
    } catch (error) {
      onError?.(error as Error)
      return false
    }
  }, [onError, loadAllLayouts])

  // Clear all layouts
  const clearAllLayouts = useCallback((): boolean => {
    if (!persistenceManager.current) return false
    
    try {
      const success = persistenceManager.current.clearLayouts(prefix)
      if (success) {
        setLayouts({})
      }
      return success
    } catch (error) {
      onError?.(error as Error)
      return false
    }
  }, [prefix, onError])

  return {
    layouts,
    isLoading,
    saveLayout,
    deleteLayout,
    exportLayouts,
    importLayouts,
    clearAllLayouts,
    reload: loadAllLayouts
  }
}

// Hook for auto-save functionality
export function useAutoSave(
  getCurrentLayout: () => LayoutConfig | null,
  options: {
    key: string
    interval?: number
    enabled?: boolean
    storage?: 'localStorage' | 'sessionStorage' | 'memory'
    onSave?: (layout: LayoutConfig) => void
    onError?: (error: Error) => void
  }
) {
  const {
    key,
    interval = 30000, // 30 seconds
    enabled = true,
    storage = 'localStorage',
    onSave,
    onError
  } = options

  const { save } = usePersistence(key, { storage, onSave, onError })
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Start auto-save
  const start = useCallback(() => {
    if (!enabled || intervalRef.current) return

    intervalRef.current = setInterval(() => {
      const layout = getCurrentLayout()
      if (layout) {
        save(layout)
      }
    }, interval)
  }, [enabled, interval, getCurrentLayout, save])

  // Stop auto-save
  const stop = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [])

  // Effect to manage auto-save lifecycle
  useEffect(() => {
    if (enabled) {
      start()
    } else {
      stop()
    }

    return stop
  }, [enabled, start, stop])

  return {
    start,
    stop,
    isRunning: !!intervalRef.current
  }
}

import { useState, useCallback, useRef, useEffect } from 'react'
import type { DragData, DropResult } from '../types'
import { 
  createDragData, 
  createDropResult, 
  isValidDragData,
  exceedsDragThreshold,
  getTouchPosition,
  isTouchDevice
} from '../utils/dnd-utils'

// Hook for draggable elements
export function useDraggable(
  data: DragData,
  options: {
    disabled?: boolean
    threshold?: number
    onDragStart?: (data: DragData) => void
    onDragEnd?: (success: boolean) => void
    createPreview?: () => HTMLElement
  } = {}
) {
  const [isDragging, setIsDragging] = useState(false)
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 })
  const dragRef = useRef<HTMLElement>(null)
  const previewRef = useRef<HTMLElement | null>(null)

  const {
    disabled = false,
    threshold = 5,
    onDragStart,
    onDragEnd,
    createPreview
  } = options

  // Handle drag start
  const handleDragStart = useCallback((e: React.DragEvent) => {
    if (disabled) return

    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/plain', data.id)
    e.dataTransfer.setData('application/json', JSON.stringify(data))

    // Create custom preview if provided
    if (createPreview) {
      const preview = createPreview()
      previewRef.current = preview
      e.dataTransfer.setDragImage(preview, 0, 0)
    }

    setIsDragging(true)
    onDragStart?.(data)
  }, [data, disabled, createPreview, onDragStart])

  // Handle drag end
  const handleDragEnd = useCallback((e: React.DragEvent) => {
    const success = e.dataTransfer.dropEffect !== 'none'
    setIsDragging(false)
    
    // Clean up preview
    if (previewRef.current && previewRef.current.parentNode) {
      previewRef.current.parentNode.removeChild(previewRef.current)
      previewRef.current = null
    }

    onDragEnd?.(success)
  }, [onDragEnd])

  // Touch drag support
  const [touchDragging, setTouchDragging] = useState(false)
  const touchStartRef = useRef({ x: 0, y: 0 })

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (disabled || !isTouchDevice()) return

    const touch = e.touches[0]
    touchStartRef.current = { x: touch.clientX, y: touch.clientY }
    setStartPosition({ x: touch.clientX, y: touch.clientY })
  }, [disabled])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (disabled || !isTouchDevice()) return

    const currentPos = getTouchPosition(e)
    
    if (!touchDragging && exceedsDragThreshold(touchStartRef.current, currentPos, threshold)) {
      setTouchDragging(true)
      onDragStart?.(data)
    }
  }, [disabled, touchDragging, threshold, data, onDragStart])

  const handleTouchEnd = useCallback(() => {
    if (touchDragging) {
      setTouchDragging(false)
      onDragEnd?.(false) // Touch drag doesn't have drop effect
    }
  }, [touchDragging, onDragEnd])

  return {
    isDragging: isDragging || touchDragging,
    dragProps: {
      ref: dragRef,
      draggable: !disabled,
      onDragStart: handleDragStart,
      onDragEnd: handleDragEnd,
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd
    }
  }
}

// Hook for droppable elements
export function useDroppable(
  onDrop: (data: DragData) => DropResult,
  options: {
    disabled?: boolean
    canDrop?: (data: DragData) => boolean
    onDragEnter?: (data: DragData) => void
    onDragLeave?: () => void
    onDragOver?: (data: DragData) => void
  } = {}
) {
  const [isOver, setIsOver] = useState(false)
  const [canDrop, setCanDrop] = useState(false)
  const dropRef = useRef<HTMLElement>(null)
  const dragDataRef = useRef<DragData | null>(null)

  const {
    disabled = false,
    canDrop: canDropPredicate,
    onDragEnter,
    onDragLeave,
    onDragOver
  } = options

  // Handle drag over
  const handleDragOver = useCallback((e: React.DragEvent) => {
    if (disabled) return

    e.preventDefault()
    
    try {
      const jsonData = e.dataTransfer.getData('application/json')
      if (jsonData) {
        const data: DragData = JSON.parse(jsonData)
        if (isValidDragData(data)) {
          dragDataRef.current = data
          const canDropHere = canDropPredicate ? canDropPredicate(data) : true
          setCanDrop(canDropHere)
          
          if (canDropHere) {
            e.dataTransfer.dropEffect = 'move'
            onDragOver?.(data)
          } else {
            e.dataTransfer.dropEffect = 'none'
          }
        }
      }
    } catch (error) {
      // Ignore parsing errors during drag over
    }
  }, [disabled, canDropPredicate, onDragOver])

  // Handle drag enter
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    if (disabled) return

    e.preventDefault()
    setIsOver(true)
    
    if (dragDataRef.current) {
      onDragEnter?.(dragDataRef.current)
    }
  }, [disabled, onDragEnter])

  // Handle drag leave
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    if (disabled) return

    // Only trigger leave if we're actually leaving the element
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsOver(false)
      setCanDrop(false)
      dragDataRef.current = null
      onDragLeave?.()
    }
  }, [disabled, onDragLeave])

  // Handle drop
  const handleDrop = useCallback((e: React.DragEvent) => {
    if (disabled) return

    e.preventDefault()
    setIsOver(false)
    setCanDrop(false)

    try {
      const jsonData = e.dataTransfer.getData('application/json')
      if (jsonData) {
        const data: DragData = JSON.parse(jsonData)
        if (isValidDragData(data)) {
          const result = onDrop(data)
          dragDataRef.current = null
          return result
        }
      }
    } catch (error) {
      console.warn('Failed to parse drop data:', error)
    }

    dragDataRef.current = null
    return createDropResult(false)
  }, [disabled, onDrop])

  return {
    isOver: isOver && canDrop,
    canDrop,
    dropProps: {
      ref: dropRef,
      onDragOver: handleDragOver,
      onDragEnter: handleDragEnter,
      onDragLeave: handleDragLeave,
      onDrop: handleDrop
    }
  }
}

// Hook for drag and drop context
export function useDragDropContext() {
  const [dragData, setDragData] = useState<DragData | null>(null)
  const [isDragging, setIsDragging] = useState(false)

  const startDrag = useCallback((data: DragData) => {
    setDragData(data)
    setIsDragging(true)
  }, [])

  const endDrag = useCallback(() => {
    setDragData(null)
    setIsDragging(false)
  }, [])

  return {
    dragData,
    isDragging,
    startDrag,
    endDrag
  }
}

// Hook for sortable lists
export function useSortable(
  items: string[],
  onReorder: (newOrder: string[]) => void,
  options: {
    disabled?: boolean
    direction?: 'horizontal' | 'vertical'
  } = {}
) {
  const [draggedItem, setDraggedItem] = useState<string | null>(null)
  const [dragOverItem, setDragOverItem] = useState<string | null>(null)

  const { disabled = false, direction = 'vertical' } = options

  const handleDragStart = useCallback((itemId: string) => {
    if (disabled) return
    setDraggedItem(itemId)
  }, [disabled])

  const handleDragOver = useCallback((itemId: string) => {
    if (disabled || !draggedItem || draggedItem === itemId) return
    setDragOverItem(itemId)
  }, [disabled, draggedItem])

  const handleDragEnd = useCallback(() => {
    if (disabled || !draggedItem || !dragOverItem) {
      setDraggedItem(null)
      setDragOverItem(null)
      return
    }

    const newItems = [...items]
    const draggedIndex = newItems.indexOf(draggedItem)
    const targetIndex = newItems.indexOf(dragOverItem)

    if (draggedIndex !== -1 && targetIndex !== -1) {
      // Remove dragged item
      newItems.splice(draggedIndex, 1)
      // Insert at new position
      const adjustedTargetIndex = draggedIndex < targetIndex ? targetIndex - 1 : targetIndex
      newItems.splice(adjustedTargetIndex, 0, draggedItem)
      
      onReorder(newItems)
    }

    setDraggedItem(null)
    setDragOverItem(null)
  }, [disabled, draggedItem, dragOverItem, items, onReorder])

  const getSortableProps = useCallback((itemId: string) => {
    const data = createDragData('tab', itemId)
    
    return {
      draggable: !disabled,
      onDragStart: () => handleDragStart(itemId),
      onDragOver: (e: React.DragEvent) => {
        e.preventDefault()
        handleDragOver(itemId)
      },
      onDragEnd: handleDragEnd,
      'data-dragging': draggedItem === itemId,
      'data-drag-over': dragOverItem === itemId
    }
  }, [disabled, draggedItem, dragOverItem, handleDragStart, handleDragOver, handleDragEnd])

  return {
    draggedItem,
    dragOverItem,
    getSortableProps
  }
}

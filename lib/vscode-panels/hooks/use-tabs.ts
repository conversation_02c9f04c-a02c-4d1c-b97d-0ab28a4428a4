import { useCallback, useMemo } from 'react'
import { useTabStore } from '../store/tab-store'
import type { TabConfig, TabState } from '../types/tab'

// Hook for managing tabs in a specific panel
export function useTabs(panelId: string) {
  const {
    tabs,
    tabsByPanel,
    activeTabByPanel,
    createTab,
    removeTab,
    updateTab,
    activateTab,
    pinTab,
    markTabDirty,
    moveTab,
    reorderTabs,
    duplicateTab
  } = useTabStore()

  // Get tabs for this panel
  const panelTabs = useMemo(() => {
    const tabIds = tabsByPanel[panelId] || []
    return tabIds.map(id => tabs[id]).filter(Boolean)
  }, [tabs, tabsByPanel, panelId])

  const activeTabId = activeTabByPanel[panelId]
  const activeTab = activeTabId ? tabs[activeTabId] : undefined

  // Tab operations
  const operations = useMemo(() => ({
    // Create tab
    create: (config: Omit<TabConfig, 'panelId'>) => {
      createTab({ ...config, panelId })
    },

    // Remove tab
    remove: (tabId: string) => {
      removeTab(tabId)
    },

    // Update tab
    update: (tabId: string, updates: Partial<TabState>) => {
      updateTab(tabId, updates)
    },

    // Activate tab
    activate: (tabId: string) => {
      activateTab(tabId, panelId)
    },

    // Pin/unpin tab
    pin: (tabId: string, pinned: boolean) => {
      pinTab(tabId, pinned)
    },

    // Mark tab as dirty
    markDirty: (tabId: string, dirty: boolean) => {
      markTabDirty(tabId, dirty)
    },

    // Move tab to another panel
    moveTo: (tabId: string, targetPanelId: string, targetIndex?: number) => {
      moveTab(tabId, targetPanelId, targetIndex)
    },

    // Reorder tabs within panel
    reorder: (tabIds: string[]) => {
      reorderTabs(panelId, tabIds)
    },

    // Duplicate tab
    duplicate: (tabId: string) => {
      duplicateTab(tabId)
    },

    // Close all tabs except specified ones
    closeAllExcept: (exceptIds: string[] = []) => {
      const tabsToClose = panelTabs
        .filter(tab => !exceptIds.includes(tab.id) && tab.isClosable)
        .map(tab => tab.id)
      
      tabsToClose.forEach(id => removeTab(id))
    },

    // Close tabs to the right of specified tab
    closeToRight: (tabId: string) => {
      const tabIds = tabsByPanel[panelId] || []
      const tabIndex = tabIds.indexOf(tabId)
      
      if (tabIndex >= 0) {
        const tabsToClose = tabIds
          .slice(tabIndex + 1)
          .filter(id => tabs[id]?.isClosable)
        
        tabsToClose.forEach(id => removeTab(id))
      }
    },

    // Close tabs to the left of specified tab
    closeToLeft: (tabId: string) => {
      const tabIds = tabsByPanel[panelId] || []
      const tabIndex = tabIds.indexOf(tabId)
      
      if (tabIndex >= 0) {
        const tabsToClose = tabIds
          .slice(0, tabIndex)
          .filter(id => tabs[id]?.isClosable)
        
        tabsToClose.forEach(id => removeTab(id))
      }
    },

    // Pin all tabs
    pinAll: () => {
      panelTabs.forEach(tab => pinTab(tab.id, true))
    },

    // Unpin all tabs
    unpinAll: () => {
      panelTabs.forEach(tab => pinTab(tab.id, false))
    }
  }), [
    panelId, panelTabs, tabsByPanel, tabs, createTab, removeTab, updateTab,
    activateTab, pinTab, markTabDirty, moveTab, reorderTabs, duplicateTab
  ])

  // Tab queries
  const queries = useMemo(() => ({
    count: panelTabs.length,
    activeTab,
    activeTabId,
    pinnedTabs: panelTabs.filter(tab => tab.isPinned),
    dirtyTabs: panelTabs.filter(tab => tab.isDirty),
    closableTabs: panelTabs.filter(tab => tab.isClosable),
    loadingTabs: panelTabs.filter(tab => tab.isLoading),
    
    // Check if tab exists
    hasTab: (tabId: string) => panelTabs.some(tab => tab.id === tabId),
    
    // Get tab by ID
    getTab: (tabId: string) => panelTabs.find(tab => tab.id === tabId),
    
    // Get tab index
    getTabIndex: (tabId: string) => {
      const tabIds = tabsByPanel[panelId] || []
      return tabIds.indexOf(tabId)
    },
    
    // Check if can close tab
    canClose: (tabId: string) => {
      const tab = tabs[tabId]
      return tab ? tab.isClosable && !tab.isPinned : false
    },
    
    // Check if has unsaved changes
    hasUnsavedChanges: () => panelTabs.some(tab => tab.isDirty)
  }), [panelTabs, activeTab, activeTabId, tabs, tabsByPanel, panelId])

  return {
    tabs: panelTabs,
    activeTab,
    activeTabId,
    ...operations,
    ...queries
  }
}

// Hook for managing a specific tab
export function useTab(tabId: string) {
  const {
    tabs,
    updateTab,
    removeTab,
    activateTab,
    pinTab,
    markTabDirty
  } = useTabStore()

  const tab = tabs[tabId]

  const operations = useMemo(() => ({
    // Update tab
    update: (updates: Partial<TabState>) => {
      updateTab(tabId, updates)
    },

    // Remove tab
    remove: () => {
      removeTab(tabId)
    },

    // Activate tab
    activate: () => {
      if (tab) {
        activateTab(tabId, tab.panelId)
      }
    },

    // Pin/unpin tab
    pin: (pinned: boolean) => {
      pinTab(tabId, pinned)
    },

    // Toggle pin state
    togglePin: () => {
      if (tab) {
        pinTab(tabId, !tab.isPinned)
      }
    },

    // Mark as dirty
    markDirty: (dirty: boolean) => {
      markTabDirty(tabId, dirty)
    },

    // Toggle dirty state
    toggleDirty: () => {
      if (tab) {
        markTabDirty(tabId, !tab.isDirty)
      }
    }
  }), [tabId, tab, updateTab, removeTab, activateTab, pinTab, markTabDirty])

  const queries = useMemo(() => ({
    exists: !!tab,
    isActive: tab?.isActive ?? false,
    isPinned: tab?.isPinned ?? false,
    isDirty: tab?.isDirty ?? false,
    isClosable: tab?.isClosable ?? true,
    isLoading: tab?.isLoading ?? false,
    canClose: tab ? tab.isClosable && !tab.isPinned : false
  }), [tab])

  return {
    tab,
    ...operations,
    ...queries
  }
}

// Hook for tab navigation
export function useTabNavigation(panelId: string) {
  const { tabs, activeTabId } = useTabs(panelId)
  const { activateTab } = useTabStore()

  const navigation = useMemo(() => {
    const currentIndex = activeTabId ? tabs.findIndex(tab => tab.id === activeTabId) : -1
    
    return {
      currentIndex,
      canGoNext: currentIndex < tabs.length - 1,
      canGoPrevious: currentIndex > 0,
      
      // Navigate to next tab
      next: () => {
        if (currentIndex < tabs.length - 1) {
          const nextTab = tabs[currentIndex + 1]
          activateTab(nextTab.id, panelId)
        }
      },
      
      // Navigate to previous tab
      previous: () => {
        if (currentIndex > 0) {
          const prevTab = tabs[currentIndex - 1]
          activateTab(prevTab.id, panelId)
        }
      },
      
      // Navigate to first tab
      first: () => {
        if (tabs.length > 0) {
          activateTab(tabs[0].id, panelId)
        }
      },
      
      // Navigate to last tab
      last: () => {
        if (tabs.length > 0) {
          activateTab(tabs[tabs.length - 1].id, panelId)
        }
      },
      
      // Navigate to tab by index
      goTo: (index: number) => {
        if (index >= 0 && index < tabs.length) {
          activateTab(tabs[index].id, panelId)
        }
      }
    }
  }, [tabs, activeTabId, panelId, activateTab])

  return navigation
}

// Hook for tab keyboard shortcuts
export function useTabShortcuts(panelId: string) {
  const { next, previous, first, last } = useTabNavigation(panelId)
  const { closeAllExcept, closeToRight } = useTabs(panelId)

  const shortcuts = useMemo(() => ({
    // Ctrl+Tab - Next tab
    nextTab: next,
    
    // Ctrl+Shift+Tab - Previous tab
    previousTab: previous,
    
    // Ctrl+1 - First tab
    firstTab: first,
    
    // Ctrl+9 - Last tab
    lastTab: last,
    
    // Ctrl+W - Close current tab
    closeCurrentTab: () => {
      // Implementation would depend on current active tab
    },
    
    // Ctrl+Shift+W - Close all tabs
    closeAllTabs: () => {
      closeAllExcept([])
    },
    
    // Ctrl+K W - Close all tabs except current
    closeOtherTabs: () => {
      // Implementation would depend on current active tab
    },
    
    // Ctrl+K Ctrl+Shift+W - Close tabs to the right
    closeTabsToRight: () => {
      // Implementation would depend on current active tab
    }
  }), [next, previous, first, last, closeAllExcept, closeToRight])

  return shortcuts
}

// Re-export all hooks
export * from './use-panel'
export * from './use-tabs'
export * from './use-layout'
export * from './use-drag-drop'
export * from './use-persistence'

// Convenience hooks that combine multiple stores
import { usePanelStore } from '../store/panel-store'
import { useTabStore } from '../store/tab-store'
import { useLayoutStore } from '../store/layout-store'

// Combined workspace hook
export function useWorkspace() {
  const panelStore = usePanelStore()
  const tabStore = useTabStore()
  const layoutStore = useLayoutStore()

  return {
    // Panel operations
    panels: panelStore.panels,
    activePanelId: panelStore.activePanelId,
    createPanel: panelStore.createPanel,
    removePanel: panelStore.removePanel,
    activatePanel: panelStore.activatePanel,

    // Tab operations
    tabs: tabStore.tabs,
    tabsByPanel: tabStore.tabsByPanel,
    createTab: tabStore.createTab,
    removeTab: tabStore.removeTab,
    activateTab: tabStore.activateTab,

    // Layout operations
    layout: layoutStore.currentLayout,
    setLayout: layoutStore.setLayout,
    saveLayout: layoutStore.saveLayout,

    // Combined operations
    createPanelWithTab: (panelConfig: any, tabConfig: any) => {
      panelStore.createPanel(panelConfig)
      tabStore.createTab({ ...tabConfig, panelId: panelConfig.id })
    }
  }
}

// Performance monitoring hook
export function usePerformance() {
  const panelCount = usePanelStore(state => Object.keys(state.panels).length)
  const tabCount = useTabStore(state => Object.keys(state.tabs).length)
  
  return {
    panelCount,
    tabCount,
    memoryUsage: {
      panels: panelCount * 1024, // Rough estimate
      tabs: tabCount * 512
    }
  }
}

// Keyboard shortcuts hook
export function useKeyboardShortcuts() {
  const { activatePanel, createPanel } = usePanelStore()
  const { activateTab, createTab } = useTabStore()

  // TODO: Implement keyboard shortcuts
  return {
    registerShortcut: (key: string, callback: () => void) => {
      // Implementation for registering keyboard shortcuts
    },
    unregisterShortcut: (key: string) => {
      // Implementation for unregistering keyboard shortcuts
    }
  }
}

// Theme hook
export function useTheme() {
  // TODO: Implement theme management
  return {
    theme: 'auto' as const,
    setTheme: (theme: 'light' | 'dark' | 'auto') => {
      // Implementation for theme switching
    }
  }
}

// Accessibility hook
export function useAccessibility() {
  // TODO: Implement accessibility features
  return {
    announceToScreenReader: (message: string) => {
      // Implementation for screen reader announcements
    },
    focusPanel: (panelId: string) => {
      // Implementation for programmatic focus
    },
    focusTab: (tabId: string) => {
      // Implementation for programmatic focus
    }
  }
}

import { use<PERSON><PERSON>back, useMemo } from 'react'
import { usePanelStore } from '../store/panel-store'
import type { PanelConfig, PanelState } from '../types/panel'
import type { Size, Position } from '../types'

// Hook for managing a specific panel
export function usePanel(panelId: string) {
  const {
    panels,
    activePanelId,
    createPanel,
    removePanel,
    updatePanel,
    activatePanel,
    minimizePanel,
    maximizePanel,
    restorePanel,
    resizePanel,
    movePanel
  } = usePanelStore()

  const panel = panels[panelId]
  const isActive = activePanelId === panelId

  // Panel operations
  const operations = useMemo(() => ({
    // Create panel
    create: (config: Omit<PanelConfig, 'id'>) => {
      createPanel({ ...config, id: panelId })
    },

    // Remove panel
    remove: () => {
      removePanel(panelId)
    },

    // Update panel
    update: (updates: Partial<PanelState>) => {
      updatePanel(panelId, updates)
    },

    // Activate panel
    activate: () => {
      activatePanel(panelId)
    },

    // Minimize panel
    minimize: () => {
      minimizePanel(panelId)
    },

    // Maximize panel
    maximize: () => {
      maximizePanel(panelId)
    },

    // Restore panel
    restore: () => {
      restorePanel(panelId)
    },

    // Resize panel
    resize: (size: Size) => {
      resizePanel(panelId, size)
    },

    // Move panel
    move: (position: Position) => {
      movePanel(panelId, position)
    },

    // Toggle minimize
    toggleMinimize: () => {
      if (panel?.isMinimized) {
        restorePanel(panelId)
      } else {
        minimizePanel(panelId)
      }
    },

    // Toggle maximize
    toggleMaximize: () => {
      if (panel?.isMaximized) {
        restorePanel(panelId)
      } else {
        maximizePanel(panelId)
      }
    }
  }), [
    panelId, panel, createPanel, removePanel, updatePanel,
    activatePanel, minimizePanel, maximizePanel, restorePanel,
    resizePanel, movePanel
  ])

  // Panel state queries
  const queries = useMemo(() => ({
    exists: !!panel,
    isActive,
    isMinimized: panel?.isMinimized ?? false,
    isMaximized: panel?.isMaximized ?? false,
    isVisible: panel?.isVisible ?? false,
    isResizing: panel?.isResizing ?? false,
    canResize: panel?.resizeMode !== 'none',
    canMinimize: true, // TODO: Add to panel config
    canMaximize: true, // TODO: Add to panel config
    canClose: true     // TODO: Add to panel config
  }), [panel, isActive])

  return {
    panel,
    isActive,
    ...operations,
    ...queries
  }
}

// Hook for managing multiple panels
export function usePanels(panelIds?: string[]) {
  const {
    panels,
    panelOrder,
    activePanelId,
    createPanel,
    removePanel,
    reorderPanels
  } = usePanelStore()

  // Filter panels if specific IDs provided
  const filteredPanels = useMemo(() => {
    if (panelIds) {
      return panelIds.map(id => panels[id]).filter(Boolean)
    }
    return Object.values(panels)
  }, [panels, panelIds])

  // Panel operations
  const operations = useMemo(() => ({
    // Create multiple panels
    createMultiple: (configs: PanelConfig[]) => {
      configs.forEach(config => createPanel(config))
    },

    // Remove multiple panels
    removeMultiple: (ids: string[]) => {
      ids.forEach(id => removePanel(id))
    },

    // Reorder panels
    reorder: (newOrder: string[]) => {
      reorderPanels(newOrder)
    },

    // Close all panels
    closeAll: () => {
      Object.keys(panels).forEach(id => removePanel(id))
    },

    // Get panels by state
    getByState: (state: keyof PanelState, value: any) => {
      return Object.values(panels).filter(panel => panel[state] === value)
    }
  }), [panels, createPanel, removePanel, reorderPanels])

  // Panel queries
  const queries = useMemo(() => ({
    count: filteredPanels.length,
    activePanel: activePanelId ? panels[activePanelId] : undefined,
    minimizedPanels: filteredPanels.filter(p => p.isMinimized),
    maximizedPanels: filteredPanels.filter(p => p.isMaximized),
    visiblePanels: filteredPanels.filter(p => p.isVisible),
    resizingPanels: filteredPanels.filter(p => p.isResizing)
  }), [filteredPanels, panels, activePanelId])

  return {
    panels: filteredPanels,
    panelOrder,
    activePanelId,
    ...operations,
    ...queries
  }
}

// Hook for panel resize operations
export function usePanelResize(panelId: string) {
  const {
    panels,
    resizePanel,
    startResize,
    updateResize,
    endResize
  } = usePanelStore()

  const panel = panels[panelId]

  const resize = useCallback({
    // Start resize operation
    start: (startSize: Size, startPosition: Position) => {
      startResize(panelId, startSize, startPosition)
    },

    // Update resize
    update: (size: Size, position: Position) => {
      updateResize(size, position)
    },

    // End resize
    end: () => {
      endResize()
    },

    // Resize to specific size
    to: (size: Size) => {
      resizePanel(panelId, size)
    },

    // Resize by delta
    by: (delta: { width?: number; height?: number }) => {
      if (!panel) return
      
      const newSize = {
        width: panel.size.width + (delta.width || 0),
        height: panel.size.height + (delta.height || 0)
      }
      
      resizePanel(panelId, newSize)
    }
  }, [panelId, panel, resizePanel, startResize, updateResize, endResize])

  return {
    panel,
    isResizing: panel?.isResizing ?? false,
    resize
  }
}

// Hook for panel focus management
export function usePanelFocus(panelId: string) {
  const { activePanelId, activatePanel } = usePanelStore()
  
  const isFocused = activePanelId === panelId

  const focus = useCallback(() => {
    activatePanel(panelId)
  }, [panelId, activatePanel])

  const blur = useCallback(() => {
    // TODO: Implement panel blur logic
  }, [])

  return {
    isFocused,
    focus,
    blur
  }
}

// Hook for panel validation
export function usePanelValidation(panelId: string) {
  const { panels } = usePanelStore()
  const panel = panels[panelId]

  const validation = useMemo(() => {
    if (!panel) {
      return {
        isValid: false,
        errors: ['Panel not found'],
        warnings: []
      }
    }

    const errors: string[] = []
    const warnings: string[] = []

    // Validate size constraints
    if (panel.size.width < panel.minSize.width) {
      errors.push('Panel width is below minimum')
    }
    if (panel.size.height < panel.minSize.height) {
      errors.push('Panel height is below minimum')
    }

    // Validate max size constraints
    if (panel.maxSize) {
      if (panel.size.width > panel.maxSize.width) {
        errors.push('Panel width exceeds maximum')
      }
      if (panel.size.height > panel.maxSize.height) {
        errors.push('Panel height exceeds maximum')
      }
    }

    // Check for warnings
    if (panel.size.width < 200) {
      warnings.push('Panel width is quite narrow')
    }
    if (panel.size.height < 150) {
      warnings.push('Panel height is quite short')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }, [panel])

  return validation
}

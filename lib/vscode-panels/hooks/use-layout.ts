import { useCallback, useMemo } from 'react'
import { useLayoutStore } from '../store/layout-store'
import type { LayoutConfig, LayoutNode } from '../types/layout'
import type { Direction } from '../types'

// Hook for layout management
export function useLayout() {
  const {
    currentLayout,
    savedLayouts,
    presets,
    history,
    historyIndex,
    isModified,
    setLayout,
    saveLayout,
    loadLayout,
    deleteLayout,
    duplicateLayout,
    addPanel,
    removePanel,
    movePanel,
    resizeNode,
    splitNode,
    undo,
    redo,
    reset,
    validateLayout
  } = useLayoutStore()

  // Layout operations
  const operations = useMemo(() => ({
    // Set layout
    set: (layout: LayoutConfig) => {
      setLayout(layout)
    },

    // Save current layout
    save: (name: string, description?: string) => {
      saveLayout(name, description)
    },

    // Load saved layout
    load: (layoutId: string) => {
      loadLayout(layoutId)
    },

    // Delete saved layout
    delete: (layoutId: string) => {
      deleteLayout(layoutId)
    },

    // Duplicate layout
    duplicate: (layoutId: string, newName: string) => {
      duplicateLayout(layoutId, newName)
    },

    // Add panel to layout
    addPanel: (panelId: string, targetNode?: string, position?: 'before' | 'after' | 'child') => {
      addPanel(panelId, targetNode, position)
    },

    // Remove panel from layout
    removePanel: (panelId: string) => {
      removePanel(panelId)
    },

    // Move panel in layout
    movePanel: (panelId: string, targetNode: string, position: 'before' | 'after' | 'child') => {
      movePanel(panelId, targetNode, position)
    },

    // Resize layout node
    resize: (nodeId: string, size: number) => {
      resizeNode(nodeId, size)
    },

    // Split layout node
    split: (nodeId: string, direction: Direction, ratio?: number) => {
      splitNode(nodeId, direction, ratio)
    },

    // Undo last change
    undo,

    // Redo last undone change
    redo,

    // Reset to default layout
    reset,

    // Validate current layout
    validate: () => validateLayout(currentLayout)
  }), [
    setLayout, saveLayout, loadLayout, deleteLayout, duplicateLayout,
    addPanel, removePanel, movePanel, resizeNode, splitNode,
    undo, redo, reset, validateLayout, currentLayout
  ])

  // Layout queries
  const queries = useMemo(() => ({
    current: currentLayout,
    saved: savedLayouts,
    presets,
    isModified,
    canUndo: historyIndex > 0,
    canRedo: historyIndex < history.length - 1,
    
    // Get layout statistics
    stats: {
      nodeCount: countNodes(currentLayout.root),
      maxDepth: getMaxDepth(currentLayout.root),
      panelCount: countPanels(currentLayout.root),
      groupCount: countGroups(currentLayout.root)
    },
    
    // Find node in current layout
    findNode: (nodeId: string) => findNode(currentLayout.root, nodeId),
    
    // Find parent of node
    findParent: (nodeId: string) => findParent(currentLayout.root, nodeId),
    
    // Get node path
    getPath: (nodeId: string) => getNodePath(currentLayout.root, nodeId),
    
    // Check if layout is valid
    isValid: () => {
      const validation = validateLayout(currentLayout)
      return validation.isValid
    }
  }), [
    currentLayout, savedLayouts, presets, isModified,
    historyIndex, history, validateLayout
  ])

  return {
    ...operations,
    ...queries
  }
}

// Hook for layout node operations
export function useLayoutNode(nodeId: string) {
  const { current, findNode, findParent, resize, split, removePanel } = useLayout()
  
  const node = findNode(nodeId)
  const parent = findParent(nodeId)

  const operations = useMemo(() => ({
    // Resize node
    resize: (size: number) => {
      resize(nodeId, size)
    },

    // Split node
    split: (direction: Direction, ratio?: number) => {
      split(nodeId, direction, ratio)
    },

    // Remove node (if it's a panel)
    remove: () => {
      if (node?.type === 'panel' && node.panelId) {
        removePanel(node.panelId)
      }
    },

    // Get siblings
    getSiblings: () => {
      if (!parent?.children) return []
      return parent.children.filter(child => child.id !== nodeId)
    },

    // Get index in parent
    getIndex: () => {
      if (!parent?.children) return -1
      return parent.children.findIndex(child => child.id === nodeId)
    }
  }), [nodeId, node, parent, resize, split, removePanel])

  const queries = useMemo(() => ({
    node,
    parent,
    exists: !!node,
    isPanel: node?.type === 'panel',
    isGroup: node?.type === 'group',
    isPlaceholder: node?.type === 'placeholder',
    hasChildren: !!(node?.children && node.children.length > 0),
    childCount: node?.children?.length || 0,
    canSplit: node?.type === 'panel',
    canResize: true,
    canRemove: node?.type === 'panel'
  }), [node, parent])

  return {
    ...operations,
    ...queries
  }
}

// Hook for layout presets
export function useLayoutPresets() {
  const { presets, applyPreset, saveAsPreset, deletePreset } = useLayoutStore()

  const operations = useMemo(() => ({
    // Apply preset
    apply: (presetId: string) => {
      applyPreset(presetId)
    },

    // Save current layout as preset
    saveAs: (name: string, description?: string, category?: string) => {
      saveAsPreset(name, description, category)
    },

    // Delete preset
    delete: (presetId: string) => {
      deletePreset(presetId)
    }
  }), [applyPreset, saveAsPreset, deletePreset])

  const queries = useMemo(() => ({
    all: presets,
    byCategory: (category: string) => presets.filter(p => p.category === category),
    default: presets.filter(p => p.isDefault),
    custom: presets.filter(p => p.isCustom),
    count: presets.length
  }), [presets])

  return {
    ...operations,
    ...queries
  }
}

// Utility functions
function countNodes(node: LayoutNode): number {
  let count = 1
  if (node.children) {
    count += node.children.reduce((sum, child) => sum + countNodes(child), 0)
  }
  return count
}

function countPanels(node: LayoutNode): number {
  let count = node.type === 'panel' ? 1 : 0
  if (node.children) {
    count += node.children.reduce((sum, child) => sum + countPanels(child), 0)
  }
  return count
}

function countGroups(node: LayoutNode): number {
  let count = node.type === 'group' ? 1 : 0
  if (node.children) {
    count += node.children.reduce((sum, child) => sum + countGroups(child), 0)
  }
  return count
}

function getMaxDepth(node: LayoutNode): number {
  if (!node.children || node.children.length === 0) {
    return 0
  }
  return 1 + Math.max(...node.children.map(child => getMaxDepth(child)))
}

function findNode(root: LayoutNode, nodeId: string): LayoutNode | undefined {
  if (root.id === nodeId) {
    return root
  }

  if (root.children) {
    for (const child of root.children) {
      const found = findNode(child, nodeId)
      if (found) return found
    }
  }

  return undefined
}

function findParent(root: LayoutNode, nodeId: string): LayoutNode | undefined {
  if (root.children) {
    for (const child of root.children) {
      if (child.id === nodeId) {
        return root
      }

      const found = findParent(child, nodeId)
      if (found) return found
    }
  }

  return undefined
}

function getNodePath(root: LayoutNode, nodeId: string): string[] {
  const path: string[] = []

  function buildPath(node: LayoutNode, currentPath: string[]): boolean {
    if (node.id === nodeId) {
      path.push(...currentPath, node.id)
      return true
    }

    if (node.children) {
      for (const child of node.children) {
        if (buildPath(child, [...currentPath, node.id])) {
          return true
        }
      }
    }

    return false
  }

  buildPath(root, [])
  return path
}

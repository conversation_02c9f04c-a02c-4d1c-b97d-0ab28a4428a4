import type { ComponentType, ReactNode } from 'react'
import type { PanelProps } from './panel'

// Layout node types
export type LayoutNodeType = 'panel' | 'group' | 'placeholder'

// Panel group configuration
export interface PanelGroupConfig {
  id?: string
  direction?: 'horizontal' | 'vertical'
  size?: number
  minSize?: number
  children?: (PanelGroupConfig | string)[]
  metadata?: Record<string, any>
}

// Panel group props
export interface PanelGroupProps extends Omit<PanelGroupConfig, 'children'> {
  children?: ReactNode
  className?: string
  style?: React.CSSProperties
}

// Layout node
export interface LayoutNode {
  id: string
  type: LayoutNodeType
  direction?: 'horizontal' | 'vertical'
  size?: number
  minSize?: number
  children?: LayoutNode[]
  metadata?: Record<string, any>
  panel?: Omit<PanelProps, 'children'>
  panelId?: string // For backward compatibility
}

// Layout configuration
export interface LayoutConfig {
  id: string
  name: string
  version: string
  root: LayoutNode
  panels: Record<string, PanelProps>
  metadata?: Record<string, any>
}

// Layout theme
export type LayoutTheme = 'light' | 'dark' | 'auto'

// Layout events
export interface LayoutEvents {
  onLayoutChange?: (layout: LayoutConfig) => void
  onThemeChange?: (theme: LayoutTheme) => void
  onPanelAdd?: (panel: PanelProps) => void
  onPanelRemove?: (id: string) => void
  onPanelActivate?: (id: string) => void
  onDrop?: (source: string, target: string, position: 'before' | 'after') => void
}

// Layout validation
export function isLayoutConfig(config: unknown): config is LayoutConfig {
  if (!config || typeof config !== 'object') return false
  
  const layout = config as Partial<LayoutConfig>
  return Boolean(
    layout.id &&
    layout.name &&
    layout.version &&
    layout.root &&
    layout.panels &&
    typeof layout.id === 'string' &&
    typeof layout.name === 'string' &&
    typeof layout.version === 'string' &&
    typeof layout.root === 'object' &&
    typeof layout.panels === 'object'
  )
}

// Workspace configuration
export interface WorkspaceConfig {
  theme?: LayoutTheme
  persistLayout?: boolean
  allowLayoutModification?: boolean
  showLayoutControls?: boolean
  layoutConfig?: LayoutConfig
}

// Workspace props
export interface WorkspaceProps extends WorkspaceConfig {
  children: ReactNode
  className?: string
  style?: React.CSSProperties
  onLayoutChange?: (layout: LayoutConfig) => void
}

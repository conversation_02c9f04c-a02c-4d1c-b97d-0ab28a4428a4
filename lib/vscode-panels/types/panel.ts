import type { ComponentType, ReactNode } from 'react'
import type { LucideIcon } from 'lucide-react'
import type { LayoutConfig } from './layout'

// Panel state
export interface PanelState {
  id: string
  title: string
  icon?: ComponentType | LucideIcon
  
  // Dimensions
  width?: number
  height?: number
  minWidth?: number
  minHeight?: number
  defaultWidth?: number
  defaultHeight?: number
  
  // State flags
  isPinned?: boolean
  isDirty?: boolean
  isActive?: boolean
  isClosable?: boolean
  isCollapsed?: boolean
  isLoading?: boolean
  
  // Layout
  direction?: 'horizontal' | 'vertical'
  metadata?: Record<string, any>
}

// Panel store
export interface PanelStore {
  panels: Record<string, PanelState>
  activePanel: string | null
  draggedPanel: string | null
  layout?: LayoutConfig
  
  // Panel operations
  updatePanel: (id: string, update: Partial<PanelState>) => void
  activatePanel: (id: string) => void
  removePanel: (id: string) => void
  collapsePanel: (id: string, isCollapsed: boolean) => void
  reorderPanel: (sourceId: string, targetId: string, position: 'before' | 'after') => void
  resizePanel: (id: string, dimensions: { width?: number; height?: number }) => void
  
  // Drag and drop
  startPanelDrag: (id: string) => void
  endPanelDrag: (dropped: boolean) => void
  
  // Bulk operations
  resetPanels: () => void
  loadPanels: (panels: Record<string, PanelState>) => void
}

// Panel action
export interface PanelAction {
  id: string
  label: string
  icon?: ComponentType | LucideIcon
  onClick: () => void
  disabled?: boolean
  tooltip?: string
}

// Panel header props
export interface PanelHeaderProps {
  title: string
  icon?: ComponentType | LucideIcon
  isActive?: boolean
  isCollapsed?: boolean
  isClosable?: boolean
  onClose?: () => void
  onCollapse?: () => void
  actions?: PanelAction[]
  className?: string
  style?: React.CSSProperties
}

// Panel content props
export interface PanelContentProps {
  children: ReactNode
  className?: string
  style?: React.CSSProperties
}

// Panel props
export interface PanelProps {
  id: string
  title: string
  children: ReactNode
  className?: string
  style?: React.CSSProperties
  
  // Visual props
  icon?: ComponentType | LucideIcon
  minWidth?: number
  minHeight?: number
  defaultWidth?: number
  defaultHeight?: number
  
  // State flags
  isActive?: boolean
  isCollapsed?: boolean
  isClosable?: boolean
  
  // Event handlers
  onActivate?: () => void
  onClose?: () => void
  onCollapse?: (isCollapsed: boolean) => void
  onResize?: (width?: number, height?: number) => void
  onContextMenu?: (event: React.MouseEvent) => void
  
  // Drag and drop
  draggable?: boolean
  onDragStart?: (event: React.DragEvent) => void
  onDragEnd?: (event: React.DragEvent) => void
  
  // Layout props
  direction?: 'horizontal' | 'vertical'
  
  // Custom props
  headerActions?: PanelAction[]
  contextMenu?: PanelAction[]
  metadata?: Record<string, any>
}

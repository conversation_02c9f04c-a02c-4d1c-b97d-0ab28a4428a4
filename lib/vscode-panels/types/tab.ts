import type { ComponentType, ReactNode } from 'react'
import type { LucideIcon } from 'lucide-react'

// Tab state
export interface TabState {
  id: string
  title: string
  icon?: ComponentType | LucideIcon
  isPinned: boolean
  isDirty: boolean
  isActive: boolean
  isClosable: boolean
  isLoading: boolean
  panelId: string
  order: number
  metadata: Record<string, any>
}

// Tab store
export interface TabStore {
  tabs: Record<string, TabState>
  activeTab: string | null
  draggedTab: string | null
  
  // Tab operations
  updateTab: (id: string, update: Partial<TabState>) => void
  activateTab: (id: string, panelId: string) => void
  closeTab: (id: string) => void
  pinTab: (id: string, isPinned: boolean) => void
  markTabDirty: (id: string, isDirty: boolean) => void
  reorderTabs: (panelId: string, orderedIds: string[]) => void
  
  // Drag and drop
  startTabDrag: (id: string) => void
  endTabDrag: (dropped: boolean) => void
  
  // Bulk operations
  resetTabs: () => void
  loadTabs: (tabs: Record<string, TabState>) => void
}

// Tab action
export interface TabAction {
  id: string
  label: string
  icon?: ComponentType | LucideIcon
  onClick: () => void
  disabled?: boolean
  tooltip?: string
}

// Tab bar props
export interface TabBarProps {
  children: ReactNode
  activeTabId?: string
  onTabActivate?: (id: string) => void
  onTabReorder?: (orderedIds: string[]) => void
  className?: string
  style?: React.CSSProperties
}

// Main tab props
export interface TabProps {
  id: string
  title: string
  children: ReactNode
  className?: string
  style?: React.CSSProperties
  
  // Visual props
  icon?: ComponentType | LucideIcon
  tooltip?: string
  badge?: string | number
  
  // State flags
  isActive?: boolean
  isPinned?: boolean
  isDirty?: boolean
  isClosable?: boolean
  isLoading?: boolean
  
  // Event handlers
  onActivate?: () => void
  onClose?: () => void
  onPin?: (isPinned: boolean) => void
  onContextMenu?: (event: React.MouseEvent) => void
  onDoubleClick?: (event: React.MouseEvent) => void
  
  // Drag and drop
  draggable?: boolean
  onDragStart?: (event: React.DragEvent) => void
  onDragEnd?: (event: React.DragEvent) => void
  
  // Custom actions
  actions?: TabAction[]
  contextMenu?: TabAction[]
  metadata?: Record<string, any>
}

// Tab content props
export interface TabContentProps {
  children: ReactNode
  className?: string
  style?: React.CSSProperties
}

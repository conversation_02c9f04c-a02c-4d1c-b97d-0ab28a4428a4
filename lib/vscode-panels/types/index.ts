// Re-export all types for easy importing
export * from './panel'
export * from './tab'
export * from './layout'

// Common base types
export interface Position {
  x: number
  y: number
}

export interface Size {
  width: number
  height: number
}

export interface Rect extends Position, Size {}

export interface DragData {
  type: 'tab' | 'panel'
  id: string
  sourcePanel?: string
  data?: any
}

export interface DropResult {
  success: boolean
  targetPanel?: string
  targetIndex?: number
}

// Theme types
export type Theme = 'light' | 'dark' | 'auto'

export interface ThemeConfig {
  colors: {
    background: string
    foreground: string
    border: string
    accent: string
    muted: string
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
  }
  borderRadius: {
    sm: string
    md: string
    lg: string
  }
}

// Animation types
export interface AnimationConfig {
  duration: number
  easing: string
  enabled: boolean
}

// Event types
export interface PanelEvent {
  type: 'resize' | 'move' | 'close' | 'focus'
  panelId: string
  data?: any
}

export interface TabEvent {
  type: 'create' | 'close' | 'pin' | 'unpin' | 'move' | 'activate'
  tabId: string
  panelId: string
  data?: any
}

export interface LayoutEvent {
  type: 'save' | 'restore' | 'reset' | 'change'
  data?: any
}

// Plugin types
export interface PluginAPI {
  registerCommand: (command: string, handler: Function) => void
  registerPanel: (panelType: string, component: React.ComponentType) => void
  registerTab: (tabType: string, component: React.ComponentType) => void
  emit: (event: string, data?: any) => void
  on: (event: string, handler: Function) => void
  off: (event: string, handler: Function) => void
}

export interface Plugin {
  name: string
  version?: string
  description?: string
  activate: (api: PluginAPI) => void
  deactivate?: () => void
}

// Utility types
export type Direction = 'horizontal' | 'vertical'
export type Orientation = 'left' | 'right' | 'top' | 'bottom'
export type ResizeMode = 'none' | 'horizontal' | 'vertical' | 'both'

// Error types
export interface VSCodePanelError extends Error {
  code: string
  context?: any
}

// Configuration types
export interface VSCodePanelConfig {
  theme: Theme
  animations: AnimationConfig
  persistence: {
    enabled: boolean
    key: string
    storage: 'localStorage' | 'sessionStorage'
  }
  dragDrop: {
    enabled: boolean
    threshold: number
    ghostOpacity: number
  }
  panels: {
    minWidth: number
    minHeight: number
    defaultWidth: number
    defaultHeight: number
  }
  tabs: {
    maxWidth: number
    showCloseButton: boolean
    showPinButton: boolean
  }
}

// Default configuration
export const DEFAULT_CONFIG: VSCodePanelConfig = {
  theme: 'auto',
  animations: {
    duration: 200,
    easing: 'ease-in-out',
    enabled: true,
  },
  persistence: {
    enabled: true,
    key: 'vscode-panels-layout',
    storage: 'localStorage',
  },
  dragDrop: {
    enabled: true,
    threshold: 5,
    ghostOpacity: 0.5,
  },
  panels: {
    minWidth: 100,
    minHeight: 100,
    defaultWidth: 300,
    defaultHeight: 200,
  },
  tabs: {
    maxWidth: 200,
    showCloseButton: true,
    showPinButton: true,
  },
}

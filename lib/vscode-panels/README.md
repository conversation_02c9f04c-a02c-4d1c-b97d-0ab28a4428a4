# VS Code Panels and Tabs System

A comprehensive library inspired by VS Code's panels and tabs system, built with React, TypeScript, and Zustand.

## 🚀 Features

- **Resizable Panels**: Drag handles for panel resizing
- **Draggable Tabs**: Reorder tabs with drag and drop
- **Tab Pinning**: Pin important tabs to prevent accidental closure
- **Layout Persistence**: Save and restore layout configurations
- **Extensible Architecture**: Plugin system for custom components
- **TypeScript Support**: Full type safety throughout
- **Zustand State Management**: Efficient state management
- **Framer Motion Animations**: Smooth animations and transitions

## 📦 Installation

```bash
# The library is included in the project
import { Workspace, Panel, Tab } from '@/lib/vscode-panels'
```

## 🎯 Quick Start

### Basic Workspace

```tsx
import { Workspace, Panel, Tab } from '@/lib/vscode-panels'

function MyApp() {
  return (
    <Workspace>
      <Panel id="main" title="Main Panel">
        <Tab id="tab1" title="File 1" pinned>
          <div>Content for tab 1</div>
        </Tab>
        <Tab id="tab2" title="File 2">
          <div>Content for tab 2</div>
        </Tab>
      </Panel>
    </Workspace>
  )
}
```

### Advanced Layout

```tsx
import { 
  Workspace, 
  PanelGroup, 
  Panel, 
  Tab,
  useLayout 
} from '@/lib/vscode-panels'

function AdvancedApp() {
  const { saveLayout, restoreLayout } = useLayout()
  
  return (
    <Workspace 
      onLayoutChange={saveLayout}
      initialLayout={restoreLayout()}
    >
      <PanelGroup direction="horizontal">
        <Panel id="sidebar" title="Explorer" minWidth={200}>
          <Tab id="files" title="Files">
            <FileExplorer />
          </Tab>
          <Tab id="search" title="Search">
            <SearchPanel />
          </Tab>
        </Panel>
        
        <PanelGroup direction="vertical">
          <Panel id="editor" title="Editor">
            <Tab id="main.tsx" title="main.tsx" pinned>
              <CodeEditor file="main.tsx" />
            </Tab>
            <Tab id="utils.ts" title="utils.ts">
              <CodeEditor file="utils.ts" />
            </Tab>
          </Panel>
          
          <Panel id="terminal" title="Terminal" minHeight={150}>
            <Tab id="bash" title="bash">
              <Terminal />
            </Tab>
          </Panel>
        </PanelGroup>
      </PanelGroup>
    </Workspace>
  )
}
```

## 🔧 API Reference

### Components

#### Workspace
Main container for the entire panel system.

```tsx
interface WorkspaceProps {
  children: React.ReactNode
  className?: string
  onLayoutChange?: (layout: LayoutConfig) => void
  initialLayout?: LayoutConfig
  theme?: 'light' | 'dark' | 'auto'
}
```

#### Panel
Individual panel container that can hold multiple tabs.

```tsx
interface PanelProps {
  id: string
  title: string
  children: React.ReactNode
  minWidth?: number
  minHeight?: number
  defaultWidth?: number
  defaultHeight?: number
  resizable?: boolean
  closable?: boolean
  onClose?: () => void
}
```

#### Tab
Individual tab within a panel.

```tsx
interface TabProps {
  id: string
  title: string
  children: React.ReactNode
  pinned?: boolean
  closable?: boolean
  icon?: React.ComponentType
  onClose?: () => void
  onPin?: (pinned: boolean) => void
}
```

### Hooks

#### usePanel
Manage panel state and operations.

```tsx
const {
  panels,
  activePanel,
  createPanel,
  closePanel,
  resizePanel,
  movePanel
} = usePanel()
```

#### useTabs
Manage tab state and operations.

```tsx
const {
  tabs,
  activeTab,
  createTab,
  closeTab,
  pinTab,
  moveTab,
  reorderTabs
} = useTabs(panelId)
```

#### useLayout
Manage overall layout configuration.

```tsx
const {
  layout,
  saveLayout,
  restoreLayout,
  resetLayout
} = useLayout()
```

## 🎨 Theming

The system supports full theming through CSS variables:

```css
:root {
  --vscode-panel-bg: #1e1e1e;
  --vscode-panel-border: #333;
  --vscode-tab-bg: #2d2d30;
  --vscode-tab-active-bg: #1e1e1e;
  --vscode-tab-text: #cccccc;
  --vscode-tab-active-text: #ffffff;
}
```

## 🔌 Plugin System

Create custom plugins to extend functionality:

```tsx
import { BasePlugin } from '@/lib/vscode-panels/plugins'

class MyCustomPlugin extends BasePlugin {
  name = 'my-custom-plugin'
  
  onTabCreate(tab: Tab) {
    // Custom logic when tab is created
  }
  
  onPanelResize(panel: Panel, size: Size) {
    // Custom logic when panel is resized
  }
}

// Register plugin
const workspace = useWorkspace()
workspace.registerPlugin(new MyCustomPlugin())
```

## 📱 Responsive Design

The system automatically adapts to different screen sizes:

- **Desktop**: Full panel and tab functionality
- **Tablet**: Collapsible panels with tab switching
- **Mobile**: Stack layout with drawer navigation

## 🧪 Testing

```tsx
import { render, screen } from '@testing-library/react'
import { Workspace, Panel, Tab } from '@/lib/vscode-panels'

test('renders workspace with panels and tabs', () => {
  render(
    <Workspace>
      <Panel id="test" title="Test Panel">
        <Tab id="tab1" title="Tab 1">
          <div>Test content</div>
        </Tab>
      </Panel>
    </Workspace>
  )
  
  expect(screen.getByText('Test Panel')).toBeInTheDocument()
  expect(screen.getByText('Tab 1')).toBeInTheDocument()
})
```

## 🤝 Contributing

This library is part of the Soimagine Digital Agency project. Follow the existing patterns and conventions when extending functionality.

## 📄 License

Part of the Soimagine Digital Agency project.

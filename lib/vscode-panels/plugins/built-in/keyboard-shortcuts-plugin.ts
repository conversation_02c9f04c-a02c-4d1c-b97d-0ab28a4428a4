import { BasePlugin } from '../base-plugin'
import type { PluginAPI } from '../../types'

interface ShortcutConfig {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  metaKey?: boolean
  handler: () => void
  description?: string
}

export class KeyboardShortcutsPlugin extends BasePlugin {
  name = 'keyboard-shortcuts-plugin'
  version = '1.0.0'
  description = 'Keyboard shortcuts plugin for VS Code panels'

  private shortcuts = new Map<string, ShortcutConfig>()
  private isListening = false

  protected onActivate(): void {
    if (!this.api) return

    // Register shortcut management commands
    this.registerCommand('shortcuts.register', this.registerShortcut.bind(this))
    this.registerCommand('shortcuts.unregister', this.unregisterShortcut.bind(this))
    this.registerCommand('shortcuts.list', this.listShortcuts.bind(this))

    // Register default shortcuts
    this.registerDefaultShortcuts()

    // Start listening for keyboard events
    this.startListening()
  }

  protected onDeactivate(): void {
    this.stopListening()
    this.shortcuts.clear()
  }

  private registerDefaultShortcuts(): void {
    // Tab navigation shortcuts
    this.registerShortcut({
      key: 'Tab',
      ctrlKey: true,
      handler: () => this.emit('tab.next'),
      description: 'Next tab'
    })

    this.registerShortcut({
      key: 'Tab',
      ctrlKey: true,
      shiftKey: true,
      handler: () => this.emit('tab.previous'),
      description: 'Previous tab'
    })

    // Tab management shortcuts
    this.registerShortcut({
      key: 'w',
      ctrlKey: true,
      handler: () => this.emit('tab.close'),
      description: 'Close current tab'
    })

    this.registerShortcut({
      key: 'w',
      ctrlKey: true,
      shiftKey: true,
      handler: () => this.emit('tab.closeAll'),
      description: 'Close all tabs'
    })

    this.registerShortcut({
      key: 't',
      ctrlKey: true,
      handler: () => this.emit('tab.new'),
      description: 'New tab'
    })

    // Panel management shortcuts
    this.registerShortcut({
      key: 'j',
      ctrlKey: true,
      handler: () => this.emit('panel.toggleTerminal'),
      description: 'Toggle terminal panel'
    })

    this.registerShortcut({
      key: 'b',
      ctrlKey: true,
      handler: () => this.emit('panel.toggleSidebar'),
      description: 'Toggle sidebar'
    })

    // Layout shortcuts
    this.registerShortcut({
      key: 's',
      ctrlKey: true,
      shiftKey: true,
      handler: () => this.emit('layout.save'),
      description: 'Save layout'
    })

    this.registerShortcut({
      key: 'r',
      ctrlKey: true,
      shiftKey: true,
      handler: () => this.emit('layout.reset'),
      description: 'Reset layout'
    })

    // Theme shortcuts
    this.registerShortcut({
      key: 'k',
      ctrlKey: true,
      handler: () => this.emit('theme.toggle'),
      description: 'Toggle theme'
    })

    // Number key shortcuts for tab selection (Ctrl+1, Ctrl+2, etc.)
    for (let i = 1; i <= 9; i++) {
      this.registerShortcut({
        key: i.toString(),
        ctrlKey: true,
        handler: () => this.emit('tab.select', { index: i - 1 }),
        description: `Select tab ${i}`
      })
    }
  }

  private registerShortcut(config: ShortcutConfig): void {
    const key = this.createShortcutKey(config)
    this.shortcuts.set(key, config)
  }

  private unregisterShortcut(config: Partial<ShortcutConfig>): void {
    const key = this.createShortcutKey(config as ShortcutConfig)
    this.shortcuts.delete(key)
  }

  private createShortcutKey(config: ShortcutConfig): string {
    const parts: string[] = []
    
    if (config.ctrlKey) parts.push('Ctrl')
    if (config.shiftKey) parts.push('Shift')
    if (config.altKey) parts.push('Alt')
    if (config.metaKey) parts.push('Meta')
    
    parts.push(config.key)
    
    return parts.join('+')
  }

  private startListening(): void {
    if (this.isListening || typeof document === 'undefined') return

    document.addEventListener('keydown', this.handleKeyDown)
    this.isListening = true
  }

  private stopListening(): void {
    if (!this.isListening || typeof document === 'undefined') return

    document.removeEventListener('keydown', this.handleKeyDown)
    this.isListening = false
  }

  private handleKeyDown = (event: KeyboardEvent): void => {
    // Don't handle shortcuts when typing in input fields
    if (this.isInputElement(event.target as Element)) {
      return
    }

    const config: ShortcutConfig = {
      key: event.key,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      altKey: event.altKey,
      metaKey: event.metaKey,
      handler: () => {}
    }

    const key = this.createShortcutKey(config)
    const shortcut = this.shortcuts.get(key)

    if (shortcut) {
      event.preventDefault()
      event.stopPropagation()
      
      try {
        shortcut.handler()
      } catch (error) {
        console.error('Error executing shortcut:', error)
      }
    }
  }

  private isInputElement(element: Element): boolean {
    if (!element) return false

    const tagName = element.tagName.toLowerCase()
    const inputTypes = ['input', 'textarea', 'select']
    
    if (inputTypes.includes(tagName)) {
      return true
    }

    // Check for contenteditable
    if (element.getAttribute('contenteditable') === 'true') {
      return true
    }

    // Check if element is inside a contenteditable
    let parent = element.parentElement
    while (parent) {
      if (parent.getAttribute('contenteditable') === 'true') {
        return true
      }
      parent = parent.parentElement
    }

    return false
  }

  private listShortcuts(): ShortcutConfig[] {
    return Array.from(this.shortcuts.values())
  }

  // Public API methods
  public registerShortcutPublic(config: ShortcutConfig): void {
    this.registerShortcut(config)
  }

  public unregisterShortcutPublic(config: Partial<ShortcutConfig>): void {
    this.unregisterShortcut(config)
  }

  public getShortcuts(): ShortcutConfig[] {
    return this.listShortcuts()
  }

  public getShortcutByKey(key: string): ShortcutConfig | undefined {
    return this.shortcuts.get(key)
  }
}

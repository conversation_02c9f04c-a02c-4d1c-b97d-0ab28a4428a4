import { BasePlugin } from '../base-plugin'
import type { PluginAPI, Theme } from '../../types'

export class ThemePlugin extends BasePlugin {
  name = 'theme-plugin'
  version = '1.0.0'
  description = 'Theme management plugin for VS Code panels'

  private currentTheme: Theme = 'auto'
  private mediaQuery?: MediaQueryList

  protected onActivate(): void {
    if (!this.api) return

    // Register theme commands
    this.registerCommand('theme.setLight', () => this.setTheme('light'))
    this.registerCommand('theme.setDark', () => this.setTheme('dark'))
    this.registerCommand('theme.setAuto', () => this.setTheme('auto'))
    this.registerCommand('theme.toggle', () => this.toggleTheme())

    // Initialize theme
    this.initializeTheme()

    // Listen for system theme changes
    this.setupSystemThemeListener()
  }

  protected onDeactivate(): void {
    // Clean up media query listener
    if (this.mediaQuery) {
      this.mediaQuery.removeEventListener('change', this.handleSystemThemeChange)
    }
  }

  private initializeTheme(): void {
    // Try to load saved theme from localStorage
    try {
      const savedTheme = localStorage.getItem('vscode-panels-theme') as Theme
      if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
        this.currentTheme = savedTheme
      }
    } catch {
      // Ignore localStorage errors
    }

    this.applyTheme()
  }

  private setupSystemThemeListener(): void {
    if (typeof window === 'undefined') return

    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    this.mediaQuery.addEventListener('change', this.handleSystemThemeChange)
  }

  private handleSystemThemeChange = (): void => {
    if (this.currentTheme === 'auto') {
      this.applyTheme()
    }
  }

  private setTheme(theme: Theme): void {
    this.currentTheme = theme
    
    // Save to localStorage
    try {
      localStorage.setItem('vscode-panels-theme', theme)
    } catch {
      // Ignore localStorage errors
    }

    this.applyTheme()
    this.emit('theme.changed', { theme })
  }

  private toggleTheme(): void {
    const resolvedTheme = this.getResolvedTheme()
    const newTheme = resolvedTheme === 'dark' ? 'light' : 'dark'
    this.setTheme(newTheme)
  }

  private applyTheme(): void {
    if (typeof document === 'undefined') return

    const resolvedTheme = this.getResolvedTheme()
    
    // Update document class
    document.documentElement.classList.remove('light', 'dark')
    document.documentElement.classList.add(resolvedTheme)
    
    // Update CSS custom properties
    this.updateCSSProperties(resolvedTheme)
  }

  private getResolvedTheme(): 'light' | 'dark' {
    if (this.currentTheme === 'auto') {
      if (typeof window !== 'undefined') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      }
      return 'light'
    }
    return this.currentTheme
  }

  private updateCSSProperties(theme: 'light' | 'dark'): void {
    if (typeof document === 'undefined') return

    const root = document.documentElement
    
    if (theme === 'dark') {
      root.style.setProperty('--vscode-panel-bg', '#1e1e1e')
      root.style.setProperty('--vscode-panel-border', '#333')
      root.style.setProperty('--vscode-tab-bg', '#2d2d30')
      root.style.setProperty('--vscode-tab-active-bg', '#1e1e1e')
      root.style.setProperty('--vscode-tab-text', '#cccccc')
      root.style.setProperty('--vscode-tab-active-text', '#ffffff')
      root.style.setProperty('--vscode-resize-handle', '#333')
      root.style.setProperty('--vscode-resize-handle-hover', '#007acc')
      root.style.setProperty('--vscode-accent-color', '#007acc')
    } else {
      root.style.setProperty('--vscode-panel-bg', '#ffffff')
      root.style.setProperty('--vscode-panel-border', '#e1e4e8')
      root.style.setProperty('--vscode-tab-bg', '#f6f8fa')
      root.style.setProperty('--vscode-tab-active-bg', '#ffffff')
      root.style.setProperty('--vscode-tab-text', '#586069')
      root.style.setProperty('--vscode-tab-active-text', '#24292e')
      root.style.setProperty('--vscode-resize-handle', '#e1e4e8')
      root.style.setProperty('--vscode-resize-handle-hover', '#0366d6')
      root.style.setProperty('--vscode-accent-color', '#0366d6')
    }
  }

  // Public API
  getCurrentTheme(): Theme {
    return this.currentTheme
  }

  getResolvedThemePublic(): 'light' | 'dark' {
    return this.getResolvedTheme()
  }

  setThemePublic(theme: Theme): void {
    this.setTheme(theme)
  }
}

import { BasePlugin } from '../base-plugin'
import { PersistenceManager, LocalStorageAdapter } from '../../utils/persistence-utils'
import type { PluginAPI, LayoutConfig } from '../../types'

export class PersistencePlugin extends BasePlugin {
  name = 'persistence-plugin'
  version = '1.0.0'
  description = 'Layout persistence plugin for VS Code panels'

  private persistenceManager: PersistenceManager
  private autoSaveInterval?: NodeJS.Timeout
  private autoSaveEnabled = true
  private autoSaveDelay = 30000 // 30 seconds

  constructor() {
    super()
    this.persistenceManager = new PersistenceManager(new LocalStorageAdapter())
  }

  protected onActivate(): void {
    if (!this.api) return

    // Register persistence commands
    this.registerCommand('persistence.save', this.saveLayout.bind(this))
    this.registerCommand('persistence.load', this.loadLayout.bind(this))
    this.registerCommand('persistence.delete', this.deleteLayout.bind(this))
    this.registerCommand('persistence.list', this.listLayouts.bind(this))
    this.registerCommand('persistence.export', this.exportLayouts.bind(this))
    this.registerCommand('persistence.import', this.importLayouts.bind(this))
    this.registerCommand('persistence.clear', this.clearLayouts.bind(this))
    this.registerCommand('persistence.enableAutoSave', this.enableAutoSave.bind(this))
    this.registerCommand('persistence.disableAutoSave', this.disableAutoSave.bind(this))

    // Listen for layout changes
    this.on('layout.changed', this.handleLayoutChange.bind(this))

    // Start auto-save if enabled
    if (this.autoSaveEnabled) {
      this.startAutoSave()
    }
  }

  protected onDeactivate(): void {
    this.stopAutoSave()
  }

  private saveLayout(key: string, layout: LayoutConfig): boolean {
    try {
      const success = this.persistenceManager.saveLayout(key, layout)
      if (success) {
        this.emit('persistence.saved', { key, layout })
      }
      return success
    } catch (error) {
      this.emit('persistence.error', { operation: 'save', error })
      return false
    }
  }

  private loadLayout(key: string): LayoutConfig | null {
    try {
      const layout = this.persistenceManager.loadLayout(key)
      if (layout) {
        this.emit('persistence.loaded', { key, layout })
      }
      return layout
    } catch (error) {
      this.emit('persistence.error', { operation: 'load', error })
      return null
    }
  }

  private deleteLayout(key: string): boolean {
    try {
      const success = this.persistenceManager.removeLayout(key)
      if (success) {
        this.emit('persistence.deleted', { key })
      }
      return success
    } catch (error) {
      this.emit('persistence.error', { operation: 'delete', error })
      return false
    }
  }

  private listLayouts(prefix = 'vscode-panels-'): string[] {
    try {
      return this.persistenceManager.listLayouts(prefix)
    } catch (error) {
      this.emit('persistence.error', { operation: 'list', error })
      return []
    }
  }

  private exportLayouts(prefix = 'vscode-panels-'): string {
    try {
      const exported = this.persistenceManager.exportLayouts(prefix)
      this.emit('persistence.exported', { data: exported })
      return exported
    } catch (error) {
      this.emit('persistence.error', { operation: 'export', error })
      return '{}'
    }
  }

  private importLayouts(jsonData: string): boolean {
    try {
      const success = this.persistenceManager.importLayouts(jsonData)
      if (success) {
        this.emit('persistence.imported', { data: jsonData })
      }
      return success
    } catch (error) {
      this.emit('persistence.error', { operation: 'import', error })
      return false
    }
  }

  private clearLayouts(prefix = 'vscode-panels-'): boolean {
    try {
      const success = this.persistenceManager.clearLayouts(prefix)
      if (success) {
        this.emit('persistence.cleared', { prefix })
      }
      return success
    } catch (error) {
      this.emit('persistence.error', { operation: 'clear', error })
      return false
    }
  }

  private handleLayoutChange(data: { layout: LayoutConfig }): void {
    if (this.autoSaveEnabled) {
      // Debounced auto-save will be triggered by the interval
      this.emit('persistence.layoutChanged', data)
    }
  }

  private startAutoSave(): void {
    if (this.autoSaveInterval) return

    this.autoSaveInterval = setInterval(() => {
      this.emit('persistence.autoSaveRequested')
    }, this.autoSaveDelay)
  }

  private stopAutoSave(): void {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
      this.autoSaveInterval = undefined
    }
  }

  private enableAutoSave(): void {
    this.autoSaveEnabled = true
    this.startAutoSave()
    this.emit('persistence.autoSaveEnabled')
  }

  private disableAutoSave(): void {
    this.autoSaveEnabled = false
    this.stopAutoSave()
    this.emit('persistence.autoSaveDisabled')
  }

  // Public API methods
  public saveLayoutPublic(key: string, layout: LayoutConfig): boolean {
    return this.saveLayout(key, layout)
  }

  public loadLayoutPublic(key: string): LayoutConfig | null {
    return this.loadLayout(key)
  }

  public deleteLayoutPublic(key: string): boolean {
    return this.deleteLayout(key)
  }

  public listLayoutsPublic(prefix?: string): string[] {
    return this.listLayouts(prefix)
  }

  public exportLayoutsPublic(prefix?: string): string {
    return this.exportLayouts(prefix)
  }

  public importLayoutsPublic(jsonData: string): boolean {
    return this.importLayouts(jsonData)
  }

  public clearLayoutsPublic(prefix?: string): boolean {
    return this.clearLayouts(prefix)
  }

  public setAutoSaveDelay(delay: number): void {
    this.autoSaveDelay = delay
    if (this.autoSaveEnabled) {
      this.stopAutoSave()
      this.startAutoSave()
    }
  }

  public getAutoSaveDelay(): number {
    return this.autoSaveDelay
  }

  public isAutoSaveEnabled(): boolean {
    return this.autoSaveEnabled
  }
}

// Re-export plugin system
export * from './plugin-manager'
export * from './base-plugin'

// Plugin types
export type { Plugin, PluginAPI } from '../types'

// Built-in plugins
export { ThemePlugin } from './built-in/theme-plugin'
export { KeyboardShortcutsPlugin } from './built-in/keyboard-shortcuts-plugin'
export { PersistencePlugin } from './built-in/persistence-plugin'

// Plugin utilities
export function createPlugin(config: {
  name: string
  version?: string
  description?: string
  activate: (api: any) => void
  deactivate?: () => void
}): Plugin {
  return {
    name: config.name,
    version: config.version || '1.0.0',
    description: config.description,
    activate: config.activate,
    deactivate: config.deactivate
  }
}

// Plugin registry
const pluginRegistry = new Map<string, Plugin>()

export function registerPlugin(plugin: Plugin): void {
  pluginRegistry.set(plugin.name, plugin)
}

export function unregisterPlugin(name: string): void {
  pluginRegistry.delete(name)
}

export function getPlugin(name: string): Plugin | undefined {
  return pluginRegistry.get(name)
}

export function getAllPlugins(): Plugin[] {
  return Array.from(pluginRegistry.values())
}

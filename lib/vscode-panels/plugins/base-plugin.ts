import type { Plugin, PluginAPI } from '../types'

// Base plugin class that other plugins can extend
export abstract class BasePlugin implements Plugin {
  abstract name: string
  version: string = '1.0.0'
  description?: string
  
  protected api?: PluginAPI
  protected isActive: boolean = false

  // Plugin lifecycle
  activate(api: PluginAPI): void {
    this.api = api
    this.isActive = true
    this.onActivate()
  }

  deactivate(): void {
    this.onDeactivate()
    this.isActive = false
    this.api = undefined
  }

  // Override these methods in subclasses
  protected onActivate(): void {
    // Override in subclass
  }

  protected onDeactivate(): void {
    // Override in subclass
  }

  // Utility methods for subclasses
  protected registerCommand(command: string, handler: Function): void {
    this.api?.registerCommand(command, handler)
  }

  protected registerPanel(panelType: string, component: React.ComponentType): void {
    this.api?.registerPanel(panelType, component)
  }

  protected registerTab(tabType: string, component: React.ComponentType): void {
    this.api?.registerTab(tabType, component)
  }

  protected emit(event: string, data?: any): void {
    this.api?.emit(event, data)
  }

  protected on(event: string, handler: Function): void {
    this.api?.on(event, handler)
  }

  protected off(event: string, handler: Function): void {
    this.api?.off(event, handler)
  }

  // Plugin state
  get active(): boolean {
    return this.isActive
  }

  // Plugin info
  getInfo(): { name: string; version: string; description?: string; active: boolean } {
    return {
      name: this.name,
      version: this.version,
      description: this.description,
      active: this.isActive
    }
  }
}

// Simple plugin factory
export function createSimplePlugin(config: {
  name: string
  version?: string
  description?: string
  onActivate?: (api: PluginAPI) => void
  onDeactivate?: () => void
}): Plugin {
  return {
    name: config.name,
    version: config.version || '1.0.0',
    description: config.description,
    activate: config.onActivate || (() => {}),
    deactivate: config.onDeactivate || (() => {})
  }
}

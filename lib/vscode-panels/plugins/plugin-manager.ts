import type { Plugin, Plugin<PERSON>I } from '../types'

// Plugin manager class
export class PluginManager {
  private plugins = new Map<string, Plugin>()
  private activePlugins = new Set<string>()
  private api: PluginAPI

  constructor() {
    this.api = this.createAPI()
  }

  // Create the plugin API
  private createAPI(): PluginAPI {
    return {
      registerCommand: (command: string, handler: Function) => {
        // TODO: Implement command registration
        console.log(`Command registered: ${command}`)
      },
      
      registerPanel: (panelType: string, component: React.ComponentType) => {
        // TODO: Implement panel type registration
        console.log(`Panel type registered: ${panelType}`)
      },
      
      registerTab: (tabType: string, component: React.ComponentType) => {
        // TODO: Implement tab type registration
        console.log(`Tab type registered: ${tabType}`)
      },
      
      emit: (event: string, data?: any) => {
        // TODO: Implement event emission
        console.log(`Event emitted: ${event}`, data)
      },
      
      on: (event: string, handler: Function) => {
        // TODO: Implement event listener registration
        console.log(`Event listener registered: ${event}`)
      },
      
      off: (event: string, handler: Function) => {
        // TODO: Implement event listener removal
        console.log(`Event listener removed: ${event}`)
      }
    }
  }

  // Register a plugin
  register(plugin: Plugin): boolean {
    if (this.plugins.has(plugin.name)) {
      console.warn(`Plugin ${plugin.name} is already registered`)
      return false
    }

    this.plugins.set(plugin.name, plugin)
    console.log(`Plugin ${plugin.name} registered`)
    return true
  }

  // Unregister a plugin
  unregister(name: string): boolean {
    if (!this.plugins.has(name)) {
      console.warn(`Plugin ${name} is not registered`)
      return false
    }

    // Deactivate if active
    if (this.activePlugins.has(name)) {
      this.deactivate(name)
    }

    this.plugins.delete(name)
    console.log(`Plugin ${name} unregistered`)
    return true
  }

  // Activate a plugin
  activate(name: string): boolean {
    const plugin = this.plugins.get(name)
    if (!plugin) {
      console.warn(`Plugin ${name} is not registered`)
      return false
    }

    if (this.activePlugins.has(name)) {
      console.warn(`Plugin ${name} is already active`)
      return false
    }

    try {
      plugin.activate(this.api)
      this.activePlugins.add(name)
      console.log(`Plugin ${name} activated`)
      return true
    } catch (error) {
      console.error(`Failed to activate plugin ${name}:`, error)
      return false
    }
  }

  // Deactivate a plugin
  deactivate(name: string): boolean {
    const plugin = this.plugins.get(name)
    if (!plugin) {
      console.warn(`Plugin ${name} is not registered`)
      return false
    }

    if (!this.activePlugins.has(name)) {
      console.warn(`Plugin ${name} is not active`)
      return false
    }

    try {
      if (plugin.deactivate) {
        plugin.deactivate()
      }
      this.activePlugins.delete(name)
      console.log(`Plugin ${name} deactivated`)
      return true
    } catch (error) {
      console.error(`Failed to deactivate plugin ${name}:`, error)
      return false
    }
  }

  // Get plugin info
  getPlugin(name: string): Plugin | undefined {
    return this.plugins.get(name)
  }

  // List all plugins
  listPlugins(): Plugin[] {
    return Array.from(this.plugins.values())
  }

  // List active plugins
  listActivePlugins(): Plugin[] {
    return Array.from(this.activePlugins)
      .map(name => this.plugins.get(name))
      .filter((plugin): plugin is Plugin => plugin !== undefined)
  }

  // Check if plugin is active
  isActive(name: string): boolean {
    return this.activePlugins.has(name)
  }

  // Activate all plugins
  activateAll(): void {
    for (const plugin of this.plugins.values()) {
      if (!this.activePlugins.has(plugin.name)) {
        this.activate(plugin.name)
      }
    }
  }

  // Deactivate all plugins
  deactivateAll(): void {
    for (const name of this.activePlugins) {
      this.deactivate(name)
    }
  }

  // Get plugin statistics
  getStats() {
    return {
      total: this.plugins.size,
      active: this.activePlugins.size,
      inactive: this.plugins.size - this.activePlugins.size
    }
  }
}

// Global plugin manager instance
export const pluginManager = new PluginManager()

// Convenience functions
export function registerPlugin(plugin: Plugin): boolean {
  return pluginManager.register(plugin)
}

export function unregisterPlugin(name: string): boolean {
  return pluginManager.unregister(name)
}

export function activatePlugin(name: string): boolean {
  return pluginManager.activate(name)
}

export function deactivatePlugin(name: string): boolean {
  return pluginManager.deactivate(name)
}

export function getPlugin(name: string): Plugin | undefined {
  return pluginManager.getPlugin(name)
}

export function listPlugins(): Plugin[] {
  return pluginManager.listPlugins()
}

export function listActivePlugins(): Plugin[] {
  return pluginManager.listActivePlugins()
}

export function isPluginActive(name: string): boolean {
  return pluginManager.isActive(name)
}

// Core components
export {
  Panel,
  PanelGroup,
  PanelHeader,
  PanelContent,
  ResizeHandle,
  Tab,
  TabBar,
  TabContent,
  Workspace
} from './components'

// Component props types
export type {
  PanelProps,
  PanelGroupConfig,
  TabProps,
  TabBarProps,
  TabContentProps,
  WorkspaceProps,
  TabAction
} from './components'

// State types
export type {
  PanelState,
  PanelStore,
  PanelDirection
} from './types/panel'

export type {
  TabState,
  TabStore
} from './types/tab'

export type {
  LayoutConfig,
  LayoutNode,
  LayoutNodeType,
  LayoutTheme,
  WorkspaceConfig,
  LayoutEvents
} from './types/layout'

// Store hooks
export { usePanelStore } from './store/panel-store'
export { useTabStore } from './store/tab-store'
export { useWorkspaceStore } from './store/workspace-store'

// Constants
export { CONSTANTS } from './constants'

// Utils
export {
  // Layout utils
  findNode,
  removeNode,
  insertNode,
  createLayout,
  createPanelNode,
  createGroupNode,
  createPlaceholderNode,

  // Storage utils
  saveLayout,
  loadLayout,
  saveTheme,
  loadTheme,
  
  // Theme utils
  getSystemTheme,
  
  // DOM utils
  getElementDimensions,
  
  // Math utils
  clamp,
  
  // Event utils
  debounce,
  throttle
} from './utils'

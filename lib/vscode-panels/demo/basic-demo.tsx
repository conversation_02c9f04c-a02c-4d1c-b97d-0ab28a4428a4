"use client"

import React, { useState } from 'react'
import { FileText, Search, Terminal, Settings, Code, Folder } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Workspace, 
  Panel, 
  Tab, 
  PanelGroup,
  useWorkspaceStore 
} from '../index'

// Demo content components
function FileExplorer() {
  return (
    <div className="p-4 space-y-2">
      <div className="font-medium text-sm mb-3">Explorer</div>
      <div className="space-y-1">
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm">
          <Folder className="w-4 h-4" />
          <span>src</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm ml-4">
          <FileText className="w-4 h-4" />
          <span>App.tsx</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm ml-4">
          <FileText className="w-4 h-4" />
          <span>index.tsx</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm">
          <Folder className="w-4 h-4" />
          <span>components</span>
        </div>
        <div className="flex items-center gap-2 p-1 hover:bg-muted rounded text-sm ml-4">
          <FileText className="w-4 h-4" />
          <span>Panel.tsx</span>
        </div>
      </div>
    </div>
  )
}

function CodeEditor({ fileName = "App.tsx" }: { fileName?: string }) {
  return (
    <div className="p-4 font-mono text-sm">
      <div className="text-muted-foreground mb-2">{fileName}</div>
      <div className="space-y-1">
        <div><span className="text-blue-500">import</span> React <span className="text-blue-500">from</span> <span className="text-green-500">'react'</span></div>
        <div><span className="text-blue-500">import</span> &#123; Workspace, Panel &#125; <span className="text-blue-500">from</span> <span className="text-green-500">'@/lib/vscode-panels'</span></div>
        <div></div>
        <div><span className="text-blue-500">function</span> <span className="text-yellow-500">App</span>() &#123;</div>
        <div className="ml-4"><span className="text-blue-500">return</span> (</div>
        <div className="ml-8">&lt;<span className="text-red-500">Workspace</span>&gt;</div>
        <div className="ml-12">&lt;<span className="text-red-500">Panel</span> <span className="text-blue-500">id</span>=<span className="text-green-500">"main"</span> <span className="text-blue-500">title</span>=<span className="text-green-500">"Main Panel"</span>&gt;</div>
        <div className="ml-16">&lt;<span className="text-red-500">div</span>&gt;Hello World!&lt;/<span className="text-red-500">div</span>&gt;</div>
        <div className="ml-12">&lt;/<span className="text-red-500">Panel</span>&gt;</div>
        <div className="ml-8">&lt;/<span className="text-red-500">Workspace</span>&gt;</div>
        <div className="ml-4">)</div>
        <div>&#125;</div>
      </div>
    </div>
  )
}

function SearchPanel() {
  return (
    <div className="p-4">
      <div className="font-medium text-sm mb-3">Search</div>
      <div className="space-y-3">
        <input 
          type="text" 
          placeholder="Search files..." 
          className="w-full px-3 py-2 border rounded-md text-sm"
        />
        <div className="text-xs text-muted-foreground">
          No results found
        </div>
      </div>
    </div>
  )
}

function TerminalPanel() {
  return (
    <div className="p-4 bg-black text-green-400 font-mono text-sm">
      <div className="mb-2">Terminal</div>
      <div className="space-y-1">
        <div>$ npm install @soimagine/vscode-panels</div>
        <div>✓ Package installed successfully</div>
        <div>$ npm run dev</div>
        <div>Starting development server...</div>
        <div className="flex items-center">
          <span>$ </span>
          <span className="animate-pulse">_</span>
        </div>
      </div>
    </div>
  )
}

function SettingsPanel() {
  return (
    <div className="p-4">
      <div className="font-medium text-sm mb-3">Settings</div>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm">Theme</span>
          <select className="px-2 py-1 border rounded text-sm">
            <option>Auto</option>
            <option>Light</option>
            <option>Dark</option>
          </select>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm">Auto Save</span>
          <input type="checkbox" defaultChecked />
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm">Line Numbers</span>
          <input type="checkbox" defaultChecked />
        </div>
      </div>
    </div>
  )
}

// Main demo component
export function BasicVSCodeDemo() {
  const [activeDemo, setActiveDemo] = useState<'basic' | 'advanced' | 'custom'>('basic')
  
  return (
    <div className="w-full h-screen flex flex-col">
      {/* Demo Controls */}
      <div className="p-4 border-b bg-background">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">VS Code Panels Demo</h1>
          <div className="flex gap-2">
            <Button
              variant={activeDemo === 'basic' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveDemo('basic')}
            >
              Basic
            </Button>
            <Button
              variant={activeDemo === 'advanced' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveDemo('advanced')}
            >
              Advanced
            </Button>
            <Button
              variant={activeDemo === 'custom' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveDemo('custom')}
            >
              Custom
            </Button>
          </div>
        </div>
      </div>

      {/* Demo Content */}
      <div className="flex-1">
        {activeDemo === 'basic' && <BasicDemo />}
        {activeDemo === 'advanced' && <AdvancedDemo />}
        {activeDemo === 'custom' && <CustomDemo />}
      </div>
    </div>
  )
}

// Basic demo with simple panels
function BasicDemo() {
  return (
    <Workspace
      className="w-full h-full"
      showLayoutControls={true}
      theme="auto"
    >
      <PanelGroup direction="horizontal">
        {/* Sidebar */}
        <Panel
          id="sidebar"
          title="Explorer"
          icon={Folder}
          minWidth={200}
          defaultWidth={250}
        >
          <Tab id="files" title="Files" icon={FileText}>
            <FileExplorer />
          </Tab>
          <Tab id="search" title="Search" icon={Search}>
            <SearchPanel />
          </Tab>
        </Panel>

        {/* Main content */}
        <PanelGroup direction="vertical">
          {/* Editor */}
          <Panel
            id="editor"
            title="Editor"
            icon={Code}
            defaultHeight={400}
          >
            <Tab id="app-tsx" title="App.tsx" icon={FileText} isPinned>
              <CodeEditor fileName="App.tsx" />
            </Tab>
            <Tab id="index-tsx" title="index.tsx" icon={FileText}>
              <CodeEditor fileName="index.tsx" />
            </Tab>
          </Panel>

          {/* Terminal */}
          <Panel
            id="terminal"
            title="Terminal"
            icon={Terminal}
            minHeight={150}
            defaultHeight={200}
          >
            <Tab id="bash" title="bash">
              <TerminalPanel />
            </Tab>
          </Panel>
        </PanelGroup>
      </PanelGroup>
    </Workspace>
  )
}

// Advanced demo with more features
function AdvancedDemo() {
  return (
    <Workspace
      className="w-full h-full"
      showLayoutControls={true}
      theme="dark"
      persistLayout={true}
    >
      <PanelGroup direction="horizontal">
        {/* Left sidebar */}
        <Panel
          id="left-sidebar"
          title="Explorer"
          icon={Folder}
          minWidth={200}
          defaultWidth={300}
        >
          <Tab id="files" title="Files" icon={FileText} isPinned>
            <FileExplorer />
          </Tab>
          <Tab id="search" title="Search" icon={Search}>
            <SearchPanel />
          </Tab>
          <Tab id="settings" title="Settings" icon={Settings}>
            <SettingsPanel />
          </Tab>
        </Panel>

        {/* Main area */}
        <PanelGroup direction="vertical">
          {/* Editor group */}
          <PanelGroup direction="horizontal">
            <Panel
              id="main-editor"
              title="Main Editor"
              icon={Code}
            >
              <Tab id="app" title="App.tsx" icon={FileText} isPinned isDirty>
                <CodeEditor fileName="App.tsx" />
              </Tab>
              <Tab id="components" title="Components.tsx" icon={FileText}>
                <CodeEditor fileName="Components.tsx" />
              </Tab>
            </Panel>

            <Panel
              id="side-editor"
              title="Side Editor"
              icon={Code}
              defaultWidth={400}
            >
              <Tab id="utils" title="utils.ts" icon={FileText}>
                <CodeEditor fileName="utils.ts" />
              </Tab>
            </Panel>
          </PanelGroup>

          {/* Bottom panel */}
          <Panel
            id="bottom-panel"
            title="Terminal"
            icon={Terminal}
            minHeight={150}
            defaultHeight={250}
          >
            <Tab id="terminal-1" title="Terminal 1">
              <TerminalPanel />
            </Tab>
            <Tab id="terminal-2" title="Terminal 2">
              <TerminalPanel />
            </Tab>
          </Panel>
        </PanelGroup>
      </PanelGroup>
    </Workspace>
  )
}

// Custom demo with dynamic panels
function CustomDemo() {
  const { createPanelWithTab } = useWorkspaceStore()
  
  const addPanel = () => {
    const id = `panel-${Date.now()}`
    createPanelWithTab(
      {
        id,
        title: `Panel ${id.slice(-4)}`,
        defaultWidth: 300,
        defaultHeight: 200
      },
      {
        id: `tab-${Date.now()}`,
        title: `Tab ${id.slice(-4)}`,
        content: <div className="p-4">Dynamic panel content</div>
      }
    )
  }

  return (
    <div className="w-full h-full flex flex-col">
      <div className="p-4 border-b">
        <Button onClick={addPanel}>Add Panel</Button>
      </div>
      
      <div className="flex-1">
        <Workspace
          className="w-full h-full"
          showLayoutControls={true}
          allowLayoutModification={true}
        >
          <Panel
            id="welcome"
            title="Welcome"
            icon={FileText}
          >
            <Tab id="welcome-tab" title="Welcome">
              <div className="p-8 text-center">
                <h2 className="text-xl font-bold mb-4">Custom Demo</h2>
                <p className="text-muted-foreground mb-4">
                  Click "Add Panel" to dynamically create new panels
                </p>
                <Button onClick={addPanel}>Add Panel</Button>
              </div>
            </Tab>
          </Panel>
        </Workspace>
      </div>
    </div>
  )
}

export default BasicVSCodeDemo

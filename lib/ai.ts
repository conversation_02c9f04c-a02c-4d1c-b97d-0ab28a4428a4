import { xai } from "@ai-sdk/xai"
import { generateText, streamText } from "ai"

export async function generateAIResponse(prompt: string, systemPrompt?: string) {
  try {
    const { text } = await generateText({
      model: xai("grok-beta"),
      prompt,
      system:
        systemPrompt ||
        "You are Soimagine's AI assistant, designed to help with digital agency tasks like web development, design, and content creation. Be helpful, creative, and professional.",
    })

    return { text, error: null }
  } catch (error) {
    console.error("Error generating AI response:", error)

    // Provide a helpful fallback response
    const fallbackText = `I'm here to help you with your digital agency needs! Here are some ways I can assist:

**Web Development**
- Create responsive websites
- Build web applications
- Implement modern frameworks

**Mobile Applications**
- Cross-platform development
- Native app solutions
- User experience optimization

**AI Solutions**
- Automation implementation
- Intelligent features
- Data analysis tools

**Design Services**
- UI/UX design
- Brand identity
- Visual presentations

What specific project can I help you with today?`

    return {
      text: fallbackText,
      error,
    }
  }
}

export function streamAIResponse(
  prompt: string,
  systemPrompt?: string,
  onChunk?: (chunk: { type: string; text: string }) => void,
  onFinish?: (result: { text: string }) => void,
) {
  try {
    const result = streamText({
      model: xai("grok-beta"),
      prompt,
      system:
        systemPrompt ||
        "You are Soimagine's AI assistant, designed to help with digital agency tasks like web development, design, and content creation. Be helpful, creative, and professional.",
      onChunk: (chunk) => {
        if (chunk.type === "text-delta" && onChunk) {
          onChunk({ type: "text", text: chunk.text })
        }
      },
    })

    if (onFinish) {
      result.text.then((text) => onFinish({ text }))
    }

    return result
  } catch (error) {
    console.error("Error streaming AI response:", error)

    const fallbackText = `I'm ready to help you with your digital agency projects! I can assist with web development, mobile apps, AI solutions, and design services. What would you like to work on?`

    if (onFinish) {
      onFinish({ text: fallbackText })
    }

    return {
      text: Promise.resolve(fallbackText),
    }
  }
}

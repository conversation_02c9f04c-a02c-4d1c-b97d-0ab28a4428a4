// Predefined challenge templates for the AI challenge generation system

export interface ChallengeTemplate {
  id: string
  title: string
  description: string
  instructions: string
  type: string
  category: string
  difficulty: string
  creditReward: number
  xpReward: number
  timeLimit?: number
  requirements?: any
  tags: string[]
}

export const challengeTemplates: ChallengeTemplate[] = [
  // Social Media Challenges
  {
    id: 'social-share-basic',
    title: 'Share the Love',
    description: 'Share Soimagine\'s latest portfolio piece on your favorite social platform',
    instructions: `1. Visit Soimagine's portfolio page
2. Choose your favorite project
3. Share it on any social media platform with your thoughts
4. Include the hashtag #SoimagineDesign
5. Submit a screenshot as proof`,
    type: 'SOCIAL_SHARE',
    category: 'SOCIAL_MEDIA',
    difficulty: 'BEGINNER',
    creditReward: 15,
    xpReward: 25,
    timeLimit: 30,
    requirements: {
      platforms: ['any'],
      hashtags: ['#SoimagineDesign'],
      contentType: 'portfolio_share'
    },
    tags: ['social', 'sharing', 'portfolio', 'beginner']
  },
  {
    id: 'multi-platform-blast',
    title: 'Multi-Platform Marketing Blast',
    description: 'Share Soimagine content across 3 different social platforms in one day',
    instructions: `1. Choose 3 different social media platforms
2. Create unique posts for each platform showcasing Soimagine's services
3. Tailor the content to each platform's audience
4. Use relevant hashtags and mention @SoimagineDigital
5. Submit screenshots from all 3 platforms`,
    type: 'SOCIAL_SHARE',
    category: 'SOCIAL_MEDIA',
    difficulty: 'INTERMEDIATE',
    creditReward: 50,
    xpReward: 100,
    timeLimit: 1440, // 24 hours
    requirements: {
      platforms: ['minimum_3'],
      hashtags: ['#SoimagineDigital', '#WebDevelopment', '#DigitalAgency'],
      contentType: 'service_promotion',
      minPosts: 3
    },
    tags: ['social', 'multi-platform', 'marketing', 'intermediate']
  },

  // Content Creation Challenges
  {
    id: 'blog-post-creation',
    title: 'Write a Marketing Blog Post',
    description: 'Create an engaging blog post about digital marketing trends',
    instructions: `1. Research current digital marketing trends
2. Write a 500-800 word blog post
3. Include actionable tips for businesses
4. Mention how Soimagine's services align with these trends
5. Submit your blog post for review`,
    type: 'CONTENT_CREATION',
    category: 'CONTENT',
    difficulty: 'INTERMEDIATE',
    creditReward: 75,
    xpReward: 150,
    timeLimit: 2880, // 48 hours
    requirements: {
      wordCount: { min: 500, max: 800 },
      topics: ['digital_marketing', 'trends', 'business_tips'],
      brandMention: true
    },
    tags: ['content', 'writing', 'blog', 'marketing', 'intermediate']
  },
  {
    id: 'infographic-design',
    title: 'Design a Marketing Infographic',
    description: 'Create a visually appealing infographic about web development benefits',
    instructions: `1. Research web development benefits for businesses
2. Design an infographic using any tool (Canva, Figma, etc.)
3. Include Soimagine's branding elements
4. Make it shareable and informative
5. Submit the final design file`,
    type: 'CONTENT_CREATION',
    category: 'CONTENT',
    difficulty: 'ADVANCED',
    creditReward: 100,
    xpReward: 200,
    timeLimit: 4320, // 72 hours
    requirements: {
      format: ['png', 'jpg', 'pdf'],
      dimensions: { min_width: 800, min_height: 1000 },
      brandElements: true,
      topic: 'web_development_benefits'
    },
    tags: ['content', 'design', 'infographic', 'visual', 'advanced']
  },

  // Referral Challenges
  {
    id: 'first-referral',
    title: 'Bring a Friend',
    description: 'Refer your first friend to join the Soimagine community',
    instructions: `1. Share your unique referral link with friends
2. Explain the benefits of joining Soimagine's platform
3. Help them complete their registration
4. Both you and your friend earn bonus credits!`,
    type: 'REFERRAL',
    category: 'REFERRALS',
    difficulty: 'BEGINNER',
    creditReward: 100,
    xpReward: 150,
    requirements: {
      referrals: 1,
      conversionRequired: true
    },
    tags: ['referral', 'community', 'growth', 'beginner']
  },
  {
    id: 'referral-champion',
    title: 'Referral Champion',
    description: 'Successfully refer 5 new members to the platform',
    instructions: `1. Share your referral link across your network
2. Engage with potential referrals and explain the value
3. Help them get started with their first challenge
4. Achieve 5 successful conversions to unlock champion status`,
    type: 'REFERRAL',
    category: 'REFERRALS',
    difficulty: 'EXPERT',
    creditReward: 500,
    xpReward: 750,
    requirements: {
      referrals: 5,
      conversionRequired: true,
      timeframe: '30_days'
    },
    tags: ['referral', 'champion', 'network', 'expert']
  },

  // Learning Challenges
  {
    id: 'marketing-fundamentals',
    title: 'Master Marketing Fundamentals',
    description: 'Complete a comprehensive quiz on digital marketing basics',
    instructions: `1. Study the provided marketing fundamentals guide
2. Take the 20-question quiz
3. Achieve a score of 80% or higher
4. Apply learnings to create a mini marketing plan
5. Submit your quiz results and plan`,
    type: 'LEARNING',
    category: 'LEARNING',
    difficulty: 'BEGINNER',
    creditReward: 40,
    xpReward: 80,
    timeLimit: 1440, // 24 hours
    requirements: {
      quizScore: { minimum: 80 },
      topics: ['digital_marketing', 'seo', 'social_media', 'content_marketing'],
      deliverable: 'marketing_plan'
    },
    tags: ['learning', 'quiz', 'fundamentals', 'marketing', 'beginner']
  },

  // Engagement Challenges
  {
    id: 'community-helper',
    title: 'Community Helper',
    description: 'Help 3 new community members with their first challenges',
    instructions: `1. Find new members in the community forum
2. Offer helpful advice and encouragement
3. Share your experience and tips
4. Help them complete their first challenge
5. Build lasting connections in the community`,
    type: 'ENGAGEMENT',
    category: 'COMMUNITY',
    difficulty: 'INTERMEDIATE',
    creditReward: 60,
    xpReward: 120,
    requirements: {
      helpCount: 3,
      newMembersOnly: true,
      interactionType: 'challenge_assistance'
    },
    tags: ['community', 'helping', 'mentoring', 'engagement', 'intermediate']
  },

  // Special Event Challenges
  {
    id: 'launch-week-special',
    title: 'Launch Week Celebration',
    description: 'Participate in our platform launch week by completing daily tasks',
    instructions: `1. Complete one challenge each day for 7 days
2. Share your progress on social media daily
3. Engage with other participants
4. Attend the virtual launch event
5. Celebrate with the community!`,
    type: 'COMMUNITY',
    category: 'SPECIAL_EVENT',
    difficulty: 'INTERMEDIATE',
    creditReward: 200,
    xpReward: 400,
    timeLimit: 10080, // 7 days
    requirements: {
      dailyTasks: 7,
      socialSharing: true,
      eventAttendance: true,
      consecutiveDays: true
    },
    tags: ['special', 'launch', 'event', 'community', 'celebration']
  }
]

// Helper functions for challenge generation
export function getTemplatesByCategory(category: string): ChallengeTemplate[] {
  return challengeTemplates.filter(template => template.category === category)
}

export function getTemplatesByDifficulty(difficulty: string): ChallengeTemplate[] {
  return challengeTemplates.filter(template => template.difficulty === difficulty)
}

export function getTemplatesByType(type: string): ChallengeTemplate[] {
  return challengeTemplates.filter(template => template.type === type)
}

export function getRandomTemplate(): ChallengeTemplate {
  return challengeTemplates[Math.floor(Math.random() * challengeTemplates.length)]
}

export function getTemplateById(id: string): ChallengeTemplate | undefined {
  return challengeTemplates.find(template => template.id === id)
}

// Template customization functions
export function customizeTemplate(
  template: ChallengeTemplate,
  customizations: Partial<ChallengeTemplate>
): ChallengeTemplate {
  return {
    ...template,
    ...customizations,
    id: `${template.id}_custom_${Date.now()}`
  }
}

export function scaleRewards(
  template: ChallengeTemplate,
  multiplier: number
): ChallengeTemplate {
  return {
    ...template,
    creditReward: Math.round(template.creditReward * multiplier),
    xpReward: Math.round(template.xpReward * multiplier)
  }
}

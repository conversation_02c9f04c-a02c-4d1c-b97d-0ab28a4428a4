import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@next-auth/prisma-adapter'
import CredentialsProvider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from './prisma'
import { Prisma } from '@prisma/client'

const MAX_LOGIN_ATTEMPTS = 5
const LOCK_TIME = 15 * 60 * 1000 // 15 minutes
const DEBUG = process.env.NODE_ENV === 'development'

function log(...args: any[]) {
  if (DEBUG) {
    console.log('[Auth]', ...args)
  }
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            log('Missing credentials:', { email: !!credentials?.email, password: !!credentials?.password });
            throw new Error('Please enter both email and password');
          }

          log('Attempting login for:', credentials.email);

          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email
            },
            select: {
              id: true,
              email: true,
              name: true,
              password: true,
              image: true,
              role: true,
              emailVerified: true,
              isVerified: true,
              lockedUntil: true,
              failedLoginAttempts: true
            }
          });

          if (!user || !user.password) {
            log('User not found or no password:', credentials.email);
            throw new Error('Invalid email or password');
          }

          // Auto-verify in development
          if (DEBUG && !user.emailVerified) {
            log('Auto-verifying email in development for:', credentials.email);
            await prisma.user.update({
              where: { id: user.id },
              data: { 
                emailVerified: new Date(),
                isVerified: true
              }
            });
            user.emailVerified = new Date();
            user.isVerified = true;
          }

          // Check if account is locked
          if (!DEBUG && user.lockedUntil && user.lockedUntil > new Date()) {
            const remainingTime = Math.ceil(
              (user.lockedUntil.getTime() - Date.now()) / 1000 / 60
            );
            log('Account locked:', { email: user.email, remainingTime });
            throw new Error(
              `Account is locked. Please try again in ${remainingTime} minutes`
            );
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            // Increment failed attempts
            const failedAttempts = (user.failedLoginAttempts || 0) + 1;
            const updates: Prisma.UserUpdateInput = { failedLoginAttempts: failedAttempts };
            
            // Lock account if max attempts reached
            if (failedAttempts >= MAX_LOGIN_ATTEMPTS) {
              updates.lockedUntil = new Date(Date.now() + LOCK_TIME);
            }
            
            await prisma.user.update({
              where: { id: user.id },
              data: updates
            });

            if (failedAttempts >= MAX_LOGIN_ATTEMPTS) {
              throw new Error(
                `Account locked for ${LOCK_TIME / 1000 / 60} minutes due to too many failed attempts`
              );
            }

            log('Invalid password for:', credentials.email);
            throw new Error('Invalid email or password');
          }

          // Reset failed attempts on successful login
          await prisma.user.update({
            where: { id: user.id },
            data: { 
              lastLoginAt: new Date(),
              failedLoginAttempts: 0,
              lockedUntil: null
            }
          });

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            role: user.role as "USER" | "ADMIN",
            emailVerified: user.emailVerified,
            isVerified: !!user.emailVerified,
            lockedUntil: user.lockedUntil,
            failedLoginAttempts: user.failedLoginAttempts
          };
        } catch (error) {
          log('Authentication error:', error);
          throw error;
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  callbacks: {
    async signIn({ user, account }) {
      // Skip all checks in development
      if (DEBUG) {
        log('Allowing sign in in development mode:', user.email);
        return true;
      }

      // For credentials login, verify email
      if (account?.type === 'credentials' && !user.emailVerified) {
        log('Email not verified:', user.email);
        throw new Error('Please verify your email before logging in');
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.emailVerified = user.emailVerified;
        token.isVerified = user.isVerified;
        token.lockedUntil = user.lockedUntil;
        token.failedLoginAttempts = user.failedLoginAttempts;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as "USER" | "ADMIN";
        session.user.emailVerified = token.emailVerified as Date | null;
        session.user.isVerified = token.isVerified as boolean;
        session.user.lockedUntil = token.lockedUntil as Date | null;
        session.user.failedLoginAttempts = token.failedLoginAttempts as number;
      }
      return session;
    }
  },
  pages: {
    signIn: '/login',
    register: '/signup',
    error: '/auth/error',
    verifyRequest: '/verify-email'
  },
  events: {
    async signIn({ user }) {
      await prisma.authEvent.create({
        data: {
          type: 'SIGN_IN',
          userId: user.id,
          success: true,
          metadata: {
            emailVerified: !!user.emailVerified
          }
        }
      });
    },
    async signOut({ token }) {
      if (token?.sub) {
        await prisma.authEvent.create({
          data: {
            type: 'SIGN_OUT',
            userId: token.sub,
            success: true
          }
        });
      }
    }
  }
}

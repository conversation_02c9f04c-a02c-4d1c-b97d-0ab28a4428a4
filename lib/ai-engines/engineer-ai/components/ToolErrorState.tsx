'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  ExclamationTriangleIcon,
  ArrowPathIcon,
  InformationCircleIcon,
  CodeBracketIcon,
  WrenchScrewdriverIcon,
  LightBulbIcon,
  BugAntIcon
} from '@heroicons/react/24/outline';

interface ToolErrorStateProps {
  toolName: string;
  error: string;
  args?: any;
  onRetry?: () => void;
}

export function ToolErrorState({ 
  toolName, 
  error, 
  args, 
  onRetry 
}: ToolErrorStateProps) {
  const getToolIcon = (toolName: string) => {
    switch (toolName) {
      case 'generatePrototype':
        return <CodeBracketIcon className="w-5 h-5 text-red-500" />;
      case 'modifyCode':
        return <WrenchScrewdriverIcon className="w-5 h-5 text-red-500" />;
      case 'explainCode':
        return <LightBulbIcon className="w-5 h-5 text-red-500" />;
      case 'debugCode':
        return <BugAntIcon className="w-5 h-5 text-red-500" />;
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />;
    }
  };

  const getErrorType = (error: string, toolName: string) => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
      return {
        type: 'network',
        title: 'Network Error',
        description: 'Unable to connect to the code generation service.',
        suggestions: [
          'Check your internet connection',
          'Try again in a few moments',
          'Contact support if the issue persists'
        ]
      };
    } else if (error.toLowerCase().includes('timeout')) {
      return {
        type: 'timeout',
        title: 'Request Timeout',
        description: 'Code generation took too long to complete.',
        suggestions: [
          'Try with a simpler request',
          'Reduce the complexity level',
          'Break down into smaller tasks'
        ]
      };
    } else if (error.toLowerCase().includes('syntax') || error.toLowerCase().includes('parse')) {
      return {
        type: 'syntax',
        title: 'Code Syntax Error',
        description: 'There was an issue with the generated code syntax.',
        suggestions: [
          'Try regenerating with different parameters',
          'Check the input requirements',
          'Use a different framework or template'
        ]
      };
    } else if (toolName === 'generatePrototype') {
      return {
        type: 'generation',
        title: 'Code Generation Error',
        description: 'Failed to generate the requested prototype.',
        suggestions: [
          'Try with a clearer description',
          'Choose a different framework',
          'Reduce the complexity level',
          'Check if all required parameters are provided'
        ]
      };
    } else {
      return {
        type: 'general',
        title: 'Tool Execution Error',
        description: `An error occurred while executing the ${toolName} tool.`,
        suggestions: [
          'Try again with different parameters',
          'Check your input for any issues',
          'Contact support if the problem continues'
        ]
      };
    }
  };

  const errorInfo = getErrorType(error, toolName);

  const shakeVariants = {
    animate: {
      x: [0, -10, 10, -10, 10, 0],
      transition: {
        duration: 0.5,
        ease: "easeInOut"
      }
    }
  };

  const fadeInVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  return (
    <motion.div
      className="tool-error-state"
      variants={fadeInVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4"
        variants={shakeVariants}
        animate="animate"
      >
        {/* Error Header */}
        <div className="flex items-start space-x-3 mb-4">
          <ExclamationTriangleIcon className="w-6 h-6 text-red-500 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              {getToolIcon(toolName)}
              <h4 className="font-semibold text-red-900 dark:text-red-100">
                {errorInfo.title}
              </h4>
            </div>
            <p className="text-sm text-red-800 dark:text-red-200 mb-3">
              {errorInfo.description}
            </p>
            <div className="text-xs text-red-800 dark:text-red-200 font-mono bg-red-100 dark:bg-red-900/40 p-2 rounded border">
              {error}
            </div>
          </div>
        </div>

        {/* Tool Information */}
        {args && Object.keys(args).length > 0 && (
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <InformationCircleIcon className="w-4 h-4 text-red-500" />
              <span className="text-sm font-medium text-red-900 dark:text-red-100">
                Parameters used:
              </span>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded border p-3">
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {Object.entries(args).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <span className="text-gray-600 dark:text-gray-400 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}:
                    </span>
                    <span className="text-gray-800 dark:text-gray-200 font-mono max-w-xs truncate">
                      {typeof value === 'boolean' 
                        ? value.toString() 
                        : typeof value === 'object'
                        ? JSON.stringify(value)
                        : String(value)
                      }
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Suggestions */}
        <div className="mb-4">
          <h6 className="text-sm font-medium text-red-900 dark:text-red-100 mb-2">
            Suggestions to fix this issue:
          </h6>
          <ul className="text-xs text-red-800 dark:text-red-200 space-y-1">
            {errorInfo.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="text-red-500 mt-1">•</span>
                <span>{suggestion}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Tool-specific Help */}
        {toolName === 'generatePrototype' && (
          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-700">
            <h6 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              Code Generation Tips:
            </h6>
            <ul className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Be specific about what you want to build</li>
              <li>• Choose appropriate complexity level for your needs</li>
              <li>• Ensure the framework supports your requirements</li>
              <li>• Try starting with a simpler version first</li>
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className="text-xs text-red-800 dark:text-red-200">
            Error occurred at {new Date().toLocaleTimeString()}
          </div>
          
          {onRetry && (
            <motion.button
              onClick={onRetry}
              className="flex items-center space-x-2 px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white rounded text-sm font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowPathIcon className="w-4 h-4" />
              <span>Retry</span>
            </motion.button>
          )}
        </div>

        {/* Debug Information (for development) */}
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4">
            <summary className="text-xs text-red-700 dark:text-red-300 cursor-pointer hover:text-red-900 dark:hover:text-red-100">
              Debug Information (Development Only)
            </summary>
            <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
              <div><strong>Tool:</strong> {toolName}</div>
              <div><strong>Error:</strong> {error}</div>
              <div><strong>Args:</strong> {JSON.stringify(args, null, 2)}</div>
              <div><strong>Timestamp:</strong> {new Date().toISOString()}</div>
            </div>
          </details>
        )}
      </motion.div>
    </motion.div>
  );
}

'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BugAntIcon,
  ExclamationTriangleIcon,
  LightBulbIcon,
  CheckIcon,
  XMarkIcon,
  DocumentDuplicateIcon,
  EyeIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { type DebugCardProps } from '../types';

export function DebugCard({ 
  debug, 
  originalCode, 
  onApplyFix 
}: DebugCardProps) {
  const [showFixedCode, setShowFixedCode] = useState(true);
  const [copied, setCopied] = useState(false);

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  const handleCopyCode = async () => {
    try {
      const codeToCopy = debug.fixedCode || originalCode;
      await navigator.clipboard.writeText(codeToCopy);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <XMarkIcon className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />;
      case 'suggestion':
        return <LightBulbIcon className="w-5 h-5 text-blue-500" />;
      default:
        return <BugAntIcon className="w-5 h-5 text-gray-500" />;
    }
  };

  const getIssueColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700';
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700';
      case 'suggestion':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700';
      default:
        return 'bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-600';
    }
  };

  const getIssueTextColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'text-red-800 dark:text-red-200';
      case 'warning':
        return 'text-yellow-800 dark:text-yellow-200';
      case 'suggestion':
        return 'text-blue-800 dark:text-blue-200';
      default:
        return 'text-gray-800 dark:text-gray-200';
    }
  };

  if (!debug) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          No debug data available
        </p>
      </div>
    );
  }

  const errorCount = debug.issues.filter(issue => issue.type === 'error').length;
  const warningCount = debug.issues.filter(issue => issue.type === 'warning').length;
  const suggestionCount = debug.issues.filter(issue => issue.type === 'suggestion').length;

  return (
    <motion.div
      className="debug-card"
      initial="hidden"
      animate="visible"
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Code Debug Analysis
            </h3>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              {debug.explanation}
            </p>
          </div>
          <div className="flex space-x-2 ml-4">
            <motion.button
              onClick={handleCopyCode}
              className="flex items-center space-x-2 px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {copied ? (
                <>
                  <CheckIcon className="w-4 h-4" />
                  <span>Copied!</span>
                </>
              ) : (
                <>
                  <DocumentDuplicateIcon className="w-4 h-4" />
                  <span>Copy</span>
                </>
              )}
            </motion.button>
            {debug.fixedCode && (
              <motion.button
                onClick={onApplyFix}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <CheckIcon className="w-4 h-4" />
                <span>Apply Fix</span>
              </motion.button>
            )}
          </div>
        </div>

        {/* Issue Summary */}
        <div className="flex items-center space-x-4 text-sm">
          {errorCount > 0 && (
            <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
              <XMarkIcon className="w-4 h-4" />
              <span>{errorCount} Error{errorCount !== 1 ? 's' : ''}</span>
            </div>
          )}
          {warningCount > 0 && (
            <div className="flex items-center space-x-1 text-yellow-600 dark:text-yellow-400">
              <ExclamationTriangleIcon className="w-4 h-4" />
              <span>{warningCount} Warning{warningCount !== 1 ? 's' : ''}</span>
            </div>
          )}
          {suggestionCount > 0 && (
            <div className="flex items-center space-x-1 text-blue-600 dark:text-blue-400">
              <LightBulbIcon className="w-4 h-4" />
              <span>{suggestionCount} Suggestion{suggestionCount !== 1 ? 's' : ''}</span>
            </div>
          )}
        </div>
      </motion.div>

      {/* Issues List */}
      <motion.div variants={itemVariants} className="mb-6">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
          Issues Found ({debug.issues.length})
        </h4>
        <div className="space-y-3">
          {debug.issues.map((issue, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`p-4 rounded-lg border ${getIssueColor(issue.type)}`}
            >
              <div className="flex items-start space-x-3">
                {getIssueIcon(issue.type)}
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={`text-sm font-medium capitalize ${getIssueTextColor(issue.type)}`}>
                      {issue.type}
                    </span>
                    {issue.line && (
                      <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                        Line {issue.line}
                      </span>
                    )}
                  </div>
                  <p className={`text-sm mb-3 ${getIssueTextColor(issue.type)}`}>
                    {issue.description}
                  </p>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                    <h6 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Solution:
                    </h6>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {issue.solution}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Code Comparison */}
      {debug.fixedCode && (
        <motion.div variants={itemVariants} className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              Code Comparison
            </h4>
            <motion.button
              onClick={() => setShowFixedCode(!showFixedCode)}
              className="flex items-center space-x-2 px-3 py-1 bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 rounded transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <EyeIcon className="w-4 h-4" />
              <span>{showFixedCode ? 'Hide Fixed Code' : 'Show Fixed Code'}</span>
            </motion.button>
          </div>

          {showFixedCode && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Original Code */}
              <div className="bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-700">
                <div className="px-3 py-2 bg-red-100 dark:bg-red-900/40 border-b border-red-200 dark:border-red-700">
                  <span className="text-sm font-medium text-red-800 dark:text-red-200">
                    Original Code (with issues)
                  </span>
                </div>
                <pre className="p-4 text-sm font-mono text-red-800 dark:text-red-200 overflow-auto max-h-64">
                  <code>{originalCode}</code>
                </pre>
              </div>

              {/* Fixed Code */}
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                <div className="px-3 py-2 bg-green-100 dark:bg-green-900/40 border-b border-green-200 dark:border-green-700">
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">
                    Fixed Code
                  </span>
                </div>
                <pre className="p-4 text-sm font-mono text-green-800 dark:text-green-200 overflow-auto max-h-64">
                  <code>{debug.fixedCode}</code>
                </pre>
              </div>
            </div>
          )}
        </motion.div>
      )}

      {/* Prevention Tips */}
      {debug.preventionTips && debug.preventionTips.length > 0 && (
        <motion.div variants={itemVariants} className="mb-6">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center space-x-2">
            <ShieldCheckIcon className="w-5 h-5 text-green-500" />
            <span>Prevention Tips</span>
          </h4>
          <div className="space-y-2">
            {debug.preventionTips.map((tip, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start space-x-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700"
              >
                <ShieldCheckIcon className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-green-800 dark:text-green-200 text-sm">
                  {tip}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Action Footer */}
      <motion.div 
        variants={itemVariants}
        className="bg-gradient-to-r from-red-50 to-green-50 dark:from-red-900/20 dark:to-green-900/20 rounded-lg p-4 border border-red-200 dark:border-red-700"
      >
        <div className="flex items-center justify-between">
          <div>
            <h5 className="font-semibold text-gray-900 dark:text-gray-100">
              {debug.fixedCode ? 'Ready to apply fixes?' : 'Issues identified'}
            </h5>
            <p className="text-gray-700 dark:text-gray-300 text-sm">
              {debug.fixedCode 
                ? 'Review the fixes above and apply them to resolve the issues'
                : 'Follow the solutions provided to fix the identified issues'
              }
            </p>
          </div>
          {debug.fixedCode && (
            <motion.button
              onClick={onApplyFix}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <CheckIcon className="w-4 h-4" />
              <span>Apply Fixes</span>
            </motion.button>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
}

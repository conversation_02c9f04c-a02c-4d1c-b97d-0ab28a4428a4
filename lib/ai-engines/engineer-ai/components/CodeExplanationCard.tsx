'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  LightBulbIcon,
  CodeBracketIcon,
  BookOpenIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  DocumentDuplicateIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { type CodeExplanationCardProps } from '../types';

export function CodeExplanationCard({ 
  explanation, 
  originalCode 
}: CodeExplanationCardProps) {
  const [expandedSection, setExpandedSection] = useState<string | null>('explanation');
  const [copied, setCopied] = useState(false);

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(originalCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  if (!explanation) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          No explanation data available
        </p>
      </div>
    );
  }

  return (
    <motion.div
      className="code-explanation-card"
      initial="hidden"
      animate="visible"
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Code Explanation
            </h3>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              Detailed analysis and explanation of your code
            </p>
          </div>
          <motion.button
            onClick={handleCopyCode}
            className="flex items-center space-x-2 px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {copied ? (
              <>
                <CheckIcon className="w-4 h-4" />
                <span>Copied!</span>
              </>
            ) : (
              <>
                <DocumentDuplicateIcon className="w-4 h-4" />
                <span>Copy Code</span>
              </>
            )}
          </motion.button>
        </div>
      </motion.div>

      {/* Main Explanation */}
      <motion.div variants={itemVariants} className="mb-6">
        <button
          onClick={() => toggleSection('explanation')}
          className="w-full flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <LightBulbIcon className="w-5 h-5 text-blue-500" />
            <span className="font-semibold text-blue-900 dark:text-blue-100">
              Overview & Explanation
            </span>
          </div>
          {expandedSection === 'explanation' ? (
            <ChevronUpIcon className="w-5 h-5 text-blue-500" />
          ) : (
            <ChevronDownIcon className="w-5 h-5 text-blue-500" />
          )}
        </button>
        
        {expandedSection === 'explanation' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 p-4 bg-white dark:bg-gray-800 rounded-lg border border-blue-200 dark:border-blue-700"
          >
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              {explanation.explanation}
            </p>
          </motion.div>
        )}
      </motion.div>

      {/* Key Points */}
      <motion.div variants={itemVariants} className="mb-6">
        <button
          onClick={() => toggleSection('keyPoints')}
          className="w-full flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <CheckIcon className="w-5 h-5 text-green-500" />
            <span className="font-semibold text-green-900 dark:text-green-100">
              Key Points ({explanation.keyPoints.length})
            </span>
          </div>
          {expandedSection === 'keyPoints' ? (
            <ChevronUpIcon className="w-5 h-5 text-green-500" />
          ) : (
            <ChevronDownIcon className="w-5 h-5 text-green-500" />
          )}
        </button>
        
        {expandedSection === 'keyPoints' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 space-y-2"
          >
            {explanation.keyPoints.map((point, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start space-x-2 p-3 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-700"
              >
                <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0">
                  {index + 1}
                </div>
                <span className="text-green-800 dark:text-green-200 text-sm">
                  {point}
                </span>
              </motion.div>
            ))}
          </motion.div>
        )}
      </motion.div>

      {/* Code Blocks Analysis */}
      {explanation.codeBlocks && explanation.codeBlocks.length > 0 && (
        <motion.div variants={itemVariants} className="mb-6">
          <button
            onClick={() => toggleSection('codeBlocks')}
            className="w-full flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <CodeBracketIcon className="w-5 h-5 text-purple-500" />
              <span className="font-semibold text-purple-900 dark:text-purple-100">
                Code Block Analysis ({explanation.codeBlocks.length})
              </span>
            </div>
            {expandedSection === 'codeBlocks' ? (
              <ChevronUpIcon className="w-5 h-5 text-purple-500" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-purple-500" />
            )}
          </button>
          
          {expandedSection === 'codeBlocks' && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3 space-y-4"
            >
              {explanation.codeBlocks.map((block, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700 overflow-hidden"
                >
                  <div className="px-3 py-2 bg-purple-100 dark:bg-purple-900/40 border-b border-purple-200 dark:border-purple-700">
                    <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
                      {block.lineStart && block.lineEnd 
                        ? `Lines ${block.lineStart}-${block.lineEnd}`
                        : `Code Block ${index + 1}`
                      }
                    </span>
                  </div>
                  <div className="p-3">
                    <pre className="text-sm font-mono text-purple-800 dark:text-purple-200 mb-3 bg-white dark:bg-gray-800 p-2 rounded overflow-x-auto">
                      <code>{block.code}</code>
                    </pre>
                    <p className="text-sm text-purple-700 dark:text-purple-300">
                      {block.explanation}
                    </p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Concepts */}
      <motion.div variants={itemVariants} className="mb-6">
        <button
          onClick={() => toggleSection('concepts')}
          className="w-full flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <BookOpenIcon className="w-5 h-5 text-yellow-500" />
            <span className="font-semibold text-yellow-900 dark:text-yellow-100">
              Programming Concepts ({explanation.concepts.length})
            </span>
          </div>
          {expandedSection === 'concepts' ? (
            <ChevronUpIcon className="w-5 h-5 text-yellow-500" />
          ) : (
            <ChevronDownIcon className="w-5 h-5 text-yellow-500" />
          )}
        </button>
        
        {expandedSection === 'concepts' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3"
          >
            <div className="flex flex-wrap gap-2">
              {explanation.concepts.map((concept, index) => (
                <motion.span
                  key={index}
                  variants={itemVariants}
                  className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 rounded-full text-sm font-medium"
                >
                  {concept}
                </motion.span>
              ))}
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Improvements */}
      {explanation.improvements && explanation.improvements.length > 0 && (
        <motion.div variants={itemVariants} className="mb-6">
          <button
            onClick={() => toggleSection('improvements')}
            className="w-full flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <LightBulbIcon className="w-5 h-5 text-orange-500" />
              <span className="font-semibold text-orange-900 dark:text-orange-100">
                Suggested Improvements ({explanation.improvements.length})
              </span>
            </div>
            {expandedSection === 'improvements' ? (
              <ChevronUpIcon className="w-5 h-5 text-orange-500" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-orange-500" />
            )}
          </button>
          
          {expandedSection === 'improvements' && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3 space-y-2"
            >
              {explanation.improvements.map((improvement, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="flex items-start space-x-2 p-3 bg-orange-50 dark:bg-orange-900/20 rounded border border-orange-200 dark:border-orange-700"
                >
                  <LightBulbIcon className="w-4 h-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span className="text-orange-800 dark:text-orange-200 text-sm">
                    {improvement}
                  </span>
                </motion.div>
              ))}
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Action Footer */}
      <motion.div 
        variants={itemVariants}
        className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700"
      >
        <div className="text-center">
          <h5 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Understanding Your Code
          </h5>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            Use this explanation to better understand how your code works and how to improve it
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
}

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { type EngineerToolInvocation } from '../types';
import { PrototypeCard } from './PrototypeCard';
import { CodeModificationCard } from './CodeModificationCard';
import { CodeExplanationCard } from './CodeExplanationCard';
import { DebugCard } from './DebugCard';
import { ToolLoadingState } from './ToolLoadingState';
import { ToolErrorState } from './ToolErrorState';

interface EngineerToolRendererProps {
  toolInvocation: EngineerToolInvocation;
  className?: string;
}

export function EngineerToolRenderer({ 
  toolInvocation, 
  className = '' 
}: EngineerToolRendererProps) {
  const { toolName, state, args, result } = toolInvocation;

  // Animation variants for smooth transitions
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    exit: { 
      opacity: 0, 
      y: -20,
      transition: {
        duration: 0.2,
        ease: 'easeIn'
      }
    }
  };

  // Render based on tool state
  const renderToolContent = () => {
    switch (state) {
      case 'partial-call':
        return (
          <ToolLoadingState 
            toolName={toolName}
            message="Preparing tool call..."
            showPartialArgs={args}
          />
        );

      case 'call':
        return (
          <ToolLoadingState 
            toolName={toolName}
            message={getLoadingMessage(toolName)}
            args={args}
          />
        );

      case 'result':
        if (result?.error) {
          return (
            <ToolErrorState 
              toolName={toolName}
              error={result.error}
              args={args}
            />
          );
        }

        return renderToolResult();

      default:
        return (
          <ToolErrorState 
            toolName={toolName}
            error="Unknown tool state"
            args={args}
          />
        );
    }
  };

  // Render the actual tool result based on tool type
  const renderToolResult = () => {
    switch (toolName) {
      case 'generatePrototype':
        return (
          <PrototypeCard 
            prototype={result}
            onRunCode={() => console.log('Run code')}
            onEditCode={() => console.log('Edit code')}
          />
        );

      case 'modifyCode':
        return (
          <CodeModificationCard 
            modification={result}
            originalCode={args?.fileContent || ''}
            onApplyChanges={() => console.log('Apply changes')}
          />
        );

      case 'explainCode':
        return (
          <CodeExplanationCard 
            explanation={result}
            originalCode={args?.code || ''}
          />
        );

      case 'debugCode':
        return (
          <DebugCard 
            debug={result}
            originalCode={args?.code || ''}
            onApplyFix={() => console.log('Apply fix')}
          />
        );

      default:
        return (
          <ToolErrorState 
            toolName={toolName}
            error="Unknown tool type"
            args={args}
          />
        );
    }
  };

  // Get appropriate loading message for each tool
  const getLoadingMessage = (toolName: string): string => {
    switch (toolName) {
      case 'generatePrototype':
        return 'Generating code prototype...';
      case 'modifyCode':
        return 'Modifying code...';
      case 'explainCode':
        return 'Analyzing code...';
      case 'debugCode':
        return 'Debugging code...';
      default:
        return 'Processing request...';
    }
  };

  return (
    <motion.div
      className={`engineer-tool-renderer ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      layout
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
        {/* Tool Header */}
        <div className="px-4 py-3 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {getToolDisplayName(toolName)}
              </span>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 capitalize">
              {state.replace('-', ' ')}
            </div>
          </div>
        </div>

        {/* Tool Content */}
        <div className="p-4">
          {renderToolContent()}
        </div>
      </div>
    </motion.div>
  );
}

// Helper function to get display names for tools
function getToolDisplayName(toolName: string): string {
  switch (toolName) {
    case 'generatePrototype':
      return 'Code Prototype';
    case 'modifyCode':
      return 'Code Modification';
    case 'explainCode':
      return 'Code Explanation';
    case 'debugCode':
      return 'Code Debugging';
    default:
      return toolName;
  }
}

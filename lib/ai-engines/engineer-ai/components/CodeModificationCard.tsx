'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckIcon,
  XMarkIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  ArrowPathIcon,
  PlusIcon,
  MinusIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import { type CodeModificationCardProps } from '../types';

export function CodeModificationCard({ 
  modification, 
  originalCode, 
  onApplyChanges 
}: CodeModificationCardProps) {
  const [showDiff, setShowDiff] = useState(true);
  const [copied, setCopied] = useState(false);

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(modification.modifiedCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const getChangeIcon = (type: string) => {
    switch (type) {
      case 'added':
        return <PlusIcon className="w-4 h-4 text-green-500" />;
      case 'removed':
        return <MinusIcon className="w-4 h-4 text-red-500" />;
      case 'modified':
        return <PencilIcon className="w-4 h-4 text-blue-500" />;
      default:
        return <PencilIcon className="w-4 h-4 text-gray-500" />;
    }
  };

  const getChangeColor = (type: string) => {
    switch (type) {
      case 'added':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700';
      case 'removed':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700';
      case 'modified':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700';
      default:
        return 'bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-600';
    }
  };

  if (!modification) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          No modification data available
        </p>
      </div>
    );
  }

  return (
    <motion.div
      className="code-modification-card"
      initial="hidden"
      animate="visible"
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Code Modification Complete
            </h3>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              {modification.explanation}
            </p>
          </div>
          <div className="flex space-x-2 ml-4">
            <motion.button
              onClick={handleCopyCode}
              className="flex items-center space-x-2 px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {copied ? (
                <>
                  <CheckIcon className="w-4 h-4" />
                  <span>Copied!</span>
                </>
              ) : (
                <>
                  <DocumentDuplicateIcon className="w-4 h-4" />
                  <span>Copy</span>
                </>
              )}
            </motion.button>
            <motion.button
              onClick={onApplyChanges}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <CheckIcon className="w-4 h-4" />
              <span>Apply</span>
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Changes Summary */}
      <motion.div variants={itemVariants} className="mb-6">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
          Changes Made ({modification.changes.length})
        </h4>
        <div className="space-y-2">
          {modification.changes.map((change, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`p-3 rounded-lg border ${getChangeColor(change.type)}`}
            >
              <div className="flex items-start space-x-3">
                {getChangeIcon(change.type)}
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-sm font-medium capitalize">
                      {change.type}
                    </span>
                    {change.lineNumber && (
                      <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                        Line {change.lineNumber}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {change.description}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Code Diff View */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Code Changes
          </h4>
          <motion.button
            onClick={() => setShowDiff(!showDiff)}
            className="flex items-center space-x-2 px-3 py-1 bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 rounded transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <EyeIcon className="w-4 h-4" />
            <span>{showDiff ? 'Hide Diff' : 'Show Diff'}</span>
          </motion.button>
        </div>

        {showDiff && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Original Code */}
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-700">
              <div className="px-3 py-2 bg-red-100 dark:bg-red-900/40 border-b border-red-200 dark:border-red-700">
                <span className="text-sm font-medium text-red-800 dark:text-red-200">
                  Original Code
                </span>
              </div>
              <pre className="p-4 text-sm font-mono text-red-800 dark:text-red-200 overflow-auto max-h-64">
                <code>{originalCode}</code>
              </pre>
            </div>

            {/* Modified Code */}
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
              <div className="px-3 py-2 bg-green-100 dark:bg-green-900/40 border-b border-green-200 dark:border-green-700">
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  Modified Code
                </span>
              </div>
              <pre className="p-4 text-sm font-mono text-green-800 dark:text-green-200 overflow-auto max-h-64">
                <code>{modification.modifiedCode}</code>
              </pre>
            </div>
          </div>
        )}
      </motion.div>

      {/* Suggestions */}
      {modification.suggestions && modification.suggestions.length > 0 && (
        <motion.div variants={itemVariants} className="mb-6">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
            Additional Suggestions
          </h4>
          <div className="space-y-2">
            {modification.suggestions.map((suggestion, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start space-x-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700"
              >
                <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-yellow-800 dark:text-yellow-200 text-sm">
                  {suggestion}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Action Footer */}
      <motion.div 
        variants={itemVariants}
        className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700"
      >
        <div className="flex items-center justify-between">
          <div>
            <h5 className="font-semibold text-blue-900 dark:text-blue-100">
              Ready to apply changes?
            </h5>
            <p className="text-blue-700 dark:text-blue-300 text-sm">
              Review the modifications above and apply them to your code
            </p>
          </div>
          <div className="flex space-x-2">
            <motion.button
              onClick={() => console.log('Reject changes')}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <XMarkIcon className="w-4 h-4" />
              <span>Reject</span>
            </motion.button>
            <motion.button
              onClick={onApplyChanges}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <CheckIcon className="w-4 h-4" />
              <span>Apply Changes</span>
            </motion.button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}

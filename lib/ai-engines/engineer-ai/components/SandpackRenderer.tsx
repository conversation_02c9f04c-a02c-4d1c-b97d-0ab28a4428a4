'use client';

import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  RotateCw,
  Code2,
  Eye,
  Copy,
  Check,
  AlertTriangle,
  Cog
} from 'lucide-react';
import { type SandpackRendererProps, type ProjectStructure } from '../types';

// Production-grade Sandpack integration
// Install: npm install @codesandbox/sandpack-react
// Note: These imports will work when @codesandbox/sandpack-react is installed
// For now, we'll provide type-safe fallbacks
type SandpackFilesType = Record<string, string | { code: string; hidden?: boolean; active?: boolean; readOnly?: boolean }>;
type SandpackPredefinedTemplate = string;
type SandpackTheme = 'light' | 'dark' | string;

// Mock Sandpack components for development (replace with real imports when package is installed)
const SandpackProvider = ({ children, ...props }: any) => <div {...props}>{children}</div>;
const SandpackLayout = ({ children }: any) => <div className="sandpack-layout">{children}</div>;
const SandpackCodeEditor = (props: any) => (
  <div className="sandpack-editor" style={props.style}>
    <div className="p-4 bg-gray-50 dark:bg-gray-900 text-sm font-mono">
      Code editor will appear here when Sandpack is installed
    </div>
  </div>
);
const SandpackPreview = (props: any) => (
  <div className="sandpack-preview" style={props.style}>
    <div className="p-4 bg-white dark:bg-gray-800 flex items-center justify-center">
      Live preview will appear here when Sandpack is installed
    </div>
  </div>
);
const SandpackFileExplorer = () => <div className="sandpack-file-explorer">File explorer</div>;
const SandpackConsole = (props: any) => (
  <div className="sandpack-console" style={props.style}>
    <div className="p-2 bg-black text-green-400 text-xs font-mono">
      Console output will appear here when Sandpack is installed
    </div>
  </div>
);
const useSandpack = () => ({
  sandpack: {
    listen: (_callback: (message: { type: string; message?: string }) => void) => {
      // Mock listener - replace with real Sandpack when installed
      return () => {}; // Unsubscribe function
    }
  }
});

interface SandpackRendererState {
  isRunning: boolean;
  error: string | null;
  isLoading: boolean;
  showPreview: boolean;
  showConsole: boolean;
  activeFile: string;
}

export function SandpackRenderer({
  project,
  height = 500,
  theme = 'light',
  showTabs = true,
  showLineNumbers = true,
  showInlineErrors = true,
  showConsole = false,
  autorun = true,
  className = ''
}: SandpackRendererProps) {
  const [state, setState] = useState<SandpackRendererState>({
    isRunning: autorun,
    error: null,
    isLoading: false,
    showPreview: true,
    showConsole,
    activeFile: getDefaultActiveFile(project)
  });
  const [copied, setCopied] = useState(false);
  const errorTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Memoize Sandpack files conversion for performance
  const sandpackFiles = useMemo((): SandpackFilesType => {
    const files: SandpackFilesType = {};

    Object.entries(project.files).forEach(([path, content]) => {
      if (typeof content === 'string') {
        files[path] = content;
      } else {
        files[path] = {
          code: content.code || '',
          hidden: content.hidden,
          active: content.active,
          readOnly: content.readOnly,
        };
      }
    });

    return files;
  }, [project.files]);

  // Memoize template and dependencies
  const sandpackTemplate = useMemo((): SandpackPredefinedTemplate => {
    return project.template as SandpackPredefinedTemplate;
  }, [project.template]);

  const customSetup = useMemo(() => ({
    dependencies: project.dependencies || {},
    devDependencies: project.devDependencies || {},
    entry: project.entry || '/index.js',
  }), [project.dependencies, project.devDependencies, project.entry]);

  // Error handling with debouncing
  const handleError = useCallback((error: Error) => {
    setState(prev => ({ ...prev, error: error.message, isLoading: false }));

    // Clear error after 10 seconds
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
    }
    errorTimeoutRef.current = setTimeout(() => {
      setState(prev => ({ ...prev, error: null }));
    }, 10000);
  }, []);

  // Optimized copy handler with error handling
  const handleCopyCode = useCallback(async () => {
    try {
      const currentFileContent = typeof project.files[state.activeFile] === 'string'
        ? project.files[state.activeFile] as string
        : (project.files[state.activeFile] as any)?.code || '';

      await navigator.clipboard.writeText(currentFileContent);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
      handleError(new Error('Failed to copy code to clipboard'));
    }
  }, [project.files, state.activeFile, handleError]);

  // Refresh handler with loading state
  const handleRefresh = useCallback(() => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    // Simulate refresh delay
    setTimeout(() => {
      setState(prev => ({ ...prev, isLoading: false }));
    }, 500);
  }, []);

  // Toggle handlers
  const togglePreview = useCallback(() => {
    setState(prev => ({ ...prev, showPreview: !prev.showPreview }));
  }, []);

  const toggleConsole = useCallback(() => {
    setState(prev => ({ ...prev, showConsole: !prev.showConsole }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
      }
    };
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  };

  // Error boundary fallback
  if (state.error) {
    return (
      <motion.div
        className={`sandpack-renderer-error ${className}`}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="border border-red-200 dark:border-red-700 rounded-lg bg-red-50 dark:bg-red-900/20 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle className="w-6 h-6 text-red-500" />
            <h3 className="text-lg font-semibold text-red-900 dark:text-red-100">
              Sandpack Error
            </h3>
          </div>
          <p className="text-red-800 dark:text-red-200 mb-4">{state.error}</p>
          <motion.button
            onClick={handleRefresh}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <RotateCw className="w-4 h-4" />
            <span>Retry</span>
          </motion.button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className={`sandpack-renderer ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800">
        {/* Header Controls */}
        <div className="flex items-center justify-between px-4 py-2 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center space-x-2">
            <motion.button
              onClick={handleRefresh}
              disabled={state.isLoading}
              className="flex items-center space-x-1 px-3 py-1 rounded text-sm font-medium bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 transition-colors disabled:opacity-50"
              whileHover={{ scale: state.isLoading ? 1 : 1.05 }}
              whileTap={{ scale: state.isLoading ? 1 : 0.95 }}
            >
              {state.isLoading ? (
                <Cog className="w-4 h-4 animate-spin" />
              ) : (
                <RotateCw className="w-4 h-4" />
              )}
              <span>{state.isLoading ? 'Loading...' : 'Refresh'}</span>
            </motion.button>
          </div>

          <div className="flex items-center space-x-2">
            <motion.button
              onClick={togglePreview}
              className={`flex items-center space-x-1 px-3 py-1 rounded text-sm font-medium transition-colors ${
                state.showPreview
                  ? 'bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900/30 dark:text-purple-300'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-600 dark:text-gray-300'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {state.showPreview ? <Eye className="w-4 h-4" /> : <Code2 className="w-4 h-4" />}
              <span>{state.showPreview ? 'Preview' : 'Code'}</span>
            </motion.button>

            <motion.button
              onClick={toggleConsole}
              className={`flex items-center space-x-1 px-3 py-1 rounded text-sm font-medium transition-colors ${
                state.showConsole
                  ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-600 dark:text-gray-300'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Console</span>
            </motion.button>

            <motion.button
              onClick={handleCopyCode}
              disabled={copied}
              className="flex items-center space-x-1 px-3 py-1 rounded text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-600 dark:text-gray-300 transition-colors disabled:opacity-50"
              whileHover={{ scale: copied ? 1 : 1.05 }}
              whileTap={{ scale: copied ? 1 : 0.95 }}
            >
              {copied ? (
                <>
                  <Check className="w-4 h-4 text-green-500" />
                  <span>Copied!</span>
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4" />
                  <span>Copy</span>
                </>
              )}
            </motion.button>
          </div>
        </div>

        {/* Production Sandpack Integration */}
        <div style={{ height }}>
          <SandpackProvider
            template={sandpackTemplate}
            files={sandpackFiles}
            customSetup={customSetup}
            theme={theme as SandpackTheme}
            options={{
              showNavigator: showTabs,
              showLineNumbers,
              showInlineErrors,
              autorun,
              autoReload: true,
              bundlerURL: undefined, // Use default
              startRoute: project.entry,
              skipEval: false,
              logLevel: 'info',
              classes: {
                'sp-wrapper': 'sandpack-wrapper',
                'sp-layout': 'sandpack-layout',
                'sp-tab-button': 'sandpack-tab',
              }
            }}
          >
            <SandpackLayout>
              {/* File Explorer (if multiple files) */}
              {showTabs && Object.keys(sandpackFiles).length > 1 && (
                <SandpackFileExplorer />
              )}

              {/* Code Editor */}
              <SandpackCodeEditor
                showTabs={showTabs}
                showLineNumbers={showLineNumbers}
                showInlineErrors={showInlineErrors}
                wrapContent
                closableTabs
                style={{
                  height: state.showConsole ? `${height - 150}px` : `${height}px`,
                }}
              />

              {/* Preview Panel */}
              {state.showPreview && (
                <SandpackPreview
                  showNavigator={false}
                  showRefreshButton={false}
                  showOpenInCodeSandbox={false}
                  style={{
                    height: state.showConsole ? `${height - 150}px` : `${height}px`,
                  }}
                />
              )}
            </SandpackLayout>

            {/* Console Panel */}
            {state.showConsole && (
              <div className="border-t border-gray-200 dark:border-gray-600">
                <SandpackConsole
                  style={{
                    height: '150px',
                    backgroundColor: theme === 'dark' ? '#1a1a1a' : '#ffffff',
                  }}
                />
              </div>
            )}

            {/* Custom Error Handler */}
            <SandpackErrorHandler onError={handleError} />
          </SandpackProvider>
        </div>

        {/* Footer Info */}
        <div className="px-4 py-2 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex justify-between items-center">
            <span>Template: {project.template}</span>
            <span>{Object.keys(sandpackFiles).length} files</span>
            <span>Dependencies: {Object.keys(project.dependencies || {}).length}</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

// Custom Error Handler Component
function SandpackErrorHandler({ onError }: { onError: (error: Error) => void }) {
  const { sandpack } = useSandpack();

  useEffect(() => {
    const unsubscribe = sandpack.listen((message) => {
      if (message.type === 'error') {
        onError(new Error(message.message || 'Sandpack execution error'));
      }
    });

    return unsubscribe;
  }, [sandpack, onError]);

  return null;
}

// Helper functions with error handling
function getDefaultActiveFile(project: ProjectStructure): string {
  try {
    const files = Object.keys(project.files);

    if (files.length === 0) {
      console.warn('No files found in project');
      return '';
    }

    // Prefer main files in order of priority
    const mainFiles = ['/App.tsx', '/App.vue', '/index.tsx', '/main.ts', '/script.ts', '/index.js'];
    for (const mainFile of mainFiles) {
      if (files.includes(mainFile)) {
        return mainFile;
      }
    }

    // Return first file if no main file found
    return files[0];
  } catch (error) {
    console.error('Error getting default active file:', error);
    return '';
  }
}

function getFileName(filePath: string): string {
  try {
    return filePath.split('/').pop() || filePath;
  } catch (error) {
    console.error('Error getting file name:', error);
    return filePath;
  }
}

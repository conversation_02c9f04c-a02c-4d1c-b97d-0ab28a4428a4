'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  CogIcon,
  CodeBracketIcon,
  WrenchScrewdriverIcon,
  LightBulbIcon,
  BugAntIcon
} from '@heroicons/react/24/outline';

interface ToolLoadingStateProps {
  toolName: string;
  message: string;
  args?: any;
  showPartialArgs?: any;
}

export function ToolLoadingState({ 
  toolName, 
  message, 
  args, 
  showPartialArgs 
}: ToolLoadingStateProps) {
  const getToolIcon = (toolName: string) => {
    switch (toolName) {
      case 'generatePrototype':
        return <CodeBracketIcon className="w-6 h-6 text-green-500" />;
      case 'modifyCode':
        return <WrenchScrewdriverIcon className="w-6 h-6 text-blue-500" />;
      case 'explainCode':
        return <LightBulbIcon className="w-6 h-6 text-yellow-500" />;
      case 'debugCode':
        return <BugAntIcon className="w-6 h-6 text-red-500" />;
      default:
        return <CogIcon className="w-6 h-6 text-gray-500" />;
    }
  };

  const getLoadingSteps = (toolName: string) => {
    switch (toolName) {
      case 'generatePrototype':
        return [
          'Analyzing requirements...',
          'Setting up project structure...',
          'Generating code files...',
          'Configuring dependencies...',
          'Finalizing prototype...'
        ];
      case 'modifyCode':
        return [
          'Parsing existing code...',
          'Applying modifications...',
          'Validating changes...',
          'Optimizing code...'
        ];
      case 'explainCode':
        return [
          'Analyzing code structure...',
          'Identifying patterns...',
          'Generating explanations...',
          'Creating examples...'
        ];
      case 'debugCode':
        return [
          'Scanning for issues...',
          'Analyzing error patterns...',
          'Generating fixes...',
          'Validating solutions...'
        ];
      default:
        return ['Processing...'];
    }
  };

  const spinVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "linear"
      }
    }
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.1, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const dotsVariants = {
    animate: {
      transition: {
        staggerChildren: 0.2,
        repeat: Infinity,
        repeatType: "loop" as const
      }
    }
  };

  const dotVariants = {
    animate: {
      y: [0, -10, 0],
      transition: {
        duration: 0.6,
        ease: "easeInOut"
      }
    }
  };

  const stepsVariants = {
    animate: {
      transition: {
        staggerChildren: 0.3,
        repeat: Infinity,
        repeatType: "loop" as const
      }
    }
  };

  const stepVariants = {
    animate: {
      opacity: [0.3, 1, 0.3],
      transition: {
        duration: 2,
        ease: "easeInOut"
      }
    }
  };

  const loadingSteps = getLoadingSteps(toolName);

  return (
    <div className="tool-loading-state">
      {/* Loading Header */}
      <div className="flex items-center space-x-3 mb-6">
        <motion.div variants={pulseVariants} animate="animate">
          {getToolIcon(toolName)}
        </motion.div>
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <span className="text-gray-700 dark:text-gray-300 font-medium">
              {message}
            </span>
            <motion.div
              className="flex space-x-1"
              variants={dotsVariants}
              animate="animate"
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-1 h-1 bg-green-500 rounded-full"
                  variants={dotVariants}
                />
              ))}
            </motion.div>
          </div>
        </div>
        <motion.div variants={spinVariants} animate="animate">
          <CogIcon className="w-5 h-5 text-gray-400" />
        </motion.div>
      </div>

      {/* Loading Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <motion.div
            className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>
      </div>

      {/* Loading Steps */}
      <div className="mb-6">
        <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Processing Steps:
        </h5>
        <motion.div 
          className="space-y-2"
          variants={stepsVariants}
          animate="animate"
        >
          {loadingSteps.map((step, index) => (
            <motion.div
              key={index}
              variants={stepVariants}
              className="flex items-center space-x-2 text-sm"
            >
              <div className="w-2 h-2 bg-blue-500 rounded-full" />
              <span className="text-gray-600 dark:text-gray-400">{step}</span>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Tool Arguments Display */}
      {(args || showPartialArgs) && (
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-4">
          <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {showPartialArgs ? 'Partial Parameters:' : 'Parameters:'}
          </h5>
          <div className="space-y-2">
            {Object.entries(args || showPartialArgs || {}).map(([key, value]) => (
              <div key={key} className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}:
                </span>
                <span className="text-gray-800 dark:text-gray-200 font-mono max-w-xs truncate">
                  {typeof value === 'boolean' 
                    ? value.toString() 
                    : typeof value === 'object'
                    ? JSON.stringify(value)
                    : String(value)
                  }
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Code Generation Preview */}
      {toolName === 'generatePrototype' && (
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-green-300">Generating code...</span>
          </div>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
          >
            <div className="text-gray-500">// Setting up project structure</div>
            <div className="text-blue-400">import React from 'react';</div>
            <div className="text-yellow-400">export default function App() {`{`}</div>
            <div className="text-gray-500 ml-4">// Your code will appear here...</div>
            <div className="text-yellow-400">{`}`}</div>
          </motion.div>
        </div>
      )}

      {/* Status Indicator */}
      <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
        <motion.div
          className="w-2 h-2 bg-green-500 rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <span>AI is working on your request...</span>
      </div>
    </div>
  );
}

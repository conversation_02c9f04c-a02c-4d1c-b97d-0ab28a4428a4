'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  PlayIcon,
  PencilIcon,
  LightBulbIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { type PrototypeCardProps } from '../types';
import { SandpackRenderer } from './SandpackRenderer';

export function PrototypeCard({ 
  prototype, 
  onRunCode, 
  onEditCode 
}: PrototypeCardProps) {
  const [showCode, setShowCode] = useState(true);
  const [expandedSection, setExpandedSection] = useState<string | null>('features');

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  if (!prototype) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          No prototype data available
        </p>
      </div>
    );
  }

  return (
    <motion.div
      className="prototype-card"
      initial="hidden"
      animate="visible"
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
    >
      {/* Prototype Header */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Code Prototype Generated
            </h3>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              {prototype.description}
            </p>
          </div>
          <div className="flex space-x-2 ml-4">
            <motion.button
              onClick={onRunCode}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <PlayIcon className="w-4 h-4" />
              <span>Run</span>
            </motion.button>
            <motion.button
              onClick={onEditCode}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <PencilIcon className="w-4 h-4" />
              <span>Edit</span>
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Sandpack Code Renderer */}
      {showCode && (
        <motion.div variants={itemVariants} className="mb-6">
          <SandpackRenderer 
            project={prototype.project}
            height={500}
            showTabs={true}
            showLineNumbers={true}
            showConsole={false}
            autorun={true}
          />
        </motion.div>
      )}

      {/* Features Section */}
      <motion.div variants={itemVariants} className="mb-6">
        <button
          onClick={() => toggleSection('features')}
          className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <CheckCircleIcon className="w-5 h-5 text-green-500" />
            <span className="font-semibold text-gray-800 dark:text-gray-200">
              Features ({prototype.features.length})
            </span>
          </div>
          {expandedSection === 'features' ? (
            <ChevronUpIcon className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronDownIcon className="w-5 h-5 text-gray-500" />
          )}
        </button>
        
        {expandedSection === 'features' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 space-y-2"
          >
            {prototype.features.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start space-x-2 p-2"
              >
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">
                  {feature}
                </span>
              </motion.div>
            ))}
          </motion.div>
        )}
      </motion.div>

      {/* Instructions Section */}
      <motion.div variants={itemVariants} className="mb-6">
        <button
          onClick={() => toggleSection('instructions')}
          className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <PlayIcon className="w-5 h-5 text-blue-500" />
            <span className="font-semibold text-gray-800 dark:text-gray-200">
              How to Use
            </span>
          </div>
          {expandedSection === 'instructions' ? (
            <ChevronUpIcon className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronDownIcon className="w-5 h-5 text-gray-500" />
          )}
        </button>
        
        {expandedSection === 'instructions' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 space-y-2"
          >
            {prototype.instructions.map((instruction, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start space-x-3 p-2"
              >
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0">
                  {index + 1}
                </div>
                <span className="text-gray-700 dark:text-gray-300 text-sm">
                  {instruction}
                </span>
              </motion.div>
            ))}
          </motion.div>
        )}
      </motion.div>

      {/* Tips Section */}
      <motion.div variants={itemVariants} className="mb-6">
        <button
          onClick={() => toggleSection('tips')}
          className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <LightBulbIcon className="w-5 h-5 text-yellow-500" />
            <span className="font-semibold text-gray-800 dark:text-gray-200">
              Tips & Tricks
            </span>
          </div>
          {expandedSection === 'tips' ? (
            <ChevronUpIcon className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronDownIcon className="w-5 h-5 text-gray-500" />
          )}
        </button>
        
        {expandedSection === 'tips' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 space-y-2"
          >
            {prototype.tips.map((tip, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start space-x-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded"
              >
                <LightBulbIcon className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                <span className="text-yellow-800 dark:text-yellow-200 text-sm">
                  {tip}
                </span>
              </motion.div>
            ))}
          </motion.div>
        )}
      </motion.div>

      {/* Next Steps Section */}
      <motion.div variants={itemVariants}>
        <button
          onClick={() => toggleSection('nextSteps')}
          className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <ArrowRightIcon className="w-5 h-5 text-purple-500" />
            <span className="font-semibold text-gray-800 dark:text-gray-200">
              Next Steps
            </span>
          </div>
          {expandedSection === 'nextSteps' ? (
            <ChevronUpIcon className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronDownIcon className="w-5 h-5 text-gray-500" />
          )}
        </button>
        
        {expandedSection === 'nextSteps' && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 space-y-2"
          >
            {prototype.nextSteps.map((step, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start space-x-2 p-2"
              >
                <ArrowRightIcon className="w-4 h-4 text-purple-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">
                  {step}
                </span>
              </motion.div>
            ))}
          </motion.div>
        )}
      </motion.div>

      {/* Action Footer */}
      <motion.div 
        variants={itemVariants}
        className="mt-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700"
      >
        <div className="flex items-center justify-between">
          <div>
            <h5 className="font-semibold text-green-900 dark:text-green-100">
              Ready to customize?
            </h5>
            <p className="text-green-700 dark:text-green-300 text-sm">
              Modify the code above or ask for specific changes
            </p>
          </div>
          <motion.button
            onClick={() => setShowCode(!showCode)}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium text-sm transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {showCode ? 'Hide Code' : 'Show Code'}
          </motion.button>
        </div>
      </motion.div>
    </motion.div>
  );
}

// Validation utilities for Engineer AI
import { z } from 'zod';
import { 
  generatePrototypeSchema,
  modifyCodeSchema,
  explainCodeSchema,
  debugCodeSchema,
  type Framework,
  type ProjectType,
  type Complexity,
  type UILibrary
} from '../types';

/**
 * Validate generate prototype parameters
 */
export function validateGeneratePrototypeParams(params: unknown): {
  isValid: boolean;
  data?: z.infer<typeof generatePrototypeSchema>;
  errors?: string[];
  warnings?: string[];
} {
  try {
    const data = generatePrototypeSchema.parse(params);
    const warnings: string[] = [];

    // Additional validation and warnings
    if (data.description.length < 20) {
      warnings.push('Consider providing a more detailed description for better results');
    }

    if (data.complexity === 'advanced' && data.framework === 'vanilla') {
      warnings.push('Advanced complexity with vanilla JavaScript may be challenging');
    }

    if (data.features && data.features.length > 10) {
      warnings.push('Too many features may result in a complex prototype');
    }

    return { 
      isValid: true, 
      data, 
      warnings: warnings.length > 0 ? warnings : undefined 
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      isValid: false,
      errors: ['Invalid parameters']
    };
  }
}

/**
 * Validate modify code parameters
 */
export function validateModifyCodeParams(params: unknown): {
  isValid: boolean;
  data?: z.infer<typeof modifyCodeSchema>;
  errors?: string[];
  warnings?: string[];
} {
  try {
    const data = modifyCodeSchema.parse(params);
    const warnings: string[] = [];

    // Check if file content is not empty
    if (!data.fileContent.trim()) {
      return {
        isValid: false,
        errors: ['File content cannot be empty']
      };
    }

    // Check modification description
    if (data.modification.length < 10) {
      warnings.push('Consider providing a more detailed modification description');
    }

    // Check file size
    if (data.fileContent.length > 10000) {
      warnings.push('Large files may take longer to process');
    }

    return { 
      isValid: true, 
      data, 
      warnings: warnings.length > 0 ? warnings : undefined 
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      isValid: false,
      errors: ['Invalid parameters']
    };
  }
}

/**
 * Validate explain code parameters
 */
export function validateExplainCodeParams(params: unknown): {
  isValid: boolean;
  data?: z.infer<typeof explainCodeSchema>;
  errors?: string[];
  warnings?: string[];
} {
  try {
    const data = explainCodeSchema.parse(params);
    const warnings: string[] = [];

    // Check if code is not empty
    if (!data.code.trim()) {
      return {
        isValid: false,
        errors: ['Code cannot be empty']
      };
    }

    // Check code length
    if (data.code.length < 10) {
      warnings.push('Very short code snippets may not provide detailed explanations');
    }

    if (data.code.length > 5000) {
      warnings.push('Large code blocks may be truncated in explanations');
    }

    return { 
      isValid: true, 
      data, 
      warnings: warnings.length > 0 ? warnings : undefined 
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      isValid: false,
      errors: ['Invalid parameters']
    };
  }
}

/**
 * Validate debug code parameters
 */
export function validateDebugCodeParams(params: unknown): {
  isValid: boolean;
  data?: z.infer<typeof debugCodeSchema>;
  errors?: string[];
  warnings?: string[];
} {
  try {
    const data = debugCodeSchema.parse(params);
    const warnings: string[] = [];

    // Check if code is not empty
    if (!data.code.trim()) {
      return {
        isValid: false,
        errors: ['Code cannot be empty']
      };
    }

    // Check expected behavior
    if (data.expectedBehavior.length < 10) {
      warnings.push('More detailed expected behavior description will help with debugging');
    }

    // Check if error message is provided
    if (!data.errorMessage) {
      warnings.push('Providing an error message will help identify specific issues');
    }

    return { 
      isValid: true, 
      data, 
      warnings: warnings.length > 0 ? warnings : undefined 
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      isValid: false,
      errors: ['Invalid parameters']
    };
  }
}

/**
 * Validate framework and complexity combination
 */
export function validateFrameworkComplexity(
  framework: Framework,
  complexity: Complexity,
  projectType: ProjectType
): {
  isValid: boolean;
  warnings?: string[];
} {
  const warnings: string[] = [];

  // Check framework-complexity combinations
  if (framework === 'vanilla' && complexity === 'advanced') {
    warnings.push('Advanced complexity with vanilla JavaScript may be challenging to implement');
  }

  if (framework === 'angular' && complexity === 'simple') {
    warnings.push('Angular is typically used for more complex applications');
  }

  // Check project type compatibility
  if (projectType === 'app' && complexity === 'simple') {
    warnings.push('Applications typically benefit from intermediate or advanced complexity');
  }

  if (projectType === 'component' && complexity === 'advanced') {
    warnings.push('Components are usually simpler - consider intermediate complexity');
  }

  return {
    isValid: true, // These are warnings, not errors
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Validate UI library compatibility
 */
export function validateUILibraryCompatibility(
  framework: Framework,
  uiLibrary: UILibrary
): {
  isValid: boolean;
  warnings?: string[];
} {
  const warnings: string[] = [];

  // Check framework-UI library compatibility
  const incompatibleCombinations = [
    { framework: 'vue', uiLibrary: 'material-ui' },
    { framework: 'vue', uiLibrary: 'chakra-ui' },
    { framework: 'angular', uiLibrary: 'material-ui' }, // Angular has its own Material
    { framework: 'angular', uiLibrary: 'chakra-ui' },
    { framework: 'svelte', uiLibrary: 'material-ui' },
    { framework: 'svelte', uiLibrary: 'chakra-ui' },
    { framework: 'vanilla', uiLibrary: 'material-ui' },
    { framework: 'vanilla', uiLibrary: 'chakra-ui' },
  ];

  const isIncompatible = incompatibleCombinations.some(
    combo => combo.framework === framework && combo.uiLibrary === uiLibrary
  );

  if (isIncompatible) {
    warnings.push(`${uiLibrary} may not be fully compatible with ${framework}`);
  }

  return {
    isValid: true, // These are warnings, not errors
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Validate code content for potential issues
 */
export function validateCodeContent(code: string, language?: string): {
  isValid: boolean;
  errors?: string[];
  warnings?: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Basic validation
  if (!code.trim()) {
    errors.push('Code cannot be empty');
    return { isValid: false, errors };
  }

  // Check for potentially harmful content
  const harmfulPatterns = [
    /eval\s*\(/i,
    /document\.write\s*\(/i,
    /innerHTML\s*=/i,
    /dangerouslySetInnerHTML/i,
  ];

  harmfulPatterns.forEach(pattern => {
    if (pattern.test(code)) {
      warnings.push('Code contains potentially unsafe patterns');
    }
  });

  // Language-specific validation
  if (language) {
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'typescript':
        // Check for common issues
        if (code.includes('var ')) {
          warnings.push('Consider using "let" or "const" instead of "var"');
        }
        
        if (code.includes('==') && !code.includes('===')) {
          warnings.push('Consider using strict equality (===) instead of loose equality (==)');
        }
        break;

      case 'react':
        // React-specific checks
        if (!code.includes('import React') && code.includes('<')) {
          warnings.push('React components should import React');
        }
        break;
    }
  }

  // Check code length
  if (code.length > 50000) {
    warnings.push('Code is very large and may impact performance');
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Comprehensive validation for all engineer tool parameters
 */
export function validateEngineerToolParams(
  toolName: string,
  params: unknown
): {
  isValid: boolean;
  data?: any;
  errors?: string[];
  warnings?: string[];
} {
  switch (toolName) {
    case 'generatePrototype':
      return validateGeneratePrototypeParams(params);
    
    case 'modifyCode':
      return validateModifyCodeParams(params);
    
    case 'explainCode':
      return validateExplainCodeParams(params);
    
    case 'debugCode':
      return validateDebugCodeParams(params);
    
    default:
      return {
        isValid: false,
        errors: [`Unknown tool: ${toolName}`]
      };
  }
}

/**
 * Sanitize user input for tool parameters
 */
export function sanitizeToolInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .slice(0, 50000); // Limit length
}

/**
 * Check if a string contains potentially harmful content
 */
export function containsHarmfulContent(content: string): boolean {
  const harmfulPatterns = [
    /javascript:/i,
    /<script/i,
    /on\w+\s*=/i,
    /data:text\/html/i,
    /vbscript:/i,
    /eval\s*\(/i,
  ];

  return harmfulPatterns.some(pattern => pattern.test(content));
}

// Code Helper Functions for Engineer AI
import { type EngineerToolInvocation, type Framework, type ProjectType } from '../types';

/**
 * Extract tool invocations from AI SDK message parts
 */
export function extractToolInvocations(messageParts: any[]): EngineerToolInvocation[] {
  return messageParts
    .filter(part => part.type === 'tool-invocation')
    .map(part => ({
      toolCallId: part.toolInvocation.toolCallId,
      toolName: part.toolInvocation.toolName,
      state: part.toolInvocation.state,
      args: part.toolInvocation.args,
      result: part.toolInvocation.result
    }));
}

/**
 * Detect programming language from code content
 */
export function detectLanguage(code: string): string {
  // React/JSX patterns
  if (code.includes('import React') || code.includes('jsx') || code.includes('<div')) {
    return code.includes('typescript') || code.includes(': ') ? 'typescript' : 'javascript';
  }
  
  // Vue patterns
  if (code.includes('<template>') || code.includes('<script setup>')) {
    return 'vue';
  }
  
  // Angular patterns
  if (code.includes('@Component') || code.includes('angular')) {
    return 'typescript';
  }
  
  // HTML patterns
  if (code.includes('<!DOCTYPE html>') || code.includes('<html>')) {
    return 'html';
  }
  
  // CSS patterns
  if (code.includes('{') && code.includes(':') && code.includes(';') && !code.includes('function')) {
    return 'css';
  }
  
  // TypeScript patterns
  if (code.includes('interface ') || code.includes('type ') || code.includes(': string') || code.includes(': number')) {
    return 'typescript';
  }
  
  // JavaScript patterns
  if (code.includes('function ') || code.includes('const ') || code.includes('let ') || code.includes('var ')) {
    return 'javascript';
  }
  
  // Python patterns
  if (code.includes('def ') || code.includes('import ') || code.includes('from ')) {
    return 'python';
  }
  
  return 'javascript'; // Default fallback
}

/**
 * Extract code blocks from markdown text
 */
export function extractCodeBlocks(text: string): Array<{ code: string; language?: string }> {
  const codeBlockRegex = /```(\w+)?\n?([\s\S]*?)```/g;
  const blocks: Array<{ code: string; language?: string }> = [];
  let match;
  
  while ((match = codeBlockRegex.exec(text)) !== null) {
    blocks.push({
      code: match[2].trim(),
      language: match[1] || undefined
    });
  }
  
  return blocks;
}

/**
 * Format code with basic indentation
 */
export function formatCode(code: string, language: string = 'javascript'): string {
  // Basic formatting - in a real implementation, use Prettier or similar
  let formatted = code.trim();
  
  if (language === 'javascript' || language === 'typescript') {
    // Basic JS/TS formatting
    formatted = formatted
      .replace(/\{/g, ' {\n')
      .replace(/\}/g, '\n}')
      .replace(/;/g, ';\n')
      .replace(/,/g, ',\n');
  }
  
  return formatted;
}

/**
 * Validate code syntax (basic validation)
 */
export function validateCodeSyntax(code: string, language: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!code.trim()) {
    errors.push('Code cannot be empty');
    return { isValid: false, errors };
  }
  
  switch (language) {
    case 'javascript':
    case 'typescript':
      // Check for balanced braces
      const openBraces = (code.match(/\{/g) || []).length;
      const closeBraces = (code.match(/\}/g) || []).length;
      if (openBraces !== closeBraces) {
        errors.push('Mismatched curly braces');
      }
      
      // Check for balanced parentheses
      const openParens = (code.match(/\(/g) || []).length;
      const closeParens = (code.match(/\)/g) || []).length;
      if (openParens !== closeParens) {
        errors.push('Mismatched parentheses');
      }
      
      // Check for balanced brackets
      const openBrackets = (code.match(/\[/g) || []).length;
      const closeBrackets = (code.match(/\]/g) || []).length;
      if (openBrackets !== closeBrackets) {
        errors.push('Mismatched square brackets');
      }
      break;
      
    case 'html':
      // Basic HTML validation
      const openTags = code.match(/<[^/][^>]*>/g) || [];
      const closeTags = code.match(/<\/[^>]*>/g) || [];
      
      // This is a very basic check - real validation would be more complex
      if (openTags.length !== closeTags.length) {
        errors.push('Possible mismatched HTML tags');
      }
      break;
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get file extension for a given language
 */
export function getFileExtension(language: string): string {
  switch (language.toLowerCase()) {
    case 'javascript':
      return '.js';
    case 'typescript':
      return '.ts';
    case 'react':
      return '.jsx';
    case 'react-typescript':
      return '.tsx';
    case 'vue':
      return '.vue';
    case 'html':
      return '.html';
    case 'css':
      return '.css';
    case 'python':
      return '.py';
    case 'java':
      return '.java';
    case 'cpp':
      return '.cpp';
    case 'csharp':
      return '.cs';
    default:
      return '.txt';
  }
}

/**
 * Get appropriate file name for framework and project type
 */
export function getDefaultFileName(framework: Framework, projectType: ProjectType): string {
  switch (framework) {
    case 'react':
      return projectType === 'component' ? '/Component.tsx' : '/App.tsx';
    case 'vue':
      return projectType === 'component' ? '/Component.vue' : '/App.vue';
    case 'angular':
      return projectType === 'component' ? '/component.ts' : '/app.component.ts';
    case 'svelte':
      return projectType === 'component' ? '/Component.svelte' : '/App.svelte';
    case 'vanilla':
      return '/index.html';
    case 'node':
      return '/index.js';
    default:
      return '/App.tsx';
  }
}

/**
 * Generate tool display name for UI
 */
export function getToolDisplayName(toolName: string): string {
  switch (toolName) {
    case 'generatePrototype':
      return 'Code Prototype';
    case 'modifyCode':
      return 'Code Modification';
    case 'explainCode':
      return 'Code Explanation';
    case 'debugCode':
      return 'Code Debugging';
    default:
      return toolName.replace(/([A-Z])/g, ' $1').trim();
  }
}

/**
 * Get framework display name
 */
export function getFrameworkDisplayName(framework: Framework): string {
  switch (framework) {
    case 'react':
      return 'React';
    case 'vue':
      return 'Vue.js';
    case 'angular':
      return 'Angular';
    case 'svelte':
      return 'Svelte';
    case 'solid':
      return 'Solid.js';
    case 'vanilla':
      return 'Vanilla JavaScript';
    case 'nextjs':
      return 'Next.js';
    case 'node':
      return 'Node.js';
    default:
      return framework;
  }
}

/**
 * Get project type display name
 */
export function getProjectTypeDisplayName(projectType: ProjectType): string {
  switch (projectType) {
    case 'component':
      return 'Component';
    case 'app':
      return 'Application';
    case 'utility':
      return 'Utility';
    case 'demo':
      return 'Demo';
    case 'tutorial':
      return 'Tutorial';
    case 'prototype':
      return 'Prototype';
    case 'experiment':
      return 'Experiment';
    default:
      return projectType;
  }
}

/**
 * Calculate code complexity score
 */
export function calculateComplexity(code: string): { score: number; level: 'simple' | 'intermediate' | 'advanced' } {
  let score = 0;
  
  // Count various complexity indicators
  const lines = code.split('\n').length;
  const functions = (code.match(/function|=>/g) || []).length;
  const conditionals = (code.match(/if|else|switch|case/g) || []).length;
  const loops = (code.match(/for|while|forEach|map|filter/g) || []).length;
  const classes = (code.match(/class |interface |type /g) || []).length;
  const imports = (code.match(/import |require\(/g) || []).length;
  
  // Calculate score based on various factors
  score += Math.min(lines / 10, 10); // Max 10 points for lines
  score += functions * 2; // 2 points per function
  score += conditionals * 1.5; // 1.5 points per conditional
  score += loops * 2; // 2 points per loop
  score += classes * 3; // 3 points per class/interface
  score += imports * 0.5; // 0.5 points per import
  
  // Determine level
  let level: 'simple' | 'intermediate' | 'advanced';
  if (score < 10) {
    level = 'simple';
  } else if (score < 25) {
    level = 'intermediate';
  } else {
    level = 'advanced';
  }
  
  return { score: Math.round(score), level };
}

/**
 * Extract dependencies from code
 */
export function extractDependencies(code: string, framework: Framework): string[] {
  const dependencies: string[] = [];
  
  // Extract import statements
  const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
  let match;
  
  while ((match = importRegex.exec(code)) !== null) {
    const dep = match[1];
    // Skip relative imports
    if (!dep.startsWith('.') && !dep.startsWith('/')) {
      dependencies.push(dep);
    }
  }
  
  // Extract require statements
  const requireRegex = /require\(['"]([^'"]+)['"]\)/g;
  while ((match = requireRegex.exec(code)) !== null) {
    const dep = match[1];
    if (!dep.startsWith('.') && !dep.startsWith('/')) {
      dependencies.push(dep);
    }
  }
  
  return [...new Set(dependencies)]; // Remove duplicates
}

/**
 * Generate unique tool call ID
 */
export function generateToolCallId(toolName: string): string {
  return `${toolName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Format tool execution error for display
 */
export function formatToolError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (typeof error === 'object' && error !== null) {
    return JSON.stringify(error);
  }
  
  return 'An unknown error occurred';
}

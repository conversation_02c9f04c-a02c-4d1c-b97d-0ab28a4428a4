// Sandpack Helper Functions for Engineer AI
import { type ProjectStructure, type SandpackFiles, type SandpackTemplate, type Framework } from '../types';

/**
 * Convert framework to Sandpack template
 */
export function frameworkToSandpackTemplate(framework: Framework): SandpackTemplate {
  switch (framework) {
    case 'react':
      return 'react-ts';
    case 'vue':
      return 'vue-ts';
    case 'angular':
      return 'angular';
    case 'svelte':
      return 'svelte';
    case 'solid':
      return 'solid';
    case 'vanilla':
      return 'vanilla-ts';
    case 'nextjs':
      return 'nextjs';
    case 'node':
      return 'node';
    default:
      return 'react-ts';
  }
}

/**
 * Get default dependencies for a framework
 */
export function getDefaultDependencies(framework: Framework, uiLibrary?: string): Record<string, string> {
  const deps: Record<string, string> = {};

  switch (framework) {
    case 'react':
      deps['react'] = '^18.2.0';
      deps['react-dom'] = '^18.2.0';
      break;
    case 'vue':
      deps['vue'] = '^3.3.0';
      break;
    case 'angular':
      deps['@angular/core'] = '^16.0.0';
      deps['@angular/common'] = '^16.0.0';
      deps['@angular/platform-browser'] = '^16.0.0';
      break;
    case 'svelte':
      deps['svelte'] = '^4.0.0';
      break;
    case 'solid':
      deps['solid-js'] = '^1.7.0';
      break;
  }

  // Add UI library dependencies
  if (uiLibrary) {
    switch (uiLibrary) {
      case 'tailwind':
        deps['tailwindcss'] = '^3.3.0';
        deps['autoprefixer'] = '^10.4.14';
        deps['postcss'] = '^8.4.24';
        break;
      case 'material-ui':
        deps['@mui/material'] = '^5.14.0';
        deps['@emotion/react'] = '^11.11.0';
        deps['@emotion/styled'] = '^11.11.0';
        break;
      case 'chakra-ui':
        deps['@chakra-ui/react'] = '^2.7.0';
        deps['@emotion/react'] = '^11.11.0';
        deps['@emotion/styled'] = '^11.11.0';
        break;
      case 'ant-design':
        deps['antd'] = '^5.7.0';
        break;
      case 'styled-components':
        deps['styled-components'] = '^6.0.0';
        break;
    }
  }

  return deps;
}

/**
 * Get default dev dependencies for a framework
 */
export function getDefaultDevDependencies(framework: Framework): Record<string, string> {
  const devDeps: Record<string, string> = {};

  switch (framework) {
    case 'react':
      devDeps['@types/react'] = '^18.2.0';
      devDeps['@types/react-dom'] = '^18.2.0';
      devDeps['typescript'] = '^5.0.0';
      break;
    case 'vue':
      devDeps['@vitejs/plugin-vue'] = '^4.2.0';
      devDeps['typescript'] = '^5.0.0';
      break;
    case 'angular':
      devDeps['@angular/cli'] = '^16.0.0';
      devDeps['typescript'] = '^5.0.0';
      break;
    case 'svelte':
      devDeps['@sveltejs/vite-plugin-svelte'] = '^2.4.0';
      devDeps['typescript'] = '^5.0.0';
      break;
  }

  return devDeps;
}

/**
 * Create a basic project structure
 */
export function createBasicProject(
  framework: Framework,
  projectName: string = 'My Project',
  uiLibrary?: string
): ProjectStructure {
  const template = frameworkToSandpackTemplate(framework);
  const dependencies = getDefaultDependencies(framework, uiLibrary);
  const devDependencies = getDefaultDevDependencies(framework);
  
  const files: SandpackFiles = {};

  // Generate basic files based on framework
  switch (framework) {
    case 'react':
      files['/App.tsx'] = generateReactApp(projectName, uiLibrary);
      files['/index.tsx'] = generateReactIndex();
      files['/styles.css'] = generateStyles(uiLibrary);
      break;
    case 'vue':
      files['/App.vue'] = generateVueApp(projectName, uiLibrary);
      files['/main.ts'] = generateVueMain();
      files['/style.css'] = generateStyles(uiLibrary);
      break;
    case 'vanilla':
      files['/index.html'] = generateVanillaHTML(projectName);
      files['/script.ts'] = generateVanillaScript(projectName);
      files['/style.css'] = generateStyles(uiLibrary);
      break;
    default:
      files['/App.tsx'] = generateReactApp(projectName, uiLibrary);
      files['/index.tsx'] = generateReactIndex();
      files['/styles.css'] = generateStyles(uiLibrary);
  }

  return {
    files,
    template,
    dependencies,
    devDependencies,
    entry: getEntryFile(framework),
    main: getMainFile(framework),
  };
}

/**
 * Merge two project structures
 */
export function mergeProjects(base: ProjectStructure, overlay: Partial<ProjectStructure>): ProjectStructure {
  return {
    files: { ...base.files, ...overlay.files },
    template: overlay.template || base.template,
    dependencies: { ...base.dependencies, ...overlay.dependencies },
    devDependencies: { ...base.devDependencies, ...overlay.devDependencies },
    entry: overlay.entry || base.entry,
    main: overlay.main || base.main,
  };
}

/**
 * Validate project structure
 */
export function validateProject(project: ProjectStructure): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check if files exist
  if (!project.files || Object.keys(project.files).length === 0) {
    errors.push('Project must have at least one file');
  }

  // Check if entry file exists
  if (project.entry && !project.files[project.entry]) {
    errors.push(`Entry file ${project.entry} not found`);
  }

  // Check if main file exists
  if (project.main && !project.files[project.main]) {
    errors.push(`Main file ${project.main} not found`);
  }

  // Validate file contents
  Object.entries(project.files).forEach(([path, content]) => {
    const fileContent = typeof content === 'string' ? content : content.code;
    if (!fileContent || fileContent.trim().length === 0) {
      errors.push(`File ${path} is empty`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Extract project metadata
 */
export function extractProjectMetadata(project: ProjectStructure) {
  const fileCount = Object.keys(project.files).length;
  const dependencyCount = Object.keys(project.dependencies || {}).length;
  const devDependencyCount = Object.keys(project.devDependencies || {}).length;
  
  // Calculate total lines of code
  let totalLines = 0;
  Object.values(project.files).forEach(file => {
    const content = typeof file === 'string' ? file : file.code || '';
    totalLines += content.split('\n').length;
  });

  // Detect languages used
  const languages = new Set<string>();
  Object.entries(project.files).forEach(([path, file]) => {
    const ext = path.split('.').pop()?.toLowerCase();
    if (ext) {
      switch (ext) {
        case 'js':
        case 'jsx':
          languages.add('JavaScript');
          break;
        case 'ts':
        case 'tsx':
          languages.add('TypeScript');
          break;
        case 'vue':
          languages.add('Vue');
          break;
        case 'html':
          languages.add('HTML');
          break;
        case 'css':
          languages.add('CSS');
          break;
      }
    }
  });

  return {
    fileCount,
    dependencyCount,
    devDependencyCount,
    totalLines,
    languages: Array.from(languages),
    template: project.template,
    hasEntry: !!project.entry,
    hasMain: !!project.main,
  };
}

// Helper functions for generating basic files
function generateReactApp(projectName: string, uiLibrary?: string): string {
  const className = uiLibrary === 'tailwind' 
    ? 'min-h-screen bg-gray-100 flex items-center justify-center'
    : 'app';

  return `import React from 'react';
import './styles.css';

export default function App() {
  return (
    <div className="${className}">
      <div className="${uiLibrary === 'tailwind' ? 'bg-white p-8 rounded-lg shadow-md' : 'container'}">
        <h1 className="${uiLibrary === 'tailwind' ? 'text-2xl font-bold text-gray-800 mb-4' : 'title'}">
          ${projectName}
        </h1>
        <p className="${uiLibrary === 'tailwind' ? 'text-gray-600' : 'description'}">
          Welcome to your new React application!
        </p>
      </div>
    </div>
  );
}`;
}

function generateReactIndex(): string {
  return `import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';

const container = document.getElementById('root');
const root = createRoot(container!);
root.render(<App />);`;
}

function generateVueApp(projectName: string, uiLibrary?: string): string {
  return `<template>
  <div class="${uiLibrary === 'tailwind' ? 'min-h-screen bg-gray-100 flex items-center justify-center' : 'app'}">
    <div class="${uiLibrary === 'tailwind' ? 'bg-white p-8 rounded-lg shadow-md' : 'container'}">
      <h1 class="${uiLibrary === 'tailwind' ? 'text-2xl font-bold text-gray-800 mb-4' : 'title'}">
        ${projectName}
      </h1>
      <p class="${uiLibrary === 'tailwind' ? 'text-gray-600' : 'description'}">
        Welcome to your new Vue application!
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
// Your Vue logic here
</script>`;
}

function generateVueMain(): string {
  return `import { createApp } from 'vue';
import App from './App.vue';
import './style.css';

createApp(App).mount('#app');`;
}

function generateVanillaHTML(projectName: string): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${projectName}</title>
  <link rel="stylesheet" href="./style.css">
</head>
<body>
  <div class="app">
    <div class="container">
      <h1 class="title">${projectName}</h1>
      <p class="description">Welcome to your new application!</p>
    </div>
  </div>
  <script src="./script.ts"></script>
</body>
</html>`;
}

function generateVanillaScript(projectName: string): string {
  return `// ${projectName}
console.log('Application initialized');

document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded');
});`;
}

function generateStyles(uiLibrary?: string): string {
  if (uiLibrary === 'tailwind') {
    return `@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}`;
  }

  return `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f5f5f5;
}

.app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 1rem;
}

.description {
  color: #666;
  line-height: 1.5;
}`;
}

function getEntryFile(framework: Framework): string {
  switch (framework) {
    case 'vue': return '/main.ts';
    case 'vanilla': return '/index.html';
    default: return '/index.tsx';
  }
}

function getMainFile(framework: Framework): string {
  switch (framework) {
    case 'vue': return '/App.vue';
    case 'vanilla': return '/script.ts';
    default: return '/App.tsx';
  }
}

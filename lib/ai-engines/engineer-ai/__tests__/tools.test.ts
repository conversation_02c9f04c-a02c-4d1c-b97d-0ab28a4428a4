import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { 
  generatePrototypeTool, 
  modifyCodeTool, 
  explainCodeTool, 
  debugCodeTool 
} from '../tools';

// Mock the utility functions
jest.mock('../utils/validators', () => ({
  validateCodeContent: jest.fn(() => ({ isValid: true, errors: [], warnings: [] })),
  containsHarmfulContent: jest.fn(() => false),
  sanitizeToolInput: jest.fn((input: string) => input.trim()),
}));

jest.mock('../utils/codeHelpers', () => ({
  detectLanguage: jest.fn(() => 'javascript'),
  calculateComplexity: jest.fn(() => ({ score: 5, level: 'intermediate' })),
  extractDependencies: jest.fn(() => ['react', 'typescript']),
}));

jest.mock('../utils/sandpackHelpers', () => ({
  createBasicProject: jest.fn(() => ({
    files: {
      '/App.tsx': 'import React from "react";\n\nexport default function App() {\n  return <div>Hello World</div>;\n}',
      '/index.tsx': 'import React from "react";\nimport { createRoot } from "react-dom/client";\nimport App from "./App";\n\nconst container = document.getElementById("root");\nconst root = createRoot(container!);\nroot.render(<App />);',
      '/styles.css': 'body { margin: 0; font-family: sans-serif; }'
    },
    template: 'react-ts',
    dependencies: { 'react': '^18.2.0', 'react-dom': '^18.2.0' },
    devDependencies: { '@types/react': '^18.2.0', 'typescript': '^5.0.0' },
    entry: '/index.tsx',
    main: '/App.tsx'
  })),
  validateProject: jest.fn(() => ({ isValid: true, errors: [] })),
}));

describe('Engineer AI Tools', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generatePrototypeTool', () => {
    it('should generate a React prototype successfully', async () => {
      const params = {
        description: 'Create a simple counter app',
        projectType: 'component' as const,
        framework: 'react' as const,
        complexity: 'simple' as const,
        uiLibrary: 'tailwind' as const,
        features: ['state', 'interactive'],
        includeTests: false,
        includeDocumentation: true
      };

      const result = await generatePrototypeTool.execute(params);

      expect(result).toHaveProperty('project');
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('features');
      expect(result).toHaveProperty('instructions');
      expect(result).toHaveProperty('tips');
      expect(result).toHaveProperty('nextSteps');

      expect(result.project.files).toHaveProperty('/App.tsx');
      expect(result.project.template).toBe('react-ts');
      expect(result.description).toBe('Create a simple counter app');
    });

    it('should handle Vue framework', async () => {
      const params = {
        description: 'Create a Vue todo app',
        projectType: 'app' as const,
        framework: 'vue' as const,
        complexity: 'intermediate' as const,
        uiLibrary: 'none' as const,
        features: ['routing', 'state-management'],
        includeTests: true,
        includeDocumentation: true
      };

      const result = await generatePrototypeTool.execute(params);

      expect(result.project.files).toHaveProperty('/App.tsx'); // Mock returns React files
      expect(result.features).toContain('routing');
      expect(result.features).toContain('state-management');
    });

    it('should reject harmful content', async () => {
      const mockContainsHarmfulContent = require('../utils/validators').containsHarmfulContent;
      mockContainsHarmfulContent.mockReturnValueOnce(true);

      const params = {
        description: '<script>alert("xss")</script>',
        projectType: 'component' as const,
        framework: 'react' as const,
        complexity: 'simple' as const,
        uiLibrary: 'none' as const,
        features: [],
        includeTests: false,
        includeDocumentation: false
      };

      await expect(generatePrototypeTool.execute(params)).rejects.toThrow(
        'Description contains potentially harmful content'
      );
    });

    it('should reject short descriptions', async () => {
      const params = {
        description: 'Hi',
        projectType: 'component' as const,
        framework: 'react' as const,
        complexity: 'simple' as const,
        uiLibrary: 'none' as const,
        features: [],
        includeTests: false,
        includeDocumentation: false
      };

      await expect(generatePrototypeTool.execute(params)).rejects.toThrow(
        'Description must be at least 10 characters long'
      );
    });
  });

  describe('modifyCodeTool', () => {
    it('should modify code successfully', async () => {
      const params = {
        fileContent: 'function hello() { console.log("hello"); }',
        fileName: 'test.js',
        modification: 'Add error handling to the function',
        preserveStructure: true
      };

      const result = await modifyCodeTool.execute(params);

      expect(result).toHaveProperty('modifiedCode');
      expect(result).toHaveProperty('changes');
      expect(result).toHaveProperty('explanation');
      expect(result).toHaveProperty('suggestions');

      expect(result.modifiedCode).toContain('function');
      expect(Array.isArray(result.changes)).toBe(true);
      expect(typeof result.explanation).toBe('string');
      expect(Array.isArray(result.suggestions)).toBe(true);
    });

    it('should reject large files', async () => {
      const largeContent = 'a'.repeat(100001); // Exceeds 100KB limit
      
      const params = {
        fileContent: largeContent,
        fileName: 'large.js',
        modification: 'Add comments',
        preserveStructure: true
      };

      await expect(modifyCodeTool.execute(params)).rejects.toThrow(
        'File content is too large (max 100KB)'
      );
    });

    it('should handle invalid code content', async () => {
      const mockValidateCodeContent = require('../utils/validators').validateCodeContent;
      mockValidateCodeContent.mockReturnValueOnce({ 
        isValid: false, 
        errors: ['Syntax error'] 
      });

      const params = {
        fileContent: 'invalid code {{{',
        fileName: 'invalid.js',
        modification: 'Fix syntax',
        preserveStructure: true
      };

      await expect(modifyCodeTool.execute(params)).rejects.toThrow(
        'Invalid code content: Syntax error'
      );
    });
  });

  describe('explainCodeTool', () => {
    it('should explain code successfully', async () => {
      const params = {
        code: 'const [count, setCount] = useState(0);',
        language: 'javascript',
        focusArea: 'hooks'
      };

      const result = await explainCodeTool.execute(params);

      expect(result).toHaveProperty('explanation');
      expect(result).toHaveProperty('keyPoints');
      expect(result).toHaveProperty('codeBlocks');
      expect(result).toHaveProperty('concepts');
      expect(result).toHaveProperty('suggestions');

      expect(typeof result.explanation).toBe('string');
      expect(Array.isArray(result.keyPoints)).toBe(true);
      expect(Array.isArray(result.codeBlocks)).toBe(true);
      expect(Array.isArray(result.concepts)).toBe(true);
      expect(Array.isArray(result.suggestions)).toBe(true);
    });

    it('should auto-detect language when not provided', async () => {
      const params = {
        code: 'import React from "react";',
        focusArea: 'imports'
      };

      const result = await explainCodeTool.execute(params);

      expect(result.explanation).toContain('javascript');
    });

    it('should reject very short code', async () => {
      const params = {
        code: 'x',
        language: 'javascript',
        focusArea: 'overview'
      };

      await expect(explainCodeTool.execute(params)).rejects.toThrow(
        'Code must be at least 5 characters long'
      );
    });
  });

  describe('debugCodeTool', () => {
    it('should debug code successfully', async () => {
      const params = {
        code: 'const users = data.map(user => user.name);',
        errorMessage: 'Cannot read property "map" of undefined',
        expectedBehavior: 'Should display list of user names',
        language: 'javascript'
      };

      const result = await debugCodeTool.execute(params);

      expect(result).toHaveProperty('issues');
      expect(result).toHaveProperty('fixedCode');
      expect(result).toHaveProperty('explanation');
      expect(result).toHaveProperty('preventionTips');

      expect(Array.isArray(result.issues)).toBe(true);
      expect(typeof result.fixedCode).toBe('string');
      expect(typeof result.explanation).toBe('string');
      expect(Array.isArray(result.preventionTips)).toBe(true);
    });

    it('should handle code without error message', async () => {
      const params = {
        code: 'function broken() { return undefined.property; }',
        expectedBehavior: 'Should return a valid property',
        language: 'javascript'
      };

      const result = await debugCodeTool.execute(params);

      expect(result.issues).toBeDefined();
      expect(result.fixedCode).toBeDefined();
    });

    it('should auto-detect language for debugging', async () => {
      const params = {
        code: 'console.error("test");',
        expectedBehavior: 'Should log without errors'
      };

      const result = await debugCodeTool.execute(params);

      expect(result.fixedCode).toContain('console.log');
    });
  });

  describe('Error Handling', () => {
    it('should handle tool execution errors gracefully', async () => {
      // Mock a function to throw an error
      const mockCreateBasicProject = require('../utils/sandpackHelpers').createBasicProject;
      mockCreateBasicProject.mockImplementationOnce(() => {
        throw new Error('Mock error');
      });

      const params = {
        description: 'Test error handling',
        projectType: 'component' as const,
        framework: 'react' as const,
        complexity: 'simple' as const,
        uiLibrary: 'none' as const,
        features: [],
        includeTests: false,
        includeDocumentation: false
      };

      await expect(generatePrototypeTool.execute(params)).rejects.toThrow(
        'Failed to generate prototype'
      );
    });
  });
});

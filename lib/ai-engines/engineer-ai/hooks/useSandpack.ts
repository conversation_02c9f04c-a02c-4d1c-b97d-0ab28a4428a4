'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { type ProjectStructure, type SandpackFiles } from '../types';

interface UseSandpackOptions {
  autorun?: boolean;
  onError?: (error: Error) => void;
  onConsoleLog?: (log: any) => void;
}

interface UseSandpackReturn {
  // State
  isRunning: boolean;
  error: string | null;
  consoleOutput: any[];
  activeFile: string;
  
  // Actions
  run: () => void;
  stop: () => void;
  refresh: () => void;
  setActiveFile: (file: string) => void;
  updateFile: (file: string, content: string) => void;
  addFile: (file: string, content: string) => void;
  removeFile: (file: string) => void;
  
  // Project management
  loadProject: (project: ProjectStructure) => void;
  exportProject: () => ProjectStructure | null;
  resetProject: () => void;
  
  // File operations
  getFileContent: (file: string) => string;
  getAllFiles: () => SandpackFiles;
  hasUnsavedChanges: boolean;
  
  // Utilities
  formatCode: (code: string, language?: string) => string;
  validateCode: (code: string, language?: string) => { isValid: boolean; errors: string[] };
}

export function useSandpack(options: UseSandpackOptions = {}): UseSandpackReturn {
  const { autorun = true, onError, onConsoleLog } = options;
  
  const [isRunning, setIsRunning] = useState(autorun);
  const [error, setError] = useState<string | null>(null);
  const [consoleOutput, setConsoleOutput] = useState<any[]>([]);
  const [activeFile, setActiveFileState] = useState<string>('');
  const [project, setProject] = useState<ProjectStructure | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  const originalProjectRef = useRef<ProjectStructure | null>(null);

  // Run the sandbox
  const run = useCallback(() => {
    setIsRunning(true);
    setError(null);
    
    // In a real implementation, this would start the Sandpack execution
    console.log('Running Sandpack project...');
  }, []);

  // Stop the sandbox
  const stop = useCallback(() => {
    setIsRunning(false);
    console.log('Stopping Sandpack project...');
  }, []);

  // Refresh the sandbox
  const refresh = useCallback(() => {
    setIsRunning(false);
    setError(null);
    setConsoleOutput([]);
    
    setTimeout(() => {
      if (autorun) {
        setIsRunning(true);
      }
    }, 100);
  }, [autorun]);

  // Set active file
  const setActiveFile = useCallback((file: string) => {
    if (project && project.files[file]) {
      setActiveFileState(file);
    }
  }, [project]);

  // Update file content
  const updateFile = useCallback((file: string, content: string) => {
    if (!project) return;
    
    setProject(prev => {
      if (!prev) return prev;
      
      const newFiles = { ...prev.files };
      if (typeof newFiles[file] === 'string') {
        newFiles[file] = content;
      } else {
        newFiles[file] = { ...newFiles[file] as any, code: content };
      }
      
      return { ...prev, files: newFiles };
    });
    
    setHasUnsavedChanges(true);
  }, [project]);

  // Add new file
  const addFile = useCallback((file: string, content: string) => {
    if (!project) return;
    
    setProject(prev => {
      if (!prev) return prev;
      
      return {
        ...prev,
        files: {
          ...prev.files,
          [file]: content
        }
      };
    });
    
    setHasUnsavedChanges(true);
  }, [project]);

  // Remove file
  const removeFile = useCallback((file: string) => {
    if (!project) return;
    
    setProject(prev => {
      if (!prev) return prev;
      
      const newFiles = { ...prev.files };
      delete newFiles[file];
      
      return { ...prev, files: newFiles };
    });
    
    // If removing active file, switch to another file
    if (activeFile === file) {
      const remainingFiles = Object.keys(project.files).filter(f => f !== file);
      if (remainingFiles.length > 0) {
        setActiveFileState(remainingFiles[0]);
      }
    }
    
    setHasUnsavedChanges(true);
  }, [project, activeFile]);

  // Load project
  const loadProject = useCallback((newProject: ProjectStructure) => {
    setProject(newProject);
    originalProjectRef.current = JSON.parse(JSON.stringify(newProject));
    setHasUnsavedChanges(false);
    
    // Set active file to the first available file
    const files = Object.keys(newProject.files);
    if (files.length > 0) {
      // Prefer main files
      const mainFiles = ['/App.tsx', '/App.vue', '/index.tsx', '/main.ts', '/script.ts'];
      const mainFile = mainFiles.find(f => files.includes(f));
      setActiveFileState(mainFile || files[0]);
    }
    
    if (autorun) {
      run();
    }
  }, [autorun, run]);

  // Export project
  const exportProject = useCallback((): ProjectStructure | null => {
    return project;
  }, [project]);

  // Reset project to original state
  const resetProject = useCallback(() => {
    if (originalProjectRef.current) {
      setProject(JSON.parse(JSON.stringify(originalProjectRef.current)));
      setHasUnsavedChanges(false);
    }
  }, []);

  // Get file content
  const getFileContent = useCallback((file: string): string => {
    if (!project || !project.files[file]) return '';
    
    const fileData = project.files[file];
    return typeof fileData === 'string' ? fileData : fileData.code || '';
  }, [project]);

  // Get all files
  const getAllFiles = useCallback((): SandpackFiles => {
    return project?.files || {};
  }, [project]);

  // Format code (basic implementation)
  const formatCode = useCallback((code: string, language?: string): string => {
    // In a real implementation, you would use a proper code formatter
    // like Prettier or similar
    return code.trim();
  }, []);

  // Validate code (basic implementation)
  const validateCode = useCallback((code: string, language?: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    // Basic validation
    if (!code.trim()) {
      errors.push('Code cannot be empty');
    }
    
    // Language-specific validation
    if (language === 'javascript' || language === 'typescript') {
      // Check for basic syntax issues
      const openBraces = (code.match(/\{/g) || []).length;
      const closeBraces = (code.match(/\}/g) || []).length;
      
      if (openBraces !== closeBraces) {
        errors.push('Mismatched braces');
      }
      
      const openParens = (code.match(/\(/g) || []).length;
      const closeParens = (code.match(/\)/g) || []).length;
      
      if (openParens !== closeParens) {
        errors.push('Mismatched parentheses');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }, []);

  // Auto-run when project changes
  useEffect(() => {
    if (project && autorun && !isRunning) {
      run();
    }
  }, [project, autorun, isRunning, run]);

  // Handle errors
  useEffect(() => {
    if (error && onError) {
      onError(new Error(error));
    }
  }, [error, onError]);

  return {
    // State
    isRunning,
    error,
    consoleOutput,
    activeFile,
    
    // Actions
    run,
    stop,
    refresh,
    setActiveFile,
    updateFile,
    addFile,
    removeFile,
    
    // Project management
    loadProject,
    exportProject,
    resetProject,
    
    // File operations
    getFileContent,
    getAllFiles,
    hasUnsavedChanges,
    
    // Utilities
    formatCode,
    validateCode,
  };
}

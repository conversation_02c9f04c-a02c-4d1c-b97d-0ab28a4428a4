'use client';

import { useChat } from '@ai-sdk/react';
import { type Message } from 'ai';
import { useCallback } from 'react';
import { type EngineerToolInvocation } from '../types';

interface UseEngineerChatOptions {
  api?: string;
  maxSteps?: number;
  onToolCall?: (toolCall: EngineerToolInvocation) => void;
  onToolResult?: (result: any) => void;
  onError?: (error: Error) => void;
}

export function useEngineerChat(options: UseEngineerChatOptions = {}) {
  const {
    api = '/api/chat/engineer',
    maxSteps = 5,
    onToolCall,
    onToolResult,
    onError
  } = options;

  const chat = useChat({
    api,
    maxSteps,
    onToolCall: async ({ toolCall }) => {
      // Handle client-side tool calls if needed
      const engineerToolCall: EngineerToolInvocation = {
        toolCallId: toolCall.toolCallId,
        toolName: toolCall.toolName as any,
        state: 'call',
        args: toolCall.args
      };

      onToolCall?.(engineerToolCall);

      // For engineer tools, we let the server handle execution
      // But we can add client-side logic here if needed
      return undefined;
    },
    onError: (error) => {
      console.error('Engineer chat error:', error);
      onError?.(error);
    },
    onFinish: (message) => {
      // Process tool results from the final message
      if (message.parts) {
        message.parts.forEach((part) => {
          if (part.type === 'tool-invocation' && part.toolInvocation.state === 'result') {
            onToolResult?.(part.toolInvocation.result);
          }
        });
      }
    }
  });

  // Helper function to get tool invocations from messages
  const getToolInvocations = useCallback((messages: Message[]): EngineerToolInvocation[] => {
    const invocations: EngineerToolInvocation[] = [];
    
    messages.forEach((message) => {
      if (message.role === 'assistant' && message.parts) {
        message.parts.forEach((part) => {
          if (part.type === 'tool-invocation') {
            invocations.push({
              toolCallId: part.toolInvocation.toolCallId,
              toolName: part.toolInvocation.toolName as any,
              state: part.toolInvocation.state as any,
              args: part.toolInvocation.args,
              result: part.toolInvocation.result
            });
          }
        });
      }
    });

    return invocations;
  }, []);

  // Helper function to check if there are pending tool calls
  const hasPendingToolCalls = useCallback((messages: Message[]): boolean => {
    const invocations = getToolInvocations(messages);
    return invocations.some(inv => inv.state === 'call' || inv.state === 'partial-call');
  }, [getToolInvocations]);

  // Helper function to get the latest tool results
  const getLatestToolResults = useCallback((messages: Message[]) => {
    const invocations = getToolInvocations(messages);
    return invocations
      .filter(inv => inv.state === 'result')
      .map(inv => ({
        toolName: inv.toolName,
        result: inv.result,
        toolCallId: inv.toolCallId
      }));
  }, [getToolInvocations]);

  // Enhanced send message with engineer-specific features
  const sendEngineerMessage = useCallback((message: string, options?: {
    includeContext?: boolean;
    codeContext?: string;
    framework?: string;
  }) => {
    const { includeContext = true, codeContext, framework } = options || {};
    
    // Add engineer-specific context if needed
    let enhancedMessage = message;
    if (includeContext) {
      enhancedMessage = `As a software engineer, please help with: ${message}`;
      
      if (codeContext) {
        enhancedMessage += `\n\nCode context:\n\`\`\`\n${codeContext}\n\`\`\``;
      }
      
      if (framework) {
        enhancedMessage += `\n\nPreferred framework: ${framework}`;
      }
    }

    return chat.handleSubmit(new Event('submit') as any, {
      data: { message: enhancedMessage }
    });
  }, [chat]);

  // Helper to extract code from messages
  const extractCodeFromMessages = useCallback((messages: Message[]): string[] => {
    const codeBlocks: string[] = [];
    
    messages.forEach((message) => {
      if (message.content) {
        // Extract code blocks from markdown
        const codeBlockRegex = /```[\s\S]*?```/g;
        const matches = message.content.match(codeBlockRegex);
        if (matches) {
          matches.forEach(match => {
            // Remove the ``` markers and language identifier
            const code = match.replace(/```\w*\n?/, '').replace(/```$/, '');
            codeBlocks.push(code.trim());
          });
        }
      }
    });
    
    return codeBlocks;
  }, []);

  // Helper to get generated prototypes
  const getGeneratedPrototypes = useCallback((messages: Message[]) => {
    const invocations = getToolInvocations(messages);
    return invocations
      .filter(inv => inv.toolName === 'generatePrototype' && inv.state === 'result')
      .map(inv => inv.result);
  }, [getToolInvocations]);

  return {
    ...chat,
    // Enhanced methods
    sendEngineerMessage,
    getToolInvocations: () => getToolInvocations(chat.messages),
    hasPendingToolCalls: () => hasPendingToolCalls(chat.messages),
    getLatestToolResults: () => getLatestToolResults(chat.messages),
    extractCodeFromMessages: () => extractCodeFromMessages(chat.messages),
    getGeneratedPrototypes: () => getGeneratedPrototypes(chat.messages),
    
    // Engineer-specific helpers
    isEngineerMode: true,
    assistantType: 'engineer' as const,
  };
}

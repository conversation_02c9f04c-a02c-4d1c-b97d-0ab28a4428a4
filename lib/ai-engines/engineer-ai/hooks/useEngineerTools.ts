'use client';

import { useState, useCallback } from 'react';
import { 
  type UseEngineerToolsOptions,
  type UseEngineerToolsReturn,
  type GeneratePrototypeParams,
  type ModifyCodeParams,
  type ExplainCodeParams,
  type DebugCodeParams,
  generatePrototypeSchema,
  modifyCodeSchema,
  explainCodeSchema,
  debugCodeSchema
} from '../types';

export function useEngineerTools(options: UseEngineerToolsOptions = {}): UseEngineerToolsReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { onToolCall, onToolResult } = options;

  // Generate prototype tool
  const generatePrototype = useCallback(async (params: GeneratePrototypeParams) => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate parameters
      const validatedParams = generatePrototypeSchema.parse(params);
      
      // Notify tool call start
      onToolCall?.({
        toolCallId: `prototype-${Date.now()}`,
        toolName: 'generatePrototype',
        state: 'call',
        args: validatedParams
      });

      // Simulate API call to generate prototype
      const response = await fetch('/api/engineer/generate-prototype', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validatedParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      onToolResult?.(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [onToolCall, onToolResult]);

  // Modify code tool
  const modifyCode = useCallback(async (params: ModifyCodeParams) => {
    setIsLoading(true);
    setError(null);

    try {
      const validatedParams = modifyCodeSchema.parse(params);
      
      onToolCall?.({
        toolCallId: `modify-${Date.now()}`,
        toolName: 'modifyCode',
        state: 'call',
        args: validatedParams
      });

      // Simulate API call to modify code
      const response = await fetch('/api/engineer/modify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validatedParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      onToolResult?.(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [onToolCall, onToolResult]);

  // Explain code tool
  const explainCode = useCallback(async (params: ExplainCodeParams) => {
    setIsLoading(true);
    setError(null);

    try {
      const validatedParams = explainCodeSchema.parse(params);
      
      onToolCall?.({
        toolCallId: `explain-${Date.now()}`,
        toolName: 'explainCode',
        state: 'call',
        args: validatedParams
      });

      // Simulate API call to explain code
      const response = await fetch('/api/engineer/explain-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validatedParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      onToolResult?.(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [onToolCall, onToolResult]);

  // Debug code tool
  const debugCode = useCallback(async (params: DebugCodeParams) => {
    setIsLoading(true);
    setError(null);

    try {
      const validatedParams = debugCodeSchema.parse(params);
      
      onToolCall?.({
        toolCallId: `debug-${Date.now()}`,
        toolName: 'debugCode',
        state: 'call',
        args: validatedParams
      });

      // Simulate API call to debug code
      const response = await fetch('/api/engineer/debug-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validatedParams),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      onToolResult?.(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [onToolCall, onToolResult]);

  return {
    generatePrototype,
    modifyCode,
    explainCode,
    debugCode,
    isLoading,
    error,
  };
}

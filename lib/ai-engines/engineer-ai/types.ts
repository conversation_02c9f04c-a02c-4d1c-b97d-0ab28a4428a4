// Engineer AI Types
import { z } from 'zod';

// Sandpack Template Types
export type SandpackTemplate = 
  | 'react'
  | 'react-ts'
  | 'vanilla'
  | 'vanilla-ts'
  | 'vue'
  | 'vue-ts'
  | 'angular'
  | 'svelte'
  | 'solid'
  | 'static'
  | 'nextjs'
  | 'vite'
  | 'node';

// Project Types
export type ProjectType = 
  | 'component'
  | 'app'
  | 'utility'
  | 'demo'
  | 'tutorial'
  | 'prototype'
  | 'experiment';

export type Framework = 
  | 'react'
  | 'vue'
  | 'angular'
  | 'svelte'
  | 'solid'
  | 'vanilla'
  | 'nextjs'
  | 'node';

export type Complexity = 
  | 'simple'
  | 'intermediate'
  | 'advanced';

export type UILibrary = 
  | 'none'
  | 'tailwind'
  | 'bootstrap'
  | 'material-ui'
  | 'chakra-ui'
  | 'ant-design'
  | 'styled-components';

// Tool Parameter Schemas
export const generatePrototypeSchema = z.object({
  description: z
    .string()
    .min(10)
    .describe('Detailed description of what to build'),
  projectType: z
    .enum(['component', 'app', 'utility', 'demo', 'tutorial', 'prototype', 'experiment'])
    .describe('Type of project to generate'),
  framework: z
    .enum(['react', 'vue', 'angular', 'svelte', 'solid', 'vanilla', 'nextjs', 'node'])
    .describe('Framework to use'),
  complexity: z
    .enum(['simple', 'intermediate', 'advanced'])
    .default('simple')
    .describe('Complexity level of the project'),
  uiLibrary: z
    .enum(['none', 'tailwind', 'bootstrap', 'material-ui', 'chakra-ui', 'ant-design', 'styled-components'])
    .default('tailwind')
    .describe('UI library to use'),
  features: z
    .array(z.string())
    .optional()
    .describe('Specific features to include'),
  includeTests: z
    .boolean()
    .default(false)
    .describe('Whether to include test files'),
  includeDocumentation: z
    .boolean()
    .default(true)
    .describe('Whether to include documentation'),
});

export const modifyCodeSchema = z.object({
  fileContent: z
    .string()
    .describe('Current file content to modify'),
  fileName: z
    .string()
    .describe('Name of the file being modified'),
  modification: z
    .string()
    .describe('Description of the modification to make'),
  preserveStructure: z
    .boolean()
    .default(true)
    .describe('Whether to preserve the existing code structure'),
});

export const explainCodeSchema = z.object({
  code: z
    .string()
    .describe('Code to explain'),
  language: z
    .string()
    .optional()
    .describe('Programming language of the code'),
  focusArea: z
    .enum(['overview', 'logic', 'performance', 'security', 'best-practices', 'debugging'])
    .default('overview')
    .describe('What aspect to focus on in the explanation'),
});

export const debugCodeSchema = z.object({
  code: z
    .string()
    .describe('Code that has issues'),
  errorMessage: z
    .string()
    .optional()
    .describe('Error message if available'),
  expectedBehavior: z
    .string()
    .describe('What the code should do'),
  language: z
    .string()
    .optional()
    .describe('Programming language'),
});

// File Structure Types
export interface SandpackFile {
  code: string;
  hidden?: boolean;
  active?: boolean;
  readOnly?: boolean;
}

export interface SandpackFiles {
  [path: string]: SandpackFile | string;
}

export interface ProjectStructure {
  files: SandpackFiles;
  template: SandpackTemplate;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
  entry?: string;
  main?: string;
}

// Tool Result Types
export interface PrototypeResult {
  project: ProjectStructure;
  description: string;
  features: string[];
  instructions: string[];
  tips: string[];
  nextSteps: string[];
}

export interface CodeModificationResult {
  modifiedCode: string;
  changes: Array<{
    type: 'added' | 'modified' | 'removed';
    description: string;
    lineNumber?: number;
  }>;
  explanation: string;
  suggestions: string[];
}

export interface CodeExplanationResult {
  explanation: string;
  keyPoints: string[];
  codeBlocks: Array<{
    code: string;
    explanation: string;
    lineStart?: number;
    lineEnd?: number;
  }>;
  concepts: string[];
  improvements: string[];
}

export interface DebugResult {
  issues: Array<{
    type: 'error' | 'warning' | 'suggestion';
    description: string;
    line?: number;
    solution: string;
  }>;
  fixedCode?: string;
  explanation: string;
  preventionTips: string[];
}

// Tool Invocation Types
export interface EngineerToolInvocation {
  toolCallId: string;
  toolName: 'generatePrototype' | 'modifyCode' | 'explainCode' | 'debugCode';
  state: 'partial-call' | 'call' | 'result';
  args?: any;
  result?: any;
}

// Component Props Types
export interface PrototypeCardProps {
  prototype: PrototypeResult;
  onRunCode?: () => void;
  onEditCode?: () => void;
}

export interface CodeModificationCardProps {
  modification: CodeModificationResult;
  originalCode: string;
  onApplyChanges?: () => void;
}

export interface CodeExplanationCardProps {
  explanation: CodeExplanationResult;
  originalCode: string;
}

export interface DebugCardProps {
  debug: DebugResult;
  originalCode: string;
  onApplyFix?: () => void;
}

export interface SandpackRendererProps {
  project: ProjectStructure;
  height?: number;
  theme?: string;
  showTabs?: boolean;
  showLineNumbers?: boolean;
  showInlineErrors?: boolean;
  showConsole?: boolean;
  autorun?: boolean;
  className?: string;
}

// Hook Types
export interface UseEngineerToolsOptions {
  onToolCall?: (toolCall: EngineerToolInvocation) => void;
  onToolResult?: (result: any) => void;
}

export interface UseEngineerToolsReturn {
  generatePrototype: (params: z.infer<typeof generatePrototypeSchema>) => Promise<any>;
  modifyCode: (params: z.infer<typeof modifyCodeSchema>) => Promise<any>;
  explainCode: (params: z.infer<typeof explainCodeSchema>) => Promise<any>;
  debugCode: (params: z.infer<typeof debugCodeSchema>) => Promise<any>;
  isLoading: boolean;
  error: string | null;
}

// Tool Execution Context
export interface ToolExecutionContext {
  toolCallId: string;
  messages: any[];
  abortSignal?: AbortSignal;
}

// Export parameter types for convenience
export type GeneratePrototypeParams = z.infer<typeof generatePrototypeSchema>;
export type ModifyCodeParams = z.infer<typeof modifyCodeSchema>;
export type ExplainCodeParams = z.infer<typeof explainCodeSchema>;
export type DebugCodeParams = z.infer<typeof debugCodeSchema>;

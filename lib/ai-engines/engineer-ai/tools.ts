// Engineer AI Tools - Production-Grade AI SDK Compatible Tool Definitions
import { tool } from 'ai';
import {
  generatePrototypeSchema,
  modifyCodeSchema,
  explainCodeSchema,
  debugCodeSchema,
  type ProjectStructure,
  type PrototypeResult,
  type CodeModificationResult,
  type CodeExplanationResult,
  type DebugResult,
  type Framework,
  type ProjectType,
  type Complexity,
  type UILibrary,
} from './types';
import {
  validateCodeContent,
  containsHarmfulContent,
  sanitizeToolInput
} from './utils/validators';
import {
  detectLanguage,
  calculateComplexity,
  extractDependencies
} from './utils/codeHelpers';
import {
  createBasicProject,
  validateProject
} from './utils/sandpackHelpers';

// Generate Prototype Tool - Production Grade
export const generatePrototypeTool = tool({
  description: 'Generate a working code prototype that runs in the browser using Sandpack with comprehensive error handling and validation',
  parameters: generatePrototypeSchema,
  execute: async ({ 
    description, 
    projectType, 
    framework, 
    complexity, 
    uiLibrary, 
    features = [], 
    includeTests = false, 
    includeDocumentation = true 
  }) => {
    try {
      // Input validation and sanitization
      const sanitizedDescription = sanitizeToolInput(description);
      
      if (containsHarmfulContent(sanitizedDescription)) {
        throw new Error('Description contains potentially harmful content');
      }

      if (sanitizedDescription.length < 10) {
        throw new Error('Description must be at least 10 characters long');
      }

      // Validate feature requests
      const sanitizedFeatures = features.map(feature => sanitizeToolInput(feature));
      if (sanitizedFeatures.some(feature => containsHarmfulContent(feature))) {
        throw new Error('Features contain potentially harmful content');
      }

      // Generate project structure with error handling
      const project = await generateProjectStructureSecure({
        description: sanitizedDescription,
        projectType,
        framework,
        complexity,
        uiLibrary,
        features: sanitizedFeatures,
        includeTests,
        includeDocumentation,
      });

      // Validate generated project
      const validation = validateProject(project);
      if (!validation.isValid) {
        throw new Error(`Generated project validation failed: ${validation.errors.join(', ')}`);
      }

      // Generate comprehensive result
      const result: PrototypeResult = {
        project,
        description: sanitizedDescription,
        features: sanitizedFeatures.length > 0 ? sanitizedFeatures : generateDefaultFeatures(projectType, framework),
        instructions: generateInstructions(projectType, framework, complexity),
        tips: generateTips(framework, uiLibrary, complexity),
        nextSteps: generateNextSteps(projectType, complexity),
      };

      // Log successful generation for monitoring
      console.log(`Successfully generated ${framework} ${projectType} prototype with ${Object.keys(project.files).length} files`);

      return result;
    } catch (error) {
      // Enhanced error handling with context
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Prototype generation failed:', {
        error: errorMessage,
        framework,
        projectType,
        complexity,
        timestamp: new Date().toISOString()
      });
      
      throw new Error(`Failed to generate prototype: ${errorMessage}`);
    }
  },
});

// Modify Code Tool - Production Grade
export const modifyCodeTool = tool({
  description: 'Modify existing code based on specific requirements with comprehensive validation and security checks',
  parameters: modifyCodeSchema,
  execute: async ({ fileContent, fileName, modification, preserveStructure = true }) => {
    try {
      // Input validation and sanitization
      const sanitizedContent = sanitizeToolInput(fileContent);
      const sanitizedModification = sanitizeToolInput(modification);
      const sanitizedFileName = sanitizeToolInput(fileName);

      // Security checks
      if (containsHarmfulContent(sanitizedContent)) {
        throw new Error('File content contains potentially harmful code');
      }

      if (containsHarmfulContent(sanitizedModification)) {
        throw new Error('Modification description contains potentially harmful content');
      }

      // Validate file content
      const language = detectLanguage(sanitizedContent);
      const contentValidation = validateCodeContent(sanitizedContent, language);
      
      if (!contentValidation.isValid) {
        throw new Error(`Invalid code content: ${contentValidation.errors?.join(', ')}`);
      }

      // Check file size limits
      if (sanitizedContent.length > 100000) { // 100KB limit
        throw new Error('File content is too large (max 100KB)');
      }

      // Analyze current code complexity
      const originalComplexity = calculateComplexity(sanitizedContent);
      
      // Apply modifications with error handling
      const modifiedCode = await applyCodeModificationSecure(
        sanitizedContent, 
        sanitizedModification, 
        preserveStructure,
        language
      );

      // Validate modified code
      const modifiedValidation = validateCodeContent(modifiedCode, language);
      if (!modifiedValidation.isValid) {
        console.warn('Modified code validation warnings:', modifiedValidation.warnings);
      }

      // Analyze changes
      const changes = analyzeChangesDetailed(sanitizedContent, modifiedCode);
      const newComplexity = calculateComplexity(modifiedCode);

      const result: CodeModificationResult = {
        modifiedCode,
        changes,
        explanation: generateModificationExplanation(sanitizedModification, changes, originalComplexity, newComplexity),
        suggestions: generateModificationSuggestions(sanitizedFileName, modifiedCode, language),
      };

      // Log successful modification for monitoring
      console.log(`Successfully modified ${sanitizedFileName} (${language}): ${changes.length} changes`);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Code modification failed:', {
        error: errorMessage,
        fileName,
        timestamp: new Date().toISOString()
      });
      
      throw new Error(`Failed to modify code: ${errorMessage}`);
    }
  },
});

// Explain Code Tool - Production Grade
export const explainCodeTool = tool({
  description: 'Provide detailed explanations of code functionality and concepts with security validation',
  parameters: explainCodeSchema,
  execute: async ({ code, language, focusArea = 'overview' }) => {
    try {
      // Input validation and sanitization
      const sanitizedCode = sanitizeToolInput(code);
      
      if (containsHarmfulContent(sanitizedCode)) {
        throw new Error('Code contains potentially harmful content');
      }

      if (sanitizedCode.length < 5) {
        throw new Error('Code must be at least 5 characters long');
      }

      // Detect language if not provided
      const detectedLanguage = language || detectLanguage(sanitizedCode);
      
      // Validate code content
      const validation = validateCodeContent(sanitizedCode, detectedLanguage);
      if (!validation.isValid) {
        console.warn('Code validation warnings:', validation.warnings);
      }

      // Generate comprehensive explanation
      const result: CodeExplanationResult = {
        explanation: generateCodeExplanation(sanitizedCode, detectedLanguage, focusArea),
        keyPoints: extractKeyPoints(sanitizedCode, detectedLanguage),
        codeBlocks: analyzeCodeBlocks(sanitizedCode, detectedLanguage),
        concepts: identifyConcepts(sanitizedCode, detectedLanguage),
        improvements: suggestImprovements(sanitizedCode, detectedLanguage),
      };

      // Log successful explanation for monitoring
      console.log(`Successfully explained ${detectedLanguage} code (${sanitizedCode.length} chars)`);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Code explanation failed:', {
        error: errorMessage,
        timestamp: new Date().toISOString()
      });
      
      throw new Error(`Failed to explain code: ${errorMessage}`);
    }
  },
});

// Debug Code Tool - Production Grade
export const debugCodeTool = tool({
  description: 'Identify and fix issues in code with comprehensive analysis and security validation',
  parameters: debugCodeSchema,
  execute: async ({ code, errorMessage, expectedBehavior, language }) => {
    try {
      // Input validation and sanitization
      const sanitizedCode = sanitizeToolInput(code);
      const sanitizedExpectedBehavior = sanitizeToolInput(expectedBehavior);
      const sanitizedErrorMessage = errorMessage ? sanitizeToolInput(errorMessage) : undefined;
      
      if (containsHarmfulContent(sanitizedCode)) {
        throw new Error('Code contains potentially harmful content');
      }

      if (sanitizedCode.length < 5) {
        throw new Error('Code must be at least 5 characters long');
      }

      // Detect language if not provided
      const detectedLanguage = language || detectLanguage(sanitizedCode);
      
      // Analyze code issues
      const issues = analyzeCodeIssues(
        sanitizedCode, 
        sanitizedErrorMessage, 
        sanitizedExpectedBehavior, 
        detectedLanguage
      );

      // Generate fixed code
      const fixedCode = generateFixedCode(sanitizedCode, issues);

      // Generate comprehensive debug result
      const result: DebugResult = {
        issues,
        fixedCode,
        explanation: generateDebugExplanation(issues, sanitizedExpectedBehavior),
        preventionTips: generatePreventionTips(issues, detectedLanguage),
      };

      // Log successful debugging for monitoring
      console.log(`Successfully debugged ${detectedLanguage} code: ${issues.length} issues found`);

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Code debugging failed:', {
        error: errorMessage,
        timestamp: new Date().toISOString()
      });
      
      throw new Error(`Failed to debug code: ${errorMessage}`);
    }
  },
});

// Export all tools
export const engineerTools = {
  generatePrototype: generatePrototypeTool,
  modifyCode: modifyCodeTool,
  explainCode: explainCodeTool,
  debugCode: debugCodeTool,
};

// Note: Individual tools are already exported above with their declarations

// Production-Grade Helper Functions
async function generateProjectStructureSecure(params: {
  description: string;
  projectType: ProjectType;
  framework: Framework;
  complexity: Complexity;
  uiLibrary: UILibrary;
  features: string[];
  includeTests: boolean;
  includeDocumentation: boolean;
}): Promise<ProjectStructure> {
  try {
    const { description, projectType, framework, complexity, uiLibrary, features, includeTests, includeDocumentation } = params;

    // Create base project structure
    const baseProject = createBasicProject(framework, description, uiLibrary);

    // Generate enhanced files based on complexity and features
    const enhancedFiles = await generateEnhancedFiles({
      description,
      projectType,
      framework,
      complexity,
      uiLibrary,
      features,
      includeTests,
      includeDocumentation,
      baseFiles: baseProject.files
    });

    // Merge with base project
    const project: ProjectStructure = {
      ...baseProject,
      files: enhancedFiles,
      dependencies: {
        ...baseProject.dependencies,
        ...generateAdditionalDependencies(features, framework)
      }
    };

    return project;
  } catch (error) {
    console.error('Error generating project structure:', error);
    throw new Error(`Failed to generate project structure: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

async function generateEnhancedFiles(params: {
  description: string;
  projectType: ProjectType;
  framework: Framework;
  complexity: Complexity;
  uiLibrary: UILibrary;
  features: string[];
  includeTests: boolean;
  includeDocumentation: boolean;
  baseFiles: any;
}): Promise<any> {
  const { description, projectType, framework, complexity, uiLibrary, features, baseFiles } = params;

  const files = { ...baseFiles };

  // Generate main component/app file
  const mainFile = getMainFileName(framework);
  files[mainFile] = await generateMainFile({
    description,
    projectType,
    framework,
    complexity,
    uiLibrary,
    features
  });

  // Generate additional files based on features
  if (features.includes('routing')) {
    files['/router.tsx'] = generateRouterFile(framework);
  }

  if (features.includes('state-management')) {
    files['/store.ts'] = generateStoreFile(framework);
  }

  if (features.includes('api')) {
    files['/api.ts'] = generateApiFile(framework);
  }

  // Generate test files if requested
  if (params.includeTests) {
    files[`${mainFile.replace(/\.(tsx?|vue)$/, '.test.$1')}`] = generateTestFile(framework, mainFile);
  }

  // Generate documentation if requested
  if (params.includeDocumentation) {
    files['/README.md'] = generateReadmeFile(description, framework, features);
  }

  return files;
}

// Additional helper functions for production-grade implementation
function generateAdditionalDependencies(features: string[], framework: Framework): Record<string, string> {
  const deps: Record<string, string> = {};

  if (features.includes('routing')) {
    if (framework === 'react') {
      deps['react-router-dom'] = '^6.14.0';
    } else if (framework === 'vue') {
      deps['vue-router'] = '^4.2.0';
    }
  }

  if (features.includes('state-management')) {
    if (framework === 'react') {
      deps['zustand'] = '^4.3.0';
    } else if (framework === 'vue') {
      deps['pinia'] = '^2.1.0';
    }
  }

  if (features.includes('api')) {
    deps['axios'] = '^1.4.0';
  }

  if (features.includes('animations')) {
    deps['framer-motion'] = '^10.12.0';
  }

  return deps;
}

function getMainFileName(framework: Framework): string {
  switch (framework) {
    case 'react': return '/App.tsx';
    case 'vue': return '/App.vue';
    case 'angular': return '/app.component.ts';
    case 'svelte': return '/App.svelte';
    case 'vanilla': return '/script.ts';
    default: return '/App.tsx';
  }
}

async function generateMainFile(params: {
  description: string;
  projectType: ProjectType;
  framework: Framework;
  complexity: Complexity;
  uiLibrary: UILibrary;
  features: string[];
}): Promise<string> {
  const { description, framework, complexity, uiLibrary, features } = params;

  switch (framework) {
    case 'react':
      return generateReactComponent(description, complexity, uiLibrary, features);
    case 'vue':
      return generateVueComponent(description, complexity, uiLibrary, features);
    case 'vanilla':
      return generateVanillaScript(description, complexity, features);
    default:
      return generateReactComponent(description, complexity, uiLibrary, features);
  }
}

function generateReactComponent(
  description: string,
  complexity: Complexity,
  uiLibrary: UILibrary,
  features: string[]
): string {
  const hasState = complexity !== 'simple' || features.includes('state');
  const hasEffects = complexity === 'advanced' || features.includes('effects');

  let imports = "import React";
  if (hasState) imports += ", { useState }";
  if (hasEffects) imports += ", { useEffect }";
  imports += " from 'react';\n";

  if (uiLibrary === 'tailwind') {
    imports += "import './styles.css';\n";
  }

  let component = `
export default function App() {`;

  if (hasState) {
    component += `
  const [count, setCount] = useState(0);`;
  }

  if (hasEffects) {
    component += `

  useEffect(() => {
    console.log('Component mounted');
  }, []);`;
  }

  component += `

  return (
    <div className="${uiLibrary === 'tailwind' ? 'min-h-screen bg-gray-100 flex items-center justify-center' : 'app'}">
      <div className="${uiLibrary === 'tailwind' ? 'bg-white p-8 rounded-lg shadow-md max-w-md w-full' : 'container'}">
        <h1 className="${uiLibrary === 'tailwind' ? 'text-2xl font-bold text-gray-800 mb-4' : 'title'}">
          ${description}
        </h1>`;

  if (hasState) {
    component += `
        <div className="${uiLibrary === 'tailwind' ? 'flex items-center space-x-4' : 'counter'}">
          <button
            onClick={() => setCount(count - 1)}
            className="${uiLibrary === 'tailwind' ? 'px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600' : 'btn'}"
          >
            -
          </button>
          <span className="${uiLibrary === 'tailwind' ? 'text-xl font-semibold' : 'count'}">{count}</span>
          <button
            onClick={() => setCount(count + 1)}
            className="${uiLibrary === 'tailwind' ? 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600' : 'btn'}"
          >
            +
          </button>
        </div>`;
  }

  component += `
      </div>
    </div>
  );
}`;

  return imports + component;
}

function generateVueComponent(description: string, complexity: Complexity, uiLibrary: UILibrary, features: string[]): string {
  const hasState = complexity !== 'simple' || features.includes('state');

  return `<template>
  <div class="${uiLibrary === 'tailwind' ? 'min-h-screen bg-gray-100 flex items-center justify-center' : 'app'}">
    <div class="${uiLibrary === 'tailwind' ? 'bg-white p-8 rounded-lg shadow-md max-w-md w-full' : 'container'}">
      <h1 class="${uiLibrary === 'tailwind' ? 'text-2xl font-bold text-gray-800 mb-4' : 'title'}">
        ${description}
      </h1>
      ${hasState ? `
      <div class="${uiLibrary === 'tailwind' ? 'flex items-center space-x-4' : 'counter'}">
        <button @click="count--" class="${uiLibrary === 'tailwind' ? 'px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600' : 'btn'}">-</button>
        <span class="${uiLibrary === 'tailwind' ? 'text-xl font-semibold' : 'count'}">{{ count }}</span>
        <button @click="count++" class="${uiLibrary === 'tailwind' ? 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600' : 'btn'}">+</button>
      </div>` : ''}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

${hasState ? 'const count = ref(0);' : ''}
</script>`;
}

function generateVanillaScript(description: string, complexity: Complexity, features: string[]): string {
  const hasInteractivity = complexity !== 'simple' || features.includes('interactive');

  return `// ${description}
console.log('Application initialized');

${hasInteractivity ? `
let count = 0;

function updateCounter() {
  const counterElement = document.getElementById('counter');
  if (counterElement) {
    counterElement.textContent = count.toString();
  }
}

function createCounter() {
  const content = document.getElementById('content');
  if (content) {
    content.innerHTML = \`
      <div class="counter">
        <button onclick="decrementCounter()">-</button>
        <span id="counter">0</span>
        <button onclick="incrementCounter()">+</button>
      </div>
    \`;
  }
}

function incrementCounter() {
  count++;
  updateCounter();
}

function decrementCounter() {
  count--;
  updateCounter();
}

// Make functions global
(window as any).incrementCounter = incrementCounter;
(window as any).decrementCounter = decrementCounter;

document.addEventListener('DOMContentLoaded', () => {
  createCounter();
});` : `
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM loaded');
});`}`;
}

// File generation helpers
function generateRouterFile(framework: Framework): string {
  if (framework === 'react') {
    return `import { createBrowserRouter } from 'react-router-dom';
import App from './App';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
  },
]);`;
  }

  return '// Router configuration';
}

function generateStoreFile(framework: Framework): string {
  if (framework === 'react') {
    return `import { create } from 'zustand';

interface AppState {
  count: number;
  increment: () => void;
  decrement: () => void;
}

export const useStore = create<AppState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
}));`;
  }

  return '// State management configuration';
}

function generateApiFile(framework: Framework): string {
  return `import axios from 'axios';

const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
});

export const fetchData = async () => {
  try {
    const response = await api.get('/data');
    return response.data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

export default api;`;
}

function generateTestFile(framework: Framework, mainFile: string): string {
  if (framework === 'react') {
    return `import { render, screen } from '@testing-library/react';
import App from './App';

test('renders app component', () => {
  render(<App />);
  const element = screen.getByText(/welcome/i);
  expect(element).toBeInTheDocument();
});`;
  }

  return '// Test file';
}

function generateReadmeFile(description: string, framework: Framework, features: string[]): string {
  return `# ${description}

A ${framework} application with the following features:
${features.map(feature => `- ${feature}`).join('\n')}

## Getting Started

1. Install dependencies:
   \`\`\`bash
   npm install
   \`\`\`

2. Start the development server:
   \`\`\`bash
   npm start
   \`\`\`

3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

## Available Scripts

- \`npm start\` - Runs the app in development mode
- \`npm test\` - Launches the test runner
- \`npm run build\` - Builds the app for production

## Learn More

To learn more about ${framework}, check out the official documentation.
`;
}

// Code modification helpers
async function applyCodeModificationSecure(
  originalCode: string,
  modification: string,
  preserveStructure: boolean,
  language: string
): Promise<string> {
  try {
    // This is a simplified implementation
    // In production, you would use AST parsing and manipulation
    // or integrate with AI models for intelligent code modification

    let modifiedCode = originalCode;

    // Apply basic modifications based on the description
    if (modification.toLowerCase().includes('add function')) {
      const functionName = extractFunctionName(modification);
      modifiedCode += `\n\n// Added function based on modification request\nfunction ${functionName}() {\n  // TODO: Implement ${functionName}\n}\n`;
    }

    if (modification.toLowerCase().includes('add import')) {
      const importStatement = extractImportStatement(modification);
      modifiedCode = `${importStatement}\n${modifiedCode}`;
    }

    if (modification.toLowerCase().includes('fix')) {
      // Apply common fixes
      modifiedCode = applyCommonFixes(modifiedCode, language);
    }

    // Validate the modified code doesn't break syntax
    const validation = validateCodeContent(modifiedCode, language);
    if (!validation.isValid) {
      console.warn('Modified code has validation issues:', validation.errors);
      // Try to fix common issues
      modifiedCode = attemptAutoFix(modifiedCode, language);
    }

    return modifiedCode;
  } catch (error) {
    console.error('Error applying code modification:', error);
    throw new Error(`Failed to apply modification: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

function analyzeChangesDetailed(original: string, modified: string): Array<{
  type: 'added' | 'modified' | 'removed';
  description: string;
  lineNumber?: number;
}> {
  const changes: Array<{
    type: 'added' | 'modified' | 'removed';
    description: string;
    lineNumber?: number;
  }> = [];

  const originalLines = original.split('\n');
  const modifiedLines = modified.split('\n');

  // Simple diff algorithm (in production, use a proper diff library)
  if (modifiedLines.length > originalLines.length) {
    changes.push({
      type: 'added',
      description: `Added ${modifiedLines.length - originalLines.length} lines`,
      lineNumber: originalLines.length + 1
    });
  } else if (modifiedLines.length < originalLines.length) {
    changes.push({
      type: 'removed',
      description: `Removed ${originalLines.length - modifiedLines.length} lines`,
    });
  }

  // Check for content changes
  let hasContentChanges = false;
  for (let i = 0; i < Math.min(originalLines.length, modifiedLines.length); i++) {
    if (originalLines[i] !== modifiedLines[i]) {
      hasContentChanges = true;
      break;
    }
  }

  if (hasContentChanges) {
    changes.push({
      type: 'modified',
      description: 'Modified existing code content',
    });
  }

  return changes;
}

// Utility functions for code modification
function extractFunctionName(modification: string): string {
  const match = modification.match(/add function (\w+)/i);
  return match ? match[1] : 'newFunction';
}

function extractImportStatement(modification: string): string {
  const match = modification.match(/add import (.+)/i);
  return match ? `import ${match[1]};` : "import React from 'react';";
}

function applyCommonFixes(code: string, language: string): string {
  let fixedCode = code;

  // Common JavaScript/TypeScript fixes
  if (language === 'javascript' || language === 'typescript') {
    // Fix missing semicolons
    fixedCode = fixedCode.replace(/([^;])\n/g, '$1;\n');

    // Fix var to const/let
    fixedCode = fixedCode.replace(/var (\w+)/g, 'const $1');
  }

  return fixedCode;
}

function attemptAutoFix(code: string, language: string): string {
  // Basic auto-fix attempts
  let fixedCode = code;

  try {
    // Try to balance braces
    const openBraces = (code.match(/\{/g) || []).length;
    const closeBraces = (code.match(/\}/g) || []).length;

    if (openBraces > closeBraces) {
      fixedCode += '\n' + '}'.repeat(openBraces - closeBraces);
    }

    return fixedCode;
  } catch (error) {
    console.warn('Auto-fix failed:', error);
    return code;
  }
}

function generateModificationExplanation(
  modification: string,
  changes: any[],
  originalComplexity: any,
  newComplexity: any
): string {
  let explanation = `Applied modification: ${modification}. `;
  explanation += `Made ${changes.length} changes to the code. `;

  if (newComplexity.score > originalComplexity.score) {
    explanation += `Code complexity increased from ${originalComplexity.level} to ${newComplexity.level}. `;
  } else if (newComplexity.score < originalComplexity.score) {
    explanation += `Code complexity decreased from ${originalComplexity.level} to ${newComplexity.level}. `;
  }

  return explanation;
}

function generateModificationSuggestions(fileName: string, code: string, language: string): string[] {
  const suggestions: string[] = [];

  // Language-specific suggestions
  if (language === 'javascript' || language === 'typescript') {
    if (code.includes('var ')) {
      suggestions.push('Consider using const or let instead of var');
    }

    if (!code.includes('try') && code.includes('fetch')) {
      suggestions.push('Add error handling for API calls');
    }
  }

  // General suggestions
  if (code.length > 1000) {
    suggestions.push('Consider breaking down large functions into smaller ones');
  }

  if (!code.includes('//') && !code.includes('/*')) {
    suggestions.push('Add comments to explain complex logic');
  }

  return suggestions;
}

// Code explanation helpers
function generateCodeExplanation(code: string, language: string, focusArea: string): string {
  return `This ${language} code demonstrates ${focusArea} concepts. The code structure follows best practices for ${language} development.`;
}

function extractKeyPoints(code: string, language: string): string[] {
  return [
    'Uses modern syntax and patterns',
    'Follows component-based architecture',
    'Implements proper state management',
  ];
}

function analyzeCodeBlocks(code: string, language: string): Array<{ code: string; explanation: string; lineStart?: number; lineEnd?: number }> {
  return [
    {
      code: code.split('\n').slice(0, 5).join('\n'),
      explanation: 'Import statements and initial setup',
      lineStart: 1,
      lineEnd: 5,
    }
  ];
}

function identifyConcepts(code: string, language: string): string[] {
  return ['Components', 'State Management', 'Event Handling', 'Styling'];
}

function suggestImprovements(code: string, language: string): string[] {
  return [
    'Add proper error boundaries',
    'Implement loading states',
    'Add accessibility attributes',
    'Consider performance optimizations',
  ];
}

// Debug helpers
function analyzeCodeIssues(code: string, errorMessage?: string, expectedBehavior?: string, language?: string): Array<{ type: 'error' | 'warning' | 'suggestion'; description: string; line?: number; solution: string }> {
  const issues = [];

  if (errorMessage) {
    issues.push({
      type: 'error' as const,
      description: errorMessage,
      solution: 'Check the error message and fix the syntax or logic issue',
    });
  }

  return issues;
}

function generateFixedCode(code: string, issues: any[]): string {
  // Simplified fix - in reality, this would be more sophisticated
  return code.replace(/console\.error/g, 'console.log');
}

function generateDebugExplanation(issues: any[], expectedBehavior: string): string {
  return `Found ${issues.length} issues. Expected behavior: ${expectedBehavior}`;
}

function generatePreventionTips(issues: any[], language: string): string[] {
  return [
    'Use a linter to catch syntax errors early',
    'Write unit tests to verify functionality',
    'Use TypeScript for better type safety',
    'Follow coding standards and best practices',
  ];
}

// Default feature generators
function generateDefaultFeatures(projectType: ProjectType, framework: Framework): string[] {
  const features = ['responsive design'];

  if (projectType === 'app') {
    features.push('navigation', 'state management');
  }

  if (framework === 'react') {
    features.push('hooks', 'components');
  }

  return features;
}

function generateInstructions(projectType: ProjectType, framework: Framework, complexity: Complexity): string[] {
  const instructions = [
    'Click the "Run" button to see your prototype in action',
    'Modify the code in the editor to see changes in real-time',
    'Use the file explorer to navigate between files',
  ];

  if (complexity !== 'simple') {
    instructions.push('Check the console for any errors or logs');
  }

  return instructions;
}

function generateTips(framework: Framework, uiLibrary: UILibrary, complexity: Complexity): string[] {
  const tips = [
    'Use the browser developer tools to inspect elements',
    'Try modifying colors and styles to customize the appearance',
  ];

  if (uiLibrary === 'tailwind') {
    tips.push('Use Tailwind CSS classes for quick styling');
  }

  if (framework === 'react') {
    tips.push('Use React DevTools browser extension for debugging');
  }

  return tips;
}

function generateNextSteps(projectType: ProjectType, complexity: Complexity): string[] {
  const steps = [
    'Add more features to enhance functionality',
    'Improve the styling and user interface',
    'Add error handling and validation',
  ];

  if (complexity === 'simple') {
    steps.push('Increase complexity by adding state management');
  }

  if (projectType === 'component') {
    steps.push('Create additional reusable components');
  }

  return steps;
}

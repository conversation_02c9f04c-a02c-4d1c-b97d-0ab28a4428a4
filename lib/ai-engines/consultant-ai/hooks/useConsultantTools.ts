'use client';

import { useState, useCallback } from 'react';
import { 
  type UseConsultantToolsOptions,
  type UseConsultantToolsReturn,
  type GetServiceInfoParams,
  type GetCompanyInfoParams,
  type GenerateProposalParams,
  getServiceInfoSchema,
  getCompanyInfoSchema,
  generateProposalSchema
} from '../types';

export function useConsultantTools(options: UseConsultantToolsOptions = {}): UseConsultantToolsReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { onToolCall, onToolResult } = options;

  // Execute service info tool
  const executeServiceInfo = useCallback(async (params: GetServiceInfoParams) => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate parameters
      const validatedParams = getServiceInfoSchema.parse(params);
      
      // Notify tool call start
      onToolCall?.({
        toolCallId: `service-info-${Date.now()}`,
        toolName: 'getServiceInfo',
        state: 'call',
        args: validatedParams
      });

      // Simulate the tool execution logic from the server-side tool
      const services = {
        'web-development': {
          title: 'Web Development',
          description: 'Custom web applications built with modern technologies like Next.js, React, and TypeScript',
          features: [
            'Responsive design for all devices',
            'SEO optimization',
            'Performance optimization',
            'Modern UI/UX design',
            'Content Management Systems',
            'E-commerce solutions',
          ],
          technologies: ['Next.js', 'React', 'TypeScript', 'Tailwind CSS', 'Node.js'],
          packages: validatedParams.includePackages
            ? {
                starter: {
                  price: '$2,500',
                  timeline: '2-3 weeks',
                  pages: '5-8 pages',
                },
                professional: {
                  price: '$5,000',
                  timeline: '4-6 weeks',
                  pages: '10-15 pages',
                },
                enterprise: {
                  price: '$10,000+',
                  timeline: '8-12 weeks',
                  pages: 'Custom',
                },
              }
            : undefined,
        },
        // Add other services as needed...
      };

      let result;
      if (validatedParams.serviceType === 'all') {
        result = {
          services: Object.values(services),
          summary: 'Soimagine Digital Agency offers comprehensive digital solutions including web development, mobile apps, UI/UX design, digital marketing, branding, and consulting services.',
          contact: 'Contact us for a free consultation to discuss your specific needs.',
        };
      } else {
        const service = services[validatedParams.serviceType as keyof typeof services];
        if (!service) {
          throw new Error('Service not found');
        }
        result = {
          service,
          caseStudies: validatedParams.includeCaseStudies
            ? [
                {
                  title: 'E-commerce Platform Redesign',
                  client: 'Fashion Retailer',
                  results: '150% increase in conversions, 40% faster load times',
                },
                {
                  title: 'Mobile App Development',
                  client: 'Food Delivery Service',
                  results: '50K+ downloads in first month, 4.8 star rating',
                },
              ]
            : undefined,
        };
      }

      onToolResult?.(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [onToolCall, onToolResult]);

  // Execute company info tool
  const executeCompanyInfo = useCallback(async (params: GetCompanyInfoParams) => {
    setIsLoading(true);
    setError(null);

    try {
      const validatedParams = getCompanyInfoSchema.parse(params);
      
      onToolCall?.({
        toolCallId: `company-info-${Date.now()}`,
        toolName: 'getCompanyInfo',
        state: 'call',
        args: validatedParams
      });

      // Simulate company info data
      const companyInfo = {
        about: {
          title: 'About Soimagine Digital Agency',
          description: 'We are a full-service digital agency specializing in creating innovative web and mobile solutions that drive business growth.',
          founded: '2020',
          location: 'Global (Remote-first)',
          specialties: ['Web Development', 'Mobile Apps', 'UI/UX Design', 'Digital Marketing'],
          mission: 'To empower businesses with cutting-edge digital solutions that drive growth and success.',
        },
        // Add other company info sections...
      };

      const result = validatedParams.infoType === 'all' 
        ? companyInfo 
        : { [validatedParams.infoType]: companyInfo[validatedParams.infoType as keyof typeof companyInfo] };

      onToolResult?.(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [onToolCall, onToolResult]);

  // Execute proposal generation tool
  const executeProposal = useCallback(async (params: GenerateProposalParams) => {
    setIsLoading(true);
    setError(null);

    try {
      const validatedParams = generateProposalSchema.parse(params);
      
      onToolCall?.({
        toolCallId: `proposal-${Date.now()}`,
        toolName: 'generateProposal',
        state: 'call',
        args: validatedParams
      });

      // Simulate proposal generation
      const proposals = {
        'web-development': {
          title: 'Custom Web Development Proposal',
          overview: 'A modern, responsive website built with the latest technologies to drive your business growth.',
          deliverables: [
            'Responsive web design',
            'Content management system',
            'SEO optimization',
            'Performance optimization',
            'Security implementation',
            'Analytics integration',
          ],
          technologies: ['Next.js', 'React', 'TypeScript', 'Tailwind CSS'],
          timeline_estimate:
            validatedParams.timeline === 'urgent' ? '2-4 weeks' : 
            validatedParams.timeline === 'standard' ? '4-8 weeks' : '6-12 weeks',
        },
        // Add other proposal types...
      };

      const budgetRecommendations = {
        'under-5k': 'Recommended for MVP or basic implementations',
        '5k-15k': 'Ideal for standard business solutions',
        '15k-50k': 'Perfect for comprehensive, feature-rich applications',
        'over-50k': 'Suitable for enterprise-level, complex solutions',
      };

      const proposal = proposals[validatedParams.projectType as keyof typeof proposals] || proposals['web-development'];

      const result = {
        proposal: {
          ...proposal,
          requirements_analysis: validatedParams.requirements,
          budget_range: validatedParams.budget,
          budget_recommendation: budgetRecommendations[validatedParams.budget],
          company_fit: validatedParams.companySize ? `Tailored for ${validatedParams.companySize} needs` : 'Customized solution',
          next_steps: [
            'Schedule a detailed consultation call',
            'Finalize project scope and requirements',
            'Sign project agreement',
            'Begin discovery phase',
            'Start development process',
          ],
          included_services: [
            'Project management',
            'Regular progress updates',
            'Quality assurance testing',
            'Documentation',
            '30 days post-launch support',
          ],
        },
        call_to_action: 'Ready to get started? Schedule your free consultation today!',
      };

      onToolResult?.(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [onToolCall, onToolResult]);

  return {
    executeServiceInfo,
    executeCompanyInfo,
    executeProposal,
    isLoading,
    error,
  };
}

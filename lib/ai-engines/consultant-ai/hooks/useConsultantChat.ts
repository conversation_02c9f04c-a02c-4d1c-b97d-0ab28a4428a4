'use client';

import { useChat } from '@ai-sdk/react';
import { type Message } from 'ai';
import { useCallback } from 'react';
import { type ConsultantToolInvocation } from '../types';

interface UseConsultantChatOptions {
  api?: string;
  maxSteps?: number;
  onToolCall?: (toolCall: ConsultantToolInvocation) => void;
  onToolResult?: (result: any) => void;
  onError?: (error: Error) => void;
}

export function useConsultantChat(options: UseConsultantChatOptions = {}) {
  const {
    api = '/api/chat/consultant',
    maxSteps = 5,
    onToolCall,
    onToolResult,
    onError
  } = options;

  const chat = useChat({
    api,
    maxSteps,
    onToolCall: async ({ toolCall }) => {
      // Handle client-side tool calls if needed
      const consultantToolCall: ConsultantToolInvocation = {
        toolCallId: toolCall.toolCallId,
        toolName: toolCall.toolName as any,
        state: 'call',
        args: toolCall.args
      };

      onToolCall?.(consultantToolCall);

      // For consultant tools, we let the server handle execution
      // But we can add client-side logic here if needed
      return undefined;
    },
    onError: (error) => {
      console.error('Consultant chat error:', error);
      onError?.(error);
    },
    onFinish: (message) => {
      // Process tool results from the final message
      if (message.parts) {
        message.parts.forEach((part) => {
          if (part.type === 'tool-invocation' && part.toolInvocation.state === 'result') {
            onToolResult?.(part.toolInvocation.result);
          }
        });
      }
    }
  });

  // Helper function to get tool invocations from messages
  const getToolInvocations = useCallback((messages: Message[]): ConsultantToolInvocation[] => {
    const invocations: ConsultantToolInvocation[] = [];
    
    messages.forEach((message) => {
      if (message.role === 'assistant' && message.parts) {
        message.parts.forEach((part) => {
          if (part.type === 'tool-invocation') {
            invocations.push({
              toolCallId: part.toolInvocation.toolCallId,
              toolName: part.toolInvocation.toolName as any,
              state: part.toolInvocation.state as any,
              args: part.toolInvocation.args,
              result: part.toolInvocation.result
            });
          }
        });
      }
    });

    return invocations;
  }, []);

  // Helper function to check if there are pending tool calls
  const hasPendingToolCalls = useCallback((messages: Message[]): boolean => {
    const invocations = getToolInvocations(messages);
    return invocations.some(inv => inv.state === 'call' || inv.state === 'partial-call');
  }, [getToolInvocations]);

  // Helper function to get the latest tool results
  const getLatestToolResults = useCallback((messages: Message[]) => {
    const invocations = getToolInvocations(messages);
    return invocations
      .filter(inv => inv.state === 'result')
      .map(inv => ({
        toolName: inv.toolName,
        result: inv.result,
        toolCallId: inv.toolCallId
      }));
  }, [getToolInvocations]);

  // Enhanced send message with consultant-specific features
  const sendConsultantMessage = useCallback((message: string, options?: {
    includeContext?: boolean;
    priority?: 'low' | 'normal' | 'high';
  }) => {
    const { includeContext = true, priority = 'normal' } = options || {};
    
    // Add consultant-specific context if needed
    let enhancedMessage = message;
    if (includeContext) {
      enhancedMessage = `As a consultant for Soimagine Digital Agency, please help with: ${message}`;
    }

    return chat.handleSubmit(new Event('submit') as any, {
      data: { message: enhancedMessage, priority }
    });
  }, [chat]);

  return {
    ...chat,
    // Enhanced methods
    sendConsultantMessage,
    getToolInvocations: () => getToolInvocations(chat.messages),
    hasPendingToolCalls: () => hasPendingToolCalls(chat.messages),
    getLatestToolResults: () => getLatestToolResults(chat.messages),
    
    // Consultant-specific helpers
    isConsultantMode: true,
    assistantType: 'consultant' as const,
  };
}

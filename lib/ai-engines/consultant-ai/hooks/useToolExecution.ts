'use client';

import { useState, useCallback, useRef } from 'react';
import { type ToolExecutionContext } from '../types';

interface ToolExecutionState {
  isExecuting: boolean;
  error: string | null;
  result: any;
  executionTime: number;
  attempts: number;
}

interface UseToolExecutionOptions {
  maxRetries?: number;
  retryDelay?: number;
  timeout?: number;
  onStart?: (context: ToolExecutionContext) => void;
  onSuccess?: (result: any, context: ToolExecutionContext) => void;
  onError?: (error: Error, context: ToolExecutionContext) => void;
  onComplete?: (state: ToolExecutionState, context: ToolExecutionContext) => void;
}

export function useToolExecution(options: UseToolExecutionOptions = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    timeout = 30000,
    onStart,
    onSuccess,
    onError,
    onComplete
  } = options;

  const [state, setState] = useState<ToolExecutionState>({
    isExecuting: false,
    error: null,
    result: null,
    executionTime: 0,
    attempts: 0
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const startTimeRef = useRef<number>(0);

  // Execute a tool with retry logic and error handling
  const executeWithRetry = useCallback(async <T>(
    toolFunction: (context: ToolExecutionContext) => Promise<T>,
    context: Omit<ToolExecutionContext, 'abortSignal'>
  ): Promise<T> => {
    // Create new abort controller for this execution
    abortControllerRef.current = new AbortController();
    const fullContext: ToolExecutionContext = {
      ...context,
      abortSignal: abortControllerRef.current.signal
    };

    startTimeRef.current = Date.now();
    
    setState(prev => ({
      ...prev,
      isExecuting: true,
      error: null,
      result: null,
      executionTime: 0,
      attempts: 0
    }));

    onStart?.(fullContext);

    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        setState(prev => ({ ...prev, attempts: attempt }));

        // Set up timeout
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Tool execution timed out after ${timeout}ms`));
          }, timeout);
        });

        // Execute the tool function
        const result = await Promise.race([
          toolFunction(fullContext),
          timeoutPromise
        ]);

        // Check if aborted
        if (fullContext.abortSignal?.aborted) {
          throw new Error('Tool execution was aborted');
        }

        const executionTime = Date.now() - startTimeRef.current;
        
        setState(prev => ({
          ...prev,
          isExecuting: false,
          result,
          executionTime,
          error: null
        }));

        onSuccess?.(result, fullContext);
        onComplete?.(state, fullContext);
        
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // If this is the last attempt or a non-retryable error, throw
        if (attempt > maxRetries || 
            fullContext.abortSignal?.aborted ||
            lastError.message.includes('timeout') ||
            lastError.message.includes('aborted')) {
          break;
        }

        // Wait before retrying
        if (attempt <= maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }
      }
    }

    // If we get here, all retries failed
    const executionTime = Date.now() - startTimeRef.current;
    const finalState = {
      isExecuting: false,
      error: lastError?.message || 'Unknown error',
      result: null,
      executionTime,
      attempts: maxRetries + 1
    };

    setState(finalState);
    onError?.(lastError!, fullContext);
    onComplete?.(finalState, fullContext);
    
    throw lastError;
  }, [maxRetries, retryDelay, timeout, onStart, onSuccess, onError, onComplete, state]);

  // Abort current execution
  const abort = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setState(prev => ({
        ...prev,
        isExecuting: false,
        error: 'Execution aborted by user'
      }));
    }
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({
      isExecuting: false,
      error: null,
      result: null,
      executionTime: 0,
      attempts: 0
    });
  }, []);

  // Retry last execution
  const retry = useCallback(async <T>(
    toolFunction: (context: ToolExecutionContext) => Promise<T>,
    context: Omit<ToolExecutionContext, 'abortSignal'>
  ): Promise<T> => {
    reset();
    return executeWithRetry(toolFunction, context);
  }, [executeWithRetry, reset]);

  return {
    // State
    ...state,
    
    // Actions
    executeWithRetry,
    abort,
    reset,
    retry,
    
    // Computed state
    canRetry: !state.isExecuting && state.error !== null,
    canAbort: state.isExecuting,
    hasResult: state.result !== null,
    hasError: state.error !== null,
    
    // Execution stats
    successRate: state.attempts > 0 ? (state.error ? 0 : 1) : 0,
    averageExecutionTime: state.executionTime,
  };
}

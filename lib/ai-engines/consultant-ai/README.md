# Consultant AI Engine

A comprehensive AI engine for Soimagine Digital Agency's consultant assistant, built with the AI SDK and following modern React patterns.

## Overview

The Consultant AI Engine provides tools and components for handling consultant-specific interactions, including service information, company details, and project proposal generation. It follows the AI SDK documentation patterns for tool calling and human-in-the-loop interactions.

## Features

- **AI SDK Compatible Tools**: Server-side tools that integrate with the AI SDK's `streamText` function
- **Client-side Components**: React components for rendering tool invocations and results
- **Type Safety**: Full TypeScript support with Zod schema validation
- **Error Handling**: Comprehensive error states and retry mechanisms
- **Responsive UI**: Framer Motion animations and responsive design
- **Modular Architecture**: Clean separation of concerns with hooks, utils, and components

## Directory Structure

```
lib/ai-engines/consultant-ai/
├── components/           # React components for UI
│   ├── ConsultantToolRenderer.tsx    # Main tool renderer
│   ├── ServiceInfoCard.tsx          # Service information display
│   ├── CompanyInfoCard.tsx          # Company information display
│   ├── ProposalCard.tsx             # Project proposal display
│   ├── ToolLoadingState.tsx         # Loading state component
│   ├── ToolErrorState.tsx           # Error state component
│   └── index.ts                     # Component exports
├── hooks/               # React hooks
│   ├── useConsultantTools.ts        # Tool execution hook
│   ├── useConsultantChat.ts         # Chat integration hook
│   ├── useToolExecution.ts          # Tool execution utilities
│   └── index.ts                     # Hook exports
├── tools/               # AI SDK tool definitions
│   └── index.ts                     # Tool exports
├── types/               # TypeScript type definitions
│   └── index.ts                     # Type exports
├── utils/               # Utility functions
│   ├── toolHelpers.ts               # Tool helper functions
│   ├── formatters.ts                # Data formatting utilities
│   ├── validators.ts                # Validation utilities
│   └── index.ts                     # Utility exports
├── index.ts             # Main library export
└── README.md           # This file
```

## Tools

### 1. getServiceInfo
Retrieves detailed information about Soimagine Digital Agency services.

**Parameters:**
- `serviceType`: Type of service (web-development, mobile-apps, ui-ux-design, etc.)
- `includePackages`: Whether to include pricing packages (default: true)
- `includeCaseStudies`: Whether to include case studies (default: false)

### 2. getCompanyInfo
Gets information about the company, team, values, and processes.

**Parameters:**
- `infoType`: Type of information (about, team, values, process, contact, all)

### 3. generateProposal
Creates customized project proposals based on client requirements.

**Parameters:**
- `projectType`: Type of project
- `budget`: Budget range
- `timeline`: Project timeline
- `requirements`: Specific requirements
- `companySize`: Client company size (optional)

## Usage

### Basic Tool Usage

```typescript
import { consultantTools } from '@/lib/ai-engines/consultant-ai';

// In your API route
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';

export async function POST(req: Request) {
  const { messages } = await req.json();

  const result = streamText({
    model: openai('gpt-4o-mini'),
    messages,
    tools: consultantTools,
    maxSteps: 5,
  });

  return result.toDataStreamResponse();
}
```

### Client-side Component Usage

```typescript
import { ConsultantToolRenderer } from '@/lib/ai-engines/consultant-ai';

function ChatMessage({ message }) {
  return (
    <div>
      {message.parts.map((part, index) => {
        if (part.type === 'tool-invocation') {
          return (
            <ConsultantToolRenderer
              key={index}
              toolInvocation={part.toolInvocation}
            />
          );
        }
        // Handle other part types...
      })}
    </div>
  );
}
```

### Using Hooks

```typescript
import { useConsultantChat, useConsultantTools } from '@/lib/ai-engines/consultant-ai';

function ConsultantInterface() {
  const chat = useConsultantChat({
    onToolCall: (toolCall) => {
      console.log('Tool called:', toolCall);
    },
    onToolResult: (result) => {
      console.log('Tool result:', result);
    }
  });

  const tools = useConsultantTools({
    onToolCall: (toolCall) => {
      // Handle tool call
    }
  });

  return (
    <div>
      {/* Your chat interface */}
    </div>
  );
}
```

## Integration with AI SDK

This library is designed to work seamlessly with the AI SDK's tool calling features:

1. **Server-side Tools**: Use `consultantTools` in your `streamText` calls
2. **Client-side Rendering**: Use `ConsultantToolRenderer` to display tool invocations
3. **Multi-step Calls**: Set `maxSteps` to enable multi-step tool interactions
4. **Error Handling**: Built-in error states and retry mechanisms

## Type Safety

All tools use Zod schemas for parameter validation:

```typescript
import { getServiceInfoSchema } from '@/lib/ai-engines/consultant-ai';

// Validate parameters
const params = getServiceInfoSchema.parse(userInput);
```

## Styling

Components use Tailwind CSS classes and are designed to work with both light and dark themes. Framer Motion is used for smooth animations.

## Error Handling

The library includes comprehensive error handling:

- Parameter validation with Zod
- Network error detection and retry logic
- User-friendly error messages
- Troubleshooting tips for common issues

## Contributing

When adding new tools or components:

1. Add tool definitions to `tools/`
2. Create corresponding TypeScript types in `types/`
3. Add client-side components in `components/`
4. Include validation logic in `utils/validators.ts`
5. Add formatting utilities in `utils/formatters.ts`
6. Update this README with usage examples

## Dependencies

- `ai`: AI SDK core
- `@ai-sdk/react`: AI SDK React hooks
- `zod`: Schema validation
- `framer-motion`: Animations
- `@heroicons/react`: Icons
- `react`: React framework
- `typescript`: Type safety

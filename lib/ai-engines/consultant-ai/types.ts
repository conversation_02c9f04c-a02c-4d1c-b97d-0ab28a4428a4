// Consultant AI Types
import { z } from 'zod';

// Service Types
export type ServiceType = 
  | 'web-development'
  | 'mobile-apps'
  | 'ui-ux-design'
  | 'digital-marketing'
  | 'branding'
  | 'consulting'
  | 'all';

export type BudgetRange = 
  | 'under-5k'
  | '5k-15k'
  | '15k-50k'
  | 'over-50k';

export type Timeline = 
  | 'urgent'
  | 'standard'
  | 'flexible';

export type CompanySize = 
  | 'startup'
  | 'small-business'
  | 'medium-business'
  | 'enterprise';

export type InfoType = 
  | 'about'
  | 'team'
  | 'values'
  | 'process'
  | 'contact'
  | 'all';

// Tool Parameter Schemas
export const getServiceInfoSchema = z.object({
  serviceType: z.enum([
    'web-development',
    'mobile-apps',
    'ui-ux-design',
    'digital-marketing',
    'branding',
    'consulting',
    'all',
  ]).describe('Type of service to get information about'),
  includePackages: z
    .boolean()
    .default(true)
    .describe('Whether to include pricing packages'),
  includeCaseStudies: z
    .boolean()
    .default(false)
    .describe('Whether to include case studies'),
});

export const getCompanyInfoSchema = z.object({
  infoType: z
    .enum(['about', 'team', 'values', 'process', 'contact', 'all'])
    .describe('Type of company information to retrieve'),
});

export const generateProposalSchema = z.object({
  projectType: z
    .enum([
      'web-development',
      'mobile-app',
      'ui-ux-design',
      'digital-marketing',
      'branding',
      'consulting',
    ])
    .describe('Type of project'),
  budget: z
    .enum(['under-5k', '5k-15k', '15k-50k', 'over-50k'])
    .describe('Client budget range'),
  timeline: z
    .enum(['urgent', 'standard', 'flexible'])
    .describe('Project timeline requirements'),
  requirements: z
    .string()
    .describe('Specific project requirements and goals'),
  companySize: z
    .enum(['startup', 'small-business', 'medium-business', 'enterprise'])
    .optional()
    .describe('Client company size'),
});

// Tool Result Types
export interface ServiceInfo {
  title: string;
  description: string;
  features: string[];
  technologies: string[];
  packages?: {
    [key: string]: {
      price: string;
      timeline: string;
      [key: string]: string;
    };
  };
}

export interface CompanyInfo {
  title: string;
  description?: string;
  [key: string]: any;
}

export interface ProposalInfo {
  title: string;
  overview: string;
  deliverables: string[];
  technologies: string[];
  timeline_estimate: string;
  requirements_analysis: string;
  budget_range: string;
  budget_recommendation: string;
  company_fit: string;
  next_steps: string[];
  included_services: string[];
}

// Tool Invocation Types
export interface ConsultantToolInvocation {
  toolCallId: string;
  toolName: 'getServiceInfo' | 'getCompanyInfo' | 'generateProposal';
  state: 'partial-call' | 'call' | 'result';
  args?: any;
  result?: any;
}

// Component Props Types
export interface ServiceInfoCardProps {
  service: ServiceInfo;
  includePackages?: boolean;
  includeCaseStudies?: boolean;
}

export interface CompanyInfoCardProps {
  companyInfo: CompanyInfo;
  infoType: InfoType;
}

export interface ProposalCardProps {
  proposal: ProposalInfo;
  callToAction?: string;
}

// Hook Types
export interface UseConsultantToolsOptions {
  onToolCall?: (toolCall: ConsultantToolInvocation) => void;
  onToolResult?: (result: any) => void;
}

export interface UseConsultantToolsReturn {
  executeServiceInfo: (params: z.infer<typeof getServiceInfoSchema>) => Promise<any>;
  executeCompanyInfo: (params: z.infer<typeof getCompanyInfoSchema>) => Promise<any>;
  executeProposal: (params: z.infer<typeof generateProposalSchema>) => Promise<any>;
  isLoading: boolean;
  error: string | null;
}

// Tool Execution Context
export interface ToolExecutionContext {
  toolCallId: string;
  messages: any[];
  abortSignal?: AbortSignal;
}

// Export parameter types for convenience
export type GetServiceInfoParams = z.infer<typeof getServiceInfoSchema>;
export type GetCompanyInfoParams = z.infer<typeof getCompanyInfoSchema>;
export type GenerateProposalParams = z.infer<typeof generateProposalSchema>;

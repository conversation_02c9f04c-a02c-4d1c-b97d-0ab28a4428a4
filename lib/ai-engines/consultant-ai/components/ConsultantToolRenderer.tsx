'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { type ConsultantToolInvocation } from '../types';
import { ServiceInfoCard } from './ServiceInfoCard';
import { CompanyInfoCard } from './CompanyInfoCard';
import { ProposalCard } from './ProposalCard';
import { ToolLoadingState } from './ToolLoadingState';
import { ToolErrorState } from './ToolErrorState';

interface ConsultantToolRendererProps {
  toolInvocation: ConsultantToolInvocation;
  className?: string;
}

export function ConsultantToolRenderer({ 
  toolInvocation, 
  className = '' 
}: ConsultantToolRendererProps) {
  const { toolName, state, args, result } = toolInvocation;

  // Animation variants for smooth transitions
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    exit: { 
      opacity: 0, 
      y: -20,
      transition: {
        duration: 0.2,
        ease: 'easeIn'
      }
    }
  };

  // Render based on tool state
  const renderToolContent = () => {
    switch (state) {
      case 'partial-call':
        return (
          <ToolLoadingState 
            toolName={toolName}
            message="Preparing tool call..."
            showPartialArgs={args}
          />
        );

      case 'call':
        return (
          <ToolLoadingState 
            toolName={toolName}
            message={getLoadingMessage(toolName)}
            args={args}
          />
        );

      case 'result':
        if (result?.error) {
          return (
            <ToolErrorState 
              toolName={toolName}
              error={result.error}
              args={args}
            />
          );
        }

        return renderToolResult();

      default:
        return (
          <ToolErrorState 
            toolName={toolName}
            error="Unknown tool state"
            args={args}
          />
        );
    }
  };

  // Render the actual tool result based on tool type
  const renderToolResult = () => {
    switch (toolName) {
      case 'getServiceInfo':
        if (result?.service) {
          return (
            <ServiceInfoCard 
              service={result.service}
              includePackages={args?.includePackages}
              includeCaseStudies={args?.includeCaseStudies}
            />
          );
        } else if (result?.services) {
          return (
            <div className="space-y-4">
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                All Services Overview
              </div>
              <div className="text-gray-600 dark:text-gray-400">
                {result.summary}
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                {result.services.map((service: any, index: number) => (
                  <ServiceInfoCard 
                    key={index}
                    service={service}
                    includePackages={false}
                    includeCaseStudies={false}
                  />
                ))}
              </div>
              {result.contact && (
                <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-blue-800 dark:text-blue-200 font-medium">
                    {result.contact}
                  </p>
                </div>
              )}
            </div>
          );
        }
        break;

      case 'getCompanyInfo':
        return (
          <CompanyInfoCard 
            companyInfo={result}
            infoType={args?.infoType || 'about'}
          />
        );

      case 'generateProposal':
        return (
          <ProposalCard 
            proposal={result?.proposal}
            callToAction={result?.call_to_action}
          />
        );

      default:
        return (
          <ToolErrorState 
            toolName={toolName}
            error="Unknown tool type"
            args={args}
          />
        );
    }

    return (
      <ToolErrorState 
        toolName={toolName}
        error="No valid result data"
        args={args}
      />
    );
  };

  // Get appropriate loading message for each tool
  const getLoadingMessage = (toolName: string): string => {
    switch (toolName) {
      case 'getServiceInfo':
        return 'Gathering service information...';
      case 'getCompanyInfo':
        return 'Retrieving company details...';
      case 'generateProposal':
        return 'Creating customized proposal...';
      default:
        return 'Processing request...';
    }
  };

  return (
    <motion.div
      className={`consultant-tool-renderer ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      layout
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
        {/* Tool Header */}
        <div className="px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {getToolDisplayName(toolName)}
              </span>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 capitalize">
              {state.replace('-', ' ')}
            </div>
          </div>
        </div>

        {/* Tool Content */}
        <div className="p-4">
          {renderToolContent()}
        </div>
      </div>
    </motion.div>
  );
}

// Helper function to get display names for tools
function getToolDisplayName(toolName: string): string {
  switch (toolName) {
    case 'getServiceInfo':
      return 'Service Information';
    case 'getCompanyInfo':
      return 'Company Information';
    case 'generateProposal':
      return 'Project Proposal';
    default:
      return toolName;
  }
}

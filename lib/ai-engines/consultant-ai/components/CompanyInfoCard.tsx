'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  BuildingOfficeIcon,
  UserGroupIcon,
  HeartIcon,
  CogIcon,
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';
import { type CompanyInfoCardProps } from '../types';

export function CompanyInfoCard({ companyInfo, infoType }: CompanyInfoCardProps) {
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'about':
        return <BuildingOfficeIcon className="w-6 h-6 text-blue-500" />;
      case 'team':
        return <UserGroupIcon className="w-6 h-6 text-green-500" />;
      case 'values':
        return <HeartIcon className="w-6 h-6 text-red-500" />;
      case 'process':
        return <CogIcon className="w-6 h-6 text-purple-500" />;
      case 'contact':
        return <PhoneIcon className="w-6 h-6 text-orange-500" />;
      default:
        return <BuildingOfficeIcon className="w-6 h-6 text-blue-500" />;
    }
  };

  const renderContent = () => {
    if (infoType === 'all') {
      return (
        <div className="space-y-8">
          {Object.entries(companyInfo).map(([key, value]: [string, any]) => (
            <motion.div key={key} variants={itemVariants}>
              <CompanyInfoSection type={key} data={value} />
            </motion.div>
          ))}
        </div>
      );
    }

    const data = companyInfo[infoType];
    if (!data) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            No information available for {infoType}
          </p>
        </div>
      );
    }

    return <CompanyInfoSection type={infoType} data={data} />;
  };

  return (
    <motion.div
      className="company-info-card"
      initial="hidden"
      animate="visible"
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
    >
      {renderContent()}
    </motion.div>
  );
}

function CompanyInfoSection({ type, data }: { type: string; data: any }) {
  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.3 }
    }
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'about':
        return <BuildingOfficeIcon className="w-6 h-6 text-blue-500" />;
      case 'team':
        return <UserGroupIcon className="w-6 h-6 text-green-500" />;
      case 'values':
        return <HeartIcon className="w-6 h-6 text-red-500" />;
      case 'process':
        return <CogIcon className="w-6 h-6 text-purple-500" />;
      case 'contact':
        return <PhoneIcon className="w-6 h-6 text-orange-500" />;
      default:
        return <BuildingOfficeIcon className="w-6 h-6 text-blue-500" />;
    }
  };

  return (
    <motion.div variants={itemVariants} className="space-y-4">
      {/* Section Header */}
      <div className="flex items-center space-x-3 mb-4">
        {getIcon(type)}
        <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
          {data.title}
        </h3>
      </div>

      {/* Section Content */}
      {type === 'about' && (
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
            {data.description}
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
              <div className="text-sm text-blue-600 dark:text-blue-400">Founded</div>
              <div className="font-semibold text-blue-900 dark:text-blue-100">{data.founded}</div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
              <div className="text-sm text-green-600 dark:text-green-400">Location</div>
              <div className="font-semibold text-green-900 dark:text-green-100">{data.location}</div>
            </div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Specialties</h4>
            <div className="flex flex-wrap gap-2">
              {data.specialties.map((specialty: string, index: number) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full text-sm"
                >
                  {specialty}
                </span>
              ))}
            </div>
          </div>
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Our Mission</h4>
            <p className="text-blue-800 dark:text-blue-200">{data.mission}</p>
          </div>
        </div>
      )}

      {type === 'team' && (
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
            {data.description}
          </p>
          <div className="grid gap-4 md:grid-cols-2">
            {data.members.map((member: any, index: number) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h5 className="font-semibold text-gray-900 dark:text-gray-100">{member.role}</h5>
                <p className="text-gray-600 dark:text-gray-400 text-sm">{member.expertise}</p>
              </div>
            ))}
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Team Size:</span>
            <span className="font-medium text-gray-800 dark:text-gray-200">{data.size}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Approach:</span>
            <span className="font-medium text-gray-800 dark:text-gray-200">{data.approach}</span>
          </div>
        </div>
      )}

      {type === 'values' && (
        <div className="space-y-4">
          <div className="grid gap-4">
            {data.values.map((value: any, index: number) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <h5 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">{value.name}</h5>
                <p className="text-gray-600 dark:text-gray-400 text-sm">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {type === 'process' && (
        <div className="space-y-4">
          <div className="space-y-3">
            {data.phases.map((phase: any, index: number) => (
              <div key={index} className="flex items-start space-x-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <h5 className="font-semibold text-gray-900 dark:text-gray-100">{phase.phase}</h5>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">{phase.description}</p>
                  <span className="text-xs text-blue-600 dark:text-blue-400">{phase.duration}</span>
                </div>
              </div>
            ))}
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h5 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Methodology</h5>
            <p className="text-blue-800 dark:text-blue-200 text-sm">{data.methodology}</p>
          </div>
        </div>
      )}

      {type === 'contact' && (
        <div className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <EnvelopeIcon className="w-5 h-5 text-blue-500" />
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Email</div>
                <div className="font-medium text-gray-900 dark:text-gray-100">{data.email}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <PhoneIcon className="w-5 h-5 text-green-500" />
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Phone</div>
                <div className="font-medium text-gray-900 dark:text-gray-100">{data.phone}</div>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <GlobeAltIcon className="w-5 h-5 text-purple-500" />
              <div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Website</div>
                <div className="font-medium text-gray-900 dark:text-gray-100">{data.website}</div>
              </div>
            </div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h5 className="font-semibold text-green-900 dark:text-green-100 mb-2">{data.consultation}</h5>
            <p className="text-green-800 dark:text-green-200 text-sm mb-2">{data.response_time}</p>
            <p className="text-green-700 dark:text-green-300 text-sm">{data.availability}</p>
          </div>
        </div>
      )}
    </motion.div>
  );
}

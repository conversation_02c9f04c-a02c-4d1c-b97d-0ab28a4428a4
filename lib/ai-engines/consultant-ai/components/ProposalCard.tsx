'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  DocumentTextIcon,
  CheckCircleIcon,
  CpuChipIcon,
  ClockIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { type ProposalCardProps } from '../types';

export function ProposalCard({ proposal, callToAction }: ProposalCardProps) {
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  if (!proposal) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">
          No proposal data available
        </p>
      </div>
    );
  }

  return (
    <motion.div
      className="proposal-card"
      initial="hidden"
      animate="visible"
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
    >
      {/* Proposal Header */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="flex items-center space-x-3 mb-3">
          <DocumentTextIcon className="w-6 h-6 text-blue-500" />
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            {proposal.title}
          </h3>
        </div>
        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
          {proposal.overview}
        </p>
      </motion.div>

      {/* Project Details Grid */}
      <div className="grid gap-6 md:grid-cols-2 mb-6">
        {/* Deliverables */}
        <motion.div variants={itemVariants} className="space-y-3">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
            <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2" />
            Deliverables
          </h4>
          <div className="space-y-2">
            {proposal.deliverables.map((deliverable, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-start space-x-2"
              >
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300 text-sm">
                  {deliverable}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Technologies */}
        <motion.div variants={itemVariants} className="space-y-3">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
            <CpuChipIcon className="w-5 h-5 text-purple-500 mr-2" />
            Technologies
          </h4>
          <div className="flex flex-wrap gap-2">
            {proposal.technologies.map((tech, index) => (
              <motion.span
                key={index}
                variants={itemVariants}
                className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 rounded-full text-sm font-medium"
              >
                {tech}
              </motion.span>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Project Summary */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="grid gap-4 md:grid-cols-3">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <ClockIcon className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">Timeline</span>
            </div>
            <p className="text-blue-900 dark:text-blue-100 font-semibold">
              {proposal.timeline_estimate}
            </p>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <CurrencyDollarIcon className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">Budget</span>
            </div>
            <p className="text-green-900 dark:text-green-100 font-semibold capitalize">
              {proposal.budget_range.replace('-', ' - ')}
            </p>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <BuildingOfficeIcon className="w-5 h-5 text-orange-500" />
              <span className="text-sm font-medium text-orange-700 dark:text-orange-300">Fit</span>
            </div>
            <p className="text-orange-900 dark:text-orange-100 font-semibold">
              {proposal.company_fit}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Requirements Analysis */}
      <motion.div variants={itemVariants} className="mb-6">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
          Requirements Analysis
        </h4>
        <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
          <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
            {proposal.requirements_analysis}
          </p>
        </div>
      </motion.div>

      {/* Budget Recommendation */}
      <motion.div variants={itemVariants} className="mb-6">
        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-700">
          <h5 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">
            Budget Recommendation
          </h5>
          <p className="text-yellow-800 dark:text-yellow-200 text-sm">
            {proposal.budget_recommendation}
          </p>
        </div>
      </motion.div>

      {/* Next Steps */}
      <motion.div variants={itemVariants} className="mb-6">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
          Next Steps
        </h4>
        <div className="space-y-2">
          {proposal.next_steps.map((step, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="flex items-center space-x-3 p-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
            >
              <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                {index + 1}
              </div>
              <span className="text-gray-700 dark:text-gray-300 text-sm">
                {step}
              </span>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Included Services */}
      <motion.div variants={itemVariants} className="mb-6">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
          Included Services
        </h4>
        <div className="grid gap-2 md:grid-cols-2">
          {proposal.included_services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="flex items-center space-x-2"
            >
              <CheckCircleIcon className="w-4 h-4 text-green-500 flex-shrink-0" />
              <span className="text-gray-700 dark:text-gray-300 text-sm">
                {service}
              </span>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Call to Action */}
      {callToAction && (
        <motion.div 
          variants={itemVariants}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-lg"
        >
          <div className="flex items-center justify-between">
            <div>
              <h5 className="font-bold text-lg mb-2">
                {callToAction}
              </h5>
              <p className="text-blue-100 text-sm">
                Let's discuss your project and create something amazing together.
              </p>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 px-6 py-3 bg-white text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
            >
              <span>Schedule Call</span>
              <ArrowRightIcon className="w-4 h-4" />
            </motion.button>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

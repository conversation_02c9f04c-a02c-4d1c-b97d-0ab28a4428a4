'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  ExclamationTriangleIcon,
  ArrowPathIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

interface ToolErrorStateProps {
  toolName: string;
  error: string;
  args?: any;
  onRetry?: () => void;
}

export function ToolErrorState({ 
  toolName, 
  error, 
  args, 
  onRetry 
}: ToolErrorStateProps) {
  const shakeVariants = {
    animate: {
      x: [0, -10, 10, -10, 10, 0],
      transition: {
        duration: 0.5,
        ease: "easeInOut"
      }
    }
  };

  const fadeInVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  const getErrorType = (error: string) => {
    if (error.toLowerCase().includes('network') || error.toLowerCase().includes('fetch')) {
      return {
        type: 'network',
        title: 'Network Error',
        description: 'Unable to connect to the service. Please check your internet connection.',
        color: 'orange'
      };
    } else if (error.toLowerCase().includes('timeout')) {
      return {
        type: 'timeout',
        title: 'Request Timeout',
        description: 'The request took too long to complete. Please try again.',
        color: 'yellow'
      };
    } else if (error.toLowerCase().includes('unauthorized') || error.toLowerCase().includes('forbidden')) {
      return {
        type: 'auth',
        title: 'Authorization Error',
        description: 'You do not have permission to access this resource.',
        color: 'red'
      };
    } else if (error.toLowerCase().includes('not found')) {
      return {
        type: 'notfound',
        title: 'Resource Not Found',
        description: 'The requested resource could not be found.',
        color: 'gray'
      };
    } else {
      return {
        type: 'general',
        title: 'Tool Execution Error',
        description: 'An error occurred while executing the tool.',
        color: 'red'
      };
    }
  };

  const errorInfo = getErrorType(error);

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'orange':
        return {
          bg: 'bg-orange-50 dark:bg-orange-900/20',
          border: 'border-orange-200 dark:border-orange-700',
          icon: 'text-orange-500',
          title: 'text-orange-900 dark:text-orange-100',
          text: 'text-orange-800 dark:text-orange-200',
          button: 'bg-orange-600 hover:bg-orange-700'
        };
      case 'yellow':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          border: 'border-yellow-200 dark:border-yellow-700',
          icon: 'text-yellow-500',
          title: 'text-yellow-900 dark:text-yellow-100',
          text: 'text-yellow-800 dark:text-yellow-200',
          button: 'bg-yellow-600 hover:bg-yellow-700'
        };
      case 'gray':
        return {
          bg: 'bg-gray-50 dark:bg-gray-700/50',
          border: 'border-gray-200 dark:border-gray-600',
          icon: 'text-gray-500',
          title: 'text-gray-900 dark:text-gray-100',
          text: 'text-gray-700 dark:text-gray-300',
          button: 'bg-gray-600 hover:bg-gray-700'
        };
      default:
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-700',
          icon: 'text-red-500',
          title: 'text-red-900 dark:text-red-100',
          text: 'text-red-800 dark:text-red-200',
          button: 'bg-red-600 hover:bg-red-700'
        };
    }
  };

  const colors = getColorClasses(errorInfo.color);

  return (
    <motion.div
      className="tool-error-state"
      variants={fadeInVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        className={`${colors.bg} ${colors.border} border rounded-lg p-4`}
        variants={shakeVariants}
        animate="animate"
      >
        {/* Error Header */}
        <div className="flex items-start space-x-3 mb-3">
          <ExclamationTriangleIcon className={`w-6 h-6 ${colors.icon} flex-shrink-0 mt-0.5`} />
          <div className="flex-1">
            <h4 className={`font-semibold ${colors.title} mb-1`}>
              {errorInfo.title}
            </h4>
            <p className={`text-sm ${colors.text} mb-2`}>
              {errorInfo.description}
            </p>
            <div className={`text-xs ${colors.text} font-mono bg-white dark:bg-gray-800 p-2 rounded border`}>
              {error}
            </div>
          </div>
        </div>

        {/* Tool Information */}
        <div className="mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <InformationCircleIcon className={`w-4 h-4 ${colors.icon}`} />
            <span className={`text-sm font-medium ${colors.title}`}>
              Tool: {toolName}
            </span>
          </div>
          
          {args && Object.keys(args).length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded border p-3">
              <h6 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                Parameters used:
              </h6>
              <div className="space-y-1">
                {Object.entries(args).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <span className="text-gray-600 dark:text-gray-400 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}:
                    </span>
                    <span className="text-gray-800 dark:text-gray-200 font-mono">
                      {typeof value === 'boolean' 
                        ? value.toString() 
                        : typeof value === 'object'
                        ? JSON.stringify(value)
                        : String(value)
                      }
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <div className={`text-xs ${colors.text}`}>
            Error occurred at {new Date().toLocaleTimeString()}
          </div>
          
          {onRetry && (
            <motion.button
              onClick={onRetry}
              className={`flex items-center space-x-2 px-3 py-1.5 ${colors.button} text-white rounded text-sm font-medium transition-colors`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowPathIcon className="w-4 h-4" />
              <span>Retry</span>
            </motion.button>
          )}
        </div>

        {/* Troubleshooting Tips */}
        <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
          <h6 className={`text-xs font-medium ${colors.title} mb-2`}>
            Troubleshooting Tips:
          </h6>
          <ul className={`text-xs ${colors.text} space-y-1`}>
            {errorInfo.type === 'network' && (
              <>
                <li>• Check your internet connection</li>
                <li>• Try refreshing the page</li>
                <li>• Contact support if the issue persists</li>
              </>
            )}
            {errorInfo.type === 'timeout' && (
              <>
                <li>• Try again with a simpler request</li>
                <li>• Check your internet connection speed</li>
                <li>• Wait a moment before retrying</li>
              </>
            )}
            {errorInfo.type === 'auth' && (
              <>
                <li>• Make sure you're logged in</li>
                <li>• Check your account permissions</li>
                <li>• Contact an administrator</li>
              </>
            )}
            {errorInfo.type === 'notfound' && (
              <>
                <li>• Verify the requested resource exists</li>
                <li>• Check for typos in parameters</li>
                <li>• Try a different approach</li>
              </>
            )}
            {errorInfo.type === 'general' && (
              <>
                <li>• Try again in a few moments</li>
                <li>• Check your input parameters</li>
                <li>• Contact support if the issue persists</li>
              </>
            )}
          </ul>
        </div>
      </motion.div>
    </motion.div>
  );
}

'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircleIcon, 
  CpuChipIcon, 
  CurrencyDollarIcon,
  ClockIcon 
} from '@heroicons/react/24/outline';
import { type ServiceInfoCardProps } from '../types';

export function ServiceInfoCard({ 
  service, 
  includePackages = true, 
  includeCaseStudies = false 
}: ServiceInfoCardProps) {
  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.3 }
    }
  };

  return (
    <motion.div
      className="service-info-card"
      initial="hidden"
      animate="visible"
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.1
          }
        }
      }}
    >
      {/* Service Header */}
      <motion.div variants={itemVariants} className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          {service.title}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
          {service.description}
        </p>
      </motion.div>

      {/* Features Section */}
      <motion.div variants={itemVariants} className="mb-6">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
          <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2" />
          Key Features
        </h4>
        <div className="grid gap-2">
          {service.features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="flex items-start space-x-2"
            >
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
              <span className="text-gray-700 dark:text-gray-300 text-sm">
                {feature}
              </span>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Technologies Section */}
      <motion.div variants={itemVariants} className="mb-6">
        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
          <CpuChipIcon className="w-5 h-5 text-purple-500 mr-2" />
          Technologies
        </h4>
        <div className="flex flex-wrap gap-2">
          {service.technologies.map((tech, index) => (
            <motion.span
              key={index}
              variants={itemVariants}
              className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 rounded-full text-sm font-medium"
            >
              {tech}
            </motion.span>
          ))}
        </div>
      </motion.div>

      {/* Packages Section */}
      {includePackages && service.packages && (
        <motion.div variants={itemVariants} className="mb-6">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <CurrencyDollarIcon className="w-5 h-5 text-green-500 mr-2" />
            Pricing Packages
          </h4>
          <div className="grid gap-4 md:grid-cols-3">
            {Object.entries(service.packages).map(([packageName, packageInfo]) => (
              <motion.div
                key={packageName}
                variants={itemVariants}
                className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
              >
                <h5 className="font-semibold text-gray-900 dark:text-gray-100 capitalize mb-2">
                  {packageName}
                </h5>
                <div className="space-y-2">
                  {Object.entries(packageInfo).map(([key, value]) => (
                    <div key={key} className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}:
                      </span>
                      <span className="text-gray-800 dark:text-gray-200 font-medium">
                        {value}
                      </span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Call to Action */}
      <motion.div 
        variants={itemVariants}
        className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700"
      >
        <div className="flex items-center justify-between">
          <div>
            <h5 className="font-semibold text-blue-900 dark:text-blue-100">
              Ready to get started?
            </h5>
            <p className="text-blue-700 dark:text-blue-300 text-sm">
              Schedule a free consultation to discuss your project
            </p>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium text-sm transition-colors"
          >
            Get Quote
          </motion.button>
        </div>
      </motion.div>
    </motion.div>
  );
}

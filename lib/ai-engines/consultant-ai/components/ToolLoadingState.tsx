'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  CogIcon,
  InformationCircleIcon,
  DocumentTextIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';

interface ToolLoadingStateProps {
  toolName: string;
  message: string;
  args?: any;
  showPartialArgs?: any;
}

export function ToolLoadingState({ 
  toolName, 
  message, 
  args, 
  showPartialArgs 
}: ToolLoadingStateProps) {
  const getToolIcon = (toolName: string) => {
    switch (toolName) {
      case 'getServiceInfo':
        return <InformationCircleIcon className="w-6 h-6 text-blue-500" />;
      case 'getCompanyInfo':
        return <BuildingOfficeIcon className="w-6 h-6 text-green-500" />;
      case 'generateProposal':
        return <DocumentTextIcon className="w-6 h-6 text-purple-500" />;
      default:
        return <CogIcon className="w-6 h-6 text-gray-500" />;
    }
  };

  const spinVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "linear"
      }
    }
  };

  const pulseVariants = {
    animate: {
      scale: [1, 1.1, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const dotsVariants = {
    animate: {
      transition: {
        staggerChildren: 0.2,
        repeat: Infinity,
        repeatType: "loop" as const
      }
    }
  };

  const dotVariants = {
    animate: {
      y: [0, -10, 0],
      transition: {
        duration: 0.6,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="tool-loading-state">
      {/* Loading Header */}
      <div className="flex items-center space-x-3 mb-4">
        <motion.div variants={pulseVariants} animate="animate">
          {getToolIcon(toolName)}
        </motion.div>
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <span className="text-gray-700 dark:text-gray-300 font-medium">
              {message}
            </span>
            <motion.div
              className="flex space-x-1"
              variants={dotsVariants}
              animate="animate"
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-1 h-1 bg-blue-500 rounded-full"
                  variants={dotVariants}
                />
              ))}
            </motion.div>
          </div>
        </div>
        <motion.div variants={spinVariants} animate="animate">
          <CogIcon className="w-5 h-5 text-gray-400" />
        </motion.div>
      </div>

      {/* Loading Progress Bar */}
      <div className="mb-4">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <motion.div
            className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>
      </div>

      {/* Tool Arguments Display */}
      {(args || showPartialArgs) && (
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
          <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {showPartialArgs ? 'Partial Parameters:' : 'Parameters:'}
          </h5>
          <div className="space-y-2">
            {Object.entries(args || showPartialArgs || {}).map(([key, value]) => (
              <div key={key} className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}:
                </span>
                <span className="text-gray-800 dark:text-gray-200 font-mono">
                  {typeof value === 'boolean' 
                    ? value.toString() 
                    : typeof value === 'object'
                    ? JSON.stringify(value)
                    : String(value)
                  }
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Loading Steps Indicator */}
      <div className="mt-4">
        <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
          <motion.div
            className="w-2 h-2 bg-blue-500 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <span>Processing your request...</span>
        </div>
      </div>
    </div>
  );
}

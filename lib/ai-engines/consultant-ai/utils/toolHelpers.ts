// Tool Helper Functions for Consultant AI
import { type ConsultantToolInvocation, type ServiceType, type BudgetRange, type Timeline } from '../types';

/**
 * Extract tool invocations from AI SDK message parts
 */
export function extractToolInvocations(messageParts: any[]): ConsultantToolInvocation[] {
  return messageParts
    .filter(part => part.type === 'tool-invocation')
    .map(part => ({
      toolCallId: part.toolInvocation.toolCallId,
      toolName: part.toolInvocation.toolName,
      state: part.toolInvocation.state,
      args: part.toolInvocation.args,
      result: part.toolInvocation.result
    }));
}

/**
 * Check if a tool invocation is complete
 */
export function isToolInvocationComplete(invocation: ConsultantToolInvocation): boolean {
  return invocation.state === 'result';
}

/**
 * Check if a tool invocation is pending
 */
export function isToolInvocationPending(invocation: ConsultantToolInvocation): boolean {
  return invocation.state === 'call' || invocation.state === 'partial-call';
}

/**
 * Get tool display name for UI
 */
export function getToolDisplayName(toolName: string): string {
  switch (toolName) {
    case 'getServiceInfo':
      return 'Service Information';
    case 'getCompanyInfo':
      return 'Company Information';
    case 'generateProposal':
      return 'Project Proposal';
    default:
      return toolName.replace(/([A-Z])/g, ' $1').trim();
  }
}

/**
 * Get tool icon class for UI
 */
export function getToolIconClass(toolName: string): string {
  switch (toolName) {
    case 'getServiceInfo':
      return 'text-blue-500';
    case 'getCompanyInfo':
      return 'text-green-500';
    case 'generateProposal':
      return 'text-purple-500';
    default:
      return 'text-gray-500';
  }
}

/**
 * Generate a unique tool call ID
 */
export function generateToolCallId(toolName: string): string {
  return `${toolName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate service type
 */
export function isValidServiceType(serviceType: string): serviceType is ServiceType {
  const validTypes: ServiceType[] = [
    'web-development',
    'mobile-apps',
    'ui-ux-design',
    'digital-marketing',
    'branding',
    'consulting',
    'all'
  ];
  return validTypes.includes(serviceType as ServiceType);
}

/**
 * Validate budget range
 */
export function isValidBudgetRange(budget: string): budget is BudgetRange {
  const validRanges: BudgetRange[] = [
    'under-5k',
    '5k-15k',
    '15k-50k',
    'over-50k'
  ];
  return validRanges.includes(budget as BudgetRange);
}

/**
 * Validate timeline
 */
export function isValidTimeline(timeline: string): timeline is Timeline {
  const validTimelines: Timeline[] = ['urgent', 'standard', 'flexible'];
  return validTimelines.includes(timeline as Timeline);
}

/**
 * Get service type display name
 */
export function getServiceTypeDisplayName(serviceType: ServiceType): string {
  switch (serviceType) {
    case 'web-development':
      return 'Web Development';
    case 'mobile-apps':
      return 'Mobile Apps';
    case 'ui-ux-design':
      return 'UI/UX Design';
    case 'digital-marketing':
      return 'Digital Marketing';
    case 'branding':
      return 'Branding';
    case 'consulting':
      return 'Consulting';
    case 'all':
      return 'All Services';
    default:
      return serviceType;
  }
}

/**
 * Get budget range display name
 */
export function getBudgetRangeDisplayName(budget: BudgetRange): string {
  switch (budget) {
    case 'under-5k':
      return 'Under $5,000';
    case '5k-15k':
      return '$5,000 - $15,000';
    case '15k-50k':
      return '$15,000 - $50,000';
    case 'over-50k':
      return 'Over $50,000';
    default:
      return budget;
  }
}

/**
 * Get timeline display name
 */
export function getTimelineDisplayName(timeline: Timeline): string {
  switch (timeline) {
    case 'urgent':
      return 'Urgent (ASAP)';
    case 'standard':
      return 'Standard (Normal timeline)';
    case 'flexible':
      return 'Flexible (No rush)';
    default:
      return timeline;
  }
}

/**
 * Calculate estimated project cost based on service type and budget
 */
export function calculateEstimatedCost(serviceType: ServiceType, budget: BudgetRange): {
  min: number;
  max: number;
  currency: string;
} {
  const budgetRanges = {
    'under-5k': { min: 1000, max: 5000 },
    '5k-15k': { min: 5000, max: 15000 },
    '15k-50k': { min: 15000, max: 50000 },
    'over-50k': { min: 50000, max: 100000 }
  };

  const serviceMultipliers = {
    'web-development': 1.0,
    'mobile-apps': 1.5,
    'ui-ux-design': 0.8,
    'digital-marketing': 0.6,
    'branding': 0.7,
    'consulting': 1.2,
    'all': 1.0
  };

  const baseRange = budgetRanges[budget];
  const multiplier = serviceMultipliers[serviceType];

  return {
    min: Math.round(baseRange.min * multiplier),
    max: Math.round(baseRange.max * multiplier),
    currency: 'USD'
  };
}

/**
 * Get recommended timeline based on service type and budget
 */
export function getRecommendedTimeline(serviceType: ServiceType, budget: BudgetRange): Timeline {
  // Higher budget projects typically have more flexibility
  if (budget === 'over-50k') {
    return 'flexible';
  }
  
  // Complex services need more time
  if (serviceType === 'mobile-apps' || serviceType === 'consulting') {
    return budget === 'under-5k' ? 'urgent' : 'standard';
  }
  
  // Simpler services can be done faster
  if (serviceType === 'branding' || serviceType === 'ui-ux-design') {
    return budget === 'under-5k' ? 'urgent' : 'standard';
  }
  
  return 'standard';
}

/**
 * Format tool execution error for display
 */
export function formatToolError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (typeof error === 'object' && error !== null) {
    return JSON.stringify(error);
  }
  
  return 'An unknown error occurred';
}

// Validation utilities for Consultant AI
import { z } from 'zod';
import { 
  getServiceInfoSchema,
  getCompanyInfoSchema,
  generateProposalSchema,
  type ServiceType,
  type BudgetRange,
  type Timeline,
  type CompanySize,
  type InfoType
} from '../types';

/**
 * Validate service info parameters
 */
export function validateServiceInfoParams(params: unknown): {
  isValid: boolean;
  data?: z.infer<typeof getServiceInfoSchema>;
  errors?: string[];
} {
  try {
    const data = getServiceInfoSchema.parse(params);
    return { isValid: true, data };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      isValid: false,
      errors: ['Invalid parameters']
    };
  }
}

/**
 * Validate company info parameters
 */
export function validateCompanyInfoParams(params: unknown): {
  isValid: boolean;
  data?: z.infer<typeof getCompanyInfoSchema>;
  errors?: string[];
} {
  try {
    const data = getCompanyInfoSchema.parse(params);
    return { isValid: true, data };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      isValid: false,
      errors: ['Invalid parameters']
    };
  }
}

/**
 * Validate proposal generation parameters
 */
export function validateProposalParams(params: unknown): {
  isValid: boolean;
  data?: z.infer<typeof generateProposalSchema>;
  errors?: string[];
} {
  try {
    const data = generateProposalSchema.parse(params);
    return { isValid: true, data };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      };
    }
    return {
      isValid: false,
      errors: ['Invalid parameters']
    };
  }
}

/**
 * Validate email address
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number (basic validation)
 */
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
  return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
}

/**
 * Validate URL
 */
export function validateUrl(url: string): boolean {
  try {
    new URL(url.startsWith('http') ? url : `https://${url}`);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate project requirements text
 */
export function validateRequirements(requirements: string): {
  isValid: boolean;
  errors?: string[];
  warnings?: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check minimum length
  if (requirements.length < 10) {
    errors.push('Requirements must be at least 10 characters long');
  }

  // Check maximum length
  if (requirements.length > 2000) {
    errors.push('Requirements must be less than 2000 characters');
  }

  // Check for meaningful content
  const wordCount = requirements.trim().split(/\s+/).length;
  if (wordCount < 5) {
    warnings.push('Consider providing more detailed requirements for a better proposal');
  }

  // Check for common issues
  if (requirements.toLowerCase().includes('asap') || requirements.toLowerCase().includes('urgent')) {
    warnings.push('Urgent projects may have limited availability and higher costs');
  }

  if (!requirements.includes('.') && !requirements.includes('!') && !requirements.includes('?')) {
    warnings.push('Consider using proper punctuation for clarity');
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Validate budget range consistency with project type
 */
export function validateBudgetConsistency(
  projectType: string,
  budget: BudgetRange
): {
  isValid: boolean;
  warnings?: string[];
} {
  const warnings: string[] = [];

  // Check if budget is realistic for project type
  const budgetWarnings: Record<string, Record<BudgetRange, string | null>> = {
    'mobile-app': {
      'under-5k': 'Mobile apps typically require a higher budget for quality development',
      '5k-15k': 'This budget may limit features for a mobile app',
      '15k-50k': null,
      'over-50k': null
    },
    'web-development': {
      'under-5k': 'Consider if this budget allows for all desired features',
      '5k-15k': null,
      '15k-50k': null,
      'over-50k': null
    },
    'ui-ux-design': {
      'under-5k': null,
      '5k-15k': null,
      '15k-50k': 'This budget is quite high for UI/UX design alone',
      'over-50k': 'This budget is very high for UI/UX design alone'
    },
    'branding': {
      'under-5k': null,
      '5k-15k': null,
      '15k-50k': 'This budget is quite high for branding alone',
      'over-50k': 'This budget is very high for branding alone'
    }
  };

  const warning = budgetWarnings[projectType]?.[budget];
  if (warning) {
    warnings.push(warning);
  }

  return {
    isValid: true, // Budget consistency doesn't invalidate, just warns
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Validate timeline feasibility
 */
export function validateTimelineFeasibility(
  projectType: string,
  timeline: Timeline,
  budget: BudgetRange
): {
  isValid: boolean;
  warnings?: string[];
} {
  const warnings: string[] = [];

  // Check if timeline is realistic for project type and budget
  if (timeline === 'urgent') {
    if (projectType === 'mobile-app') {
      warnings.push('Mobile apps typically require more time for quality development');
    }
    if (budget === 'under-5k') {
      warnings.push('Urgent projects with lower budgets may compromise quality');
    }
  }

  if (timeline === 'flexible' && budget === 'over-50k') {
    warnings.push('With a higher budget, we can deliver excellent results within standard timelines');
  }

  return {
    isValid: true, // Timeline feasibility doesn't invalidate, just warns
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Comprehensive validation for all consultant tool parameters
 */
export function validateConsultantToolParams(
  toolName: string,
  params: unknown
): {
  isValid: boolean;
  data?: any;
  errors?: string[];
  warnings?: string[];
} {
  switch (toolName) {
    case 'getServiceInfo':
      return validateServiceInfoParams(params);
    
    case 'getCompanyInfo':
      return validateCompanyInfoParams(params);
    
    case 'generateProposal':
      const proposalValidation = validateProposalParams(params);
      if (!proposalValidation.isValid || !proposalValidation.data) {
        return proposalValidation;
      }

      // Additional validations for proposal
      const { data } = proposalValidation;
      const allWarnings: string[] = [];

      // Validate requirements
      const reqValidation = validateRequirements(data.requirements);
      if (!reqValidation.isValid) {
        return {
          isValid: false,
          errors: reqValidation.errors
        };
      }
      if (reqValidation.warnings) {
        allWarnings.push(...reqValidation.warnings);
      }

      // Validate budget consistency
      const budgetValidation = validateBudgetConsistency(data.projectType, data.budget);
      if (budgetValidation.warnings) {
        allWarnings.push(...budgetValidation.warnings);
      }

      // Validate timeline feasibility
      const timelineValidation = validateTimelineFeasibility(
        data.projectType,
        data.timeline,
        data.budget
      );
      if (timelineValidation.warnings) {
        allWarnings.push(...timelineValidation.warnings);
      }

      return {
        isValid: true,
        data,
        warnings: allWarnings.length > 0 ? allWarnings : undefined
      };
    
    default:
      return {
        isValid: false,
        errors: [`Unknown tool: ${toolName}`]
      };
  }
}

/**
 * Sanitize user input for tool parameters
 */
export function sanitizeToolInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .slice(0, 2000); // Limit length
}

/**
 * Check if a string contains potentially harmful content
 */
export function containsHarmfulContent(content: string): boolean {
  const harmfulPatterns = [
    /javascript:/i,
    /<script/i,
    /on\w+\s*=/i,
    /data:text\/html/i,
    /vbscript:/i
  ];

  return harmfulPatterns.some(pattern => pattern.test(content));
}

// Formatting utilities for Consultant AI
import { type ServiceInfo, type CompanyInfo, type ProposalInfo } from '../types';

/**
 * Format currency amount
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * Format price range string
 */
export function formatPriceRange(priceString: string): {
  formatted: string;
  min?: number;
  max?: number;
  isRange: boolean;
} {
  // Handle monthly pricing
  if (priceString.includes('/month')) {
    return {
      formatted: priceString,
      isRange: false
    };
  }

  // Handle range pricing (e.g., "$10,000+")
  if (priceString.includes('+')) {
    const amount = parseFloat(priceString.replace(/[$,+]/g, ''));
    return {
      formatted: priceString,
      min: amount,
      isRange: true
    };
  }

  // Handle single price
  const amount = parseFloat(priceString.replace(/[$,]/g, ''));
  if (!isNaN(amount)) {
    return {
      formatted: formatCurrency(amount),
      min: amount,
      max: amount,
      isRange: false
    };
  }

  return {
    formatted: priceString,
    isRange: false
  };
}

/**
 * Format timeline string
 */
export function formatTimeline(timeline: string): {
  formatted: string;
  weeks?: number;
  isRange: boolean;
} {
  // Extract weeks from timeline string
  const weekMatch = timeline.match(/(\d+)(?:-(\d+))?\s*weeks?/i);
  
  if (weekMatch) {
    const minWeeks = parseInt(weekMatch[1]);
    const maxWeeks = weekMatch[2] ? parseInt(weekMatch[2]) : minWeeks;
    
    return {
      formatted: timeline,
      weeks: minWeeks,
      isRange: minWeeks !== maxWeeks
    };
  }

  return {
    formatted: timeline,
    isRange: false
  };
}

/**
 * Format service features for display
 */
export function formatServiceFeatures(features: string[]): {
  primary: string[];
  secondary: string[];
} {
  // Split features into primary (first 4) and secondary (rest)
  return {
    primary: features.slice(0, 4),
    secondary: features.slice(4)
  };
}

/**
 * Format technologies with categories
 */
export function formatTechnologies(technologies: string[]): {
  frontend: string[];
  backend: string[];
  tools: string[];
  other: string[];
} {
  const frontend = ['React', 'Next.js', 'Vue.js', 'Angular', 'TypeScript', 'JavaScript', 'Tailwind CSS', 'CSS', 'HTML'];
  const backend = ['Node.js', 'Python', 'PHP', 'Java', 'C#', '.NET', 'Ruby', 'Go'];
  const tools = ['Figma', 'Adobe Creative Suite', 'Sketch', 'InVision', 'Google Analytics', 'Google Ads', 'Facebook Ads', 'Mailchimp'];

  return {
    frontend: technologies.filter(tech => frontend.some(f => tech.includes(f))),
    backend: technologies.filter(tech => backend.some(b => tech.includes(b))),
    tools: technologies.filter(tech => tools.some(t => tech.includes(t))),
    other: technologies.filter(tech => 
      !frontend.some(f => tech.includes(f)) &&
      !backend.some(b => tech.includes(b)) &&
      !tools.some(t => tech.includes(t))
    )
  };
}

/**
 * Format company team member info
 */
export function formatTeamMember(member: any): {
  role: string;
  expertise: string[];
  displayName: string;
} {
  const expertise = typeof member.expertise === 'string' 
    ? member.expertise.split(',').map(s => s.trim())
    : member.expertise || [];

  return {
    role: member.role,
    expertise,
    displayName: member.role.replace(/([A-Z])/g, ' $1').trim()
  };
}

/**
 * Format proposal deliverables with categories
 */
export function formatProposalDeliverables(deliverables: string[]): {
  design: string[];
  development: string[];
  marketing: string[];
  other: string[];
} {
  const designKeywords = ['design', 'ui', 'ux', 'wireframe', 'prototype', 'visual', 'brand'];
  const developmentKeywords = ['development', 'app', 'website', 'api', 'backend', 'frontend', 'code'];
  const marketingKeywords = ['seo', 'marketing', 'social', 'analytics', 'advertising', 'content'];

  const categorize = (item: string, keywords: string[]) => 
    keywords.some(keyword => item.toLowerCase().includes(keyword));

  return {
    design: deliverables.filter(item => categorize(item, designKeywords)),
    development: deliverables.filter(item => categorize(item, developmentKeywords)),
    marketing: deliverables.filter(item => categorize(item, marketingKeywords)),
    other: deliverables.filter(item => 
      !categorize(item, designKeywords) &&
      !categorize(item, developmentKeywords) &&
      !categorize(item, marketingKeywords)
    )
  };
}

/**
 * Format next steps with priorities
 */
export function formatNextSteps(steps: string[]): Array<{
  step: string;
  priority: 'high' | 'medium' | 'low';
  estimated_time: string;
}> {
  const priorities = ['high', 'high', 'medium', 'medium', 'low'];
  const estimatedTimes = ['1-2 days', '3-5 days', '1 week', '1-2 weeks', '2-3 weeks'];

  return steps.map((step, index) => ({
    step,
    priority: (priorities[index] || 'low') as 'high' | 'medium' | 'low',
    estimated_time: estimatedTimes[index] || '1-2 weeks'
  }));
}

/**
 * Format contact information
 */
export function formatContactInfo(contact: any): {
  email: string;
  phone: string;
  website: string;
  formatted_phone: string;
  formatted_website: string;
} {
  const formatPhone = (phone: string) => {
    // Remove all non-digits
    const digits = phone.replace(/\D/g, '');
    
    // Format as (XXX) XXX-XXXX for US numbers
    if (digits.length === 11 && digits.startsWith('1')) {
      const number = digits.slice(1);
      return `+1 (${number.slice(0, 3)}) ${number.slice(3, 6)}-${number.slice(6)}`;
    } else if (digits.length === 10) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
    }
    
    return phone; // Return original if can't format
  };

  const formatWebsite = (website: string) => {
    if (!website.startsWith('http')) {
      return `https://${website}`;
    }
    return website;
  };

  return {
    email: contact.email || '',
    phone: contact.phone || '',
    website: contact.website || '',
    formatted_phone: contact.phone ? formatPhone(contact.phone) : '',
    formatted_website: contact.website ? formatWebsite(contact.website) : ''
  };
}

/**
 * Format duration string to human readable
 */
export function formatDuration(duration: string): string {
  // Convert various duration formats to human readable
  const patterns = [
    { regex: /(\d+)-(\d+)\s*weeks?/i, format: (m: RegExpMatchArray) => `${m[1]}-${m[2]} weeks` },
    { regex: /(\d+)\s*weeks?/i, format: (m: RegExpMatchArray) => `${m[1]} week${parseInt(m[1]) > 1 ? 's' : ''}` },
    { regex: /(\d+)-(\d+)\s*months?/i, format: (m: RegExpMatchArray) => `${m[1]}-${m[2]} months` },
    { regex: /(\d+)\s*months?/i, format: (m: RegExpMatchArray) => `${m[1]} month${parseInt(m[1]) > 1 ? 's' : ''}` },
  ];

  for (const pattern of patterns) {
    const match = duration.match(pattern.regex);
    if (match) {
      return pattern.format(match);
    }
  }

  return duration; // Return original if no pattern matches
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.slice(0, maxLength - 3) + '...';
}

/**
 * Format large numbers with K, M suffixes
 */
export function formatLargeNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

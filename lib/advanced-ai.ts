import { xai } from "@ai-sdk/xai"
import { generateText, streamText, generateObject } from "ai"
import type { z } from "zod"

export interface AICapability {
  id: string
  name: string
  description: string
  icon: string
  category: "generation" | "analysis" | "automation" | "communication"
}

export const AI_CAPABILITIES: AICapability[] = [
  {
    id: "code-generation",
    name: "Code Generation",
    description: "Generate production-ready code in multiple languages",
    icon: "Code",
    category: "generation",
  },
  {
    id: "content-writing",
    name: "Content Writing",
    description: "Create marketing copy, blog posts, and documentation",
    icon: "PenTool",
    category: "generation",
  },
  {
    id: "project-analysis",
    name: "Project Analysis",
    description: "Analyze project requirements and suggest solutions",
    icon: "BarChart3",
    category: "analysis",
  },
  {
    id: "ui-generation",
    name: "UI Generation",
    description: "Generate React components and UI layouts",
    icon: "Layout",
    category: "generation",
  },
  {
    id: "workflow-automation",
    name: "Workflow Automation",
    description: "Automate repetitive tasks and processes",
    icon: "Zap",
    category: "automation",
  },
  {
    id: "voice-assistant",
    name: "Voice Assistant",
    description: "Voice-powered AI interactions and commands",
    icon: "Mic",
    category: "communication",
  },
]

export class AdvancedAI {
  private context: Array<{ role: string; content: string }> = []
  private userPreferences: Record<string, any> = {}

  async generateWithContext(prompt: string, capability: string, options?: any) {
    try {
      const systemPrompt = this.getSystemPrompt(capability)
      const contextualPrompt = this.buildContextualPrompt(prompt, capability)

      const { text } = await generateText({
        model: xai("grok-beta"),
        prompt: contextualPrompt,
        system: systemPrompt,
        temperature: options?.temperature || 0.7,
        maxTokens: options?.maxTokens || 2000,
      })

      // Update context
      this.updateContext("user", prompt)
      this.updateContext("assistant", text)

      return { text, error: null }
    } catch (error) {
      console.error("Advanced AI error:", error)
      return { text: this.getFallbackResponse(capability, prompt), error }
    }
  }

  async streamWithContext(
    prompt: string,
    capability: string,
    onChunk: (chunk: string) => void,
    onComplete: (text: string) => void,
  ) {
    try {
      const systemPrompt = this.getSystemPrompt(capability)
      const contextualPrompt = this.buildContextualPrompt(prompt, capability)

      const result = streamText({
        model: xai("grok-beta"),
        prompt: contextualPrompt,
        system: systemPrompt,
        temperature: 0.7,
      })

      let fullText = ""

      for await (const chunk of result.textStream) {
        fullText += chunk
        onChunk(chunk)
      }

      this.updateContext("user", prompt)
      this.updateContext("assistant", fullText)
      onComplete(fullText)

      return fullText
    } catch (error) {
      console.error("Streaming error:", error)
      const fallback = this.getFallbackResponse(capability, prompt)
      onComplete(fallback)
      return fallback
    }
  }

  async generateStructured(prompt: string, schema: z.ZodSchema) {
    try {
      const result = await generateObject({
        model: xai("grok-beta"),
        prompt,
        schema,
        system: "You are a structured data generation assistant. Always follow the provided schema exactly.",
      })

      return { data: result.object, error: null }
    } catch (error) {
      console.error("Structured generation error:", error)
      return { data: null, error }
    }
  }

  private getSystemPrompt(capability: string): string {
    const prompts = {
      "code-generation": `You are an expert software developer at Soimagine Digital Agency. Generate clean, production-ready code with:
- Modern best practices and patterns
- Comprehensive error handling
- Clear documentation and comments
- Responsive design principles
- Accessibility considerations
- Performance optimizations`,

      "content-writing": `You are a professional content writer for Soimagine Digital Agency. Create engaging content that:
- Matches the brand voice and tone
- Is optimized for SEO
- Includes compelling calls-to-action
- Follows content marketing best practices
- Resonates with the target audience`,

      "project-analysis": `You are a senior project analyst at Soimagine Digital Agency. Provide detailed analysis including:
- Technical requirements breakdown
- Technology stack recommendations
- Timeline and milestone planning
- Risk assessment and mitigation
- Resource allocation suggestions
- Cost estimation guidelines`,

      "ui-generation": `You are a UI/UX expert at Soimagine Digital Agency. Generate components that are:
- Accessible and inclusive
- Mobile-first and responsive
- Following modern design principles
- Optimized for performance
- Consistent with design systems
- User-centered and intuitive`,

      "workflow-automation": `You are an automation specialist at Soimagine Digital Agency. Design workflows that:
- Eliminate manual repetitive tasks
- Integrate with existing systems
- Include proper error handling
- Are scalable and maintainable
- Follow security best practices
- Provide clear monitoring and logging`,

      "voice-assistant": `You are Soimagine's voice AI assistant. Provide responses that are:
- Conversational and natural
- Concise but informative
- Action-oriented when appropriate
- Contextually aware
- Professional yet friendly`,
    }

    return prompts[capability] || prompts["voice-assistant"]
  }

  private buildContextualPrompt(prompt: string, capability: string): string {
    const recentContext = this.context.slice(-6) // Last 3 exchanges
    const contextString = recentContext.map((c) => `${c.role}: ${c.content}`).join("\n")

    return `Context from previous conversation:
${contextString}

Current request (${capability}): ${prompt}

Please provide a response that takes into account the previous context and the specific capability requested.`
  }

  private updateContext(role: string, content: string) {
    this.context.push({ role, content })
    // Keep only last 20 messages to prevent context overflow
    if (this.context.length > 20) {
      this.context = this.context.slice(-20)
    }
  }

  private getFallbackResponse(capability: string, prompt: string): string {
    const fallbacks = {
      "code-generation": `// Soimagine Code Generation
// Request: ${prompt}

function soimagineGenerated() {
  // This is a fallback response when AI services are unavailable
  console.log("Soimagine Digital Agency - Code Template");
  
  // TODO: Implement your specific requirements here
  const result = {
    status: "template-generated",
    message: "Please customize this template for your needs"
  };
  
  return result;
}

export default soimagineGenerated;`,

      "content-writing": `# Soimagine Content Template

Based on your request: "${prompt}"

At Soimagine Digital Agency, we understand the importance of compelling content that drives results. Our team of expert content creators specializes in crafting messages that resonate with your target audience and achieve your business objectives.

## Key Benefits:
- Professional content strategy
- SEO-optimized writing
- Brand-consistent messaging
- Conversion-focused copy

Ready to elevate your content? Contact Soimagine today to discuss your project requirements.`,

      "project-analysis": `# Project Analysis Template
## Request: ${prompt}

### Executive Summary
This analysis provides a comprehensive overview of the project requirements and recommendations for successful implementation.

### Technical Requirements
- Modern web technologies (React, Next.js, TypeScript)
- Responsive design implementation
- API integration capabilities
- Database design and optimization

### Recommended Timeline
- Phase 1: Planning and Design (2-3 weeks)
- Phase 2: Development (4-6 weeks)
- Phase 3: Testing and Deployment (1-2 weeks)

### Next Steps
Contact Soimagine to discuss detailed requirements and begin project planning.`,

      "ui-generation": `<!-- Soimagine UI Component Template -->
<!-- Request: ${prompt} -->

<div class="soimagine-component">
  <div class="component-header">
    <h2 class="component-title">Generated Component</h2>
    <p class="component-description">
      This is a template component generated by Soimagine's AI system.
    </p>
  </div>
  
  <div class="component-content">
    <!-- Your custom content here -->
    <p>Customize this component to meet your specific requirements.</p>
  </div>
  
  <div class="component-actions">
    <button class="btn btn-primary">Primary Action</button>
    <button class="btn btn-secondary">Secondary Action</button>
  </div>
</div>

<style>
.soimagine-component {
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  background: #ffffff;
}
</style>`,

      "workflow-automation": `# Workflow Automation Template
## Process: ${prompt}

### Automation Overview
This workflow template provides a foundation for automating your business processes using modern tools and best practices.

### Key Components:
1. **Trigger Events** - Define what initiates the workflow
2. **Processing Steps** - Automated actions and decisions
3. **Integration Points** - Connect with existing systems
4. **Monitoring & Alerts** - Track performance and issues

### Implementation Steps:
1. Map current manual process
2. Identify automation opportunities
3. Design workflow logic
4. Implement and test
5. Deploy and monitor

Contact Soimagine to implement custom automation solutions for your business.`,

      "voice-assistant": `I'm here to help you with your digital agency needs! While I'm currently operating in fallback mode, I can still assist you with:

• Web development guidance and best practices
• Mobile app development strategies
• AI solution recommendations
• Design and user experience advice
• Project planning and management tips

What specific aspect of your project would you like to explore? I'm ready to provide detailed guidance and recommendations based on Soimagine's expertise in digital solutions.`,
    }

    return fallbacks[capability] || fallbacks["voice-assistant"]
  }

  setUserPreferences(preferences: Record<string, any>) {
    this.userPreferences = { ...this.userPreferences, ...preferences }
  }

  clearContext() {
    this.context = []
  }

  getContext() {
    return [...this.context]
  }
}

export const advancedAI = new AdvancedAI()

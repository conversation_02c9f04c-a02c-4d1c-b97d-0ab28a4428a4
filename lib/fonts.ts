import localFont from 'next/font/local'

// Century Gothic Font Family
export const centuryGothic = localFont({
  src: [
    {
      path: '../assets/fonts/century_gothic.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../assets/fonts/century_gothic_bold.ttf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../assets/fonts/century_gothic_italic.ttf',
      weight: '400',
      style: 'italic',
    },
    {
      path: '../assets/fonts/century_gothic_bold_italic.ttf',
      weight: '700',
      style: 'italic',
    },
  ],
  variable: '--font-century-gothic',
  display: 'swap',
  fallback: ['Arial', 'sans-serif'],
})

// Circular Std Font Family
export const circularStd = localFont({
  src: [
    {
      path: '../assets/fonts/CircularStd-Light.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../assets/fonts/CircularStd-Book.otf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../assets/fonts/CircularStd-Bold.otf',
      weight: '700',
      style: 'normal',
    },
  ],
  variable: '--font-circular-std',
  display: 'swap',
  fallback: ['Helvetica Neue', 'Arial', 'sans-serif'],
})

// Font class names for easy usage
export const fontClassNames = {
  centuryGothic: centuryGothic.className,
  circularStd: circularStd.className,
  centuryGothicVariable: centuryGothic.variable,
  circularStdVariable: circularStd.variable,
}

// CSS variables for use in components
export const fontVariables = `${centuryGothic.variable} ${circularStd.variable}`

import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from './auth'
import { prisma } from './prisma'

export interface CreditUsageOptions {
  creditsRequired: number
  description: string
  source: string
  assistantType?: string
}

export async function withCreditCheck(
  req: NextRequest,
  options: CreditUsageOptions,
  handler: (req: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's credit balance
    const creditBalance = await prisma.creditBalance.findUnique({
      where: { userId: session.user.id }
    })

    if (!creditBalance) {
      return NextResponse.json(
        { error: 'Credit balance not found' },
        { status: 404 }
      )
    }

    // Check if user has enough credits
    if (creditBalance.totalCredits < options.creditsRequired) {
      return NextResponse.json(
        { 
          error: 'Insufficient credits',
          required: options.creditsRequired,
          available: creditBalance.totalCredits
        },
        { status: 402 } // Payment Required
      )
    }

    // Check daily limit
    if (creditBalance.dailyCredits < options.creditsRequired) {
      return NextResponse.json(
        { 
          error: 'Daily credit limit exceeded',
          required: options.creditsRequired,
          dailyAvailable: creditBalance.dailyCredits
        },
        { status: 429 } // Too Many Requests
      )
    }

    // Check monthly limit
    if (creditBalance.monthlyCredits < options.creditsRequired) {
      return NextResponse.json(
        { 
          error: 'Monthly credit limit exceeded',
          required: options.creditsRequired,
          monthlyAvailable: creditBalance.monthlyCredits
        },
        { status: 429 } // Too Many Requests
      )
    }

    // Deduct credits before processing
    const newTotalCredits = creditBalance.totalCredits - options.creditsRequired
    const newDailyCredits = creditBalance.dailyCredits - options.creditsRequired
    const newMonthlyCredits = creditBalance.monthlyCredits - options.creditsRequired

    await prisma.$transaction(async (tx) => {
      // Update credit balance
      await tx.creditBalance.update({
        where: { userId: session.user.id },
        data: {
          totalCredits: newTotalCredits,
          dailyCredits: newDailyCredits,
          monthlyCredits: newMonthlyCredits
        }
      })

      // Record credit transaction
      await tx.creditTransaction.create({
        data: {
          userId: session.user.id,
          type: 'SPENT',
          amount: options.creditsRequired,
          description: options.description,
          source: options.source,
          metadata: {
            assistantType: options.assistantType,
            endpoint: req.url,
            userAgent: req.headers.get('user-agent'),
            timestamp: new Date().toISOString()
          },
          balanceAfter: newTotalCredits
        }
      })

      // Record API usage
      await tx.apiUsage.create({
        data: {
          userId: session.user.id,
          endpoint: req.url,
          method: req.method,
          assistantType: options.assistantType,
          creditsUsed: options.creditsRequired,
          status: 'SUCCESS',
          userAgent: req.headers.get('user-agent'),
          metadata: {
            description: options.description,
            source: options.source
          }
        }
      })
    })

    // Process the original request
    const response = await handler(req)

    // Add credit usage info to response headers
    response.headers.set('X-Credits-Used', options.creditsRequired.toString())
    response.headers.set('X-Credits-Remaining', newTotalCredits.toString())
    response.headers.set('X-Daily-Credits-Remaining', newDailyCredits.toString())
    response.headers.set('X-Monthly-Credits-Remaining', newMonthlyCredits.toString())

    return response

  } catch (error) {
    console.error('Credit middleware error:', error)
    
    // Record failed API usage
    try {
      const session = await getServerSession(authOptions)
      if (session?.user?.id) {
        await prisma.apiUsage.create({
          data: {
            userId: session.user.id,
            endpoint: req.url,
            method: req.method,
            assistantType: options.assistantType,
            creditsUsed: 0,
            status: 'ERROR',
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            userAgent: req.headers.get('user-agent'),
            metadata: {
              description: options.description,
              source: options.source,
              error: error instanceof Error ? error.stack : 'Unknown error'
            }
          }
        })
      }
    } catch (logError) {
      console.error('Failed to log API usage error:', logError)
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to calculate credit cost based on request complexity
export function calculateCreditCost(
  messageCount: number,
  assistantType: string,
  hasToolCalls: boolean = false
): number {
  let baseCost = 1

  // Different costs for different assistant types
  switch (assistantType) {
    case 'consultant':
      baseCost = 2
      break
    case 'engineer':
      baseCost = 3
      break
    case 'executive':
      baseCost = 5
      break
    default:
      baseCost = 1
  }

  // Additional cost for tool calls
  if (hasToolCalls) {
    baseCost += 2
  }

  // Scale with message count (longer conversations cost more)
  const messageCostMultiplier = Math.min(1 + (messageCount - 1) * 0.1, 2)
  
  return Math.ceil(baseCost * messageCostMultiplier)
}

import nodemailer from 'nodemailer'

const transport = nodemailer.createTransport({
  host: process.env.EMAIL_SERVER_HOST,
  port: Number(process.env.EMAIL_SERVER_PORT),
  secure: process.env.EMAIL_SERVER_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASSWORD,
  },
})

export async function sendVerificationEmail(
  email: string,
  name: string,
  token: string
) {
  const confirmUrl = `${process.env.NEXT_PUBLIC_APP_URL}/verify-email?token=${token}`

  await transport.sendMail({
    from: process.env.EMAIL_FROM,
    to: email,
    subject: 'Verify your email address',
    html: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #333; font-size: 24px; margin-bottom: 20px;">Welcome to Soimagine!</h1>
        <p style="color: #666; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          Hi ${name},
        </p>
        <p style="color: #666; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          Thanks for registering for an account. To complete your registration, please verify your email address by clicking the button below:
        </p>
        <a
          href="${confirmUrl}"
          style="display: inline-block; background-color: #0284c7; color: white; padding: 12px 24px; border-radius: 4px; text-decoration: none; margin-bottom: 20px;"
        >
          Verify Email Address
        </a>
        <p style="color: #666; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          If you didn't create an account, you can safely ignore this email.
        </p>
        <p style="color: #666; font-size: 14px;">
          This link will expire in 24 hours.
        </p>
      </div>
    `,
  })
}

export async function sendPasswordResetEmail(
  email: string,
  name: string,
  token: string
) {
  const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${token}`

  await transport.sendMail({
    from: process.env.EMAIL_FROM,
    to: email,
    subject: 'Reset your password',
    html: `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #333; font-size: 24px; margin-bottom: 20px;">Reset Your Password</h1>
        <p style="color: #666; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          Hi ${name},
        </p>
        <p style="color: #666; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          We received a request to reset your password. Click the button below to create a new password:
        </p>
        <a
          href="${resetUrl}"
          style="display: inline-block; background-color: #0284c7; color: white; padding: 12px 24px; border-radius: 4px; text-decoration: none; margin-bottom: 20px;"
        >
          Reset Password
        </a>
        <p style="color: #666; font-size: 16px; line-height: 1.5; margin-bottom: 20px;">
          If you didn't request a password reset, you can safely ignore this email.
        </p>
        <p style="color: #666; font-size: 14px;">
          This link will expire in 1 hour.
        </p>
      </div>
    `,
  })
}

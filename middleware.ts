import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth({
  callbacks: {
    authorized({ req, token }) {
      // Define public routes that don't require auth
      const publicRoutes = ['/login', '/signup', '/verify-email', '/reset-password']
      const publicPatterns = [
        '/api/auth',
        '/_next',
        '/fonts',
        '/images',
        '/favicon.ico',
        '/manifest.json',
        '/robots.txt'
      ]

      // Check if route is public
      const isPublicRoute = publicRoutes.some(route => 
        req.nextUrl.pathname === route || req.nextUrl.pathname.startsWith(`${route}/`)
      )
      const isPublicPattern = publicPatterns.some(pattern => 
        req.nextUrl.pathname.startsWith(pattern)
      )

      // Always allow access to public routes and patterns
      if (isPublicRoute || isPublicPattern) {
        return true
      }

      // No token means not authenticated
      if (!token) {
        const redirectUrl = new URL('/login', req.url)
        redirectUrl.searchParams.set('callbackUrl', req.url)
        throw new Error(`redirect=${redirectUrl}`)
      }

      // Admin routes require admin role
      if (req.nextUrl.pathname.startsWith('/admin')) {
        if (token.role !== 'ADMIN') {
          return false
        }
        return true
      }

      // Protected routes require email verification
      if (
        req.nextUrl.pathname.startsWith('/dashboard') ||
        req.nextUrl.pathname.startsWith('/engineer') ||
        req.nextUrl.pathname.startsWith('/assistant')
      ) {
        if (!token.emailVerified) {
          // Redirect to verify-email page
          const redirectUrl = new URL('/verify-email', req.url)
          throw new Error(`redirect=${redirectUrl}`)
        }
        return true
      }

      // Allow access to all other routes for authenticated users
      return true
    },
  },
  pages: {
    signIn: '/login',
    error: '/auth/error',
  },
})

// Configure which routes to protect
export const config = {
  matcher: [
    // Protected routes
    '/dashboard/:path*',
    '/engineer/:path*',
    '/assistant/:path*',
    '/admin/:path*',
    
    // Auth routes
    '/login',
    '/signup',
    '/verify-email',
    '/reset-password',
    
    // Exclude static files and API routes
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|manifest.json|images|fonts).*)',
  ],
}

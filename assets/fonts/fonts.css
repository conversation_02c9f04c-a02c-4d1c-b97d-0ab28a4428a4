/* Custom Fonts for Soimagine Digital Agency */

/* Century Gothic Font Family */
@font-face {
    font-family: 'Century Gothic';
    src: url('./century_gothic.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Century Gothic';
    src: url('./century_gothic_bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Century Gothic';
    src: url('./century_gothic_italic.ttf') format('truetype');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Century Gothic';
    src: url('./century_gothic_bold_italic.ttf') format('truetype');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

/* Circular Std Font Family */
@font-face {
    font-family: 'Circular Std';
    src: url('./CircularStd-Light.otf') format('opentype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Circular Std';
    src: url('./CircularStd-Book.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Circular Std';
    src: url('./CircularStd-Bold.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* CSS Custom Properties for Font Families */
:root {
    --font-century-gothic: 'Century Gothic', 'Arial', sans-serif;
    --font-circular-std: 'Circular Std', 'Helvetica Neue', 'Arial', sans-serif;
    --font-primary: var(--font-circular-std);
    --font-secondary: var(--font-century-gothic);
}

/* Utility Classes */
.font-century-gothic {
    font-family: var(--font-century-gothic);
}

.font-circular-std {
    font-family: var(--font-circular-std);
}

.font-primary {
    font-family: var(--font-primary);
}

.font-secondary {
    font-family: var(--font-secondary);
}
{"name": "soimagine-2025", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx scripts/seed-database.ts", "db:studio": "prisma studio", "db:reset": "prisma db push --force-reset && npm run db:seed"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "latest", "@anthropic-ai/sdk": "^0.57.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/is-prop-valid": "latest", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@react-spring/web": "^10.0.1", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@tanstack/react-query": "^5.84.1", "@tanstack/react-table": "^8.21.3", "@types/nodemailer": "^6.4.17", "ai": "latest", "apexcharts": "^5.3.2", "autoprefixer": "^10.4.20", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cropperjs": "^2.0.1", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "immer": "^10.1.1", "input-otp": "1.4.1", "lodash": "^4.17.21", "lucide-react": "^0.454.0", "moment": "^2.30.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "latest", "nextjs-toploader": "^3.8.16", "nodemailer": "^7.0.3", "openai": "^5.11.0", "prisma": "^6.8.2", "quill": "^2.0.3", "react": "^19", "react-apexcharts": "^1.7.0", "react-big-calendar": "^1.19.4", "react-calendar": "^6.0.0", "react-chartjs-2": "^5.3.0", "react-color": "^2.19.3", "react-cropper": "^2.3.3", "react-day-picker": "8.10.1", "react-dom": "^19", "react-dropzone": "^14.3.8", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.1", "react-hotkeys-hook": "^5.1.0", "react-image-crop": "^11.0.10", "react-intersection-observer": "^9.16.0", "react-player": "^3.3.1", "react-quill": "^2.0.0", "react-resizable-panels": "^2.1.7", "react-spring": "^10.0.1", "recharts": "2.15.0", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "use-debounce": "^10.0.5", "vaul": "^0.9.6", "zod": "latest", "zustand": "^5.0.5"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/lodash": "^4.17.17", "@types/next-auth": "^3.15.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.176.0", "postcss": "^8", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5"}, "packageManager": "pnpm@10.10.0"}
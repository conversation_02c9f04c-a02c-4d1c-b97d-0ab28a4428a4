# AI-Powered Hero Component

## Overview

The `AIHero` component is a modified version of the original `ThreeDHero` component that integrates AI chat functionality while maintaining the beautiful particle animation background. This component serves as the main AI interface for the Soimagine digital agency application.

## Features

### ✨ Core Features
- **Particle Animation Background**: Maintains the original animated particle system with connected nodes
- **AI Chat Interface**: Full-featured chat interface with streaming responses
- **Voice Integration**: Real-time voice chat capabilities using LiveKit Agents (mock implementation included)
- **Responsive Design**: Adapts seamlessly to different screen sizes
- **Smooth Animations**: Framer Motion animations for enhanced user experience

### 🤖 AI Integration
- **Streaming Responses**: Real-time AI response streaming using AI SDK with xai/Grok model
- **Fallback System**: Robust fallback responses when AI service is unavailable
- **Context Awareness**: AI assistant specialized for digital agency tasks
- **Error Handling**: Graceful error handling with user-friendly messages

### 🎙️ Voice Features
- **Voice Input**: Microphone button for voice-to-text input
- **Voice Output**: Text-to-speech for AI responses (when voice is enabled)
- **Recording Indicator**: Visual feedback during voice recording
- **Browser Speech API**: Uses native browser speech synthesis and recognition

### 💬 Chat Interface
- **Message History**: Persistent chat history during session
- **Streaming Indicators**: Visual indicators for streaming responses
- **Quick Actions**: Pre-defined buttons for common queries
- **Message Formatting**: Proper formatting for user and assistant messages
- **Auto-scroll**: Automatic scrolling to latest messages

## Component Structure

```
AIHero/
├── Particle Animation Canvas (Background)
├── Hero Section (Initial View)
│   ├── Title and Description
│   └── Action Buttons
└── Chat Interface (Activated View)
    ├── Header with Controls
    ├── Messages Area
    └── Input Area with Voice Support
```

## Usage

### Basic Implementation

```tsx
import { AIHero } from "@/components/ai-hero"

export default function HomePage() {
  return (
    <main>
      <AIHero />
    </main>
  )
}
```

### Demo Page
A demo page is available at `/ai-demo` to test the component functionality.

## Dependencies

### Required Packages
- `@ai-sdk/xai` - AI SDK for xai/Grok integration
- `ai` - Core AI SDK functionality
- `framer-motion` - Animation library
- `lucide-react` - Icon components
- `@radix-ui/*` - UI component primitives

### UI Components Used
- `Button` - Interactive buttons
- `Input` - Text input field
- `Card` & `CardContent` - Container components
- `ScrollArea` - Scrollable message area

## Configuration

### AI Model Configuration
The component uses the xai "grok-beta" model by default. This can be modified in the `handleSendMessage` function:

```tsx
const result = streamText({
  model: xai("grok-beta"), // Change model here
  prompt: input,
  system: "Your custom system prompt here",
})
```

### Voice Settings
Voice functionality can be customized through the `mockLiveKitService` object:

```tsx
const mockLiveKitService: LiveKitService = {
  // Customize voice settings here
  speak: (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.rate = 1.0 // Adjust speech rate
      utterance.pitch = 1.0 // Adjust pitch
      window.speechSynthesis.speak(utterance)
    }
  }
}
```

## Customization

### Styling
The component uses Tailwind CSS classes and can be customized by modifying:
- Color schemes in particle animation
- Card styling and backdrop effects
- Message bubble appearance
- Button variants and sizes

### Particle Animation
Particle system can be customized by modifying:
- Particle count: `Math.floor((canvas.width * canvas.height) / 10000)`
- Particle colors: `rgba(233, 30, 99, ${Math.random() * 0.5 + 0.2})`
- Connection distance: `if (distance < 100)`
- Animation speed: `particle.speed = Math.random() * 0.5 + 0.1`

### AI Behavior
Customize AI responses by modifying:
- System prompt in `handleSendMessage`
- Fallback responses in `lib/fallback-ai.ts`
- Streaming behavior and error handling

## Integration with LiveKit Agents

For production use with real LiveKit Agents:

1. Install LiveKit SDK: `npm install livekit-client`
2. Replace `mockLiveKitService` with actual LiveKit implementation
3. Configure LiveKit server connection
4. Implement real-time audio processing

### Example LiveKit Integration

```tsx
import { Room, connect } from 'livekit-client'

const connectToLiveKit = async () => {
  const room = new Room()
  await room.connect(wsURL, token)
  // Implement real LiveKit functionality
}
```

## Performance Considerations

- Particle animation is optimized with `requestAnimationFrame`
- Canvas resizing is handled efficiently
- Message streaming prevents UI blocking
- Memory cleanup on component unmount

## Browser Compatibility

- **Modern Browsers**: Full functionality including voice features
- **Safari**: Speech synthesis supported, may need user interaction
- **Mobile**: Touch-optimized interface, voice features may vary
- **Fallback**: Graceful degradation when features unavailable

## Future Enhancements

- Real LiveKit Agents integration
- Message persistence across sessions
- Advanced voice commands
- Multi-language support
- Custom AI model selection
- Enhanced accessibility features

## Troubleshooting

### Common Issues

1. **AI responses not working**: Check xai API configuration and network connectivity
2. **Voice features not working**: Ensure HTTPS and user permissions for microphone
3. **Particle animation performance**: Reduce particle count on lower-end devices
4. **Styling issues**: Verify Tailwind CSS configuration and component imports

### Debug Mode
Enable console logging by uncommenting debug statements in the component for troubleshooting.
